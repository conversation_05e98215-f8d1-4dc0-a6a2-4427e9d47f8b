import eslintPluginReact from 'eslint-plugin-react/configs/recommended.js';

const reactConfig = {
  ...eslintPluginReact,
  settings: {
    react: {
      version: 'detect',
    },
  },
  rules: {
    ...eslintPluginReact.rules,
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
  },
  files: ['**/*.ts', '**/*.tsx'],
  ignores: ['legacy/*', 'react-stitch/*'],
};

export { reactConfig };
