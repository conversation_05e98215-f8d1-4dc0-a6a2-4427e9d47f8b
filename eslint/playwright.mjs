import eslintPluginPlaywright from 'eslint-plugin-playwright';

const playwrightConfig = {
  ...eslintPluginPlaywright.configs['flat/recommended'],
  files: ['user-journeys/**'],
  ignores: ['user-journeys/playwright-report/trace/**/*.js'],
  rules: {
    ...eslintPluginPlaywright.configs['flat/recommended'].rules,
    '@typescript-eslint/no-floating-promises': 'error',
    // playwright conditionals are expected due to reusing same tests for multiple browsers & brands
    'playwright/no-conditional-in-test': 'off',
    'playwright/no-conditional-expect': 'off',
  },
};

export { playwrightConfig };
