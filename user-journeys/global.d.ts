import type { Page } from '@playwright/test';

export interface CommonTealiumTags {
  brand_code: string;
  brand_name: string;
  brand_number: string;
  brand_short_name: string;
  business_unit_abbr_name: string;
  business_unit_description: string;
  business_unit_id: string;
  channel: string;
  country_code: string;
  currency_code: string;
  entry_brand: string;
  is_react: string;
  tealium_account: string;
  tealium_profile: string;
}

export interface CommonAdobeTags {
  'Currency Code': string;
  Hier1: string;
  'Page Name': string;
  'Site Section': string;
  ce: string;
  eVar35: string;
  eVar4: string;
  eVar44: string;
  eVar49: string;
  eVar76: string;
  ns: string;
  prop11: string;
  prop32: string;
  prop33: string;
}

export interface CustomPage extends Page {
  capturedBundleFiles: RecordedJSFile[];
  currentBundleSize: number[];
  haveAppliedSegments: boolean;
  isStillEnglish: boolean;
}

export interface RecordedJSFile {
  size: string;
  url: string;
}

export interface user {
  firstName?: string;
  lastName?: string;
  mobilePhone?: string;
}

export interface Address {
  city?: string;
  phone?: string;
  state?: string;
  street?: string;
  zipCode?: string;
}
