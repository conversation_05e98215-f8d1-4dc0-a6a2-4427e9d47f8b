/* eslint-disable playwright/no-force-option */
// @ts-check
import { expect, Locator, Request } from '@playwright/test';
import * as allure from 'allure-js-commons';
import { getPageType, scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, scrollToTop, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Search`, { tag: ['@category'] }, () => {
  test.describe(`Rewrite`, { tag: ['@run-all'] }, () => {
    test.beforeEach(async ({ page, rewriteSearchPage, sitewide }) => {
      // await page.route(/cn.*\.jpe?g/, route => route.abort('aborted'));
      await allure.description('Search tests have product images removed to improve performance in CI.');
      switch (true) {
        case test.info().title.includes('Search from home page'):
          await test.step(`Navigate to home page`, async () => {
            await page.goto(``);
          });
          break;
        case test.info().title.includes('pagination') || test.info().title.includes('Department'):
          // eslint-disable-next-line no-case-declarations
          const searchProduct = `${brand}${market}` === 'atca' ? 'bottoms' : 'shirts';
          await test.step(`Navigate to search page with results from the term '${searchProduct}'`, async () => {
            await rewriteSearchPage.navigateToSearchPage(searchProduct);
          });
          break;
        case test.info().title.includes('markdown or % off'):
          // eslint-disable-next-line no-case-declarations
          const searchSale = 'shirts sale';
          await test.step(`Navigate to search page with results from the term '${searchSale}'`, async () => {
            await rewriteSearchPage.navigateToSearchPage(searchSale);
            await waitForHydration(page);
            await rewriteSearchPage.waitForProductCard();
            await rewriteSearchPage.goToSort('priceLowHigh');
          });
          break;
        default:
          await test.step(`Navigate to search page with results from the term 'shirts'`, async () => {
            await rewriteSearchPage.navigateToSearchPage();
          });
          break;
      }
      await test.step(`Ensure we are on a fully Hydrated NextJS page.`, async () => {
        await waitForHydration(page);
      });

      await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
        await sitewide.checkSiteWideElementsAreOnPage();
      });
    });

    test.describe(`RESULTS`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Search from home page and click a search result to navigate to Product Page`, async ({
        page,
        rewriteSearchPage,
      }) => {
        await test.step(`Search from home page`, async () => {
          await waitForHydration(page);
          await rewriteSearchPage.searchForUsingSitewideSearch('shirts');
          await waitForHydration(page);
          await rewriteSearchPage.waitForProductCard();
          await scrollToBottomUntilPageDoesNotGrow(page);
        });

        await test.step(`Click on a random result`, async () => {
          await rewriteSearchPage.selectRandomProduct();
          await waitForHydration(page);
        });

        await test.step(`Validate you can see the product page`, async () => {
          await expect.poll(async () => await getPageType(page), 'validate we made it to a product page by check the page type').toBe('product');
        });
      });
    });

    test.describe(`SORT`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Check sort by by checking item order`, async ({ page, rewriteSearchPage }) => {
        let requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 40000 });
        await test.step('I go to featured sort', async () => {
          await rewriteSearchPage.waitForProductCard();
          await rewriteSearchPage.goToSort('featured');
          await page.reload();
        });

        await test.step(`I check that the URL updated correctly for sort by featured`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), "validate the API call to /commerce/search/products/v2/style doesn't contain sortByField").toBe(null);
          expect(url.searchParams.get('sortByDir'), "validate the API call to /commerce/search/products/v2/style doesn't contain sortByDir").toBe(null);
          await waitForHydration(page);
          await rewriteSearchPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortBy not set').not.toHaveURL(/.*sortBy=.*/);
        });

        requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 60000 });
        await test.step('I go to price low-high sort', async () => {
          await rewriteSearchPage.goToSort('priceLowHigh');
        });

        await test.step(`I check that the URL updated correctly for sort by low to high`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/style sortByField is set to price').toBe('price');
          expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/style sortByDir is set to asc').toBe('asc');
          await waitForHydration(page);
          await rewriteSearchPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortBy is set to low').toHaveURL(/sortByField=price&sortByDir=asc/);
        });

        requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 40000 });
        await test.step('I go to price high-low sort', async () => {
          await scrollToTop(page);
          await rewriteSearchPage.goToSort('priceHighLow');
        });

        await test.step(`I check that the URL updated correctly for sort by high to low`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/style sortByField is set to price').toBe('price');
          expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/style sortByDir is set to desc').toBe('desc');
          await waitForHydration(page);
          await rewriteSearchPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortBy is set to high').toHaveURL(/sortByField=price&sortByDir=desc/);
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Check pagination works as expected`,
        { tag: ['@skip-brf-ca', '@skip-at-ca'] },
        async ({ page, rewriteSearchPage }) => {
          const firstPageTopRowNames: string[] = [];
          await test.step('I gather information on the first page items', async () => {
            await rewriteSearchPage.waitForProductCard();
            for (let i = 0; i < rewriteSearchPage.topRowSize; i++) {
              firstPageTopRowNames[i] = await rewriteSearchPage.getProductName(rewriteSearchPage.productCards.nth(i)).innerText();
            }
          });

          let requestPromise: Promise<Request>;
          await test.step('I go to page 2', async () => {
            await scrollToBottomUntilPageDoesNotGrow(page);
            requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 40000 });
            await rewriteSearchPage.goToNextPage();
          });

          await test.step(`I check that the URL updated correctly for page number to be 1`, async () => {
            const request = await requestPromise;
            const url = new URL(request.url());
            expect(url.searchParams.get('pageNumber'), 'validate the API call to /commerce/search/products/v2/style pageNumber is set to 1').toBe('1');
            await waitForHydration(page);
            await rewriteSearchPage.waitForProductCard();
            await expect(page, 'validate that the query param pageId is set to 1').toHaveURL(/.*pageId=1.*/);
          });
        }
      );
    });

    test.describe(`PRODUCT GRID`, { tag: ['@run-all'] }, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 3 when the viewport is a small desktop`,
        { tag: ['@skip-mobile'] },
        async ({ page, rewriteSearchPage }) => {
          await test.step(`I change the viewport size to small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1195, height: 1080 });
            await page.reload();
            await waitForHydration(page);
            await rewriteSearchPage.waitForProductCard();
          });

          await test.step('I check the grid columns to be 3', async () => {
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });

          await test.step(`I change the viewport size lesser than a small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1023, height: 1080 });
            await page.reload();
            await waitForHydration(page);
            await rewriteSearchPage.waitForProductCard();
          });

          await test.step('I check the grid columns to be 2', async () => {
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(2);
          });

          await test.step(`I change the viewport size back to small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1024, height: 1080 });
            await page.reload();
            await waitForHydration(page);
            await rewriteSearchPage.waitForProductCard();
          });

          await test.step('I check the grid columns to be 3', async () => {
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 4 when the viewport is a large desktop`,
        { tag: ['@skip-mobile', '@skip-br'] },
        async ({ page, rewriteSearchPage }) => {
          await test.step(`I change the viewport size to Large Desktop 1280 to 1567px`, async () => {
            await page.setViewportSize({ width: 1395, height: 1080 });
            await page.reload();
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeGreaterThan(1390);
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size to XL desktop 1568px+`, async () => {
            await page.setViewportSize({ width: 1695, height: 1080 });
            await page.reload();
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeGreaterThan(1568);
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size back to Large desktop 1280 to 1567px`, async () => {
            await page.setViewportSize({ width: 1280, height: 720 });
            await page.reload();
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeLessThan(1568);
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size down to Small Desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1279, height: 720 });
            await page.reload();
          });

          await test.step('I check the grid columns to be 3', async () => {
            expect(page.viewportSize()?.width).toBeLessThan(1280);
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteSearchPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 2 when the viewport is a mobile or tablet`,
        { tag: ['@skip-desktop'] },
        async ({ rewriteSearchPage }) => {
          await test.step('I check the grid columns to be 2', async () => {
            await expect(rewriteSearchPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            const columns = await rewriteSearchPage.productGridInnerGrid.evaluate(el => {
              return window.getComputedStyle(el).getPropertyValue('grid-template-columns');
            });
            expect(columns.split(' ').length).toBe(2);
          });
        }
      );
    });

    test.describe(`PRODUCT`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Ensure product card is visible and has valid attributes`, async ({ rewriteSearchPage }) => {
        await test.step('I check product card has an image', async () => {
          await rewriteSearchPage.waitForProductCard();
          const randomImage = await rewriteSearchPage.getRandomCardImage();
          await scrollElementToCenter(randomImage);
          await expect(randomImage, 'validate product cards have a product card image').toBeVisible();
        });

        await test.step('I check product card has a name', async () => {
          const randomCardImage = await rewriteSearchPage.getRandomProductName();
          await scrollElementToCenter(randomCardImage);
          await expect(randomCardImage, 'validate product cards have a product name').toHaveText(/\w+/);
        });

        await test.step('I check product card has a price', async () => {
          expect(await rewriteSearchPage.getRandomProductPrice(), 'validate prices are valid').toMatch(/\$\d{1,3}\.\d{2}/);
        });

        // if (!/^(?:at|brf|br)$/.test(brand!) && breakpoint !== 'mobile') {
        //   await test.step('I check product cards contain quick add button', async () => {
        //     expect(await rewriteSearchPage.getProductCardQuickAddCount(), 'validate quick add buttons are showing').toBe(await rewriteSearchPage.getProductCardCount());
        //   });
        // }

        // disabling this until it's confirmed that the marketing flag will be showing in prod
        // await test.step('I check if any product cards are showing a marketing flag', async () => {
        //   brand !== 'brf' && expect(await rewriteSearchPage.getProductCardsWithMarktingFlagCount(), 'validate marketing flags are showing').toBeGreaterThan(0);
        // });

        // disabling this until it's confirmed that the badges will be showing in prod
        // if (brand === 'on' && market === 'us' && breakpoint === 'desktop') {
        //   await test.step('I check for badges to appear on some product cards', async () => {
        //     await expect(rewriteSearchPage.badge, 'validate product badges are showing').not.toHaveCount(0);
        //   });
        // }

        await test.step('I check product card has a color swatch', async () => {
          await expect(rewriteSearchPage.getColorSwatchsForProductCard(0), 'validate color swatches are showing').not.toHaveCount(0);
        });

        let randomProductCard: Locator;
        let defaultProductCardImageSource: string;
        await test.step("Check product card's image source", async () => {
          let checkedCardsCount = 0;
          do {
            checkedCardsCount++;
            randomProductCard = await rewriteSearchPage.getRandomProductCard();
          } while ((await rewriteSearchPage.getColorSwatchCountForProductCard(randomProductCard)) < 2 && checkedCardsCount < 50);
          await expect(rewriteSearchPage.getColorSwatchsForProductCard(randomProductCard), 'validate color swatch being checked has a least 1').not.toHaveCount(
            0
          );
          await expect(rewriteSearchPage.getColorSwatchsForProductCard(randomProductCard), 'validate color swatch being checked has a least 2').not.toHaveCount(
            1
          );
          defaultProductCardImageSource = (await rewriteSearchPage.getProductImage(randomProductCard).getAttribute('src')) ?? 'image source not found';
        });

        await test.step('Change selected color swatch', async () => {
          await rewriteSearchPage.clickNthColorSwatchForProductCard(randomProductCard, 1);
        });

        await test.step("Check product card's source has changed", async () => {
          await expect(rewriteSearchPage.getProductImage(randomProductCard), 'validate product image changed').not.toHaveAttribute(
            'src',
            defaultProductCardImageSource
          );
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Search for Sale items and ensure markdown or % off price is displayed.`,
        { tag: ['@skip-brf', '@skip-br'] },
        async ({ rewriteSearchPage }) => {
          await test.step('Wait for product cards to load on search page.', async () => {
            await rewriteSearchPage.waitForProductCard();
          });

          // setting to never check % off until it's enabled
          // eslint-disable-next-line no-constant-condition
          if (/* brand === 'on' || brand === 'gpf' */ false) {
            await test.step('I check product card has a % off included in the price info.', async () => {
              await expect(rewriteSearchPage.productPricePercentOff, 'validate that % off price is visible').toBeVisible();
            });
          } else {
            await test.step('I check product card has a markdown included in the price info.', async () => {
              await expect(rewriteSearchPage.productPriceMarkdown, 'validate that markdown price is visible').toBeVisible();
            });
          }
        }
      );
    });

    test.describe(`FILTER`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Filter search results with Department, Category, Color, Price, and Size`, async ({
        rewriteSearchPage,
        page,
      }) => {
        const maxWaitCount = 30;
        let currentWaitCount = 0;
        while ((await rewriteSearchPage.getItemCount()) === 0 && currentWaitCount < maxWaitCount) {
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await page.waitForTimeout(100);
          currentWaitCount++;
        }
        let initialNumberOfItems = await rewriteSearchPage.getItemCount();
        await test.step('I click the filter button', async () => {
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
        });
        await test.step('I select the first department in the filters', async () => {
          await rewriteSearchPage.departmentFilterDropdown.click();
          await rewriteSearchPage.departmentFilterOptions.first().click();
          await rewriteSearchPage.closeFacetsButton.click();
        });

        await test.step('I validate the url shows the department filter selected', async () => {
          await page.waitForURL(/.*department=.*/);
          await expect(page, 'validate the url has the department facet info').toHaveURL(/.*department=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteSearchPage.waitForProductCard();
          await expect(rewriteSearchPage.productCards.first(), 'validate the first product card is visible').toBeVisible({ timeout: 60000 });
          expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        const departmentNumofItems = await rewriteSearchPage.getItemCount();

        await test.step('I select a Category', async () => {
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
          await rewriteSearchPage.categoryFilterDropdown.click();
          await rewriteSearchPage.categoryFilterOptions.first().click();
        });

        await test.step('I validate the url shows the category filter selected', async () => {
          await page.waitForURL(/.*style=.*/);
          await expect(page, 'validate the url has the category facet info').toHaveURL(/.*style=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteSearchPage.waitForProductCard();
          await expect(rewriteSearchPage.productCards.first(), 'validate the first product card is visible').toBeVisible({ timeout: 60000 });
          expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            departmentNumofItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteSearchPage.getItemCount();
          await rewriteSearchPage.closeFacetsButton.click();
          if (await rewriteSearchPage.clearAllFacetsButton.isVisible()) {
            await rewriteSearchPage.clearAllFacetsButton.click();
          } else {
            await rewriteSearchPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!(?=.*department=)(?=.*style=)).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/department=|style=/);
          await rewriteSearchPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteSearchPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        await waitForHydration(page);
        initialNumberOfItems = await rewriteSearchPage.getItemCount();
        await test.step('I select the first department in the filters', async () => {
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
          await rewriteSearchPage.departmentFilterDropdown.click();
          await rewriteSearchPage.departmentFilterOptions.first().click();
          await page.waitForURL(/.*department=.*/);
          await rewriteSearchPage.waitForProductCard();
          await waitForHydration(page);
        });

        await test.step('I select a Size', async () => {
          if (await rewriteSearchPage.sizeFilterDropdown.isVisible()) {
            await rewriteSearchPage.sizeFilterDropdown.click();
            await rewriteSearchPage.sizeFilterOptions.first().click();
          } else {
            await rewriteSearchPage.departmentFilterDropdown.click();
            await rewriteSearchPage.departmentFilterOptions.first().click();
            await rewriteSearchPage.sizeFilterDropdown.click();
            await rewriteSearchPage.sizeFilterOptions.first().click();
          }
        });

        await test.step('I validate the url shows the size filter selected', async () => {
          await page.waitForURL(/.*size=.*/);
          await expect(page, 'validate the url has the size facet info').toHaveURL(/.*size=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteSearchPage.waitForProductCard();
          await expect(rewriteSearchPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        // This is going to fail and can't be fixed until https://gapinc.atlassian.net/browse/PLPWE-2401 is completed
        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteSearchPage.getItemCount();
          await rewriteSearchPage.closeFacetsButton.click();
          await page.waitForTimeout(1000);
          if (await rewriteSearchPage.clearAllFacetsButton.isVisible()) {
            await rewriteSearchPage.clearAllFacetsButton.click();
          } else {
            await rewriteSearchPage.individualFacetClearButton.click();
          }
          await rewriteSearchPage.clearAllFacetsButton.waitFor({ state: 'hidden' });
          await page.waitForURL(/^(?!(?=.*department=)(?=.*size=)).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/department=|size=/);
          await rewriteSearchPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteSearchPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        await test.step('I select a Color', async () => {
          initialNumberOfItems = await rewriteSearchPage.getItemCount();
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
          await rewriteSearchPage.colorFilterDropdown.click();
          await rewriteSearchPage.colorFilterOptions.first().click();
        });

        await test.step('I validate the url shows the color filter selected', async () => {
          await page.waitForURL(/.*color=.*/);
          await expect(page).toHaveURL(/.*color=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteSearchPage.waitForProductCard();
          await expect(rewriteSearchPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteSearchPage.getItemCount();
          await rewriteSearchPage.closeFacetsButton.click();
          if (await rewriteSearchPage.clearAllFacetsButton.isVisible()) {
            await rewriteSearchPage.clearAllFacetsButton.click();
          } else {
            await rewriteSearchPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!color=).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/color=/);
          await rewriteSearchPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteSearchPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        initialNumberOfItems = await rewriteSearchPage.getItemCount();
        await test.step('I select a Price range', async () => {
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
          await rewriteSearchPage.priceFilterDropdown.click();
          const { width, height } = (await rewriteSearchPage.priceSlider.boundingBox())!;
          await rewriteSearchPage.priceSlider.hover({ position: { x: 0, y: height / 2 }, force: true });
          await page.mouse.down();
          await rewriteSearchPage.priceSlider.hover({ position: { x: width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
          await rewriteSearchPage.priceSlider.hover({ position: { x: width, y: height / 2 }, force: true });
          await page.mouse.down();
          await rewriteSearchPage.priceSlider.hover({ position: { x: width - width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
        });

        await test.step('I validate the url shows the price filter selected', async () => {
          await page.waitForURL(/.*price=.*/);
          await expect(page).toHaveURL(/.*price=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteSearchPage.waitForProductCard();
          await expect(rewriteSearchPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteSearchPage.getItemCount();
          await rewriteSearchPage.closeFacetsButton.click();
          if (await rewriteSearchPage.clearAllFacetsButton.isVisible()) {
            await rewriteSearchPage.clearAllFacetsButton.click();
          } else {
            await rewriteSearchPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!price=).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/price=/);
          await rewriteSearchPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteSearchPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Check flex facets functionality`, async ({ page, rewriteSearchPage }) => {
        await test.step('I click the filter button', async () => {
          await page.mouse.move(500, 500);
          await rewriteSearchPage.allFacetsButton.click();
        });

        let flexFacetCount;
        let facetCountGrabbedCount = 0;
        do {
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await page.waitForTimeout(1000);
          flexFacetCount = await rewriteSearchPage.getFlexFacetCount();
          facetCountGrabbedCount++;
        } while (flexFacetCount < 1 && facetCountGrabbedCount < 5);

        expect(flexFacetCount, 'validate that the flex facet count is greater than 0').toBeGreaterThan(0);
        for (let i = 0; i < flexFacetCount && i < 5; i++) {
          const initialNumberOfItems = await rewriteSearchPage.getItemCount();
          await test.step('I select a facet option', async () => {
            const requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 40000 });
            await rewriteSearchPage.clickNthFacet(i);
            await requestPromise;
          });

          await test.step('I validate that new products appear', async () => {
            await expect(rewriteSearchPage.currentFacets).toHaveCount(1);
            await rewriteSearchPage.waitForProductCard();
            await expect(rewriteSearchPage.productCards.first()).toBeVisible({ timeout: 60000 });
            expect(await rewriteSearchPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
              initialNumberOfItems
            );
          });

          await test.step('I clear the filters and validate products are updated', async () => {
            const filteredNumberOfItems = await rewriteSearchPage.getItemCount();
            await rewriteSearchPage.closeFacetsButton.click();
            const requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/style/, { timeout: 40000 });
            if (await rewriteSearchPage.clearAllFacetsButton.isVisible()) {
              await rewriteSearchPage.clearAllFacetsButton.click();
            } else {
              await rewriteSearchPage.individualFacetClearButton.click();
            }
            await requestPromise;
            await rewriteSearchPage.waitForProductCard();
            await waitForHydration(page);
            await rewriteSearchPage.currentFacets.waitFor({ state: 'hidden' });
            expect(await rewriteSearchPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
              filteredNumberOfItems
            );
          });

          await test.step('I click the filter button', async () => {
            await page.mouse.move(500, 500);
            await rewriteSearchPage.allFacetsButton.click();
          });
        }
      });
    });
  });
});
