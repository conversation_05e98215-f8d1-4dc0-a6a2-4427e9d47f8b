/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Search Page`, () => {
  test.beforeEach(async ({ page, searchPage }) => {
    await test.step(`Go to '${brand}'-brand search page`, async () => {
      await searchPage.navigateToSearchPage('belt');
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });

    await test.step(`Sort products by price, high to low.`, async () => {
      await searchPage.goToPriceHighLowSort();
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-brf', '@run-gpf'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Search Page visual comparison has not changed from baseline.`, async ({
      page,
      sitewide,
      searchPage,
    }) => {
      await compareCanaryToLive(page, __filename, {
        maxDiffPixelRatio: 0.15,
        maskableLocators: [sitewide.searchForm, searchPage.productCards],
        bypassNewSession: true,
      });
    });
  });
});
