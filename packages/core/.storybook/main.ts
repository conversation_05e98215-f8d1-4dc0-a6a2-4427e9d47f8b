import type { StorybookConfig } from '@storybook/nextjs';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import path, { join, dirname } from 'path';
import { env } from 'process';

export const useCoreLegacy = env.STORYBOOK_USE_CORE_LEGACY === 'true';

type WebpackArgs = NonNullable<StorybookConfig['webpackFinal']> extends (...args: infer U) => any ? U : never;

type WebpackConfig = WebpackArgs[0];
type WebpackOptions = WebpackArgs[1];

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')));
}

const setStoryPath = useCoreLegacy ? `legacy/**` : `(?!legacy)**`;
const config: StorybookConfig = {
  stories: [
    `../src/components/${setStoryPath}/*.mdx`,
    `../src/components/${setStoryPath}/*.stories.@(js|jsx|mjs|ts|tsx)`,
    `../src/components/${setStoryPath}/*-story.@(js|jsx|mjs|ts|tsx)`,
  ],
  core: {
    disableTelemetry: true,
  },
  addons: [
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@storybook/addon-essentials'),
    getAbsolutePath('@storybook/addon-interactions'),
    ...(useCoreLegacy ? ['../src/components/legacy/storybook-feature-flags'] : []),
  ],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },

  babel(config) {
    return {
      ...config,
      presets: [...(config.presets || []), '@emotion/babel-preset-css-prop'],
    };
  },
  webpackFinal: (config: WebpackConfig, options: WebpackOptions) => {
    config.resolve = config.resolve || {};
    config.resolve.plugins = config.resolve.plugins || [];
    config.resolve.plugins.push(
      new TsconfigPathsPlugin({
        configFile: path.resolve(__dirname, '../tsconfig.json'),
      })
    );
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@core': path.resolve(__dirname, '../src'),
      'next/headers': path.resolve(__dirname, './mock.js'),
      ...(useCoreLegacy
        ? {
            '@core/components/react-stitch': path.resolve(__dirname, '../src/components/react-stitch'),
            '@ecom-next/core/legacy/link': path.resolve(__dirname, '../src/components/migration/link'), // Resolves external resources that are not part of the core package
            '@sitewide': path.resolve(__dirname, '../../sitewide/src'),
            '@mui': path.resolve(__dirname, '../../marketing-ui/src'),
          }
        : {}),
    };
    return config;
  },
  staticDirs: ['./public'],
};
export default config;
