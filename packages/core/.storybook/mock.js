// Header object to mock next/headers
const headers = () => {
  const headerMap = new Map();
  headerMap.set('host', 'brol-next.aks.test.azeus.gaptech.com');
  headerMap.set('request-host', 'brol-next.aks.test.azeus.gaptech.com');
  headerMap.set('client-size-class', 'Desktop');

  return headerMap;
};
// Cookies to mock next/headers
const cookies = () => {
  const cookieMap = new Map();
  cookieMap.set('locale', 'en_US');

  return cookieMap;
};

export { headers, cookies };
