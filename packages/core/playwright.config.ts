import { defineConfig, devices } from '@playwright/test';

require('dotenv').config();
require('dotenv').config({ path: '.env.local' });

const BASE_URL = process.env.STORYBOOK_URL || 'http://localhost:6006';

export default defineConfig({
  globalSetup: require.resolve('./storybook-tests/global-setup.ts'),
  snapshotDir: './storybook-tests',
  reporter: [['html'], ['github']],
  testDir: './storybook-tests',
  testIgnore: 'legacy/**',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 3 : 0,
  workers: process.env.CI ? 10 : 3,
  use: {
    trace: 'on-first-retry',
    baseURL: BASE_URL,
    ignoreHTTPSErrors: true,
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
