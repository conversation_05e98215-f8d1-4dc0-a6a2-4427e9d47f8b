@import 'tailwindcss/base';
@import './crossbrand/base.css';
@import './common/base.css';
@import './gap/base.css';
@import './tools/figma/brand/primitive/global.generated.css';
@import './tools/figma/brand/primitive/cb.generated.css';
@import './tools/figma/brand/primitive/gap.generated.css';
@import './tools/figma/brand/legacy/base.legacy.css';
@import './tools/figma/brand/legacy/cb.legacy.css';
@import './tools/figma/brand/legacy/gap.legacy.css';

@import './tools/figma/brand/foundational/global.foundational.v1.css';
@import './tools/figma/brand/foundational/cb.foundational.v1.css';
@import './tools/figma/brand/foundational/gap.foundational.v1.css';

@import 'tailwindcss/components';
@import './crossbrand/components.css';
@import './gap/components.css';
@import './common/components.css';

@import 'tailwindcss/utilities';
@import 'tailwindcss/variants';

@config "./tailwind.config.gapfs.ts";
