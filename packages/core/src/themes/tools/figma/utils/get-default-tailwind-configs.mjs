import resolveConfig from 'tailwindcss/resolveConfig.js';
import { flattenObject } from '../find-common-variable-names.mjs';
import atConfig from '../../../tailwind.config.at.ts';

export const getFullTailwindConfig = () => {
  const defaultTailwindConfig = resolveConfig(atConfig);
  const presetThemes = defaultTailwindConfig.default.presets.map(p => p.theme.extend);
  const flattenedPresetKeys = presetThemes.reduce((acc, obj) => {
    const flattened = flattenObject(obj);
    return [...acc, ...Object.keys(flattened)];
  }, []);
  const theme = defaultTailwindConfig.theme;
  const flattenedTheme = flattenObject(theme);
  return [...Object.keys(flattenedTheme), ...flattenedPresetKeys];
};
