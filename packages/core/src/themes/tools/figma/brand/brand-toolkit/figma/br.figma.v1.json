{"borderRadius": {"border-radius-soft": "var(--border-radius-000)", "style-button-floating-action-border-radius": "var(--border-radius-000)", "style-modal-border-radius": "var(--border-radius-000)", "style-product-image-thumbnail-border-radius": "var(--border-radius-000)", "style-selector-size-border-border-radius": "var(--border-radius-000)", "style-selector-swatch-border-radius": "var(--border-radius-000)", "style-seo-crosslink-border-radius-default": "var(--border-radius-9999)"}, "borderWidth": {"style-button-hover-border-width": "var(--border-width-0)", "style-global-header-everyday-free-shipping-(edfs)-mobile-lower-border-width": "var(--border-width-0)", "style-global-header-everyday-free-shipping-(edfs)-mobile-upper-border-width": "var(--border-width-10)", "style-selector-size-border-border-width": "var(--border-width-10)", "style-selector-size-border-selected---border-width": "var(--border-width-10)", "style-selector-size-strikethrough-border-width": "var(--border-width-10)", "style-selector-swatch-border-width-focus": "var(--border-width-20)", "style-selector-swatch-border-width-selected": "var(--border-width-10)", "style-tabs-border-width": "var(--border-width-30)", "style-tabs-selected-border-width": "var(--border-width-30)"}, "colors": {"color-background-accent": "rgba(var(--color-gray-200))", "color-background-disabled": "rgba(var(--color-gray-200))", "color-background-subtle": "rgba(var(--color-gray-300))", "color-background-transparent---dark": "rgba(var(--color-black-and-white-black-50))", "color-background-transparent---light": "rgba(var(--color-black-and-white-white-50))", "color-border-accent": "rgba(var(--color-black-and-white-black))", "color-border-default": "rgba(var(--color-gray-1100))", "color-border-disabled": "rgba(var(--color-gray-600))", "color-border-info": "rgba(var(--color-black-and-white-black))", "color-border-subtle": "rgba(var(--color-gray-600))", "color-fill-accent": "rgba(var(--color-black-and-white-black))", "color-fill-action": "rgba(var(--color-gray-1200))", "color-fill-dark": "rgba(var(--color-black-and-white-black))", "color-fill-disabled": "rgba(var(--color-brands-banana-brand-1))", "color-fill-subtle": "rgba(var(--color-gray-400))", "color-gray-gray-1": "rgba(var(--color-gray-200))", "color-gray-gray-2": "rgba(var(--color-gray-400))", "color-gray-gray-3": "rgba(var(--color-gray-600))", "color-gray-gray-4": "rgba(var(--color-gray-1100))", "color-gray-gray-5": "rgba(var(--color-gray-1100))", "color-icon-disabled": "rgba(var(--color-gray-600))", "color-icon-subtle": "rgba(var(--color-gray-1100))", "color-type-accent": "rgba(var(--color-black-and-white-black))", "color-type-disabled": "rgba(var(--color-gray-1100))", "color-type-link": "rgba(var(--color-black-and-white-black))", "color-type-sale": "rgba(var(--color-black-and-white-black))", "color-type-subtle": "rgba(var(--color-gray-1100))", "color-type-visited": "rgba(var(--color-gray-1100))", "style-badge-fill-color": "rgba(var(--color-brands-banana-brand-1-80))", "style-badge-font-color": "rgba(var(--color-black-and-white-black))", "style-brand-fill": "rgba(var(--color-brands-banana-brand-2))", "style-button-critical-caution-fill": "rgba(var(--color-gray-1100))", "style-button-critical-hover-fill": "rgba(var(--color-gray-1100))", "style-button-primary-fill": "rgba(var(--color-brands-banana-brand-2))", "style-button-primary-font": "rgba(var(--color-black-and-white-black))", "style-button-primary-hover-fill": "rgba(var(--color-brands-banana-brand-1))", "style-button-primary-icon": "rgba(var(--color-black-and-white-black))", "style-button-primary-pressed-fill": "rgba(var(--color-brands-banana-brand-2))", "style-button-secondary-border": "rgba(var(--color-gray-600))", "style-button-secondary-hover-border": "rgba(var(--color-gray-1100))", "style-button-secondary-hover-fill": "rgba(var(--color-black-and-white-white))", "style-button-secondary-hover-font": "rgba(var(--color-black-and-white-black))", "style-button-secondary-hover-icon": "rgba(var(--color-gray-1100))", "style-chips-active": "rgba(var(--color-gray-700))", "style-chips-default": "rgba(var(--color-gray-400))", "style-chips-focused": "rgba(var(--color-gray-600))", "style-dynamic-placeholder-fill": "rgba(var(--color-non-branded-nb-1))", "style-global-header-account-menu-font-color": "rgba(var(--color-black-and-white-black))", "style-global-header-account-menu-icon-color": "rgba(var(--color-black-and-white-black))", "style-global-header-bag-desktop-font-color": "rgba(var(--color-black-and-white-white))", "style-global-header-bag-desktop-icon-color": "rgba(var(--color-black-and-white-black))", "style-global-header-bag-mobile-fill-color": "rgba(var(--color-red-200))", "style-global-header-everyday-free-shipping-(edfs)-desktop-font-color": "rgba(var(--color-black-and-white-black))", "style-global-header-everyday-free-shipping-(edfs)-mobile-background-color": "rgba(var(--color-brands-banana-brand-1))", "style-global-header-everyday-free-shipping-(edfs)-mobile-border-color": "rgba(var(--color-gray-600))", "style-global-header-everyday-free-shipping-(edfs)-mobile-font-color": "rgba(var(--color-black-and-white-black))", "style-global-header-sister-brand-bar-default-background-color": "rgba(var(--color-brands-banana-brand-1))", "style-global-header-sister-brand-bar-default-font-color": "rgba(var(--color-black-and-white-black))", "style-global-header-sister-brand-bar-hover-background-color": "rgba(var(--color-brands-banana-brand-2))", "style-global-header-sister-brand-bar-hover-font-color": "rgba(var(--color-black-and-white-black))", "style-global-header-sister-brand-bar-selected-font-color": "rgba(var(--color-black-and-white-black))", "style-modal-background": "rgba(var(--color-black-and-white-white))", "style-modal-header": "rgba(var(--color-gray-300))", "style-product-card-marketing-flag": "rgba(var(--color-black-and-white-black))", "style-product-price-%-off-font-color": "rgba(var(--color-gray-1100))", "style-product-price-strikethrough": "rgba(var(--color-gray-1100))", "style-product-style-flag-font-color": "rgba(var(--color-black-and-white-black))", "style-product-style-flag-font-color-2": "rgba(var(--color-black-and-white-black))", "style-selector-fulfillment-border-default": "rgba(var(--color-gray-400))", "style-selector-fulfillment-border-selected": "rgba(var(--color-black-and-white-black))", "style-selector-size-available-background-focus": "rgba(var(--color-gray-200))", "style-selector-size-available-background-hover": "rgba(var(--color-gray-200))", "style-selector-size-available-background-selected": "rgba(var(--color-black-and-white-white))", "style-selector-size-available-default": "rgba(var(--color-black-and-white-white))", "style-selector-size-available-default-(hover-available)": "rgba(var(--color-black-and-white-black))", "style-selector-size-border-default": "rgba(var(--color-gray-400))", "style-selector-size-border-disable": "rgba(var(--color-black-and-white-white))", "style-selector-size-border-hover": "rgba(var(--color-black-and-white-black))", "style-selector-size-border-hover-unavailable": "rgba(var(--color-gray-1100))", "style-selector-size-border-selected": "rgba(var(--color-black-and-white-black))", "style-selector-size-border-selected-unavailable": "rgba(var(--color-black-and-white-black))", "style-selector-size-font-default": "rgba(var(--color-black-and-white-black))", "style-selector-size-font-disable": "rgba(var(--color-gray-1100))", "style-selector-size-font-hover": "rgba(var(--color-black-and-white-black))", "style-selector-size-font-hover--unavailable": "rgba(var(--color-gray-1100))", "style-selector-size-font-link": "rgba(var(--color-black-and-white-black))", "style-selector-size-font-selected": "rgba(var(--color-black-and-white-black))", "style-selector-size-font-selected---unavailable": "rgba(var(--color-black-and-white-black))", "style-selector-size-strikethrough-default---unavailable": "rgba(var(--color-gray-400))", "style-selector-size-strikethrough-hover---unavailable": "rgba(var(--color-gray-1100))", "style-selector-size-strikethrough-selected---unavailable": "rgba(var(--color-black-and-white-black))", "style-selector-size-unavailable-background-default": "rgba(var(--color-black-and-white-white))", "style-selector-size-unavailable-background-disable": "rgba(var(--color-black-and-white-white))", "style-selector-size-unavailable-background-hover": "rgba(var(--color-black-and-white-white))", "style-selector-size-unavailable-background-selected": "rgba(var(--color-black-and-white-white))", "style-selector-swatch-border-color": "rgba(var(--color-gray-800))", "style-seo-crosslink-fill-default": "rgba(var(--color-brands-banana-brand-2))", "style-seo-crosslink-fill-hover": "rgba(var(--color-gray-1100))", "style-seo-crosslink-fill-press": "rgba(var(--color-gray-1100))", "style-tabs-border-color": "rgba(var(--color-black-and-white-white))", "style-tabs-font-color": "rgba(var(--color-black-and-white-black))"}, "fontFamily": {"font-family-base": "var(--font-family-brand-banana-1)", "font-family-display": "var(--font-family-brand-banana-2)"}, "fontSize": {"font-size--1": "var(--font-size-12)", "font-size--2": "var(--font-size-12)", "font-size-0": "var(--font-size-14)", "font-size-1": "var(--font-size-16)", "font-size-2": "var(--font-size-18)", "font-size-3": "var(--font-size-24)", "font-size-4": "var(--font-size-34)", "style-alt-font-size--2": "var(--font-size-12)", "style-count-font-size": "var(--font-size-12)", "style-global-header-bag-mobile-font-size": "var(--font-size-10)", "style-global-header-everyday-free-shipping-(edfs)-mobile-font-size": "var(--font-size-12)", "style-global-header-sister-brand-bar-mobile-font-size": "var(--font-size-12)", "style-product-card-font-size": "var(--font-size-12)", "style-product-price-purchase-price-font-size": "var(--font-size-14)", "style-product-price-small-font-size": "var(--font-size-12)", "style-selector-size-font-font-size": "var(--font-size-12)", "style-text-title-small-font-size": "var(--font-size-18)"}, "fontWeight": {"font-weight-base-default": "var(--font-weight-350)", "font-weight-base-heavier": "var(--font-weight-600)", "font-weight-base-heavier1": "var(--font-weight-semibold)", "font-weight-display-default": "var(--font-weight-400)", "font-weight-display-heavier": "var(--font-weight-400)", "style-badge-font-weight": "var(--font-weight-600)", "style-global-header-account-menu-menu-items-font-weight": "var(--font-weight-350)", "style-global-header-account-menu-rewards-font-weight": "var(--font-weight-600)", "style-global-header-everyday-free-shipping-(edfs)-desktop-font-weight": "var(--font-weight-350)", "style-global-header-everyday-free-shipping-(edfs)-mobile-font-weight": "var(--font-weight-350)", "style-global-header-sister-brand-bar-default-font-weight": "var(--font-weight-350)", "style-global-header-sister-brand-bar-selected-font-weight": "var(--font-weight-600)", "style-product-price-%-off-font-weight": "var(--font-weight-350)", "style-product-price-purchase-price-font-weight": "var(--font-weight-350)"}, "gap": {"style-global-header-sister-brand-bar-display-logic-gap": true, "style-global-header-sister-brand-bar-display-logic-gap-link-state": "<PERSON><PERSON><PERSON>"}, "letterSpacing": {"font-letter-spacing-base": "var(--font-letter-spacing-2)", "font-letter-spacing-display": "var(--font-letter-spacing-4)"}, "maxHeight": {"style-global-header-everyday-free-shipping-(edfs)-mobile-max-height": "var(--spacing-750)"}, "minHeight": {"style-button-min-height": "var(--spacing-650)", "style-global-header-everyday-free-shipping-(edfs)-mobile-min-height": "var(--spacing-500)"}, "order": {"style-global-header-account-menu-menu-items-orders-and-returns": "Orders & Returns"}, "padding": {"style-breadcrumb-horizontal-padding": "var(--spacing-0)", "style-button-padding-horizontal": "var(--spacing-650)", "style-tabs-padding-horizontal": "var(--spacing-250)", "style-tabs-padding-vertical": "var(--spacing-050)"}, "spacing": {"spacing-l": "var(--spacing-400)", "spacing-m": "var(--spacing-300)", "spacing-s": "var(--spacing-150)", "spacing-xs": "var(--spacing-075)"}, "textAlign": {"style-page-title-text-align-horizontal-horizontal": "var(--text-align-horizontal-left)"}}