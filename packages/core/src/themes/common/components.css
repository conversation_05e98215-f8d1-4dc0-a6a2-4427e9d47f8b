@import 'slick-carousel/slick/slick.css';
@import 'slick-carousel/slick/slick-theme.css';

@import '../../components/fabric/link/styles.css';
@import '../../components/fabric/meter/styles.css';
@import '../../components/fabric/accordion/styles.css';
@import '../../components/fabric/radio/styles.css';
@import '../../components/fabric/checkbox/styles.css';
@import '../../components/fabric/tabs/pdp-tabs/styles.css';
@import '../../components/fabric/loading-placeholder/styles.css';
@import '../../components/fabric/drawer/styles.css';
@import '../../components/fabric/notification/styles.css';
@import '../../components/fabric/tag-link-group/styles.css';
@import '../../components/fabric/popover/styles.css';
@import '../../components/fabric/drop-down/styles.css';
@import '../../components/fabric/modal/styles.css';
@import '../../components/fabric/switch/styles.css';
@import '../../components/fabric/text-input/styles.css';
@import '../../components/fabric/text-search-input/styles.css';
@import '../../components/fabric/button/styles.css';
@import '../../components/fabric/star-ratings/styles.css';
@import '../../components/fabric/selector/styles.css';
@import '../../components/fabric/selector-tile/styles.css';
@import '../../components/fabric/selector-swatch/styles.css';
@import '../../components/fabric/selector-dropdown/styles.css';
@import '../../components/fabric/slider/styles.css';
@import '../../components/fabric/chip/styles.css';
@import '../../components/fabric/button-icon-carousel-chevron/styles.css';
@import '../../components/fabric/button-icon-quick-add/styles.css';
@import '../../components/fabric/button-icon-play/styles.css';
@import '../../components/fabric/button-icon-pause/styles.css';
@import '../../components/fabric/product-image/styles.css';
@import '../../components/fabric/badge/styles.css';
@import '../../components/fabric/status-badge/styles.css';
@import '../../components/fabric/sticky-container/styles.css';
@import '../../../../sitewide/src/layouts/styles.css';

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@layer components {
  .fds_isolation-layer {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    overflow: auto;
    position: fixed;
    z-index: theme('zIndex.overlay');
    animation: theme('animation.iso-layer');
    background-color: theme('colors.color-background-transparent---dark');

    &.crossbrand {
      background-color: theme('colors.cb-color-overlay-black-background');
    }
  }
}

body:has(.fds_isolation-layer) {
  overflow: hidden;
}

.font-brand {
  @apply text-[100%] antialiased;
}

.font-alt {
  @apply text-[100%] antialiased;
}

.slick-arrow,
.slick-prev::before,
.slick-next::before {
  content: none;
}

.fixed-h-holder {
  min-height: var(--content-min-height, 0);

  &:has(> [data-testid]) {
    min-height: var(--content-min-height, 0);
  }

  &:has(> div:not([data-testid]):only-child:empty),
  &:empty {
    min-height: 0;
  }
}
