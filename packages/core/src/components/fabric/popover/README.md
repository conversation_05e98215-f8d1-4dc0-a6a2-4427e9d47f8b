# Popover

The `Popover` component is used to display contextual content when triggered, typically by clicking a button or icon.

## Usage

The Popover is a controlled, reusable Core UI component that positions content relative to a referenced anchor element. It relies on the [Popper.js](https://popper.js.org/) library for positioning logic.

---

## Required Props

| Name        | Type                           | Description                                                                            |
| ----------- | ------------------------------ | -------------------------------------------------------------------------------------- |
| `anchorRef` | `React.RefObject<HTMLElement>` | A reference to the element that the Popover will be anchored to.                       |
| `children`  | `React.ReactNode`              | Content to display inside the Popover.                                                 |
| `isOpen`    | `boolean`                      | Controls whether the Popover is visible.                                               |
| `onClose`   | `() => void`                   | Callback fired to request closing the Popover (e.g., via Escape key or outside click). |

---

## Optional Props

| Name                         | Type                                                           | Default         | Description                                                                                                                                                                                                                                       |
| ---------------------------- | -------------------------------------------------------------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `anchorClassName`            | `string`                                                       | `'anchor'`      | Class name to apply to the anchor element (if styled externally).                                                                                                                                                                                 |
| `className`                  | `string`                                                       | `'fds_popover'` | Custom class name for the Popover container.                                                                                                                                                                                                      |
| `disableCloseOnClickOutside` | `boolean`                                                      | `false`         | Disables closing the Popover when clicking outside of it.                                                                                                                                                                                         |
| `disableCloseOnEscape`       | `boolean`                                                      | `false`         | Disables closing the Popover with the Escape key.                                                                                                                                                                                                 |
| `isCrossBrand`               | `boolean`                                                      | `false`         | Applies cross-brand styling if `true`.                                                                                                                                                                                                            |
| `placement`                  | `Placement` (from [Popper.js](https://popper.js.org/docs/v2/)) | `'top'`         | Specifies where the Popover appears relative to the anchor. Must be one of: `'top'`, `'top-start'`, `'top-end'`, `'bottom'`, `'bottom-start'`, `'bottom-end'`, `'left'`, `'left-start'`, `'left-end'`, `'right'`, `'right-start'`, `'right-end'`. |
| `popoverOffset`              | `[number, number]`                                             | `[0, 12]`       | Offset `[x, y]` from the anchor.                                                                                                                                                                                                                  |
| `width`                      | `number`                                                       | `240`           | Sets the width of the Popover.                                                                                                                                                                                                                    |

---

## Component Behavior

- You must manage the `isOpen` state externally and toggle it based on interactions (e.g., button click).
- Escape key and outside click handlers invoke `onClose` only if not disabled via respective props.

---

## Examples

### Basic usage

```tsx
const ref = useRef(null);
const [isOpen, setIsOpen] = useState(false);

return (
  <>
    <button ref={ref} onClick={() => setIsOpen(!isOpen)}>
      Toggle
    </button>
    <Popover anchorRef={ref} isOpen={isOpen} onClose={() => setIsOpen(false)}>
      <div>Popover content</div>
    </Popover>
  </>
);
```

### Cross-brand with placement

```tsx
<Popover anchorRef={anchorRef} isOpen={isOpen} onClose={handleClose} placement='right-start' isCrossBrand popoverOffset={[8, 16]}>
  <div>Popover content</div>
</Popover>
```
