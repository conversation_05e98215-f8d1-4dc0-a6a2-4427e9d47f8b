type Dimension = {
  height: string;
  width: string;
};

type Ratio = {
  xValue: number;
  yValue: number;
};

export type LoadingPlaceholderProps = {
  /**
   * The animation delay value to be sent in the string format: delay-{value};
   * Example values: delay-100, delay-500, delay-1000. The allowed values are tailwind defaults only.
   * Tailwind default value - 0, 75, 100, 150, 200, 300, 500, 700, 1000.
   * Custom delay values allowed: 2000, 3000, 5000.
   */
  animationDelay?: string;

  /**
   * The content to be displayed when the loading is complete.
   */
  children?: React.ReactNode;
  /**
   * Allows you to pass a custom class name as a string.
   */
  className?: string;
  /**
   * Sets a fixed size for the container in px, takes a width and height.
   */
  fixedSize?: Dimension;
  /**
   * A flag to indicate if it is CrossBrand or not.
   */
  isCrossBrand?: boolean;
  /**
   * This is a boolean that will deactivate the placeholder on `true`.
   */
  loadingComplete?: boolean;
  /**
   * Sets the ratio for the container, takes a width and height.
   */
  ratio?: <PERSON>io;
};
