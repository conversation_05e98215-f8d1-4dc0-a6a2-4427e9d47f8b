import React from 'react';
import classnames from 'classnames';
import { LoadingPlaceholderProps } from './types';

const LoadingPlaceholder: React.FC<LoadingPlaceholderProps> = ({
  className,
  ratio = { xValue: 1, yValue: 1 },
  fixedSize,
  loadingComplete = false,
  animationDelay = 'delay-0',
  children,
  isCrossBrand,
}): JSX.Element => {
  const ratioPercentage = Math.round((ratio.yValue / ratio.xValue) * 100);

  const baseStyle = `fds_loading-placeholder animate-loading-placeholder ${animationDelay}`;
  const placeholderStyle = classnames(baseStyle, { crossbrand: isCrossBrand }, className);

  return loadingComplete && children ? (
    (children as JSX.Element)
  ) : (
    <div
      data-testid='loading-placeholder'
      className={placeholderStyle}
      style={{ width: fixedSize?.width, height: fixedSize?.height || '100vh', paddingBottom: fixedSize ? 0 : `${ratioPercentage}%` }}
    />
  );
};

export { LoadingPlaceholder };
