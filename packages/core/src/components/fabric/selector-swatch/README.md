# SelectorSwatch

The SelectorSwatch component is used to display the product color and pattern options.

## Usage

The SelectorSwatch is a reusable Core UI component which can be grouped together and used in components like Buy Box. It supports both single select (Radio) and Multi Select (Check box) versions

### Required Props

| Name | Type     | Default | Description                                                           |
| ---- | -------- | ------- | --------------------------------------------------------------------- |
| `id` | `string` |         | This prop is required to uniquely identify a SelectorSwatch component |

### Optional Props

The SelectorSwatch component's style and behavior can be controlled using the optional props listed below. If these props are not provided, SelectorSwatch will use the default values.

| Name           | Type             | Default               | Description                                                                                                                        |
| -------------- | ---------------- | --------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `ariaLabel`    | `string`         | `selector-swatch`     | The aria label value for the SelectorSwatch component                                                                              |
| `className`    | `string`         | `fds_selector-swatch` | This prop can be used to customize the SelectorSwatch component further by overriding the default styles                           |
| `color`        | `string`         | `#f2f2f2`             | This prop represents the background color of the Selector Swatch                                                                   |
| `imageUrl`     | `string`         |                       | This prop represents the path to the background image of the Selector Swatch. This will override the background color if provided. |
| `isAvailable`  | `boolean`        | `true`                | This represents whether the product is available. When the value is `false`, a strike-through line will appear over the swatch.    |
| `isDisabled`   | `boolean`        | `false`               | This prop indicates if a Selector Swatch is disabled                                                                               |
| `size`         | `SwatchSizeType` | `x-large`             | This prop represents the size of the Selector Swatch for one of the `SwatchSizeType` values                                        |
| `text`         | `string`         |                       | This is an optional text displayed under the Selector Swatch                                                                       |
| `singleSelect` | `boolean`        | true                  | This option can be used to control if the color swatch is a single select or a multi select option                                 |
| `groupName`    | `string`         |                       | Group identifier for a group of Swatches. This must be a unique value for each Selector Swatch group.                              |
| `checed`       | `boolean`        | false                 | This option is used to set the checked state for the color swatch selector                                                         |

## Component Styles

The styles for the SelectorSwatch component are defined in the file `packages/core/src/components/fabric/selector-swatch/styles.css`.

- `className` - This prop can be used to customize the SelectorSwatch component further by overriding the default styles. Default value is `fds_selector-swatch`.

## Using the Component

#### For main themes

```jsx
<SelectorSwatch id='selector-swatch' ariaLabel='Selector Swatch' color='#F6FCFF' />
```

#### With overridden prop values

```jsx
<SelectorSwatch id='selector-swatch' className='selector-swatch__custom-class' ariaLabel='Custom Selector Swatch' color='#F6FCFF' checked />
<SelectorSwatch id='selector-swatch' className='selector-swatch__custom-class' ariaLabel='Custom Selector Swatch' color='#F6FCFF' isDisabled />
<SelectorSwatch id='selector-swatch-pattern' className='selector-swatch__custom-class' ariaLabel='Custom Selector Swatch' imageUrl='https://www.example.com/test-image' />
```

## Custom styling variables

#### For main themes

Please refer `packages/core/src/components/fabric/selector-swatch/styles.css` for all the custom variables used.

#### For cross brand

Cross brand styling is not applicable to Selector Swatch.
