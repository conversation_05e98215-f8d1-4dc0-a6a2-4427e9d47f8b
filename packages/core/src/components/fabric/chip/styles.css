@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './cb.styles.css';

@layer components {
  .fds_chips__wrapper {
    background: theme('colors.style-chips-default');
    padding: theme('spacing.utk-spacing-s') theme('spacing.utk-spacing-m');
    display: flex;
    gap: theme('spacing.utk-spacing-s');
    align-items: center;
    justify-content: center;
    width: fit-content;
    min-width: 48px;
    min-height: 32px;
    position: relative;
    cursor: pointer;
    outline: theme('borderWidth.border-width-default') none theme('colors.color-border-default');

    &:hover,
    &:focus-visible,
    &:active {
      outline-style: solid;
    }

    &:focus-visible {
      background: theme('colors.style-chips-focused');
    }

    &:active {
      background: theme('colors.style-chips-active');
    }

    .fds_chips__swatch-icon-wrapper {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      position: relative;
      overflow: hidden;

      .fds_chips__swatch {
        background: black;
        border-radius: 50%;
        width: 16px;
        height: 16px;
      }
    }

    .fds_chips__attribute {
      font-family: theme('fontFamily.brand-base');
      color: theme('colors.color-type-copy');
      font-size: theme('fontSize.font-size--1');
      line-height: normal;
      font-weight: theme('fontWeight.font-weight-base-default');
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      letter-spacing: theme('letterSpacing.font-letter-spacing-base');
    }

    .fds_chips__remove-icon-wrapper {
      display: flex;
      flex-direction: row;
      gap: theme('spacing.utk-spacing-s');
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
    }

    .fds_chips__remove-icon {
      color: theme('colors.color-icon-default');
      width: 9px;
      height: 9px;
      position: relative;
      overflow: visible;
    }
  }
}
