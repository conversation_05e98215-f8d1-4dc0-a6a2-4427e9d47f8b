@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .fds_notification {
    font-family: theme('fontFamily.brand-base');
    display: flex;
    align-items: center;
    padding: theme('padding.utk-spacing-s');
    font-size: theme('fontSize.font-size--2');
    font-weight: theme('fontWeight.font-weight-base-heavier');
    line-height: normal;
    letter-spacing: theme('letterSpacing.font-letter-spacing-base');
    &.fds_notification--dismissible {
      padding: theme('padding.utk-spacing-l');
      .fds_notification__wrapper {
        gap: theme('padding.utk-spacing-l');
      }
      .fds_notification__dismiss-button {
        margin-left: auto;
      }
    }
    .fds_notification__wrapper {
      display: flex;
      align-items: flex-start;
      width: 100%;
      gap: theme('padding.utk-spacing-s');
    }
    &.fds_notification__container {
      border-radius: theme('borderRadius.border-radius-hard');
    }
    &.fds_notification--warning {
      color: theme('colors.color-type-copy');
      background-color: theme('colors.color-background-warning');
    }
    &.fds_notification--negative {
      color: theme('colors.color-type-copy');
      background-color: theme('colors.color-background-error');
    }
    &.fds_notification--info {
      color: theme('colors.color-type-copy');
      background-color: transparent;
    }
    &.cb {
      font-family: theme('fontFamily.crossbrand');
      padding: theme('padding.cb-padding-stack-small');
      font-size: theme('fontSize.cb-font-size-b-s-font-size');
      font-weight: theme('fontWeight.cb-font-weight-regular-font-weight');
      line-height: theme('lineHeight.cb-font-size-b-s-line-height');
      &.fds_notification--dismissible {
        padding: theme('padding.cb-padding-stack-medium');
        .fds_notification__wrapper {
          gap: theme('padding.cb-padding-stack-medium');
        }
      }
      .fds_notification__wrapper {
        gap: theme('padding.cb-padding-stack-small');
      }
      &.fds_notification--warning {
        color: theme('colors.cb-color-font-default');
        background-color: theme('colors.cb-color-surface-warning');
      }
      &.fds_notification--negative {
        color: theme('colors.cb-color-font-negative-default');
        background-color: theme('colors.cb-color-surface-negative');
      }
      &.fds_notification--info {
        color: theme('colors.cb-color-font-default');
        background-color: theme('colors.cb-color-surface-info');
      }
      &.fds_notification--text-only {
        background: none;
      }
    }
    &.fds_notification--text-only {
      background: none;
    }
  }
}
