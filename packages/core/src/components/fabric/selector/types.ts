import React from 'react';

type customProps = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'name' | 'id' | 'checked'> & {
  /**
   * Indicates the selected state of the selector.
   */
  checked?: boolean;

  /**
   * custom class name for the selector
   */
  className: string;

  /**
   * Group name for the input element, used for grouping radio buttons.
   */
  group?: string;

  /**
   * Unique identifier for the input element.
   */
  id: string;

  /**
   * Indicates if the selector is available for selection.
   */
  isAvailable?: boolean;

  /**
   * Determines if the selector is a single select (radio) or multi-select (checkbox).
   */
  singleSelect?: boolean;

  /**
   * a message to be read out by screen reader when item is not available. Will not be rendered on screen!
   */
  unavailableText?: string;
};

export type SelectorProps = React.PropsWithChildren<customProps>;
