import React, { useEffect, useState, useRef } from 'react';
import classNames from 'classnames';
import { SlashIcon } from '@ecom-next/core/fabric/icons';
import { SelectorProps } from './types';

const Selector = ({
  children,
  singleSelect,
  group,
  id,
  isAvailable = true,
  unavailableText,
  disabled = false,
  className,
  checked,
  ...inputProps
}: SelectorProps) => {
  const fdsSelectorLabelClassName = classNames('fds_selector__label', className, {
    ['fds_selector__label--unavailable']: !isAvailable,
  });
  const showLine = !isAvailable && !disabled;
  const renderUnavailableMessage = showLine && unavailableText;

  const defaultChecked = checked ? checked : false;
  const selectorRef = useRef<HTMLLabelElement>(null);
  const [isChecked, setIsChecked] = useState<boolean>(defaultChecked);
  const [slashWidth, setSlashWidth] = useState<number>(0);
  const [slashHeight, setSlashHeight] = useState<number>(0);

  useEffect(() => {
    setIsChecked(defaultChecked);
  }, [defaultChecked]);

  useEffect(() => {
    new ResizeObserver(() => {
      const selectorElement = selectorRef.current || { offsetWidth: 0, offsetHeight: 0 };
      const width = selectorElement?.offsetWidth;
      const height = selectorElement?.offsetHeight;

      setSlashWidth(width);
      setSlashHeight(height);
    }).observe(selectorRef.current as HTMLLabelElement);
  }, []);

  return (
    <>
      <input
        id={id}
        type={singleSelect ? 'radio' : 'checkbox'}
        name={group}
        disabled={disabled}
        aria-disabled={showLine}
        checked={isChecked}
        className='fds_selector__input sr-only'
        {...inputProps}
      />
      <label ref={selectorRef} id='selector-label' htmlFor={id} aria-disabled={disabled} className={fdsSelectorLabelClassName}>
        <span className='fds_selector__content'>{children}</span>
        {renderUnavailableMessage && (
          <span id={`${id}-unavailable`} className='sr-only'>
            {unavailableText}
          </span>
        )}
        {showLine && (
          <SlashIcon
            className='fds_selector__slash'
            backgroundColor='transparent'
            width={slashWidth}
            height={slashHeight}
            viewBox={`0 0 ${slashWidth} ${slashHeight}`}
            foregroundX1={0}
            foregroundY1={slashHeight}
            foregroundX2={slashWidth}
            foregroundY2={0}
          />
        )}
      </label>
    </>
  );
};

export { Selector };
