import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { Drawer } from '../Drawer';
import { DrawerProps } from '../types';

describe('Drawer Component', () => {
  const defaultProps: DrawerProps = {
    toggleState: 'open',
    callbackFn: jest.fn(),
    children: 'Test Content',
  };

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should render the Drawer component with default props', () => {
    render(<Drawer {...defaultProps}>Test Content</Drawer>);
    const drawerElement = screen.getByRole('dialog');
    expect(drawerElement).toBeInTheDocument();
    expect(drawerElement).toHaveClass('fds_drawer__bottom');
  });

  it('should apply the correct classes based on toggleState', () => {
    const { rerender } = render(
      <Drawer {...defaultProps} toggleState='open'>
        Test Content
      </Drawer>
    );
    let drawerElement: HTMLElement | null = screen.getByRole('dialog');
    expect(drawerElement).toHaveClass('fds_drawer__bottom--opened');

    rerender(
      <Drawer {...defaultProps} toggleState='close'>
        Test Content
      </Drawer>
    );
    drawerElement = screen.queryByRole('dialog');
    expect(drawerElement).not.toBeInTheDocument();
  });

  it('should apply the correct classes based on orientation and toggleState', () => {
    const { rerender } = render(
      <Drawer {...defaultProps} toggleState='open' orientation='top'>
        Test Content
      </Drawer>
    );
    let drawerElement: HTMLElement | null = screen.getByRole('dialog');
    expect(drawerElement).toHaveClass('fds_drawer__top--opened');

    rerender(
      <Drawer {...defaultProps} toggleState='close' orientation='left'>
        Test Content
      </Drawer>
    );
    drawerElement = screen.queryByRole('dialog');
    expect(drawerElement).not.toBeInTheDocument();
  });

  it('should call closeDrawer function and callbackFn when close icon is clicked', () => {
    jest.useFakeTimers();

    render(
      <Drawer {...defaultProps} toggleState='open' withCloseIcon={true}>
        Test Content
      </Drawer>
    );
    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);
    act(() => {
      jest.runAllTimers();
    });

    expect(defaultProps.callbackFn).toHaveBeenCalled();

    jest.useRealTimers();
  });

  it('should render children correctly', () => {
    render(<Drawer {...defaultProps}>Test Content</Drawer>);
    const childElement = screen.getByText('Test Content');
    expect(childElement).toBeInTheDocument();
  });

  it('should render close icon when withCloseIcon is true', () => {
    render(
      <Drawer {...defaultProps} toggleState='open' withCloseIcon={true}>
        Test Content
      </Drawer>
    );
    const closeButton = screen.getByRole('button');
    expect(closeButton).toBeInTheDocument();
  });

  it('should not render close icon when withCloseIcon is false', () => {
    render(
      <Drawer {...defaultProps} toggleState='open' withCloseIcon={false}>
        Test Content
      </Drawer>
    );
    const closeButton = screen.queryByRole('button');
    expect(closeButton).not.toBeInTheDocument();
  });

  it('should not render header when withHeader is false', () => {
    render(
      <Drawer {...defaultProps} toggleState='open' withHeader={false} headerContent={<div className='fds_drawer__header'>Drawer Header</div>}>
        Test Content
      </Drawer>
    );
    const drawerHeader = screen.queryByText('Drawer Header');
    expect(drawerHeader).not.toBeInTheDocument();
  });

  it('should render header when withHeader is true', () => {
    render(
      <Drawer {...defaultProps} toggleState='open' withHeader={true} headerContent={<div className='fds_drawer__header'>Drawer Header</div>}>
        Test Content
      </Drawer>
    );
    const drawerHeader = screen.queryByText('Drawer Header');
    expect(drawerHeader).toBeInTheDocument();
  });

  it('should apply custom class name', () => {
    render(
      <Drawer {...defaultProps} className='custom-class'>
        Test Content
      </Drawer>
    );
    const drawerElement = screen.getByRole('dialog');
    expect(drawerElement).toHaveClass('custom-class');
  });

  it('should apply full screen class when isMobileFullScreen is true', () => {
    render(
      <Drawer {...defaultProps} isMobileFullScreen={true}>
        Test Content
      </Drawer>
    );
    const drawerElement = screen.getByRole('dialog');
    expect(drawerElement).toHaveClass('fds_drawer__full_screen');
  });

  it('should apply cross brand class when isCrossBrand is true', () => {
    render(
      <Drawer {...defaultProps} isCrossBrand={true}>
        Test Content
      </Drawer>
    );
    const drawerElement = screen.getByRole('dialog');
    expect(drawerElement).toHaveClass('crossbrand');
  });

  it('should call dismissDrawer on mousedown event outside the drawer', () => {
    jest.useFakeTimers();

    render(
      <Drawer {...defaultProps} toggleState='open'>
        Test Content
      </Drawer>
    );
    const isolationLayer = screen.getByTestId('fds_drawer__isolation-layer');
    fireEvent.mouseDown(isolationLayer);
    act(() => {
      jest.runAllTimers();
    });
    expect(defaultProps.callbackFn).toHaveBeenCalled();

    jest.useRealTimers();
  });

  it('should render action button when button content is provided', () => {
    const buttonContent = <button data-testid='fds_drawer__button'>Close Drawer</button>;
    render(
      <Drawer {...defaultProps} isCrossBrand={true} buttonContent={buttonContent}>
        Test Content
      </Drawer>
    );
    const drawerButtonElement = screen.getByTestId('fds_drawer__button');
    expect(drawerButtonElement).toBeInTheDocument();
  });

  it('should not close the drawer when disableCloseOnSwipe is true', () => {
    render(<Drawer {...defaultProps} disableCloseOnSwipe={true} />);
    const drawerElement = screen.getByRole('dialog');

    fireEvent.touchStart(document, { target: drawerElement });
    fireEvent.touchEnd(document);

    expect(drawerElement).toBeInTheDocument();
  });

  it('should render the footer when footerContent is passed', () => {
    render(<Drawer {...defaultProps} footerContent={<button>submit</button>} />);
    const buttonFooter = screen.getByText('submit');

    expect(buttonFooter).toBeInTheDocument();
  });
});
