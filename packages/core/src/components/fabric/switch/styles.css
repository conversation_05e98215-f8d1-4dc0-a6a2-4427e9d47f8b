@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './cb.styles.css';

@layer components {
  .fds_switch__input {
    &:checked + .fds_switch__label .fds_switch__span {
      background-color: theme('colors.color-fill-action');
      &::after {
        transform: translateX(26px);
      }
    }
    &:checked + .fds_switch__label .fds_switch__span::before {
      left: 75%;
    }
  }

  .fds_switch__label {
    font-family: theme('fontFamily.brand-base');
    display: flex;
    align-items: center;
    justify-content: center;
    width: 54px;
    cursor: pointer;
    position: relative;
  }

  .fds_switch__span {
    width: 54px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    transition: 200ms ease-in-out;
    background-color: theme('colors.color-fill-subtle');
    border-radius: theme('borderRadius.border-radius-curved');
    padding: theme('padding.utk-spacing-3xs');
    position: absolute;
  }

  .fds_switch__span::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: 50%;
    left: 25%;
    border-radius: 50%;
    background-color: theme('colors.color-fill-handle---hover');
    transition:
      width 300ms ease,
      height 300ms ease,
      opacity 300ms ease,
      left 200ms ease;
    transform: translate(-50%, -50%);
    opacity: 0;
  }

  .fds_switch__input:focus-visible + .fds_switch__label .fds_switch__span::before,
  .fds_switch__label:hover .fds_switch__span::before {
    width: 3rem;
    height: 3rem;
    opacity: 0.3;
  }

  .fds_switch__span::after {
    content: '';
    z-index: 10;
    width: 24px;
    height: 24px;
    transition-duration: 200ms;
    box-shadow: theme('boxShadow.high');
    background-color: theme('colors.color-fill-default---white');
    border-radius: theme('borderRadius.border-radius-round');
    transform: translateX(0);
  }

  .fds_switch__text {
    z-index: 1;
    font-size: theme('fontSize.font-size--2');
    font-weight: theme('fontWeight.font-weight-base-default');
    position: absolute;

    &.fds_switch__text--on {
      left: 8px;
      color: theme('colors.color-type-inverse');
    }

    &.fds_switch__text--off {
      right: 4px;
      color: theme('colors.color-type-copy');
    }
  }
  .gap .fds_switch__text.fds_switch__text--off {
    right: 5px;
  }
}
