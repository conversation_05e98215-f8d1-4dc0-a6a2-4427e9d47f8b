@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .fds_sticky-container {
    width: inherit;
    position: fixed;
    overflow: hidden;
    height: fit-content;
    z-index: theme('zIndex.sticky');
    background-color: theme('colors.color-background-default--white');
    padding: theme('spacing.utk-spacing-m') theme('spacing.utk-spacing-l');

    &.fds_sticky-container--top {
      top: 0;
    }

    &.fds_sticky-container--bottom {
      bottom: 0;
    }

    &.fds_sticky-container--visible {
      &.fds_sticky-container--top {
        border-bottom: theme('borderWidth.border-width-default') solid theme('colors.color-border-gray');
      }

      &.fds_sticky-container--bottom {
        border-top: theme('borderWidth.border-width-default') solid theme('colors.color-border-gray');
      }
    }

    &.fds_sticky-container--animation {
      transition-delay: 0ms;
      transition-property: transform;
      transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
    }

    &.fds_sticky-container--animation-disabled {
      transition: none;
    }
  }
}
