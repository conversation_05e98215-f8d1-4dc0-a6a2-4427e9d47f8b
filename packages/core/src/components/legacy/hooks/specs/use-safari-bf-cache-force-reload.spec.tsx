// @ts-nocheck
"use client";
import {fireEvent, renderHook} from "test-utils";
import {useSafariBfCacheForceReload} from "../index";

const browsers = [
  {
    name: "Safari",
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15",
  },
  {
    name: "Chrome",
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
  },
  {
    name: "Firefox",
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/109.0",
  },
  {
    name: "Edge",
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.61",
  },
];

describe("useSafariBfCacheForceReload", () => {
  const windowEventListenerSpy = jest.spyOn(window, "addEventListener");

  const setUserAgent = (userAgent: string) => {
    Object.defineProperty(window, "navigator", {
      configurable: true,
      writable: true,
      value: {userAgent},
    });
  };

  describe("safari b/f cache event", () => {
    it("calls window.location.reload() if page persisted", () => {
      setUserAgent(browsers[0].userAgent);

      Object.defineProperty(window, "location", {
        configurable: true,
        value: {reload: jest.fn()},
      });

      windowEventListenerSpy.mockImplementation((_, handler: any) => {
        handler({persisted: true});
      });

      renderHook(() => useSafariBfCacheForceReload());
      fireEvent(window, new Event("pageshow", {bubbles: true}));
      expect(window.location.reload).toHaveBeenCalled();
    });
  });

  describe.each(browsers)(
    "pageshow event listener test for $name",
    ({name, userAgent}) => {
      const {userAgent: originalUserAgent} = window.navigator;

      beforeEach(() => {
        setUserAgent(userAgent);
      });

      afterEach(() => {
        setUserAgent(originalUserAgent);
        windowEventListenerSpy.mockRestore();
      });

      it("adds a pageshow window event listener to Safari only", () => {
        let pageshowEventListenerAdded = false;

        windowEventListenerSpy.mockImplementation((event) => {
          if (event === "pageshow") {
            pageshowEventListenerAdded = true;
          }
        });

        renderHook(() => useSafariBfCacheForceReload());

        if (name === "Safari") {
          expect(pageshowEventListenerAdded).toBe(true);
        } else {
          expect(pageshowEventListenerAdded).toBe(false);
        }
      });
    }
  );
});
