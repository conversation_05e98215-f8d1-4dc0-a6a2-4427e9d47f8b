// @ts-nocheck
"use client";
import {useState, useEffect} from "react";

type Ref = {current: any};

export const useCarouselLockVerticalScroll = (ref: Ref) => {
  const [firstClientX, setFirstClientX] = useState(0);
  const [firstClientY, setFirstClientY] = useState(0);
  const [clientX, setClientX] = useState(0);

  useEffect(() => {
    const carouselRefCurrent = ref.current;

    if (!carouselRefCurrent) return;

    const touchStart = (e: TouchEvent) => {
      setFirstClientX(e.touches[0].clientX);
      setFirstClientY(e.touches[0].clientY);
    };

    // eslint-disable-next-line consistent-return
    const preventTouch = (e: TouchEvent) => {
      const minValue = 20; // threshold

      setClientX(e.touches[0].clientX - firstClientX);

      // Vertical scrolling does not work when you start swiping horizontally.
      if (Math.abs(clientX) > minValue) {
        e.preventDefault();
        return false;
      }
    };

    carouselRefCurrent.addEventListener("touchstart", touchStart);
    carouselRefCurrent.addEventListener("touchmove", preventTouch, {
      passive: false,
    });

    // eslint-disable-next-line consistent-return
    return () => {
      carouselRefCurrent.removeEventListener("touchstart", touchStart);
      carouselRefCurrent.removeEventListener("touchmove", preventTouch, {
        passive: false,
      });
    };
  }, [clientX, firstClientX, firstClientY, ref]);
};
