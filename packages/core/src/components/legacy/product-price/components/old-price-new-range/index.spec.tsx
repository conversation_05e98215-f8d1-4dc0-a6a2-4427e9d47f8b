// @ts-nocheck
"use client";
import React from "react";
import * as PriceCaseModule from "@ecom-next/core/legacy/price-case";

import {AppContext} from "@ecom-next/sitewide/app-state-provider";
import {render} from "test-utils";
import {mockDatalayer} from "test-utils";
import {Brands} from "@ecom-next/core/react-stitch";
import {OldRangeNewRange, OldRangeNewRangeProps} from ".";

const defaultAppState = {
  brandName: Brands.Gap,
  criticalCss: [],
  criticalResources: [],
  locale: "en-US" as const,
  market: "us" as const,
  pageType: "category" as const,
  secrets: {},
  datalayer: mockDatalayer,
};

const renderComponent = (props: OldRangeNewRangeProps) =>
  render(
    <AppContext.Provider value={defaultAppState}>
      <OldRangeNewRange {...props} />
    </AppContext.Provider>
  );

jest.mock("@ecom-next/core/legacy/price-case", () => {
  const original = jest.requireActual("@ecom-next/core/legacy/price-case");
  return {
    __esModule: true,
    ...original,
  };
});

describe("<OldPriceNewRange />", () => {
  describe("snapshots", () => {
    describe("renders correctly when on product page", () => {
      const props: OldRangeNewRangeProps = {
        currentPriceRangeAriaLabel: "Now $5.99 to $7.99",
        currentPriceRangeText: "$5.99 - $7.99",
        regularPriceAriaLabel: "Was $5.99",
        regularPriceText: "$5.99",
        percentageOffText: "30% off",
        productPriceHighlightClass: "product-price__highlight",
        maxPercentageOff: "0",
        pdpFormat: true,
        showPercentage: false,
        strikethrough: false,
        brSalePriceClass: "",
        children: null,
        isHighlightSecondary: false,
        isHighlightSecondaryClass: "",
      };
      const {container} = renderComponent(props);
      it("renders correctly when on product page", () => {
        expect(container).toMatchSnapshot();
      });
    });

    describe("renders correctly when not on product page (i.e. on category page)", () => {
      const props: OldRangeNewRangeProps = {
        currentPriceRangeAriaLabel: "Now $5.99 to $7.99",
        currentPriceRangeText: "$5.99 - $7.99",
        regularPriceAriaLabel: "Was $5.99",
        regularPriceText: "$5.99",
        percentageOffText: "30% off",
        productPriceHighlightClass: "product-price__highlight",
        maxPercentageOff: 0,
        pdpFormat: false,
        showPercentage: false,
        strikethrough: false,
        brSalePriceClass: "",
        children: null,
        isHighlightSecondary: false,
        isHighlightSecondaryClass: "",
      };

      const {container} = renderComponent(props);
      it("renders correctly when not on product page", () => {
        expect(container).toMatchSnapshot();
      });
    });
  });
  describe("when pdpFormat", () => {
    describe("is true", () => {
      const props: OldRangeNewRangeProps = {
        currentPriceRangeAriaLabel: "Now $5.99 to $7.99",
        currentPriceRangeText: "$5.99 - $7.99",
        regularPriceAriaLabel: "Was $5.99",
        regularPriceText: "$5.99",
        percentageOffText: "30% off",
        productPriceHighlightClass: "product-price__highlight",
        maxPercentageOff: "30",
        pdpFormat: true,
        showPercentage: true,
        strikethrough: false,
        brSalePriceClass: "",
        children: null,
        isHighlightSecondary: false,
        isHighlightSecondaryClass: "",
      };
      it("should render provided current range price aria label & current range price text", () => {
        const {container} = renderComponent(props);
        const localizedHeading = container.querySelector(
          "h2.product-price--pdp"
        );
        expect(localizedHeading?.getAttribute("aria-label")).toEqual(
          props.currentPriceRangeAriaLabel
        );
        expect(localizedHeading?.textContent).toEqual(
          props.currentPriceRangeText
        );
      });
      it("should render provided regular price aria label", () => {
        const conditionalStrikeThroughSpy = jest.spyOn(
          PriceCaseModule,
          "ConditionalStrikeThroughPrice"
        );
        const span = document.createElement("span");
        span.setAttribute("aria-label", props.regularPriceAriaLabel);
        span.textContent = props.regularPriceText;
        span.setAttribute("class", "product-price__no-strike");
        span.setAttribute("role", "text");

        const {container} = renderComponent(props);
        expect(conditionalStrikeThroughSpy).toHaveBeenCalled();
        expect(
          container.querySelector("span.product-price__no-strike")
        ).toEqual(span);
      });
      it("should not have strikethrough class", () => {
        const conditionalStrikeThroughSpy = jest.spyOn(
          PriceCaseModule,
          "ConditionalStrikeThroughPrice"
        );
        renderComponent(props);
        expect(conditionalStrikeThroughSpy).toHaveBeenCalledWith(
          {
            noStrike: true,
            regularPriceAriaLabel: "Was $5.99",
            regularPriceText: "$5.99",
          },
          {}
        );
      });
      it("should provide display values to ShowPercentageOffText", () => {
        const percentageOffSpy = jest.spyOn(
          PriceCaseModule,
          "ShowPercentageOffText"
        );
        const {getByText} = renderComponent(props);
        expect(getByText(props.percentageOffText).textContent).toBe(
          props.percentageOffText
        );
        expect(percentageOffSpy).toHaveBeenCalledWith(
          {additionalClass: "", percentageOffText: "30% off", show: true},
          {}
        );
      });
    });
    describe("is false", () => {
      const props: OldRangeNewRangeProps = {
        currentPriceRangeAriaLabel: "Now $5.99 to $7.99",
        currentPriceRangeText: "$5.99 - $7.99",
        regularPriceAriaLabel: "Was $5.99",
        regularPriceText: "$5.99",
        percentageOffText: "30% off",
        productPriceHighlightClass: "product-price__highlight",
        maxPercentageOff: "30",
        pdpFormat: false,
        showPercentage: true,
        strikethrough: false,
        brSalePriceClass: "",
        children: null,
        isHighlightSecondary: false,
        isHighlightSecondaryClass: "",
      };
      it("should render provided current range price aria label and current range price text", () => {
        const {container} = renderComponent(props);
        const localizedHeading = container.querySelector(
          "div.product-price__highlight"
        );
        expect(localizedHeading?.getAttribute("aria-label")).toEqual(
          props.currentPriceRangeAriaLabel
        );
        expect(localizedHeading?.textContent).toEqual(
          props.currentPriceRangeText
        );
      });
      it("should render provided regular price aria label and regular price text", () => {
        const conditionalStrikeThroughSpy = jest.spyOn(
          PriceCaseModule,
          "ConditionalStrikeThroughPrice"
        );
        const span = document.createElement("span");
        span.setAttribute("aria-label", props.regularPriceAriaLabel);
        span.textContent = props.regularPriceText;
        span.setAttribute("class", "product-price__no-strike");
        span.setAttribute("role", "text");

        const {container} = renderComponent(props);
        expect(conditionalStrikeThroughSpy).toHaveBeenCalled();
        expect(
          container.querySelector("span.product-price__no-strike")
        ).toEqual(span);
      });
      it("should not have strikethrough class", () => {
        const conditionalStrikeThroughSpy = jest.spyOn(
          PriceCaseModule,
          "ConditionalStrikeThroughPrice"
        );
        renderComponent(props);
        expect(conditionalStrikeThroughSpy).toHaveBeenCalledWith(
          {
            noStrike: true,
            regularPriceAriaLabel: "Was $5.99",
            regularPriceText: "$5.99",
          },
          {}
        );
      });
      it("should provide display values to ShowPercentageOffText", () => {
        const percentageOffSpy = jest.spyOn(
          PriceCaseModule,
          "ShowPercentageOffText"
        );
        const {getByText} = renderComponent(props);
        expect(getByText(props.percentageOffText).textContent).toBe(
          props.percentageOffText
        );
        expect(percentageOffSpy).toHaveBeenCalledWith(
          {percentageOffText: "30% off", show: true},
          {}
        );
      });
    });
  });
});
