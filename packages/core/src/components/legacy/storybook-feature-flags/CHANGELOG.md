# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [0.10.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.9.0...@core-ui/storybook-feature-flags@0.10.0) (2024-11-05)

### Features

- **control-icons:** updating primary play pause icons for gap ([#6077](https://github.gapinc.com/ecomfrontend/core-ui/issues/6077)) ([b6b7eef](https://github.gapinc.com/ecomfrontend/core-ui/commit/b6b7eeff56f4b58377d806d35538638b9226fe92))

# [0.9.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.8.0...@core-ui/storybook-feature-flags@0.9.0) (2024-08-14)

### Features

- **plpwe-1494:** color swatch updates to support gap redesign ([#6037](https://github.gapinc.com/ecomfrontend/core-ui/issues/6037)) ([97886ba](https://github.gapinc.com/ecomfrontend/core-ui/commit/97886ba5ba19f33fab6c55719385e5a10db4d7e5))

# [0.8.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.7.0...@core-ui/storybook-feature-flags@0.8.0) (2024-07-24)

### Features

- **plpwe-1456:** Align sub-category title to left ([#6028](https://github.gapinc.com/ecomfrontend/core-ui/issues/6028)) ([799fc9b](https://github.gapinc.com/ecomfrontend/core-ui/commit/799fc9b565bf5894302e0be6e7ec7082caaf17b0))

# [0.7.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.6.0...@core-ui/storybook-feature-flags@0.7.0) (2024-06-20)

### Features

- **eval-5297:** use gap redesign new font when flag is active ([#6011](https://github.gapinc.com/ecomfrontend/core-ui/issues/6011)) ([c7ef6ac](https://github.gapinc.com/ecomfrontend/core-ui/commit/c7ef6acc6f1cc940f92b66e8169d8751c3604102))

# [0.6.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.5.0...@core-ui/storybook-feature-flags@0.6.0) (2024-05-06)

### Features

- **feature-flags:** add feature flag at-redesign-2024 ([#5988](https://github.gapinc.com/ecomfrontend/core-ui/issues/5988)) ([f355cfb](https://github.gapinc.com/ecomfrontend/core-ui/commit/f355cfb6dc1e6a0bdbea6de7922f6ae410f11f95))

# [0.5.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.4.0...@core-ui/storybook-feature-flags@0.5.0) (2024-05-03)

### Features

- **storybook-feature-flags:** build addon using preset API #FUI-4911 ([#5987](https://github.gapinc.com/ecomfrontend/core-ui/issues/5987)) ([ec43ace](https://github.gapinc.com/ecomfrontend/core-ui/commit/ec43ace34bbb9a31d799d2729ab89b6ce0f9a383)), closes [#FUI-4911](https://github.gapinc.com/ecomfrontend/core-ui/issues/FUI-4911)

# [0.4.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.3.1...@core-ui/storybook-feature-flags@0.4.0) (2024-04-24)

### Features

- **icons:** add new play/pause icons for BR ([#5976](https://github.gapinc.com/ecomfrontend/core-ui/issues/5976)) ([7a645ae](https://github.gapinc.com/ecomfrontend/core-ui/commit/7a645aed6d8c6657b01764bd84638ed32b018b58))

## [0.3.1](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.3.0...@core-ui/storybook-feature-flags@0.3.1) (2024-01-31)

### Reverts

- "revert(): br b2 color & SB version bump" ([#5943](https://github.gapinc.com/ecomfrontend/core-ui/issues/5943)) ([8c88ce4](https://github.gapinc.com/ecomfrontend/core-ui/commit/8c88ce454ca8c7cb15701fcf36adb265506775ed)), closes [#5942](https://github.gapinc.com/ecomfrontend/core-ui/issues/5942)

# [0.3.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.2.0...@core-ui/storybook-feature-flags@0.3.0) (2023-11-09)

### Features

- **color-swatch-br-2023-redesign:** add new style to color swatch component PLPWE-1057 ([#5921](https://github.gapinc.com/ecomfrontend/core-ui/issues/5921)) ([e9a5dc6](https://github.gapinc.com/ecomfrontend/core-ui/commit/e9a5dc6aade2cfe12aa4d0c33e2273daed7f3750))

# [0.2.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/storybook-feature-flags@0.1.0...@core-ui/storybook-feature-flags@0.2.0) (2023-10-20)

### Features

- **storybook-feature-flags:** extend storybook feature flags #FUI-4815 ([#5913](https://github.gapinc.com/ecomfrontend/core-ui/issues/5913)) ([d682e90](https://github.gapinc.com/ecomfrontend/core-ui/commit/d682e90664fd6e1e758d95a068ba366c6230d0ee)), closes [#FUI-4815](https://github.gapinc.com/ecomfrontend/core-ui/issues/FUI-4815)

# 0.1.0 (2023-10-06)

### Features

- **storybook-feature-flags:** new storybook-addon package #FUI-4728 ([#5905](https://github.gapinc.com/ecomfrontend/core-ui/issues/5905)) ([aee3f83](https://github.gapinc.com/ecomfrontend/core-ui/commit/aee3f83a7557fbc820014a1239428fc4c5e32861)), closes [#FUI-4728](https://github.gapinc.com/ecomfrontend/core-ui/issues/FUI-4728)
