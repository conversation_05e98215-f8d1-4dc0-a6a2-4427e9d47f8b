// @ts-nocheck
"use client";
import React from "react";
import {ModalBase} from "@ecom-next/core/legacy/modal-base";
import {DrawerSlide} from "@ecom-next/core/legacy/motion";
import {Content} from "./components/Content";
import {Dialog} from "./components/Dialog";
import {DrawerProps, DrawerSlidePosition} from "./types";

export const Drawer = ({
  autoFocus = true,
  children,
  container,
  crossBrand = false,
  dialogCSS,
  disablePortal = false,
  hasRoundedCorners = false,
  isOpen,
  onClose,
  position = DrawerSlidePosition.left,
  ...ariaAttributes
}: DrawerProps): JSX.Element => (
  <ModalBase
    autoFocus={autoFocus}
    container={container}
    disablePortal={disablePortal}
    isOpen={isOpen}
    onClose={onClose}
    TransitionComponent={DrawerSlide}
    TransitionProps={{position}}
  >
    {(slideStyles) => (
      <Dialog
        css={[slideStyles, dialogCSS]}
        hasRoundedCorners={hasRoundedCorners}
        position={position}
        role="dialog"
        tabIndex={0}
        {...ariaAttributes}
      >
        <Content crossBrand={crossBrand}>{children}</Content>
      </Dialog>
    )}
  </ModalBase>
);

export {DrawerSlidePosition} from "./types";
export type {DrawerProps} from "./types";
