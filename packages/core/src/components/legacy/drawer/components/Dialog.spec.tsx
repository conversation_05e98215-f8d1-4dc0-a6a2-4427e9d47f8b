// @ts-nocheck
"use client";
import {snapshotTests} from "test-utils";
import {Dialog} from "./Dialog";
import {DrawerSlidePosition} from "..";

describe("Dialog", () => {
  describe("snapshots", () => {
    snapshotTests(Dialog, [
      ["left position", {position: DrawerSlidePosition.left}],
      ["right position", {position: DrawerSlidePosition.right}],
      ["top position", {position: DrawerSlidePosition.top}],
      ["bottom position", {position: DrawerSlidePosition.bottom}],
      [
        "left position, rounded corners",
        {position: DrawerSlidePosition.left, hasRoundedCorners: true},
      ],
      [
        "right position, rounded corners",
        {position: DrawerSlidePosition.right, hasRoundedCorners: true},
      ],
      [
        "top position, rounded corners",
        {position: DrawerSlidePosition.top, hasRoundedCorners: true},
      ],
      [
        "bottom position, rounded corners",
        {position: DrawerSlidePosition.bottom, hasRoundedCorners: true},
      ],
    ]);
  });
});
