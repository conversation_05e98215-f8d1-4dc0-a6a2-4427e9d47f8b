// @ts-nocheck
"use client";
import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TouchEventHandler,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {uniqueId} from "lodash";
import {RangeSliderProps} from "./types";
import {SliderInput} from "./components/SliderInput";
import {
  Slider<PERSON>ontainer,
  SliderRange,
  SliderTrack,
} from "./components/SliderTrack";
import {LabelContainer, SliderLabel} from "./components/SliderLabel";

export const RangeSlider = (props: RangeSliderProps): JSX.Element => {
  const {
    minValue,
    maxValue,
    steps,
    getRangeLabel,
    getRangeAriaLabel,
    onMinChange,
    onMaxChange,
    className,
    id: idProp,
    sliderWidth,
    callChangeOnEnd = true,
    ...other
  } = props;

  const [minVal, setMinVal] = useState(minValue);
  const [maxVal, setMaxVal] = useState(maxValue);
  const [idState] = useState(uniqueId("range-slider"));
  const id = idProp ?? idState;
  const minId = `${id}-left`;
  const maxId = `${id}-right`;
  const LABEL_SLIDER_FACTOR = 0.93;
  const LEFT_LABEL_OFFSET = 12;
  const RIGHT_LABEL_OFFSET = 9;

  const leftLabel = useRef<HTMLLabelElement>(null);
  const rightLabel = useRef<HTMLLabelElement>(null);
  const range = useRef<HTMLInputElement>(null);

  const getPercent = useCallback(
    (value) => Math.round(((value - minValue) / (maxValue - minValue)) * 100),
    [minValue, maxValue]
  );

  const getSignedNumberAsString = (num: number) =>
    `${num >= 0 ? "+" : "-"} ${Math.abs(num)}`;
  const getLabelStylePosition = (percent: number, offset: number) =>
    `calc(${percent * LABEL_SLIDER_FACTOR}% ${getSignedNumberAsString(
      offset
    )}px)`;

  const calculateRange = () => {
    const minPercent = getPercent(minVal);
    const maxPercent = getPercent(maxVal);

    if (range.current) {
      range.current.style.left = `${minPercent}%`;
      const width = maxPercent - minPercent;
      range.current.style.width = `${width}%`;

      leftLabel.current &&
        (leftLabel.current.style.left = getLabelStylePosition(
          minPercent,
          LEFT_LABEL_OFFSET
        ));
      rightLabel.current &&
        (rightLabel.current.style.left = getLabelStylePosition(
          maxPercent,
          RIGHT_LABEL_OFFSET
        ));
    }
  };

  useEffect(() => {
    calculateRange();
  }, [minVal, maxVal, getPercent]);

  // call onMinChange callback function based on callChangeOnEnd value (true | false)
  const onMinValueChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const value = Math.min(Number(event.target.value), maxVal - steps);
    setMinVal(value);
    !callChangeOnEnd && onMinChange?.(minVal);
  };

  // call onMinChange callback function based on callChangeOnEnd value (true | false)
  const onMinValueChangeEnd = () => callChangeOnEnd && onMinChange?.(minVal);
  const onMinValueMouseUp: MouseEventHandler = () => onMinValueChangeEnd();
  const onMinValueTouchEnd: TouchEventHandler = () => onMinValueChangeEnd();

  // call onMaxChange callback function based on callChangeOnEnd value (true | false)
  const onMaxValueChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const value = Math.max(Number(event.target.value), minVal + steps);
    setMaxVal(value);
    !callChangeOnEnd && onMaxChange?.(minVal);
  };

  // call onMaxChange callback function based on callChangeOnEnd value (true | false)
  const onMaxValueChangeEnd = () => callChangeOnEnd && onMaxChange?.(maxVal);
  const onMaxValueMouseUp: MouseEventHandler = () => onMaxValueChangeEnd();
  const onMaxValueTouchEnd: TouchEventHandler = () => onMaxValueChangeEnd();

  return (
    <div className={className}>
      <LabelContainer sliderWidth={sliderWidth}>
        <SliderLabel ref={leftLabel} css={{direction: "rtl"}} htmlFor={minId}>
          {getRangeLabel ? getRangeLabel(minVal) : `${minVal}`}
        </SliderLabel>
        <SliderLabel ref={rightLabel} htmlFor={maxId}>
          {getRangeLabel ? getRangeLabel(maxVal) : `${maxVal}`}
        </SliderLabel>
      </LabelContainer>

      <SliderInput
        aria-label={getRangeAriaLabel?.(minVal) as string | undefined}
        data-testid="min-range-slider"
        id={minId}
        max={maxValue}
        min={minValue}
        onChange={onMinValueChange}
        onMouseUp={onMinValueMouseUp}
        onTouchEnd={onMinValueTouchEnd}
        sliderOrientation="left"
        sliderWidth={sliderWidth}
        step={steps}
        type="range"
        value={minVal}
      />

      <SliderInput
        aria-label={getRangeAriaLabel?.(maxVal) as string | undefined}
        data-testid="max-range-slider"
        id={maxId}
        max={maxValue}
        min={minValue}
        onChange={onMaxValueChange}
        onMouseUp={onMaxValueMouseUp}
        onTouchEnd={onMaxValueTouchEnd}
        sliderOrientation="right"
        sliderWidth={sliderWidth}
        step={steps}
        type="range"
        value={maxVal}
      />

      <SliderContainer sliderWidth={sliderWidth}>
        <SliderTrack />
        <SliderRange ref={range} />
      </SliderContainer>
    </div>
  );
};

export type {RangeSliderProps} from "./types";
