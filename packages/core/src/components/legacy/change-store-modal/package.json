{"sideEffects": false, "name": "@core-ui/change-store-modal", "version": "12.2.1", "description": "Modal dialog for selecting a store for BOPIS functionality", "author": "Foundation UI", "main": "./src/index.tsx", "scripts": {"build": "npm run clean && npm run build:js && npm run build:types", "build:js": "rollup -c", "build:types": "tsc --project tsconfig.publish.json", "check:types": "tsc --noEmit", "clean": "rm -rf build", "coverage": "npm test -- --coverage", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build && node ../../tasks/copy-files.js", "test": "jest --colors", "test:update": "npm test -- --updateSnapshot"}, "devDependencies": {"@core-ui/eslint-config-fui": "0.8.0"}, "peerDependencies": {"@core-ui/core": ">=0.29.0", "classnames": "^2.2.5", "react": "^16.9.0"}, "migratedToCore": "11.0.0"}