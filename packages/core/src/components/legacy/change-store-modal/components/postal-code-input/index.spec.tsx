// @ts-nocheck
"use client";
import React from "react";
import {AppState, DataLayer} from "@ecom-next/sitewide/app-state-provider";
import {Brands} from "@ecom-next/core/legacy/utility";
import renderer from "react-test-renderer";
import {TestContext} from "test-utils";
import {render, screen, fireEvent} from "test-utils";
import {PostalCodeInput} from ".";
import {Props} from "./types";

jest.mock("lodash/debounce", () =>
  jest.fn((fn) => {
    const debouncedFn = fn;
    debouncedFn.cancel = jest.fn();
    return debouncedFn;
  })
);

const defaultAppState: AppState = {
  brandName: Brands.Gap,
  criticalCss: [],
  criticalResources: [],
  datalayer: {
    build: () => {},
    link: () => {},
  } as unknown as DataLayer,
  locale: "en_US" as const,
  market: "us",
  pageType: "sitewide",
};

const TestPostalCodeInput = (props: Props) => (
  <TestContext appState={{...defaultAppState} as any}>
    <PostalCodeInput {...props} />
  </TestContext>
);

describe("<PostalCodeInput />", () => {
  it("renders correctly", () => {
    const tree = renderer
      .create(<TestPostalCodeInput handleChange={jest.fn()} value="94108" />)
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  it("renders correctly for br", () => {
    defaultAppState.brandName = Brands.BananaRepublic;
    const BRTestPostalCodeInput = (props: Props) => (
      <TestContext appState={{...defaultAppState} as any}>
        <PostalCodeInput {...props} />
      </TestContext>
    );
    const tree = renderer
      .create(<BRTestPostalCodeInput handleChange={jest.fn()} value="94108" />)
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  it("clears value after first focus", () => {
    const handleFocus = () => () => jest.fn();
    const handleChange = () => () => jest.fn();
    const defaultProps = {
      value: "94108",
      handleFocus,
      handleChange,
    };
    render(<TestPostalCodeInput {...defaultProps} />);
    const inputElem = screen.getByRole("textbox");
    fireEvent.focus(inputElem);
    expect(inputElem).toHaveValue("");
    fireEvent.change(inputElem, {target: {value: "94083"}});
    expect(inputElem).toHaveValue("94083");
    fireEvent.focus(inputElem);
    expect(inputElem).toHaveValue("94083");
  });

  it("calls handleChangeProp", () => {
    const handleChange = jest.fn();
    const defaultProps = {
      value: "94108",
      handleChange,
    };
    render(<TestPostalCodeInput {...defaultProps} />);
    const inputElem = screen.getByRole("textbox");
    fireEvent.change(inputElem, {target: {value: "87542"}});
    expect(handleChange).toHaveBeenCalled();
  });

  it("passes validation and calls handleDebounceChange correctly", () => {
    const handleChange = jest.fn();
    const defaultProps = {
      value: "94108",
      handleChange,
      hasError: false,
    };

    render(<TestPostalCodeInput {...defaultProps} />);
    const inputElem = screen.getByRole("textbox");
    fireEvent.change(inputElem, {target: {value: "87542"}});
    expect(handleChange).toHaveBeenCalled();
  });
});
