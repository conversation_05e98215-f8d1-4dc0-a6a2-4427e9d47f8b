// @ts-nocheck
"use client";
import {SwatchesProps} from "@ecom-next/core/legacy/swatches";
import img from "../assets/background.png";
import {ProductInfo} from "../types";

export const regularPricing = {
  currentMaxPrice: "54.99",
  currentMinPrice: "49.99",
  regularMaxPrice: "69.95",
  regularMinPrice: "69.95",
  localizedCurrentMaxPrice: "$54.99",
  localizedCurrentMinPrice: "$49.99",
  localizedRegularMaxPrice: "$69.95",
  localizedRegularMinPrice: "$69.95",
  minPercentageOff: 0,
  maxPercentageOff: 0,
  localizedCurrencySymbol: "$",
};

export const rangePricing = {
  priceType: 2,
  currentMaxPrice: "54.99",
  currentMinPrice: "49.99",
  regularMaxPrice: "54.99",
  regularMinPrice: "49.99",
  localizedCurrentMaxPrice: "$54.99",
  localizedCurrentMinPrice: "$49.99",
  localizedRegularMaxPrice: "$54.99",
  localizedRegularMinPrice: "$49.99",
  minPercentageOff: 0,
  maxPercentageOff: 0,
  localizedCurrencySymbol: "$",
};

export const twoUniquePrices = {
  priceType: 2,
  currentMaxPrice: "54.99",
  currentMinPrice: "54.99",
  regularMaxPrice: "74.99",
  regularMinPrice: "74.99",
  localizedCurrentMaxPrice: "$54.99",
  localizedCurrentMinPrice: "$54.99",
  localizedRegularMaxPrice: "$74.99",
  localizedRegularMinPrice: "$74.99",
  minPercentageOff: 0,
  maxPercentageOff: 0,
  localizedCurrencySymbol: "$",
};

export const fullRangeMarkdown = {
  priceType: 2,
  currentMaxPrice: "58.99",
  currentMinPrice: "57.99",
  regularMaxPrice: "59.99",
  regularMinPrice: "58.99",
  localizedCurrentMaxPrice: "$58.99",
  localizedCurrentMinPrice: "$57.99",
  localizedRegularMaxPrice: "$59.99",
  localizedRegularMinPrice: "$58.99",
  minPercentageOff: 0,
  maxPercentageOff: 0,
  localizedCurrencySymbol: "$",
};

export const productInfo: ProductInfo = {
  name: "Rockstar Jeans",
  productURL:
    "http://www.gap.com/browse/product.do?cid=1072543&pcid=1066503&vid=1&pid=864228032",
  productID: "123",
  url: img,
  altText: "Female model wearing rockstar jeans.",
  marketingFlag: {
    marketingFlagName: "Final Sale",
  },
  price: {
    ...fullRangeMarkdown,
  },
  customTextColor: "",
};

export const colorArray: SwatchesProps["colorArray"] = [
  {
    colorName: "carbon",
    id: "0000",
    url: "images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--carbon.jpg",
    productImageAltText: "Split Neck Tee in Carbon",
    value: "0000",
  },
  {
    colorName: "coral-retro-stripe",
    id: "0001",
    url: "images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg",
    productImageAltText: "Split Neck Tee in Coral Retro Stripe",
    value: "0001",
  },
  {
    colorName: "coral tropics",
    id: "0002",
    url: "images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg",
    productImageAltText: "Split Neck Tee in Coral Tropics",
    value: "0002",
  },
  {
    colorName: "enchant mint",
    id: "0003",
    url: "images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg",
    productImageAltText: "Split Neck Tee in Enchant Mint",
    value: "0003",
  },
  {
    colorName: "goldenopportunity",
    id: "0004",
    url: "images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg",
    productImageAltText: "Split Neck Tee in Golden Opportunity",
    value: "0004",
  },
  {
    colorName: "indigo",
    id: "0005",
    url: "images/swatches/old-navy/split-neck-tee/swatch--indigo.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--indigo.jpg",
    productImageAltText: "Split Neck Tee in Indigo",
    value: "0005",
  },
  {
    backOrderMessage: "On back order.  Est shipping date November 8, 2020",
    colorName: "navy stripe",
    id: "0006",
    url: "images/swatches/old-navy/split-neck-tee/swatch--navy-stripe.jpg",
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--navy-stripe.jpg",
    productImageAltText: "Split Neck Tee in Navy Stripe",
    value: "0006",
  },
  {
    colorName: "seasalt",
    id: "0007",
    url: "images/swatches/old-navy/split-neck-tee/swatch--seasalt.jpg",
    inStock: false,
    productImage:
      "images/product-photos/old-navy/split-neck-tee/product--seasalt.jpg",
    productImageAltText: "Split Neck Tee in Seasalt",
    value: "0007",
  },
];

export const productInfoWithColorPicker: ProductInfo = {
  name: "Split Neck Tee",
  productURL:
    "https://www.gap.com/browse/product.do?pid=577551022&cid=17076&pcid=17076&vid=1&grid=pds_91_146_1&cpos=94&cexp=1567&kcid=CategoryIDs%3D17076&ctype=Listing&cpid=res20070611340704284301355#pdp-page-content",
  productID: "123",
  url: colorArray[3].productImage,
  altText: "Split neck tee in selected color",
  marketingFlag: {
    marketingFlagName: "Final Sale",
  },
  price: {
    currentMaxPrice: "24.99",
    currentMinPrice: "19.99",
    regularMaxPrice: "39.95",
    regularMinPrice: "39.95",
    localizedCurrentMaxPrice: "$24.99",
    localizedCurrentMinPrice: "$19.99",
    localizedRegularMaxPrice: "$39.95",
    localizedRegularMinPrice: "$39.95",
    minPercentageOff: 0,
    maxPercentageOff: 0,
    localizedCurrencySymbol: "$",
  },
};
