{"sideEffects": false, "name": "@core-ui/product-card", "version": "21.5.0", "description": "Product Card Component", "author": "Discover", "main": "./src/index.tsx", "scripts": {"build": "npm run clean && npm run build:js && npm run build:types", "build:js": "rollup -c", "build:types": "tsc --project tsconfig.publish.json", "check-types": "tsc --noEmit", "clean": "rm -rf build", "coverage": "npm test -- --coverage", "lint": "eslint --ext .jsx,.js,.ts,.tsx .", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build && node ../../tasks/copy-files.js", "test": "jest --colors", "test:update": "npm test -- --updateSnapshot"}, "devDependencies": {"@core-ui/eslint-config-fui": "0.8.0"}, "peerDependencies": {"@ecom-next/plp-ui/legacy/product-image": "^4.5.2", "@ecom-next/plp-ui/legacy/quick-add": "^0.10.2", "@core-ui/core": ">=0.31.0", "@emotion/core": "^10.1.1", "react": "^16.9.0"}, "migratedToCore": "21.0.0"}