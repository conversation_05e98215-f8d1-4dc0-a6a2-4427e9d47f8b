// @ts-nocheck
"use client";
import React from "react";
import {snapshotTests} from "test-utils";
import {fireEvent, render, screen} from "test-utils";
import {noop} from "lodash";
import {Radio} from ".";
import {
  getBackground,
  getButtonSize,
  getCheckmark,
  selectValue,
} from "./components/RadioButton";

const baseProps = {
  id: "radio-button",
  label: "radio button",
  value: "selected",
  onChange: noop,
};

const childElements: JSX.Element = (
  <div className="custom-radio custom-radio__label-children">
    A <span css={{fontWeight: 700}}>Child</span> element in a Radio button.
  </div>
);

describe("<Radio />", () => {
  describe("snapshots", () => {
    snapshotTests(
      Radio,
      [
        ["default", {}],
        ["checked", {checked: true}],
        ["disabled", {disabled: true}],
        ["with help text", {helpText: "Help text"}],
        ["with small label text", {isRadioSmall: true}],
        ["with small background size", {isBackgroundSizeSmall: true}],
      ],
      baseProps
    );
  });

  describe("default state", () => {
    it("is not checked", () => {
      render(<Radio {...baseProps} />);
      expect(screen.getByLabelText(baseProps.label)).not.toHaveAttribute(
        "checked"
      );
    });

    it("should have label", () => {
      render(<Radio {...baseProps} />);
      expect(screen.getByLabelText(baseProps.label)).toBeInTheDocument();
    });

    it("should have a radio button", () => {
      render(<Radio {...baseProps} />);
      expect(screen.getByRole("radio")).toBeInTheDocument();
    });
  });

  describe("label", () => {
    it("can render without a label", () => {
      const props = {
        label: undefined,
        id: "radio",
        inputProps: {"aria-label": "a label"},
      };
      render(<Radio {...baseProps} {...props} />);
      expect(screen.queryAllByLabelText(baseProps.label)).toHaveLength(0);
    });

    it("can render with a label", () => {
      const props = {id: "radio", label: "a label"};
      const {queryByText, container} = render(
        <Radio {...baseProps} {...props} />
      );
      expect(container.querySelector(`#${props.id}`)).toBeInTheDocument();
      expect(queryByText(props.label)).toBeInTheDocument();
    });

    it("can render with label and help text", () => {
      const props = {id: "radio", label: "a label", helpText: "help text"};
      render(<Radio {...baseProps} {...props} />);
      expect(screen.getByText(props.helpText)).toBeInTheDocument();
      expect(screen.getByText(props.label)).toBeInTheDocument();
    });

    it("can style label text and help text", () => {
      const props = {
        id: "radio",
        label: "a label",
        helpText: "help text",
        styles: {
          labelText: {color: "purple"},
          helpText: {color: "orange"},
        },
      };
      render(<Radio {...baseProps} {...props} />);
      expect(screen.getByText(props.label)).toHaveStyle({color: "purple"});
      expect(screen.getByText(props.helpText)).toHaveStyle({color: "orange"});
    });

    it("can render with children", () => {
      const wrapper = render(<Radio {...baseProps} label={childElements} />);
      expect(wrapper).toMatchSnapshot();
    });
  });

  describe("inputProps", () => {
    it("spreads inputProps to input element", () => {
      const inputProps = {"aria-label": "a label", form: "some form"};
      render(<Radio {...baseProps} inputProps={inputProps} />);
      expect(screen.getByRole("radio")).toHaveProperty("form");
    });
  });

  describe("checked prop", () => {
    it("radio is not checked when passed false", () => {
      render(<Radio {...baseProps} checked={false} />);
      expect(screen.getByRole("radio")).toHaveProperty("checked", false);
    });

    it("radio is checked when passed true", () => {
      render(<Radio {...baseProps} checked />);
      expect(screen.getByRole("radio")).toHaveProperty("checked", true);
    });
  });

  describe("onChange prop", () => {
    it("is called when radio changes", () => {
      const props = {
        value: "a value",
        onChange: jest.fn(),
      };

      render(<Radio {...baseProps} {...props} />);
      fireEvent.click(screen.getByRole("radio"));
      expect(props.onChange).toHaveBeenCalled();
    });
  });

  describe("disabled prop", () => {
    it("sets aria-disabled", () => {
      render(<Radio {...baseProps} disabled />);
      expect(screen.getByRole("radio")).toHaveProperty("disabled", true);
    });
  });

  describe("classNamePrefix prop", () => {
    it("attaches the correct classNames", () => {
      const prefix = "custom-radio";
      const className = "custom-radio";
      const {container} = render(
        <Radio
          {...baseProps}
          className={className}
          classNamePrefix={prefix}
          helpText="help"
        />
      );

      expect(container.getElementsByClassName(`${prefix}`)).toBeTruthy();
      expect(container.getElementsByClassName(`${prefix}__input`)).toBeTruthy();
      expect(container.getElementsByClassName(`${prefix}__label`)).toBeTruthy();
      expect(container.querySelector("span")).toHaveClass(
        `${prefix}__label-text`
      );
      expect(container.getElementsByClassName(`${prefix}__help`)).toBeTruthy();
    });
  });

  describe("styles prop", () => {
    it("correctly styles inner elements", () => {
      const unchecked = render(
        <Radio
          {...baseProps}
          styles={{
            label: {fontSize: "0.875rem"},
            radioButton: ({checked, theme}) => ({
              width: "17px",
              height: "17px",
              backgroundColor: checked ? theme.color.wh : theme.color.bk,
            }),
          }}
        />
      );

      const radioButton = unchecked.container.querySelector(
        "div[class$='Radio']"
      );
      const labelText = unchecked.container.querySelector(
        "label[class$='Radio']"
      );
      expect(labelText).toHaveStyleRule("font-size", "0.875rem");
      expect(radioButton).toHaveStyleRules({
        width: "17px",
        height: "17px",
        "background-color": "#000000",
      });

      const checkedRadio = render(
        <Radio
          {...baseProps}
          checked
          styles={{
            label: {fontSize: "0.875rem"},
            radioButton: ({checked, theme}) => ({
              width: "17px",
              height: "17px",
              backgroundColor: checked ? theme.color.wh : theme.color.bk,
            }),
          }}
        />
      );

      const checkedRadioButton = checkedRadio.container.querySelector(
        "div[class$='Radio']"
      );
      expect(checkedRadioButton).toHaveStyleRules({
        width: "17px",
        height: "17px",
        "background-color": "#FFFFFF",
      });
    });
  });
});
/* eslint-disable @typescript-eslint/no-explicit-any */
describe("getBackground", () => {
  it("returns the customBackgroundColor given checked is true and a custom color was provided", () => {
    const props: any = {
      checked: true,
      theme: {color: {gray60: "red"}},
      customBackgroundColor: "blue",
    };
    expect(getBackground(props).styles.includes("border: 1px solid blue")).toBe(
      true
    );
    expect(getBackground(props).styles.includes("background-color: blue")).toBe(
      true
    );
  });
  it("returns the default background color given checked is true and no custom color was provided", () => {
    const props: any = {
      checked: true,
      theme: {color: {bk: "black"}},
      customBackgroundColor: undefined,
    };
    expect(
      getBackground(props).styles.includes("border: 1px solid black")
    ).toBe(true);
  });
  it("returns the custom background color given checked is true, custom color and background style was provided", () => {
    const props: any = {
      checked: true,
      theme: {color: {bk: "black"}},
      customBackgroundColor: "blue",
      styles: {
        radioButton: () => ({
          backgroundColor: "red",
        }),
      },
    };
    expect(getBackground(props).styles.includes("background-color: blue")).toBe(
      true
    );
  });
  it("returns the default background color given checked is true, no custom color was provided and background style was provided", () => {
    const props: any = {
      checked: true,
      theme: {color: {bk: "black"}},
      customBackgroundColor: undefined,
      styles: {
        radioButton: () => ({
          backgroundColor: "red",
        }),
      },
    };
    expect(
      getBackground(props).styles.includes("background-color: black")
    ).toBe(true);
  });
});

describe("radio size", () => {
  it("return the small radio whe isRadioSmall is true", () => {
    const props: any = {
      theme: {brand: "at"},
      isRadioSmall: true,
    };
    expect(getButtonSize(props).styles.includes("width: 1rem")).toBe(true);
  });

  it("return the small checkmark when isRadioSmall is true", () => {
    const props: any = {
      checked: true,
      crossBrand: true,
      isRadioSmall: true,
    };
    expect(
      getCheckmark(props).styles.includes("background-position-y: 5px")
    ).toBe(true);
    expect(getCheckmark(props).styles.includes("background-size: 16px")).toBe(
      true
    );
  });
});

describe("selectValue", () => {
  it("returns the value if the predicate is true and the value is defined", () => {
    expect(selectValue(true, 1, 2)).toBe(1);
  });
  it("returns the default value in all other cases", () => {
    expect(selectValue(false, 1, 2)).toBe(2);
    expect(selectValue(true, undefined, 2)).toBe(2);
    expect(selectValue(false, undefined, 2)).toBe(2);
    expect(selectValue(false, undefined, null)).toBe(null);
  });
});
