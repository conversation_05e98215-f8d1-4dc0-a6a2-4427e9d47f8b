// @ts-nocheck
'use client';
import { styled, css, keyframes, forBrands, getBaseColor, CSSObject } from '@ecom-next/core/react-stitch';
import { RadioStyleFn, CommonProps } from '../types';

const backgroundBounce = keyframes`
  0% {
    transform: scale(0);
  }
  90% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
`;

const defaultButtonSize = '1.5rem';
const smallButtonSize = '1rem';

export const selectValue = <T,>(predicate: boolean, value: T, defaultValue: T): T => {
  if (predicate && value != null && value) {
    return value;
  }
  return defaultValue;
};

export const getButtonSize: RadioStyleFn<CSSObject> = ({ theme, isRadioSmall }) => {
  const buttonSize = isRadioSmall ? smallButtonSize : defaultButtonSize;
  const smallRadioBottom = {
    bottom: theme.brand !== 'at' ? '-2px' : '-5px',
  };
  return css`
    width: ${buttonSize};
    height: ${buttonSize};
    ${isRadioSmall && smallRadioBottom};
  `;
};

export const getBorderColor: RadioStyleFn<CSSObject> = props => {
  const { checked, customBackgroundColor } = props;
  const baseColor = getBaseColor('gray60')(props);
  const bgColorBorder = selectValue(!!checked, customBackgroundColor, baseColor);
  return css`
    border: 1px solid ${bgColorBorder};
  `;
};

const getCheckmarkBackground = (crossBrand?: boolean): string => ')';
export const getCheckmark: RadioStyleFn = ({ checked, crossBrand, isRadioSmall }) => {
  const checkmarkBackground = getCheckmarkBackground(crossBrand);
  const checkedStyle = css`
    width: 100%;
    height: 100%;
    background-color: transparent;
    background-position-x: 4px;
    background-position-y: ${isRadioSmall ? '5px' : '7px'};
    background-size: ${isRadioSmall ? '16px' : '27px'};
    background-repeat: no-repeat;
    background-clip: border-box;
    border-top: none;
    border-right: none;
    ${crossBrand &&
    `background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A")
    `}
  `;
  return css`
    &::after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      ${checked && checkedStyle}
    }
  `;
};

export const getBackground: RadioStyleFn = ({ theme, crossBrand, checked, customBackgroundColor, isBackgroundSizeSmall }) => {
  const widthAndHeight = isBackgroundSizeSmall ? 'calc(100% - 10px)' : 'calc(100% + 2px)';
  const topAndLeft = isBackgroundSizeSmall ? '4px' : '-1px';
  const backgroundColor = forBrands(
    theme,
    {
      crossBrand: t => t.crossBrand.color.b1,
      gap: t => t.color.b1,
      gapfs: t => t.color.b1,
      on: t => t.color.b1,
      at: t => t.color.gray80,
      default: t => t.color.bk,
    },
    crossBrand
  );
  const bgColor = selectValue(!!checked, customBackgroundColor, backgroundColor);
  const checkedStyles = css`
    animation: ${backgroundBounce} 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid ${bgColor};
    background-color: ${bgColor};
  `;
  return css`
    &::before {
      content: '';
      position: absolute;
      display: block;
      width: ${widthAndHeight};
      height: ${widthAndHeight};
      border-radius: 100%;
      left: ${topAndLeft};
      top: ${topAndLeft};
      ${checked && checkedStyles}
    }
  `;
};

const RadioButton = styled.div<CommonProps>`
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  ${getButtonSize};
  ${getBorderColor};
  border-radius: 100%;
  ${getCheckmark}
  ${getBackground}
`;

export default RadioButton;
