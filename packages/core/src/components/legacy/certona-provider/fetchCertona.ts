// @ts-nocheck
"use client";
import {Deferred} from "@ecom-next/core/legacy/utility";
import {GlobalCertonaConfig, CertonaResponse} from "./types";

declare global {
  interface Window {
    certonaCache?: Record<string, CertonaResponse>;
  }
}

/**
 * Promise that resolves when tealium loads and sets certona environment.
 */
const waitForCertonaEnvironment = (): Promise<void> =>
  new Promise((resolve) => {
    const DELAY = 250;
    const checkCertonaReady = (): void => {
      if (window.callCertona && window.certona) resolve();
      else setTimeout(checkCertonaReady, DELAY, resolve);
    };

    checkCertonaReady();
  });

function getCachedResponse(pageType: string): CertonaResponse | undefined {
  return window.certonaCache?.[pageType];
}

function setCachedResponse(
  pageType: string,
  certonaResponse: CertonaResponse
): void {
  if (!window.certonaCache) window.certonaCache = {};
  window.certonaCache[pageType] = certonaResponse;
}

const fetchCertona = async (
  globalCertonaConfig: GlobalCertonaConfig
): Promise<CertonaResponse> => {
  const {pagetype} = globalCertonaConfig;

  const cachedResponse = getCachedResponse(pagetype);
  if (cachedResponse) return cachedResponse;

  await waitForCertonaEnvironment();

  /**
   * TODO: window.certona should be cleaned up after the call, but should
   * coordiate with eval first.
   */
  Object.assign(window.certona ?? {}, globalCertonaConfig);

  const certonaDataPromise = new Deferred<CertonaResponse>();

  /**
   * window.callCertona() calls window.certonaRecommendations when certona
   * data returns, which then resolves the certonaDataPromise
   */
  
  // FIXME: Calls resonance version of certonaRecommendations when overridden by core-ui logic - see https://gapinc.atlassian.net/browse/ECOMNEXT-640 
  const _resonanceCertonaRecommendations = window.certonaRecommendations; 
  window.certonaRecommendations = (certonaResponse) => { 
    _resonanceCertonaRecommendations && _resonanceCertonaRecommendations(certonaResponse);
    setCachedResponse(pagetype, certonaResponse);
    return certonaDataPromise.resolve(certonaResponse);
  };

  window.callCertona?.();
  return certonaDataPromise.promise;
};

export default fetchCertona;
