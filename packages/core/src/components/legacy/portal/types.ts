// @ts-nocheck
"use client";
type Selector = keyof HTMLElementTagNameMap | string;

export type PortalProps = {
  /**
   * A node or css-selector. The container will have the portal children appended to it.
   */
  container?: Element | Selector;
  children: JSX.Element | null;
  /**
   * Disable the portal behavior. The children stay within it's parent DOM hierarchy. Will need to disable portal on any components that are SSRed.
   */
  disablePortal?: boolean;
};
