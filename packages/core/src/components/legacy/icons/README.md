# Icons

This package contains the `<Icon>` component, as well as a library of icons, which are their own components.

See the [list of available icons](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/icons/src/icons/index.tsx).

## Default Behavior

Many icons vary by brand. By default, if there is no branded icon version in the package, the cross brand version will be displayed.

The icon components included in this package are decorative additions to user interfaces via a simple, drop-in reference to their component names. Here is an example of calling the `ChevronCircleIcon` icon:

```jsx
import {ChevronCircleIcon} from "@ecom-next/core/legacy/icons";

<ChevronCircleIcon />;
```

**Note:** Some icons have branded expressions, but by default they will import the Gap brand expression. The `AppStateProvider` will automatically pass a brand to the `Icons` component.

Each component renders as an `<svg>` element, wrapped in a `<span>`, and are `display: inline-block` by default.

By default, an icon's color will be determined by the icon requested and its `<path>` `fill` attribute. In the example below, the icon will render with a dark grey color (`#666`):

```html
<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <path
    d="M21.4439629 2.39100222l.1650349.16503493c.5857864.58578644.5857864 1.5355339 0 2.12132034L14.286 12l7.3229978 7.3226425c.5857864.5857864.5857864 1.5355339 0 2.1213204l-.1650349.1650349c-.5857865.5857864-1.535534.5857864-2.1213204 0L12 14.286l-7.32264251 7.3229978c-.58578644.5857864-1.5355339.5857864-2.12132034 0l-.16503493-.1650349c-.58578644-.5857865-.58578644-1.535534 0-2.1213204L9.714 12 2.39100222 4.67735749c-.58578644-.58578644-.58578644-1.5355339 0-2.12132034l.16503493-.16503493c.58578644-.58578644 1.5355339-.58578644 2.12132034 0L12 9.714l7.3226425-7.32299778c.5857864-.58578644 1.5355339-.58578644 2.1213204 0z"
    fill="#666"
    fillRule="nonzero"
  />
</svg>
```

The default size for the `Icons` component is a width and height of `2rem`, unless configured differently by the icon called.

For details on how to change sizes and colors, see the [API](#api) section below for usage of the `fillColor` and `size` props.

## Technical Notes

### Usage tips

The icon components in this package are meant to be used alongside or inside of other components, as a purely visual addition to the user interface. By default, the icons have the `aria-hidden` attribute, and are hidden from assistive technologies such as screen readers.

### Styling

Styling is implemented via the [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md) package in `core-ui`.

- Each icon accepts custom CSS via the `css` prop. More information on the `css` prop is available here: https://emotion.sh/docs/css-prop

- The `css` prop also accepts a function that is called with your theme as an argument allowing to common and customizable values. The following snippet is an example of using a function to style the `background-color` property of the `<Icon>` component's `<span>` (parent) element:

```js
const customIconCSS = (theme) =>
  css`
    background-color: ${theme.color.r1};
  `;
```

- You can also style the icon's SVG via child selectors:

<details>
  <summary>An example of changing the icon's fill color on `:hover`</summary>

```jsx
<CarouselArrowLeft css={{"svg path:hover": {fill: "orange"}}} />
```

</details>

#### Changing an icon's direction with CSS

Some icons in the Icon Library may convey direction based on their default design. Depending on your application's requirements, you may find it necessary to change an icon's direction, and you can achieve this via CSS.

The `ChevronIcon` is a good example of this. By default, the `ChevronIcon` appears to point downward. To change the point's direction, you use the CSS `transform` property with a `rotate()` value:

<details>
  <summary>An example of rotating the `ChevronIcon`</summary>

```jsx
<ChevronIcon css={{transform: "rotate(90deg)"}} />
```

</details>

### Props

To view documentation about the props for `Icons`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/Icons/src/types.ts).

### API

- A `className` prop is available for additional styling or other needs.
  - Example usage: `<CarouselArrowLeft className="my-custom-class" />`
- A `fillColor` prop is available for providing a `fill`, and will inject this value via CSS. This prop will override the SVG's `fill` attribute value. `fillColor` accepts any CSS color value as a string, including:
  - Named colors — `fillColor={"orange"}`
  - Hex colors — `fillColor={"#FF9E2C"}`
  - RGB and RGBa colors — `fillColor={"rgb(255, 158, 44)"}` and `fillColor={"rgba(255, 158, 44, .5)"}`
  - HSL and HSLa colors — `fillColor={"hsl(32, 100%, 59%)"}` and `fillColor={"hsla(32, 100%, 59%, .5)"}`
- A `size` prop is available for providing custom sizes

## Icon Conventions

### Component Convention

Icons should:

- Only be added if they have future use; no legacy or one-time-use icons should be added
- Include branded variant, including cross-brand if it is part of component UI (ie. `XIcon` or `ChevronIcon`)
- **Not** include any extra padding or white space around them
- **Not** include any colors unless the UI specifically calls for it or the icon has multiple colors ie. cross-brand `Notification` icons or credit card logos
- **Not** be a duplicate in color, scale or rotation

### Naming Convention

Icons now follow a naming convention that should make it easier to group and find icons that are related to each other. Here is a breakdown of an icon name:

```
UIGroupDescriptorModifierIcon
```

1. UIGroup
   This should be used **only** if there is one area for the icon to be used ie. `Notification` icons or `ValueDrawer` icons. These icons will always be tied to that specific UI and should not be used in other places.
2. Descriptor
   This should describe what the icon looks like. **Do not** use functions or colors as descriptors like "hover" or "grey". Descriptors should describe what an icon looks like as concisely as possible. For example: using the name "HeartIcon" instead of the name "FavoriteIcon".
3. Modifier
   This should be used to help group icons together under a single descriptor, but with a modification like a circled or bolded version, ie. `ChevronCircleIcon`. A modifier should **never** be about scale, color or rotation.
4. Icon
   All Icons components should end with the "Icon" suffix so that it is clear to the user that they are using an Icon component when they import and place it in their experience.

#### Naming Examples

| BAD               | GOOD        |
| ----------------- | ----------- |
| ClosingXHoverIcon | XIcon       |
| DownIcon          | ChevronIcon |
| StarActiveIcon    | StarIcon    |

#### Responsive Icons

Icons that display different versions depending on the `viewBox` size are classified as "responsive icons." These icons have an internal listener that decides which `svg` it will display based on the given `size`. For instance, the `playCircleIcon` and `playPauseIcon` show different `svg` output if the `size` prop is set above or below `32px`.

#### Color Theme

Only a few icons are affected by the `colorTheme` prop - the ones that are will change to a pre-determined look when `colorTheme` is set to "dark" or "light". If not set, it will default to "dark".
Icons that accept :

- PauseSquareIcon
- PlaySquareIcon
- ChevronSquareIcon
- ProductImageLogoIcon for 'at' brand when 'at-redesign-2024' feature flag is true

## Testing the Component in Storybook

- If you need to see an icon in a light color, we recommend changing the storybook background color to 'Gray' or 'Black' using the controls. When changing the storybook background color to 'Black', you probably want to change the storybook text color to 'White' for visibility.
- Why does the icon appear to be blank?
  - Try changing the Color Theme or fillColor knobs.
  - If no icon is rendered and color is not the issue, this means that there is no icon designed for that brand, or for crossBrand, which would generally be provided by default.

## ProductImageLogoIconProps

- It returns the brand icon!
- `overwriteBrand` is set to true by default, setting to false will default the brand icon based on the brand provided by the `StitchStyleProvider`
- If you need the default brand icon of a brand, don't send any prop.

| Prop           | type                                | description                                                                         |
| -------------- | ----------------------------------- | ----------------------------------------------------------------------------------- |
| brandProp      | "at" "brfs" "br" "gap" "gapfs" "on" | brand icon                                                                          |
| overwriteBrand | boolean                             | set true to receive a overwritten prop brand and false to receive the control brand |
