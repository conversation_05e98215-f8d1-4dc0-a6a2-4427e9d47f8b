'use client';
import React from 'react';
import { useTheme } from '@ecom-next/core/react-stitch';
import { ColorTheme, IconVariant, IconVariantStyles, SVGIconProps } from '../../types';
import { getIconStyle } from '../../helpers';

export const AthletaSVG: React.FC<SVGIconProps> = ({ isSmall = false, colorTheme = ColorTheme.dark, variant = IconVariant.DEFAULT }) => {
  const theme = useTheme();

  const styles: IconVariantStyles = {
    default: {
      dark: {
        fillColor: theme.color.bkAlpha50,
        backgroundColor: theme.color.wh,
      },
      light: {
        fillColor: theme.color.wh,
        backgroundColor: '#2C2824',
      },
    },
  };

  const { backgroundColor, fillColor } = getIconStyle(styles, variant, colorTheme) || {};

  const iconSize = isSmall ? 24 : 48;

  return (
    <svg data-testid='at-pause-circle-icon' xmlns='http://www.w3.org/2000/svg' width={iconSize} height={iconSize} viewBox='0 0 24 24' fill='none'>
      <g clipPath='url(#clip0_15245_19737)'>
        <path d='M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z' fill={fillColor} />
        <path
          d='M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z'
          fill={backgroundColor}
        />
      </g>
      <defs>
        <clipPath id='clip0_15245_19737'>
          <rect width='24' height='24' fill={theme.color.wh} />
        </clipPath>
      </defs>
    </svg>
  );
};
