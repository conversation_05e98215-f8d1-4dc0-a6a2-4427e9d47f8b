// @ts-nocheck
"use client";
import React from "react";
import {<PERSON><PERSON>} from "@ecom-next/core/legacy/utility";
import {Icon} from "../components/Icon";
import {IconContainerProps} from "../types";

const CrossBrandSVG = (
  <svg fill="none" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <path
      clipRule="evenodd"
      d="M27.0933 12.5867L31.36 15.68l-4.3733 3.0933 2.24 4.8L24 24l-.4267 5.2267-4.8-2.24-3.0933 4.2666-3.0933-4.2666-4.80003 2.24L7.36 24l-5.33333-.5333 2.24-4.6934L0 15.68l4.26667-3.0933-2.24-4.80003L7.36 7.36l.42667-5.33333 4.69333 2.24L15.5733 0l3.0934 4.26667 4.8-2.24L24 7.36l5.3333.42667-2.24 4.80003zm-10.24 7.8933l4.6934-4.0533c.32-.2134.4266-.5334.32-.96 0-.2134-.1067-.5334-.32-.7467l-4.8-4.16c-.2134-.2133-.4267-.32-.64-.32-.4267 0-.7467.2133-.96.64-.1067.4267 0 .8533.32 1.1733l2.7733 2.4534H10.3467c-.53337 0-.96003.4266-.96003.96-.10667.5333.32 1.0666.85333 1.0666h8l-2.7733 2.4534c-.32.32-.4267.7466-.32 1.1733.2133.4267.5333.64.96.64h.1066c.32 0 .5334-.2133.64-.32z"
      fill="#000000"
      fillRule="evenodd"
    />
  </svg>
);

const brandIconSVGs = {
  [Brands.Athleta]: CrossBrandSVG,
  [Brands.BananaRepublic]: CrossBrandSVG,
  [Brands.BananaRepublicFactoryStore]: CrossBrandSVG,
  [Brands.Gap]: CrossBrandSVG,
  [Brands.GapFactoryStore]: CrossBrandSVG,
  [Brands.OldNavy]: CrossBrandSVG,
  crossBrand: CrossBrandSVG,
};

export const ValueDrawerArrowBadgeIcon = (
  props: IconContainerProps
): JSX.Element => <Icon {...props} brandIconSVGs={brandIconSVGs} />;
