'use client';
import React from 'react';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { Brands } from '@ecom-next/core/legacy/utility';
import { ResponsiveIcon } from '../../components/Icon';
import { IconContainerProps } from '../../types';
import { AthletaSVG } from './PlayCircleIcon.at';

export const NEW_PLAY_ICON = 'new-play-icon';

const BR2024PlaySVG = (
  <svg data-testid={NEW_PLAY_ICON} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <g clipPath='url(#clip0_1271_34326)'>
      <path d='M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z' fill='FFFFFF' />
      <path
        d='M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z'
        fill='#2C2824'
      />
    </g>
    <defs>
      <clipPath id='clip0_1271_34326'>
        <rect fill='white' height='24' width='24' />
      </clipPath>
    </defs>
  </svg>
);

const CrossBrandLargeViewBoxSVG = (
  <svg height='48' viewBox='0 0 48 48' width='48' xmlns='http://www.w3.org/2000/svg'>
    <path d='M48 24c0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0s24 10.745 24 24z' fill='#F2F2F2' />
    <path d='M16 14.87a1.714 1.714 0 012.527-1.51l16.956 9.13c1.202.648 1.202 2.372 0 3.02l-16.956 9.13A1.714 1.714 0 0116 33.13V14.87z' />
  </svg>
);

const CrossBrandSmallViewBoxSVG = (
  <svg height='32' viewBox='0 0 32 32' width='32' xmlns='http://www.w3.org/2000/svg'>
    <path d='M32 16c0 8.837-7.163 16-16 16S0 24.837 0 16 7.163 0 16 0s16 7.163 16 16z' fill='#F2F2F2' />
    <path d='M11 9.913c0-.864.923-1.416 1.685-1.006l11.303 6.087a1.143 1.143 0 010 2.012l-11.303 6.087A1.143 1.143 0 0111 22.087V9.913z' />
  </svg>
);

export const PlayCircleIcon: React.FC<IconContainerProps> = (props): JSX.Element => {
  const isBRDesign2024 = useEnabledFeatures()['mui-br-redesign-2024'];

  const AthletaIcon = (isSmall?: boolean) => <AthletaSVG colorTheme={props.colorTheme} isSmall={isSmall} variant={props.variant} />;

  const brandIconLargeViewBoxSVGs = {
    [Brands.Athleta]: AthletaIcon(),
    [Brands.BananaRepublic]: isBRDesign2024 ? BR2024PlaySVG : CrossBrandLargeViewBoxSVG,
    [Brands.BananaRepublicFactoryStore]: isBRDesign2024 ? BR2024PlaySVG : CrossBrandLargeViewBoxSVG,
    [Brands.Gap]: CrossBrandLargeViewBoxSVG,
    [Brands.GapFactoryStore]: CrossBrandLargeViewBoxSVG,
    [Brands.OldNavy]: CrossBrandLargeViewBoxSVG,
    crossBrand: CrossBrandLargeViewBoxSVG,
  };

  const brandIconSmallViewBoxSVGs = {
    [Brands.Athleta]: AthletaIcon(true),
    [Brands.BananaRepublic]: isBRDesign2024 ? BR2024PlaySVG : CrossBrandSmallViewBoxSVG,
    [Brands.BananaRepublicFactoryStore]: isBRDesign2024 ? BR2024PlaySVG : CrossBrandSmallViewBoxSVG,
    [Brands.Gap]: CrossBrandSmallViewBoxSVG,
    [Brands.GapFactoryStore]: CrossBrandSmallViewBoxSVG,
    [Brands.OldNavy]: CrossBrandSmallViewBoxSVG,
    crossBrand: CrossBrandSmallViewBoxSVG,
  };

  return <ResponsiveIcon {...props} brandIconLargeViewBoxSVGs={brandIconLargeViewBoxSVGs} brandIconSmallViewBoxSVGs={brandIconSmallViewBoxSVGs} />;
};
