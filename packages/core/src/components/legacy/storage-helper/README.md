# Storage Helper

- What the package do?
  `Storage Helper` provides 2 React hooks to help consumer modify the localStorage content.
  - [`useStorage`](#useStorage)
  - [`useStorageEffect`](#useStorageEffect)

## useStorage<a name="useStorage"></a>

- What is `useStorage`?
  - `useStorage` is a React hook that can generate a set of helper functions. These functions provide a better way to read & write to the localStorage by specific storage key.
- When should you use `useStorage`?
  - Component reads/writes data from/to a specific storage key in localStorage.
  - Component needs to store data in JSON format, and needs data to be type-checked before it is stored.

### Default Behavior

- By default, `useStorage` hook will take `storageKey:string` as the only necessary parameter, and it takes `options` as the second optional parameter for configuration. 
```typescript
  import { useStorage } from '@ecom-next/core/legacy/storage-helper';
  
  // this is the default usage.
  const storage = useStorage('storageKey');
  
  // you can also pass the options parameter if need.
  const options = {
    jsonify: false // this option tell useStorage not parse the data into JSON object.
  };
  const stringStorage = useStorage('storageKey', options);
```
- By default, `options.jsonify` will set to `true`. In this case you can pass a generic type to enable the type check in compile-time.
```typescript
  import { useStorage } from '@ecom-next/core/legacy/storage-helper';
  
  type DemoType = {
    brand: string;
    market: string;
  }
  
  const { setData } = useStorage<DemoType>('demo');
  
  // this works
  setData({ brand: 'gap', market: 'us' });
  
  // this will throw an type error when compiling
  setData({ brand: 'gap' });
```
- Check [API Section](#useStorageAPI) for more detail.


### Technical Notes
`useStorage` uses [@ecom-next/core/legacy/utility/storage-helper](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/utility/src/storage/index.ts) as dependency

### Types

To view documentation about the types for `useStorage`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/storage-helper/src/types.d.ts).

### Cautions
- We do not cache the generated functions in memory. Therefore, if you try to create the function set against same storage key in multiple components, we strongly recommend caching the functions in a high level component.

### API<a name="useStorageAPI"></a>
- #### Functions & Properties<a name="functionIntro"></a>
  #### Storing JSON
  When `options.jsonify` is set to `true`.
  A `StorageJSONAdapterType` type object will be returned: 
    ```typescript
      export declare type StorageJSONAdapterType<
        T extends Record<string, unknown>
      > = {
        key: string;
        setData: (data: T) => void | never;
        getData: () => T | never;
        update: (data: Partial<T>) => void | never;
        set: <P extends keyof T>(key: P, value: T[P]) => void | never;
        get: <P extends keyof T>(key: P) => T[P] | undefined | never;
        clear: () => void | never;
      };
    ```

    - `key`
      -
      - The storage key. All of the following function will just be able to modify the storage content under this key.
    - `set`
      -
      - Function to modify the storage content by specific key/value pair.
        - Key/Value map will be created within an object even if an object does not yet exist in storage.
        - Parameter 'key' cannot be an empty string, since that will never be a valid key to any type. An error will be thrown in this case.
        - Parameter 'value' can not be `undefined`. An error will be thrown in this case also.
        ```typescript
          type DemoType = {
            brand: string;
            market: string;
          };
          
          const { set } = useStorage<DemoType>('demo');
          
          // In this case, { demo: { brand: 'gap' } } still will be written into storage.
          set('brand', 'gap');
          
          // This will lead to a compile error.
          set('', 10);
          
          // This will also lead to a compile error
          set('brand', undefined);
          
          // This is also, since `person` is not a valid key to type `DemoType`.
          set('person', 'James');
        ```
    - `get`
      -
      - Function to get the value of specific key in the storage key namespace.
        - Function will return `undefined` if a key does not exist.
        - Return value will be parsed in to JSON automatically.
        - Parameter 'key' must be a valid key to the generic type.
        - When the value is not able to parse into a valid JSON, an error with message `getData: can not parse stored data into a valid JSON.` will be thrown.
        ```typescript
          /* 
            In localStorage, we have:
            {
              storageKeyOne: {
                brand: "gap",
                market: "us"
              },
              storageKeyTwo: {
              },
              storageKeyThree: {
                brand: "on",
              },
            }
          */
          
          type DemoType = {
            brand: string;
            market: string;
          };
          
          const keyOneStorage = useStorage<DemoType>('storageKeyOne');
          const keyTwoStorage = useStorage<DemoType>('storageKeyTwo');
          
          const brandOne = keyOneStorage.get('brand'); // brandOne = 'gap'
          const brandTwo = keyTwoStorage.get('brand'); // brandTwo = undefined
          
          keyOneStorage.get('person'); // this will lead to a compile error.
        ```

    - `setData`
      -
      - Function to set stored data. Please note that when using `setData`, the argument must be a valid object for generic type `T`.
      ```typescript
        type DemoType = {
          brand: string;
          market: string;
        }
        
        const { setData } = useStorage<DemoType>('demo');
        // this works
        setData({ brand: 'gap', market: 'us' });
    
        // this will throw an type error when compiling
        setData({ brand: 'gap' });
      ```
    - `getData`
      -
      - Function to get the data from localStorage.
        - When the storage key is not existed in the storage, an empty object `{}` will be returned.
        - When the value is not able to parse into a valid JSON, an error with message `getData: can not parse stored data into a valid JSON.` will be thrown.
    - `update`
      -
      - Function to update the parts of the value object in one time.
      ```typescript
        type DemoType = {
          brand: string;
          market: string;
        }
        
        const { setData, update } = useStorage<DemoType>('demo');
        setData({ brand: 'gap', market: 'us' });
    
        // unlike setData, this is safe when compiling
        update({ brand: 'gap' });
        
        // this will throw an type error
        update({ person: 'James' });
      ```

    - `clear`<a name="clearIntro"></a>
      -
      - Function to clear the content of the specific storage key.
      ```typescript
          /* 
            In localStorage, we have:
            {
              storageKeyOne: {
                brand: "gap",
                market: "us"
              },
              storageKeyTwo: {
                brand: "on",
                market: "us"
              }
            }
          */
          
          type DemoType = {
            brand: string;
            market: string;
          };
          
          const { clear } = useStorage<DemoType>('storageKeyOne');
          clear(); // This will only remove the content of 'storageKeyOne'.
          
          /*
            localStorage will look like this after clear get called:
            {
              storageKeyTwo: {
                brand: "on",
                market: "us"
              }
            }
          */
      ```

  #### Storing String
    When `options.jsonify` is set to `false`. 
    A `StorageStringAdapterType` type object will be returned:
    ```typescript
      export declare type StorageStringAdapterType = {
        key: string;
        setData: (data: string) => void | never;
        getData: () => string | never;
        clear: () => void | never;
      };
    ```
    This can be considered as a simplified type version of `StorageJSONAdapterType`. `update`, `set` and `get` function will not be generated since all the data will **NOT** be converted to JSON in this case.
    - `key`
      -
      - The storage key. All of the following function will just be able to modify the storage content under this key.
    - `setData`
      -
      - In this scenario, `setData` will take a string as parameter and write the string data into localStorage
      ```typescript
        // we don't need the generic type here.
        const { setData } = useStorage('demo', { jsonify: false });
        
        setData('abcdefgh'); // { demo: 'abcdefgh' } will be written to localStorage
      ```
    - `getData`
      -
      - In this scenario, `getData` will always return the content of storage in string format
      ```typescript
        /*
          In localStorage, we have:
          {
            demo: {
              brand: "gap",
              market: "us"
            },
          }
        */
        const { getData } = useStorage('demo', { jsonify: false });
        const rawData = getData(); // rawData = "{\"brand\":\"gap\",\"market\":\"us\"}"
      ```
    - `clear`
      -
      - In this scenario, `clear` function will do exactly [same](#clearIntro) job.
   
- #### Options
  - `jsonify`
    -
    - See the [Functions](#functionIntro) section
    - default value is set to `true`.
  - `globalScope`
    - 
    - Use this option to specify the global window object
    - default value is `globalThis.window`
    - This is useful if you want to update the storage data from an iframe or another window.
    ``` typescript
      const x = window.open("about:blank");
      // In this example, we are trying to modify the localStorage in another window created by window.open().
      const storage = useStorage(storageKey, {
        jsonify: false,
        globalScope: x,
      });
      const {setData} = storage;
      setData(storageValue);
      x?.close();
     ```

## useStorageEffect<a name="useStorageEffect"></a>
- What is `useStorageEffect`?

  `useStorageEffect` is a React hook excuting 'side effect' code when storage event triggered. Different with storage event, the effect callback function can be excuted only when spcific key's value changed.

- Why?

  Imagine following use case:
  A customer lands on a category page, and uses BOPIS faceting to select a store(let's call this store S1). The customer opens a product page by opening a new window from the category page.

  Then in the product page window, perhaps he/she uses the change-store-modal to select a new store(S2), then goes back to their previous window containing the category page.

  Please note that in this case, the store on the category page continues to display S1 as the selection. But in localStorage, the selected store has been updated to S2. Therefore, any new PDP that is opened would present product data, such as 'in-stock' status, referring to the S2 store. This would be a confusing experience.

  To resolve this kind of issue, we need to listen to the [`storage`](https://developer.mozilla.org/en-US/docs/Web/API/Window/storage_event) event to make sure all the components update synchronously when the related content of localStorage is modified.
  
### Default Behavior

Here is the signature of `useStorageEffect`:

  ```typescript
    type StorageEffectType = (
      key: string | null,
      prev: string | null,
      current: string | null
    ) => void | never;

    function useStorageEffect (
      effect: StorageEffectType,
      deps: Array<string>,
      globalScope: Window = window,
    )
  ```
  - By Default, only `effect` and `deps` parameter is needed to use `useStorageEffect`.
  - `effect` is the 'side effect' of storage event. The function will received `key`(storage key), `prev`(old storage value) and `current`(new storage value) when the event triggered.
  - `deps` is the storage event effect dependencies, this is a set of storage keys. An effect function will execute when its corresponding value in storage is changed. If the deps array is set to an empty array `[]`, the effect function will execute every time a storage event fires.
  - `globalScope` is used to specify the global window object. Default value of `globalScope` is set to `globalThis.window` which is the window where the call happens. This parameter useful if you want to listen the storage event from an iframe or another window.
  
### Usage

  - Here is an actual code example to show how to use it:

  ```typescript
    useStorageEffect(
      (key, prev, current) => {
        setMessage(
          `Mesage from '${key}', previous value: ${prev}, current value ${current}`
        );
      },
      [storageKey]
    );
  ```

### Cautions
  - According to the [docs](https://developer.mozilla.org/en-US/docs/Web/API/Window/storage_event), the `storage` event of the `Window` interface fires when a storage area (localStorage) has been modified in the context of **another** document. Which means that the storage event will **not** be aware of which document/page modified the localStorage.

### Limitation
  - Note that both `prev` and `current` parameters will be in `string | null` type, so you may need to parse them yourself if you need them in a JSON format.

  
## Testing the Component in Storybook
[Link to Storybook](https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/common-utilities-usestorage--use-storage-effect-hook)
- Please Note that when use knob the modify the storage value:
  - Make sure data is a valid JSON object for `Use Storage JSON` page.
  - Key fields cannot be blank.  If a key is blank, the storage will not be modified and you will receive a console error.
