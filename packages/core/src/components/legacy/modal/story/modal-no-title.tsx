// @ts-nocheck
"use client";
import React from "react";
import {
  forBrands,
  styled,
  useTheme,
  css,
  getFont,
} from "@ecom-next/core/react-stitch";
import {Modal} from "..";
import {ModalProps} from "../types";
import withClose from "./with-close";

const Title = styled.h1(() => {
  const theme = useTheme();
  const styles = forBrands(theme, {
    gap: (theme) => css`
      font-size: 2rem;
      line-height: 1.25;
      color: ${theme.color.b1};
      ${getFont("secondary")({theme})}
    `,
    at: (theme) => css`
      font-size: 1.875rem;
      line-height: 1.25;
      letter-spacing: 1px;
      font-variant-ligatures: none;
      text-transform: uppercase;
      color: ${theme.color.b1};
      ${getFont("secondary")({theme})}
    `,
    on: (theme) => css`
      font-size: 2rem;
      line-height: 1.25;
      letter-spacing: 1px;
      text-transform: uppercase;
      color: ${theme.color.bk};
      ${getFont("secondary")({theme})}
    `,
    br: (theme) => css`
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 1px;
      text-transform: uppercase;
      color: ${theme.color.bk};
      ${getFont("tertiary")({theme})}
    `,
    brfs: (theme) => css`
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 1px;
      text-transform: uppercase;
      color: ${theme.color.bk};
      ${getFont("tertiary")({theme})}
    `,
    gapfs: (theme) => css`
      font-size: 2rem;
      line-height: 1.25;
      color: ${theme.color.b1};
      ${getFont("secondary")({theme})}
    `,
  });

  return css`
    text-align: center;
    ${styles}
  `;
});

const Content = styled.div`
  padding: 1rem;
`;

const NoTitleModal = (props: ModalProps): JSX.Element => (
  <div>
    <Modal {...props}>
      <Title>Custom title might go here</Title>
      <Content>Modal Content</Content>
    </Modal>
  </div>
);

export default withClose(NoTitleModal);
