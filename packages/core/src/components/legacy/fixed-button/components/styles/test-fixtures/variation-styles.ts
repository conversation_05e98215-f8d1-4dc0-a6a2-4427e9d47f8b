// @ts-nocheck
"use client";
import {Variant} from "../../types";

export const gapComposableButtonStyles = (variant: Variant) => {
  let styles;
  switch (variant) {
    case Variant.border:
      styles = {
        "& > span": {
          padding: "1px 0",
        },
        backgroundColor: "#FFFFFF",
        borderColor: "#002554",
        color: "#002554",
        fontSize: "16px",
        fontWeight: 400,
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "45px",
        maxHeight: "auto",
        padding: "12px 20px 11px",
        textAlign: "center",
        width: "auto",
      };
      break;
    case Variant.flat:
      styles = {
        "& > span": {
          padding: "1px 0",
        },
        "& span": {
          "svg path": {
            fill: "#002554",
            transition: "fill 250ms ease-in-out",
          },
        },
        backgroundColor: "transparent",
        border: "none",
        borderRadius: 0,
        color: "#002554",
        fontSize: "16px",
        fontWeight: 400,
        height: "auto",
        justifyContent: "left",
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "44px",
        maxHeight: "auto",
        padding: 0,
        textAlign: "left",
        width: "auto",
      };
      break;
    case Variant.outline:
      styles = {
        "& > span": {
          padding: "1px 0",
        },
        background: "transparent",
        borderColor: "#002554",
        color: "#002554",
        fontSize: "16px",
        fontWeight: 400,
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "45px",
        maxHeight: "auto",
        padding: "12px 20px 11px",
        textAlign: "center",
        width: "auto",
      };
      break;
    case Variant.underline:
      styles = {
        "& > span": {
          padding: "1px 0",
        },
        backgroundColor: "transparent",
        borderBottom: 0,
        borderLeft: "none",
        borderRadius: "0",
        borderRight: "none",
        color: "#002554",
        fontSize: "16px",
        fontWeight: 400,
        height: "auto",
        justifyContent: "left",
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "44px",
        maxHeight: "auto",
        padding: 0,
        textAlign: "left",
        textDecoration: "underline",
        textDecorationThickness: "1px",
        textUnderlineOffset: "0px",
        width: "auto",
      };
      break;
    case Variant.chevron:
      styles = {
        "& > span": {
          "& span": {
            height: "calc(16px * 0.65)",
          },
        },
        "& span": {
          "svg path": {
            fill: "#002554",
            transition: "fill 250ms ease-in-out",
          },
        },
        backgroundColor: "transparent",
        border: "none",
        borderRadius: 0,
        color: "#002554",
        fontSize: "16px",
        fontWeight: 400,
        height: "auto",
        justifyContent: "left",
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "44px",
        maxHeight: "auto",
        padding: 0,
        textAlign: "left",
        width: "auto",
      };
      break;
    case Variant.solid:
    default:
      styles = {
        "& > span": {
          padding: "1px 0",
        },
        backgroundColor: "#002554",
        borderColor: "#002554",
        color: "#FFFFFF",
        fontSize: "16px",
        fontWeight: 400,
        letterSpacing: "normal",
        lineHeight: 1.125,
        minHeight: "45px",
        maxHeight: "auto",
        padding: "12px 20px 11px",
        textAlign: "center",
        width: "auto",
      };
  }
  return styles;
};
