// @ts-nocheck
"use client";
import {Theme} from "@ecom-next/core/react-stitch";
import {
  ComposableButtonInteractiveConfig,
  ComposableButtonBaseProps,
  Variant,
} from "../../types";
import {getActiveButtonColor} from "../../styles/helpers";

const athletaInteractiveStylesConfig = (
  props: Partial<ComposableButtonBaseProps> & {theme: Theme}
): ComposableButtonInteractiveConfig => {
  const {primary, secondary} = getActiveButtonColor(props);

  return {
    hoverFocus: {
      [Variant.border]: {
        "&:hover, &:focus": {
          textDecoration: "underline",
        },
      },
      [Variant.chevron]: {
        "&:hover, &:focus": {
          textDecoration: "underline",
          textUnderlineOffset: "4px",
        },
      },
      [Variant.flat]: {
        "&:hover, &:focus": {
          textDecoration: "underline",
          textUnderlineOffset: "4px",
        },
      },
      [Variant.solid]: {
        "&:hover, &:focus": {
          textDecoration: "underline",
        },
      },
      [Variant.outline]: {
        "&:hover, &:focus": {
          textDecoration: "underline",
        },
      },
      [Variant.underline]: {
        "&:hover, &:focus": {
          textDecoration: "none",
        },
      },
    },
    active: {
      [Variant.border]: {
        "&:active": {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
          textDecoration: "none",
        },
      },
      [Variant.chevron]: {
        "&:active": {
          fontWeight: "bold",
        },
      },
      [Variant.flat]: {
        "&:active": {
          fontWeight: "bold",
        },
      },
      [Variant.solid]: {
        "&:active": {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
          textDecoration: "none",
        },
      },
      [Variant.outline]: {
        "&:active": {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
          textDecoration: "none",
        },
      },
      [Variant.underline]: {
        "&:active": {
          fontWeight: "bold",
          textDecoration: "underline",
        },
      },
    },
  };
};

export default athletaInteractiveStylesConfig;
