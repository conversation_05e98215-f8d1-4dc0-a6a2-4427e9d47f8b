// @ts-nocheck
"use client";
import React from "react";
import {LARGE, SMALL, XLARGE} from "@ecom-next/core/breakpoint-provider";
import {snapshotTests} from "test-utils";
import {render} from "test-utils";
import {GridColumnsOptions} from "../types";
import {BrPlaceholder} from "./BrPlaceholder";

describe("<GridRedesignPlaceholder />", () => {
  snapshotTests(BrPlaceholder);

  it.each`
    design
    ${GridColumnsOptions["1 Across"]}
    ${GridColumnsOptions["2 Across"]}
    ${GridColumnsOptions.GRID_TOGGLE_OFF}
  `("matches the snapshot - XLARGE", ({design}) => {
    const {container} = render(
      <BrPlaceholder gridPlaceholderRedesign={design} />,
      {breakpoint: XLARGE}
    );
    expect(container).toMatchSnapshot();
  });
  it.each`
    design
    ${GridColumnsOptions["1 Across"]}
    ${GridColumnsOptions["2 Across"]}
    ${GridColumnsOptions.GRID_TOGGLE_OFF}
  `("matches the snapshot - LARGE", ({design}) => {
    const {container} = render(
      <BrPlaceholder gridPlaceholderRedesign={design} />,
      {breakpoint: LARGE}
    );
    expect(container).toMatchSnapshot();
  });

  it.each`
    design
    ${GridColumnsOptions["1 Across"]}
    ${GridColumnsOptions["2 Across"]}
    ${GridColumnsOptions.GRID_TOGGLE_OFF}
  `("matches the snapshot - SMALL", ({design}) => {
    const {container} = render(
      <BrPlaceholder gridPlaceholderRedesign={design} />,
      {breakpoint: SMALL}
    );
    expect(container).toMatchSnapshot();
  });
});
