{"sideEffects": false, "name": "@core-ui/product-grid-placeholder", "version": "9.4.0", "description": "Renders placeholder squares of a product grid", "author": "Discover", "main": "./src/index.tsx", "scripts": {"build": "npm run clean && npm run build:js && npm run build:types", "build:js": "rollup -c", "build:types": "tsc --project tsconfig.publish.json", "check-types": "tsc --noEmit", "clean": "rm -rf build", "coverage": "npm test -- --coverage", "lint": "eslint --ext .jsx,.js,.ts,.tsx .", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build && node ../../tasks/copy-files.js", "test": "jest --colors", "test:update": "npm test -- --updateSnapshot"}, "peerDependencies": {"@core-ui/core": ">=0.23.2"}, "devDependencies": {"@core-ui/eslint-config-fui": "0.8.0"}, "migratedToCore": "9.0.0"}