// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<IsolationLayer /> snapshots renders open state correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  left: 0;
  overflow: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: -1;
  -webkit-tap-highlight-color: transparent;
  transition-property: background-color;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  background-color: rgba(0,0,0,0.5);
}

<div>
  <div
    class="emotion-0"
    data-testid="isoLayer"
  />
</div>
`;

exports[`<IsolationLayer /> snapshots renders partialPage state correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  left: 0;
  overflow: auto;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
  -webkit-tap-highlight-color: transparent;
  transition-property: background-color;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  background-color: rgba(0,0,0,0.5);
}

<div>
  <div
    class="emotion-0"
    data-testid="isoLayer"
  />
</div>
`;
