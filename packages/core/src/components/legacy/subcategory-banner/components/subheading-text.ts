// @ts-nocheck
"use client";
import {CSSObject, forBrands, styled, Theme} from "@ecom-next/core/react-stitch";
import {SubheadingThemedProps} from "../types";

const gapGapfsCustomTextStyles = (theme: Theme) =>
  ({
    padding: "0 0.5rem",
    "&::before, &::after": {
      borderTop: `2px solid ${theme.color.g2}`,
      display: "block",
      position: "absolute",
      width: "100em",
      top: "50%",
      content: "''",
    },

    "&::before": {
      right: "100%",
    },
    "&::after": {
      left: "100%",
    },
  } as CSSObject);

const brCustomTextStyles = (
  theme: Theme,
  brTertiaryFontsEnabled: boolean,
  isMobile: boolean,
  isBRRedesignSlice2Enabled: boolean
) => {
  const setFontSize = (): string => {
    if (isBRRedesignSlice2Enabled) {
      return "0.875rem";
    }
    if (isMobile) {
      return "1rem";
    }
    return "1.5rem";
  };
  const textStyle = brTertiaryFontsEnabled
    ? {
        ...theme.font.tertiary,
        letterSpacing: "-0.02em",
      }
    : {
        color: theme.color.inverse.b1,
        lineHeight: isBRRedesignSlice2Enabled ? "1.25rem" : "1.8rem",
        letterSpacing: "0.033rem",
        fontSize: setFontSize(),
        textAlign: isBRRedesignSlice2Enabled ? "left" : "center",
      };

  return {
    display: "block",
    fontWeight: "400",
    ...theme.brandFontAlt,
    ...textStyle,
  };
};

const oldNavyCustomTextStyles = () => ({
  letterSpacing: "0.1em",
});

const customTextStyles = (
  theme: Theme,
  brTertiaryFontsEnabled = false,
  isMobile = false,
  isBRRedesignSlice2Enabled = false
) =>
  forBrands(theme, {
    gap: () => gapGapfsCustomTextStyles(theme) as CSSObject,
    gapfs: () => gapGapfsCustomTextStyles(theme) as CSSObject,
    on: () => oldNavyCustomTextStyles() as CSSObject,
    br: () =>
      brCustomTextStyles(
        theme,
        brTertiaryFontsEnabled,
        isMobile,
        isBRRedesignSlice2Enabled
      ) as CSSObject,
    brfs: () =>
      brCustomTextStyles(
        theme,
        brTertiaryFontsEnabled,
        isMobile,
        isBRRedesignSlice2Enabled
      ) as CSSObject,
    default: () => ({}),
  }) as CSSObject;

export const Subheading = styled.h2(
  ({
    theme,
    brTertiaryFontsEnabled,
    isMobile,
    isBRRedesignSlice2Enabled,
  }: SubheadingThemedProps) => ({
    textTransform: "uppercase",
    letterSpacing: "0.15em",
    display: "inline-block",
    position: "relative",
    ...customTextStyles(
      theme,
      brTertiaryFontsEnabled,
      isMobile,
      isBRRedesignSlice2Enabled
    ),
  })
);
