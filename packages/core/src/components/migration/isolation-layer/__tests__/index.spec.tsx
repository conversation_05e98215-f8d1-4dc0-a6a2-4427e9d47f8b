import React from 'react';
import { render, fireEvent, screen, RenderResult } from 'test-utils';
import { IsolationLayer, IsolationLayerProps } from '../';

const ControlledIsolationLayer = ({ isOpen: isOpenProp = true, ...props }: Partial<IsolationLayerProps>): JSX.Element => {
  const [isOpen, setIsOpen] = React.useState(isOpenProp);
  return <IsolationLayer data-testid='isoLayer' isOpen={isOpen} onClick={() => setIsOpen(false)} {...props} />;
};

function renderIsoLayer<T extends Partial<IsolationLayerProps>>(props?: T): RenderResult {
  return render(<ControlledIsolationLayer {...props} />);
}

describe('<IsolationLayer />', () => {
  describe('isOpen prop', () => {
    test('does not render Layer when false', () => {
      renderIsoLayer({ isOpen: false });
      expect(screen.queryByTestId('absolute')).not.toBeInTheDocument();
    });

    test('renders Layer when true', () => {
      renderIsoLayer({ isOpen: true });
      expect(screen.getByTestId('isoLayer')).toBeInTheDocument();
    });
  });

  describe('user interaction', () => {
    test('calls onClick when backdrop is clicked', () => {
      const mockClickHandler = jest.fn();
      renderIsoLayer({ onClick: mockClickHandler });
      fireEvent.click(screen.getByTestId('isoLayer'));
      expect(mockClickHandler).toHaveBeenCalled();
    });
  });
});
