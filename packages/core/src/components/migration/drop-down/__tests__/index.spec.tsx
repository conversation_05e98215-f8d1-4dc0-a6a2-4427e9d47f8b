import React from 'react';
import { render, screen, fireEvent } from 'test-utils';
import range from 'lodash/range';
import keys from 'lodash/keys';
import entries from 'lodash/entries';
import noop from 'lodash/noop';
import { Dropdown } from '../';
import NoOptionsMessage from '../components/NoOptionsMessage';
import Menu from '../components/Menu';
import Control from '../components/Control';
import Option from '../components/Option';

describe('<Dropdown />', () => {
  const defaultProps = {
    onFocus: noop,
    onBlur: noop,
    onChange: noop,
    onOptionHighlighted: noop,
    reset: noop,
    onMenuOpen: noop,
    onMenuClose: noop,
  };
  describe('opening and closing the dropdown menu', () => {
    [
      {
        desc: 'opens the dropdown menu when control area is clicked',
        props: { defaultMenuIsOpen: false },
        expectation: [false, true],
      },
      {
        desc: 'closes opened dropdown menu when control area is clicked',
        props: { defaultMenuIsOpen: true },
        expectation: [true, false],
      },
      {
        desc: 'if menuIsOpen prop passed, menu should not close when clicked',
        props: { menuIsOpen: true },
        expectation: [true, true],
      },
    ].forEach(({ desc, props, expectation: [before, after] }) =>
      test(desc, () => {
        render(<Dropdown {...defaultProps} {...props} />);
        const control = screen.getAllByRole('button').at(0)!;
        expect(control).toHaveAttribute('aria-expanded', before.toString());
        fireEvent.click(control);
        expect(control).toHaveAttribute('aria-expanded', after.toString());
      })
    );

    describe('menu is controllable by menuIsOpen prop', () => {
      [
        {
          desc: 'when menuIsOpen is not passed in, it should not be expanded',
          props: {},
          expectation: 'false',
        },
        {
          desc: 'when menuIsOpen=false, it should not be expanded',
          props: { menuisOpen: false },
          expectation: 'false',
        },
        {
          desc: 'when menuIsOpen=true, it should be expanded',
          props: { menuIsOpen: true },
          expectation: 'true',
        },
      ].forEach(({ desc, props, expectation }) =>
        test(desc, () => {
          render(<Dropdown {...defaultProps} {...props} />);
          const control = screen.getAllByRole('button').at(0);
          expect(control).toHaveAttribute('aria-expanded', expectation);
        })
      );
    });
  });

  describe('selecting an option on the dropdown menu', () => {
    const baseProps = { menuIsOpen: true, options: range(5) };
    [
      {
        desc: 'should update the value',
        props: {},
        prop: 2,
        expectation: 'true',
      },
      {
        desc: 'should update when defaultValue is provided',
        props: { defaultValue: 3 },
        prop: 2,
        expectation: 'true',
      },
      {
        desc: 'should not update when value is provided',
        props: { value: 0 },
        prop: 2,
        expectation: 'false',
      },
    ].forEach(({ desc, props, prop, expectation }) => {
      test(desc, () => {
        render(<Dropdown {...defaultProps} {...baseProps} {...props} />);
        const option = screen.getByRole('option', { name: `${prop}` });
        fireEvent.click(option);
        expect(option).toHaveAttribute('aria-selected', expectation);
      });
    });

    test('should pick value when defaultValue is also provided', () => {
      render(<Dropdown {...defaultProps} defaultValue={3} value={0} {...baseProps} />);
      const option0 = screen.getByRole('option', { name: '0' });
      const option3 = screen.getByRole('option', { name: '3' });

      expect(option0).toHaveAttribute('aria-selected', 'true');
      expect(option3).toHaveAttribute('aria-selected', 'false');
    });

    test('should invoke the provided callback', () => {
      const props = {
        onChange: jest.fn(),
      };
      render(<Dropdown {...defaultProps} {...baseProps} {...props} />);
      const option = screen.getByRole('option', { name: '2' });
      fireEvent.click(option);
      expect(props.onChange).toHaveBeenCalledWith(2, 2);
    });
  });

  describe('hasError & errorMessage props', () => {
    test('renders error message when hasError and errorMessage props are true', () => {
      const errorMessage = 'This field is required';
      render(<Dropdown {...defaultProps} errorMessage={errorMessage} hasError />);
      const errorText = screen.getByText(errorMessage);
      expect(errorText).toBeInTheDocument();
    });
  });

  describe('components prop', () => {
    interface ComponentData {
      render: React.ReactNode;
      props: {
        menuIsOpen: boolean;
        options?: [number | string];
      };
    }

    const defaultComponents = {
      Control,
      Menu,
      NoOptionsMessage,
      Option,
    };

    const testComps: {
      [key: string]: ComponentData;
    } = keys(defaultComponents).reduce(
      (acc, compName) => ({
        ...acc,
        [compName]: {
          render: () => <div>{compName}</div>,
          props: {
            menuIsOpen: true,
            ...(compName === 'Option' ? { options: range(3) } : {}),
            ...(compName === 'Error' ? { hasError: true, errorMessage: 'Error Message' } : {}),
          },
        },
      }),
      {}
    );

    entries(testComps).forEach(([compName, compData]: [string, ComponentData]) => {
      const componentText = `uses custom ${compName} component`;
      test(componentText, () => {
        const exampleComponents = { [compName]: compData.render };
        render(<Dropdown {...defaultProps} components={exampleComponents} {...compData.props} />);
        const customComps = screen.getAllByText(compName);
        expect(customComps.length).toBeGreaterThanOrEqual(1);
      });
    });
  });

  describe('returns selected option and its index when onChange is called', () => {
    const props = {
      menuIsOpen: true,
      onChange: jest.fn(),
      options: ['Canada', 'France', 'Japan', 'Mexico', 'United Kingdom'],
    };
    test('calls onChange with Japan and index', () => {
      render(<Dropdown {...defaultProps} {...props} />);
      fireEvent.click(screen.getByRole('option', { name: 'Japan' }));
      expect(props.onChange).toHaveBeenCalledWith('Japan', 2);
    });
  });
});
