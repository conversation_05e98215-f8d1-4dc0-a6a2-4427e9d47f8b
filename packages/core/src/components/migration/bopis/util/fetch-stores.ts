import type { Brand } from '@ecom-next/utils/server';
import type { BopisStorageInterfaceType } from '../providers/bopis-storage';
import { type BopisStorageType, type ProductStore, transformStoreList } from '../adapters/store-list';
import type { FetchStoresOptions, LasConfigType } from '../collaborators/bopis-data';
import type { GeolocationType } from './get-geolocation';

type BuildURLParamsType = GeolocationType & {
  application: string;
  brandCode: string;
  domain: string;
  fields: string;
  marketCode: string;
  radius?: number | null;
  route: string;
  size?: number | null;
};

type FetchRemoteStoresParamsType = Omit<BuildURLParamsType, 'brandCode'> & {
  apiKey?: string;
  brandCode: Brand;
};

export const brandCodeMapping = {
  at: 'AT',
  br: 'BR',
  gap: 'GP',
  on: 'ON',
  brfs: 'BRFS',
  gapfs: 'GPFS',
};

const buildUrl = ({ domain, route, latitude, longitude, postalCode, marketCode, brandCode, application, fields, radius, size }: BuildURLParamsType): string => {
  let appParam = '';
  let radiusParam = '';
  let sizeParam = '';
  if (application && application.length > 0) {
    appParam = `&applicationsEnabled=${application}`;
  }

  if (radius && radius > 0) {
    radiusParam = `&radius=${radius}`;
  }

  if (size && size > 0) {
    sizeParam = `&size=${size}`;
  }

  return postalCode
    ? `${domain}${route}?zipCode=${postalCode}&marketCode=${marketCode}&brandCode=${brandCode}${appParam}&includes=${fields}${radiusParam}${sizeParam}`
    : `${domain}${route}?latitude=${latitude}&longitude=${longitude}&marketCode=${marketCode}&brandCode=${brandCode}${appParam}&includes=${fields}${radiusParam}${sizeParam}`;
};

const fetchRemoteStores = async ({
  domain,
  route,
  latitude,
  longitude,
  postalCode,
  marketCode,
  brandCode,
  application,
  fields,
  radius = null,
  size = null,
  apiKey,
}: FetchRemoteStoresParamsType): Promise<ProductStore[]> => {
  if (postalCode || (latitude && longitude)) {
    const url = buildUrl({
      domain,
      route,
      latitude,
      longitude,
      postalCode,
      marketCode,
      brandCode: brandCodeMapping[brandCode],
      application,
      fields,
      radius,
      size,
    });

    const fetchOption = {} as {
      headers?: {
        ApiKey: string;
      };
    };

    if (apiKey) {
      fetchOption.headers = { ApiKey: apiKey };
    }

    const response = await fetch(url, fetchOption);
    const responseJson = await response.json();

    if (responseJson.code === 400) {
      const isZipCodeError = responseJson.message.indexOf('zipCode could not be found') !== -1;
      const message = isZipCodeError ? responseJson.message : 'Invalid URL';
      throw new Error(message);
    }

    return transformStoreList(responseJson.stores);
  }

  return [];
};

export const fetchStores = async (
  storage: BopisStorageInterfaceType,
  postalCode: string,
  latitude: string,
  longitude: string,
  { radius = null, size = null, updateStorage = true }: FetchStoresOptions,
  {
    domain = 'https://api.gap.com',
    route = '/commerce/locations/stores/geo',
    marketCode = 'US',
    brandCode = 'on' as Brand,
    application = 'BOPIS',
    fields = 'timeZone,hours,latitude,longitude,address,phoneNumber,distance',
    apiKey,
  }: LasConfigType,
  forceRemote: boolean
): Promise<Omit<BopisStorageType, 'expiration'>> => {
  let bopis = null;
  let stores;

  if (storage && !forceRemote) {
    bopis = storage.getData() || null;
    if (bopis && !storage.hasExpired() && bopis.enabled) {
      return bopis;
    }
  }

  try {
    stores = await fetchRemoteStores({
      domain,
      route,
      latitude,
      longitude,
      postalCode,
      marketCode,
      brandCode,
      application,
      fields,
      radius,
      size,
      apiKey,
    });
  } catch (e) {
    if (updateStorage) {
      storage.setData({ enabled: storage.get('enabled') || false });
    }
    throw e;
  }

  if (stores && stores.length > 0) {
    bopis = {
      stores,
      selectedStore: stores[0],
      active: false,
      enabled: true,
      postalCode,
      latitude,
      longitude,
    };
  } else {
    bopis = {
      enabled: storage.get('enabled') || false,
      postalCode,
      latitude,
      longitude,
    };
  }
  if (updateStorage) {
    storage.setData(bopis);
  }
  return bopis;
};
