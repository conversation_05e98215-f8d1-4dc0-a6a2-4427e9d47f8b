// @ts-nocheck
'use client';
import { allowedRangeRating, starRatingToWidth, starSet, mapIndexes } from '../utils';

describe('utils', () => {
  describe('allowedRangeRating', () => {
    it('should return 0 if the rating is less than 0', () => {
      let rating = -1;
      expect(allowedRangeRating(rating)).toEqual(0);
      rating = -0.51;
      expect(allowedRangeRating(rating)).toEqual(0);
      rating = -9000;
      expect(allowedRangeRating(rating)).toEqual(0);
    });
    it('should return 5 if the rating is greater than 5', () => {
      let rating = 5.01;
      expect(allowedRangeRating(rating)).toEqual(5);
      rating = 10;
      expect(allowedRangeRating(rating)).toEqual(5);
      rating = 9001;
      expect(allowedRangeRating(rating)).toEqual(5);
    });
    it('should return the rating if it is between 0 and 5 inclusive of both ends', () => {
      let rating = 2.5;
      expect(allowedRangeRating(rating)).toEqual(rating);
      rating = 4.99;
      expect(allowedRangeRating(rating)).toEqual(rating);
      rating = 0.01;
      expect(allowedRangeRating(rating)).toEqual(rating);
      rating = 0;
      expect(allowedRangeRating(rating)).toEqual(rating);
      rating = 5;
      expect(allowedRangeRating(rating)).toEqual(rating);
    });
  });

  describe('starRatingToWidth', () => {
    it('whole number', () => {
      expect(starRatingToWidth(4)).toEqual('80%');
    });

    it('rational number', () => {
      expect(starRatingToWidth(2.5)).toEqual('50%');
    });

    it('should return 0% if number is less than 0', () => {
      expect(starRatingToWidth(-1)).toEqual('0%');
    });

    it('should return 0% if number is 0', () => {
      expect(starRatingToWidth(0)).toEqual('0%');
    });

    it('should return 100% if number is greater than 5', () => {
      expect(starRatingToWidth(5.1)).toEqual('100%');
    });
  });

  describe('starSet', () => {
    it('should return a tuple of [5,0,0] for a rating of 5', () => {
      expect(starSet(5)).toEqual([5, 0, 0]);
    });
    it('should return a tuple of [4,1,0] for a rating of 4.9', () => {
      expect(starSet(4.9)).toEqual([4, 1, 0]);
    });
    it('should return a tuple of [4,0,1] for a rating of 4', () => {
      expect(starSet(4)).toEqual([4, 0, 1]);
    });
    it('should return a tuple of [3,1,1] for a rating of 3.8', () => {
      expect(starSet(3.8)).toEqual([3, 1, 1]);
    });
    it('should return a tuple of [3,0,2] for a rating of 3', () => {
      expect(starSet(3)).toEqual([3, 0, 2]);
    });
    it('should return a tuple of [2,2,1] for a rating of 2.5', () => {
      expect(starSet(2.5)).toEqual([2, 1, 2]);
    });
    it('should return a tuple of [2,0,3] for a rating of 2', () => {
      expect(starSet(2)).toEqual([2, 0, 3]);
    });
    it('should return a tuple of [1,1,3] for a rating of 1.75', () => {
      expect(starSet(1.75)).toEqual([1, 1, 3]);
    });
    it('should return a tuple of [1,0,4] for a rating of 1', () => {
      expect(starSet(1)).toEqual([1, 0, 4]);
    });
    it('should return a tuple of [0,1,4] for a rating of .33', () => {
      expect(starSet(0.33)).toEqual([0, 1, 4]);
    });
    it('should return a tuple of [0,0,5] for a rating of 0', () => {
      expect(starSet(0)).toEqual([0, 0, 5]);
    });
  });

  describe('mapIndexes', () => {
    it('should return an array of [1,2,3] for a num of 3', () => {
      expect(mapIndexes(3)).toEqual([0, 1, 2]);
    });
    it('should return an array of [1] for a num of 1', () => {
      expect(mapIndexes(1)).toEqual([0]);
    });
    it('should return an array of [1,2,3,4,5] for a num of 5', () => {
      expect(mapIndexes(5)).toEqual([0, 1, 2, 3, 4]);
    });
    it('should return an empty array for a num of 0', () => {
      expect(mapIndexes(0)).toEqual([]);
    });
  });
});
