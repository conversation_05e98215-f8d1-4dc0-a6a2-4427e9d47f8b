import React from 'react';
import { render } from 'test-utils';
import { snapshotTests, snapshotsForInverseStyles } from 'test-utils';
import { LoadingPlaceholder } from '../index';

const childEl: JSX.Element = <h1 data-testid='childElement'>Child Element</h1>;

describe('<LoadingPlaceholder />', () => {
  describe('snapshots', () => {
    snapshotTests(LoadingPlaceholder, [
      ['default', {}],
      ['fixed size', { fixedSize: { width: 33, height: 33 } }],
      ['fixed width', { fixedSize: { width: 33 } }],
      ['fixed height', { fixedSize: { height: 33 } }],
      ['custom class name', { className: 'sample' }],
      ['animationDelay', { animationDelay: '0.8s' }],
      ['loading complete', { loadingComplete: true, children: <p>test child content</p> }],
    ]);

    snapshotsForInverseStyles(LoadingPlaceholder, [['default', { invert: true }]]);
  });

  it('should hide children by default', () => {
    const { queryByTestId } = render(<LoadingPlaceholder>{childEl}</LoadingPlaceholder>);
    expect(queryByTestId('childElement')).toBeNull();
  });

  it('should disappear when loadingComplete revealing children', () => {
    const { queryByTestId } = render(<LoadingPlaceholder loadingComplete>{childEl}</LoadingPlaceholder>);
    expect(queryByTestId('childElement')).toBeInTheDocument();
  });
});
