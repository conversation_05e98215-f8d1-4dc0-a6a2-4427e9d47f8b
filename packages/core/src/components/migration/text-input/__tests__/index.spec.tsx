import React from 'react';
import { render, RenderResult, screen, fireEvent, getByTestId } from 'test-utils';
import TextInput from '../';
import { TextInputProps } from '../types';

const label = 'Text Input Label';
const baseProps = {
  label,
};

function renderTextInput(props?: TextInputProps): RenderResult {
  return render(<TextInput {...baseProps} {...props} />);
}

function getInput(): HTMLElement {
  return screen.getByLabelText(new RegExp(label));
}

function inputText(value: string): void {
  fireEvent.change(getInput(), { target: { value } });
}

function getTextArea(): HTMLElement {
  return screen.getByRole('textbox');
}

function queryAlertMessage(): HTMLElement | null {
  return screen.queryByRole('alert');
}

describe('<TextInput />', () => {
  describe('default text input', () => {
    let textInput: RenderResult;

    beforeEach(() => {
      textInput = renderTextInput();
    });

    it('renders with a label', () => {
      expect(getInput()).toBeInTheDocument();
    });

    it('is not disabled', () => {
      expect(getTextArea()).not.toHaveAttribute('disabled');
    });

    it('has no error', () => {
      expect(queryAlertMessage()).not.toBeInTheDocument();
    });

    it('is a required field', () => {
      expect(getTextArea()).toHaveAttribute('required');
    });

    it('has no help message', () => {
      expect(getTextArea()).not.toHaveAttribute('supportingTxt');
    });

    it('has an empty value', () => {
      expect(getTextArea()).toHaveAttribute('value', '');
    });
  });

  describe('disabled text input', () => {
    it('renders a disabled input', () => {
      renderTextInput({
        disabled: true,
      });
      expect(getTextArea()).toHaveAttribute('disabled');
    });
  });

  describe('text input with error', () => {
    it('passes error state to Input component', () => {
      renderTextInput({
        hasError: true,
        errorMessage: 'error',
      });
      expect(queryAlertMessage()).toBeInTheDocument();
    });

    it('shows no error message if no error text present', () => {
      renderTextInput({
        hasError: true,
      });
      expect(queryAlertMessage()).not.toBeInTheDocument();
    });

    it('shows an error message if error text present', () => {
      const errorMessage = 'Please provide valid text label using only characters a-z.';
      renderTextInput({
        hasError: true,
        errorMessage,
      });
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('has a screenreader error attribute if there was an input error', () => {
      renderTextInput({
        hasError: true,
        errorMessage: 'error',
      });
      expect(getTextArea()).toHaveAttribute('aria-invalid', 'true');
    });

    it('does not have a screenreader error attribute if there is no input error', () => {
      renderTextInput();
      expect(getTextArea()).toHaveAttribute('aria-invalid', 'false');
    });

    it("should have the role type 'alert'", () => {
      renderTextInput({
        hasError: true,
        errorMessage: 'An error',
      });
      expect(queryAlertMessage()).toBeInTheDocument();
    });
  });

  describe('text input with optional text', () => {
    it("shows no optionalText if 'required' prop is true", () => {
      renderTextInput({
        required: true,
      });
      const optionalText = screen.queryByText('Optional');
      expect(optionalText).toBeNull();
    });
  });

  describe('when required prop is false', () => {
    it("shows default optionalText if 'required' prop is false", () => {
      const textInput = renderTextInput({
        required: false,
      });
      expect(getTextArea()).toBeInTheDocument();
      expect(textInput.getByText('Optional')).toBeInTheDocument();
    });

    it('shows custom optionalText if provided', () => {
      const props = {
        required: false,
        optionalText: "I'm optional",
      };
      const textInput = renderTextInput(props);
      expect(getTextArea()).toBeInTheDocument();
      expect(textInput.getByText(props.optionalText)).toBeInTheDocument();
    });
  });

  it('can be a required input', () => {
    renderTextInput({
      required: true,
    });
    expect(getTextArea()).toHaveAttribute('required');
  });

  it('can be a non-required input', () => {
    renderTextInput({
      required: false,
    });
    expect(getTextArea()).not.toHaveAttribute('required');
  });

  describe('text input with hint text', () => {
    const hintText = 'e.g. Banana';
    const optionalText = 'e.g. Mango';
    it('shows custom text if hint text provided', () => {
      renderTextInput({
        required: true,
        hintText,
      });
      expect(getTextArea()).toBeInTheDocument();
      expect(screen.getByText(hintText)).toBeInTheDocument();
    });

    it('shows no hint text if none is provided', () => {
      renderTextInput({
        required: true,
      });
      expect(getTextArea()).not.toHaveAttribute('hintText');
    });

    it('shows only hint text if hint text and optional text are provided', () => {
      renderTextInput({
        required: false,
        hintText,
        optionalText,
      });
      expect(getTextArea()).toBeInTheDocument();
      expect(screen.getByText(hintText)).toBeInTheDocument();
      expect(screen.queryByText(optionalText)).not.toBeInTheDocument();
    });
  });

  describe('text input with help text', () => {
    const supportingTxt = "This is helper text to give more context about the input or why it's required.";

    it('shows helper notes under the input field', () => {
      renderTextInput({ supportingTxt });
      expect(screen.getByText(supportingTxt)).toBeInTheDocument();
    });

    it('shows an error message and helper notes under the input at the same time', () => {
      const errorMessage = 'Please provide valid text label using only characters a-z.';
      renderTextInput({
        hasError: true,
        errorMessage,
        supportingTxt,
      });
      expect(screen.getByText(supportingTxt)).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('can render the text input as readonly', () => {
      renderTextInput({ readOnly: true });
      expect(getTextArea()).toHaveAttribute('readonly');
    });
  });

  it('passes value to the Input subcomponent', () => {
    const value = 'Banana';
    renderTextInput({ value });
    expect(getTextArea()).toHaveAttribute('value', value);
  });

  it('renders type attribute correctly', () => {
    const type = 'email';
    renderTextInput({ type });
    expect(getTextArea()).toHaveAttribute('type', type);
  });

  it('passes readonly prop to the Input subcomponent', () => {
    renderTextInput({ readOnly: true });
    expect(getTextArea()).toHaveAttribute('readOnly');
  });

  describe('aria-label prop', () => {
    it('is passed to label', () => {
      const ariaLabel = 'label';
      renderTextInput({ 'aria-label': ariaLabel });
      expect(screen.getByLabelText('label')).toBeInTheDocument();
    });
  });

  describe('state management', () => {
    describe('value', () => {
      describe('uncontrolled', () => {
        it('sets defaultValue prop as value', () => {
          const defaultValue = 'User sees this value on first load';
          renderTextInput({ defaultValue });
          expect(getInput()).toHaveValue(defaultValue);
        });

        it('updates value when typing into the field', () => {
          renderTextInput();
          const valueInput = 'Hello';
          inputText(valueInput);
          expect(getInput()).toHaveValue(valueInput);
        });
      });

      describe('controlled', () => {
        const value = 'Hello';
        it('sets value prop as value', () => {
          renderTextInput({ value });
          expect(getInput()).toHaveValue(value);
        });

        it('does not update value when typing into the field', () => {
          renderTextInput({ value });
          inputText(`${value} World`);
          expect(getInput()).toHaveValue(value);
        });

        it('calls onChange prop with new value', () => {
          const newValue = `${value} World`;
          const mockOnChange = jest.fn(event => {
            event.persist();
            expect(event.target.value).toEqual(newValue);
          });
          renderTextInput({ value, onChange: mockOnChange });
          inputText(newValue);
        });
      });
    });

    describe('hasError', () => {
      describe('uncontrolled', () => {
        it('uses defaultHasError prop to trigger initial error state', () => {
          const defaultHasError = true;
          renderTextInput({
            defaultHasError,
            errorMessage: 'message',
          });
          expect(screen.getByText('message')).toBeInTheDocument();
        });

        describe('handling invalid input', () => {
          const errorMessage = 'Invalid input! Do better. Try again.';
          const telephoneRegex = '^(\\(\\d{3}\\)|\\d{3})-?\\d{3}-?\\d{4}$';
          it('shows no error by default', () => {
            renderTextInput({
              errorMessage,
              pattern: telephoneRegex,
            });
            inputText('invalidnumber');

            expect(screen.queryByRole('alert')).not.toBeInTheDocument();
          });

          it('shows invalid input when text-input looses focus', () => {
            renderTextInput({ errorMessage, pattern: telephoneRegex });
            inputText('invalidnumber');
            fireEvent.blur(getInput());
            expect(screen.getByRole('alert')).toHaveTextContent(errorMessage);
          });

          it("shows invalid input when typing bad input in text-input, after it's lost focus at some point", () => {
            renderTextInput({ errorMessage, pattern: telephoneRegex });
            inputText('invalidnumber');

            fireEvent.blur(getInput());
            fireEvent.focus(getInput());
            inputText('anotherbadnumber');
            expect(screen.getByRole('alert')).toHaveTextContent(errorMessage);
          });
        });
      });

      describe('controlled', () => {
        it('uses hasError prop to trigger error state', () => {
          const hasError = true;
          renderTextInput({
            hasError,
            errorMessage: 'message',
          });
          expect(screen.getByText('message')).toBeInTheDocument();
        });
      });
    });
  });
  describe('fullStory fsTracking prop', () => {
    it('fsTracking is not passed as prop then it should add fs-exclude class', () => {
      const { container } = renderTextInput();
      const textInput = container.querySelector('input');
      expect(textInput).toHaveClass('fs-exclude');
    });
    it('fsTracking prop as mask then it should add fs-mask class', () => {
      const fsTracking = 'mask';
      const { container } = renderTextInput({ fsTracking: fsTracking });
      const textInput = container.querySelector('input');
      expect(textInput).toHaveClass('fs-mask');
    });
    it('fsTracking prop as none then it should not add fs-exclude class', () => {
      const fsTracking = 'none';
      const { container } = renderTextInput({ fsTracking: fsTracking });
      const textInput = container.querySelector('input');
      expect(textInput).not.toHaveClass('fs-exclude');
    });
    it('fsTracking prop as exclude it should add fs-exclude class', () => {
      const fsTracking = 'exclude';
      const { container } = renderTextInput({ fsTracking: fsTracking });
      const textInput = container.querySelector('input');
      expect(textInput).toHaveClass('fs-exclude');
    });
  });
});
