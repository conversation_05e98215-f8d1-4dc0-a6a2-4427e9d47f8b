import { FocusEvent<PERSON><PERSON><PERSON>, ChangeEventHand<PERSON>, ComponentPropsWithoutRef, FC } from 'react';

export type Value = string;

export interface IconProps {
  icon: FC | undefined;
  iconType: 'left' | 'right';
}

export type TextInputExternalStyles = {
  externalInputStyles: string;
  externalLabelStyles: string;
};

export type TextInputProps = {
  /**
   * In cases where the visual
   label is not specific or instructive enough,
   * this text can provide additional and alternative context to screenreader
   * users. Screen readers read this text instead of the label text.
   */
  'aria-label'?: string;
  /**
   * A class that is appended to the `label` element wrapping the entire block.
   * Can be used to add styling or target a specific input.
   */
  className?: string;
  /**
   * If `true`, use crossBrand style.
   */
  crossBrand?: boolean;
  /**
   * Determines if text input starts in error state.
   */
  defaultHasError?: boolean;
  /**
   * The initial value of the text input field.
   */
  defaultValue?: Value;
  /**
   * A string that contains the error message for an invalid input.
   */
  errorMessage?: string;
  /**
   * Fullstory class that is appended to the `input` element.
   * This class can be used to hide user sensitive, personal and confidential data in fullstory view.
   * mask for fs-mask, exclude for fs-exclude and none for no class name
   */
  fsTracking?: 'mask' | 'exclude' | 'none';
  /**
   * If `true`, and `errorMessage` exists, will render in an "error" state
   */
  hasError?: boolean;
  /**
   * Hint text can provide extra contextual help. Overrides any optionalText.
   */
  hintText?: string;
  /**
   * When `true`, renders label text in expanded state. Useful for type="date">
   */
  isExpanded?: boolean;
  /**
   * The input's label.
   */
  label?: string;
  // icon: FC | undefined;
  leftIcon?: FC | undefined;
  /**
   * Callback fired when input looses focus.
   */
  onBlur?: FocusEventHandler<HTMLInputElement>;
  /**
   * Callback fired when the value is changed.
   */
  onChange?: ChangeEventHandler<HTMLInputElement>;
  /**
   * Callback fired when input receives focus.
   */
  onFocus?: FocusEventHandler<HTMLInputElement>;
  /**
   * Optional text appears in the hint area when a field is not required, and no hintText has been provided.
   */
  optionalText?: string;
  /**
   * The regex string used to validate the input `value` string.
   */
  pattern?: string;
  /**
   * If `true`, the input will render with a readOnly state.
   */
  readOnly?: boolean;
  /**
   * Adding ref tag to track the input element.
   */
  ref?: React.Ref<HTMLInputElement>;
  /**
   * If `true`, the input will be a required field.
   */
  required?: boolean;
  rightIcon?: FC | undefined;
  /**
   * Optional styles from parent components
   */
  styles?: ({ isExpanded }: { isExpanded?: boolean | undefined }) => TextInputExternalStyles;
  /**
   * A string that gives additional context for an input field, rendering below the input.
   */
  supportingTxt?: string;

  /**
   * A string that sets the type of input --
   * see available types at https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types
   */
  type?: string;

  /**
   * The value of the input field.
   */
  value?: Value;
} & Omit<ComponentPropsWithoutRef<'input'>, 'value' | 'defaultValue'>;

export type CommonProps = Pick<TextInputProps, 'readOnly' | 'value'>;
