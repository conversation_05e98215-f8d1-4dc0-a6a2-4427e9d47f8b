import styled from '@emotion/styled';
import { Font } from '../theme/types';
import { getFont } from '../getFont';
import { FormProps, FormStyleFn, getBaseColor } from './utils';
import Note from './Note';

const fontStyle: FormStyleFn<Font> = props => getFont(props.inverse ? 'secondary' : 'primary')(props);

const Error = styled(Note)<FormProps>`
  color: ${getBaseColor('err1')};
  ${fontStyle};
`;

export default Error;
