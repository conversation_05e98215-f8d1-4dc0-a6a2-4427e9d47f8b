import { render, screen } from '../../../../../test-utils';
import { styled } from '../index';
import { StitchInverseStyleProps, StitchInverseStyleProvider, useInverseStyleContext } from '../StitchInverseStyleProvider';

describe('stitch invert style provider', () => {
  const testString = 'Hello';
  const StyledTestComponent = styled.div<Pick<StitchInverseStyleProps, 'invert'>>`
    background: ${({ invert }) => (invert ? 'black' : 'white')};
  `;

  const TestComponent = (): JSX.Element => {
    const { invert } = useInverseStyleContext();

    return <StyledTestComponent invert={invert}>{testString}</StyledTestComponent>;
  };

  it('sends invert context', () => {
    render(
      <StitchInverseStyleProvider>
        <TestComponent />
      </StitchInverseStyleProvider>
    );
    expect(screen.getByText(testString)).toHaveStyleRule('background', 'black');
  });

  it('uninverts nested component', () => {
    render(
      <StitchInverseStyleProvider>
        <StitchInverseStyleProvider invert={false}>
          <TestComponent />
        </StitchInverseStyleProvider>
      </StitchInverseStyleProvider>
    );
    expect(screen.getByText(testString)).toHaveStyleRule('background', 'white');
  });
});
