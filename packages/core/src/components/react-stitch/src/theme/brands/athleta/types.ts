import Brands, { InverseColors, ThemeConfig, ValueDrawer } from '../../types';
import { BaseTheme } from '../../base/types';

/**
 * CSS for Athleta.
 */
export type AthletaConfig = {
  color: {
    b1: string;
    b2: string;
    b3: string;
    g1: string;
    g2: string;
    g3: string;
    g4: string;
    g5: string;
    g6: string;
    bk: string;
    wh: string;
    r1: string;
    err1: string;
    inf: string;
    alpha00: string;
    valueDrawer: ValueDrawer;
    inverse: InverseColors;
  };
};

/**
 * Shape of theme object for Athleta.
 */
export type AthletaTheme = ThemeConfig<Brands.Athleta> & BaseTheme & AthletaConfig;
