import { NextResponse, NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import type { AddToBag } from './types';

const getHeaders = (req: NextRequest) => {
  const cookieStore = cookies();
  const { searchParams } = req.nextUrl;

  const JSESSIONID = cookieStore.get('JSESSIONID');
  const unknownShopperId = cookieStore.get('unknownShopperId');
  const cam = cookieStore.get('cam');
  const ktn = cookieStore.get('ktn');

  const brand = searchParams.get('brand') || 'br';
  return {
    'content-type': 'application/json',
    accept: 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9,es;q=0.8,te;q=0.7,hi;q=0.6,zh-CN;q=0.5,zh;q=0.4',
    brand: brand.toUpperCase(),
    brandtype: 'specialty',
    channel: 'WEB',
    clientrequestid: '558f2e48-1f28-4970-bce0-3393bbcf373c',
    Cookie: `JSESSIONID=${JSESSIONID?.value || ''}; unknownShopperId=${
      unknownShopperId?.value || ''
    };locale=en_US|||;${cam?.value ? `cam=${cam?.value};` : ''}${ktn?.value ? `ktn=${ktn?.value};` : ''};`,
    guest: 'true',
    locale: 'en_US',
    market: 'US',
    referer: 'https://secure-www.gap.com/shopping-bag',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
  };
};

export async function GET(req: NextRequest) {
  const headers = getHeaders(req);

  const data = await fetch('https://secure-www.gap.com/shopping-bag-xapi/get-bag', {
    headers: { ...headers },
  }).then(resp => resp.json());

  return NextResponse.json(data);
}

interface RequestInitNode18 extends RequestInit {
  duplex: 'half';
}

export async function POST(req: NextRequest) {
  const data = await req.json();
  const headers = getHeaders(req);

  const addToBagData = await fetch('https://api.gap.com/commerce/shopping-bags/items/summary?locale=en_US', {
    method: 'POST',
    body: JSON.stringify(data),
    duplex: 'half',
    headers: {
      ...headers,
      ...{
        referer: 'https://www.gap.com/',
        origin: 'https://www.gap.com',
      },
      'X-Gap-Apimode': 'leapfrog',
    },
  } as RequestInitNode18).then(resp => resp.json() as Promise<AddToBag>);

  return NextResponse.json(addToBagData);
}

export type BagData = ReturnType<typeof GET> extends Promise<NextResponse<infer U>> ? U : never;
