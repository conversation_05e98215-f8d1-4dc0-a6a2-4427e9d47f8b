/* eslint-disable no-console */
const glob = require('fast-glob');
const Netstorage = require('netstorageapi');

const cpCode = '739706';

console.log(process.env.AKAMAI_HTTP_API_KEY);
console.log(process.env.AKAMAI_KEY_NAME);

const config = {
  hostname: 'gapinc-nsu.akamaihd.net',
  keyName: process.env.AKAMAI_KEY_NAME,
  key: process.env.AKAMAI_HTTP_API_KEY,
  cpCode,
  ssl: false,
  // proxy: 'https://yourproxyurl.com:port' // Optional
};

const ns = new Netstorage(config);
const baseNetstorageDestination = `/${cpCode}/static_content/onesitecategory/components`;

console.log('Building filesMap');
const filesMap = glob
  .sync(['./static/**/*', '../public/**/*'])
  .map(file => {
    const parts = file.split('/');
    const filename = parts.pop();
    return [parts.join('/'), filename];
  })
  .reduce((acc, entry) => {
    const [folder, filename] = entry;
    let destFolder;
    if (folder.match(/^\.\.\/public/)) {
      destFolder = `mfe/${folder.replace('../public/', '').replace('./', '')}`;
    } else {
      destFolder = `mfe/_next/${folder.replace('./', '')}`;
    }

    if (!acc[destFolder]) {
      acc[destFolder] = [];
    }

    acc[destFolder].push(`${folder}/${filename}`);
    return acc;
  }, {});

console.log(filesMap);

console.log('Completed filesMap and building cmds');

Object.entries(filesMap).forEach(entry => {
  const [destFolder, localFiles] = entry;
  let sanitizedDestFolder = destFolder.replace(/^\//, '').replace(/\/$/, '');
  localFiles.forEach(localFile => {
    const destinationPath = `${baseNetstorageDestination}/${sanitizedDestFolder}/`;
    // console.log(destinationPath);
    const maxRetries = 3;
    let attempts = 0;

    const uploadWithRetry = () => {
      ns.upload(localFile, destinationPath, (error, response, body) => {
        if (error) {
          if (attempts < maxRetries) {
            setTimeout(() => {
              attempts++;
              console.log(`Retrying upload for ${localFile} to ${destinationPath}. Attempt ${attempts}`);
              uploadWithRetry();
            }, 3000);
          } else {
            console.error(`Failed to upload ${localFile} to ${destinationPath} after ${maxRetries} attempts. Error: ${error.message}`);
            process.exit(1);
          }
        } else if (response.statusCode === 200) {
          console.log(`${localFile} has been uploaded to ${destinationPath} with body: ${JSON.stringify(body)}`);
        } else {
          if (attempts < maxRetries) {
            setTimeout(() => {
              attempts++;
              console.log(`Retrying upload for ${localFile} to ${destinationPath}. Attempt ${attempts}`);
              uploadWithRetry();
            }, 3000);
          } else {
            console.error(
              `Failed to upload ${localFile} to ${destinationPath} after ${maxRetries} attempts. Response code: ${response.statusCode}, Response: ${JSON.stringify(body)}`
            );
            process.exit(1);
          }
        }
      });
    };

    uploadWithRetry();
  });
});
console.log('Completed executing cmd to upload to netstorage');
