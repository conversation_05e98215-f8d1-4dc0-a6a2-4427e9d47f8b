'use client';

import React, { ReactNode, Fragment, useState, useEffect } from 'react';
import Marketing from '@ecom-next/marketing-ui';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useProductRecommendations } from '@ecom-next/sitewide/product-recs-provider';
import { useActiveBagContext } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { Feature, logNewRelicError, useShoppingBagFeatures } from '@ecom-next/shopping-bag/utils';
import { brandCIDMap, getBrandCID } from '.';

type AiRecArgs = {
  anchor_ids: string;
  anchor_type: 'sku';
  page_type: 'CART';
};

function errorFallback(): ReactNode {
  logNewRelicError(new Error('Error loading Product Recommendations'), {
    caller: 'ProductRecommendations',
    feature: Feature.PRODUCT_RECOMMENDATION,
    message: 'Failed to load Product Recommendations',
  });
  return <Fragment />;
}

export function ProductRecommendations({ onRender = () => {}, onError = () => {} }: { onError?: (error?: Error) => void; onRender?: () => void }): ReactNode {
  const [hasError, setHasError] = useState(false);

  const handleError = (error: Error): ReactNode => {
    setHasError(true);
    onError(error);
    return errorFallback();
  };

  const {
    bagState: { data },
  } = useActiveBagContext();
  const aiRecArgs: AiRecArgs = {
    anchor_ids: data?.productList?.map(item => item.sku).join(',') || '',
    anchor_type: 'sku',
    page_type: 'CART',
  };

  const marketingProps = {
    onRender,
    onError: handleError,
  };

  const { market, locale, brand } = usePageContext();
  const cid = getBrandCID(brandCIDMap, brand) || 'error';
  const slot = `${cid}/productrecommendations` as const;
  const { getAiRecommendations } = useProductRecommendations();
  const { isAiRecsEnabled } = useShoppingBagFeatures();
  useEffect(() => {
    const setAiRecs = async () => {
      await getAiRecommendations(aiRecArgs);
    };
    if (isAiRecsEnabled) {
      setAiRecs();
    }
  }, [aiRecArgs.anchor_ids]);

  if (hasError) {
    return errorFallback();
  }

  return (
    <Marketing
      brand={brand}
      market={market}
      locale={locale}
      cid={cid}
      pageType='shoppingbag'
      slot={slot}
      errorCallback={handleError}
      defaultMarketing={<Fragment />}
      {...marketingProps}
    />
  );
}
