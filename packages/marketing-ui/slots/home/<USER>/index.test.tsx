import '@testing-library/jest-dom';
import { render, CmsMarketing, JsonMarketing, screen } from 'test-utils';
import { CmsMetaProps } from '@mui/components/legacy-mui-entry';
import { JsonProps } from '@mui/components/json-marketing';
import { MarketingContent } from '@mui/fetchMarketing';
import { DynamicMarketing } from '@mui/components/json-marketing.client';
import Marketing from '@mui/index';
import { Home, aiRecArgs } from '../index';
import contentData from './fixtures/homeData';

const getAiRecommendations = jest.fn();
jest.mock('@ecom-next/sitewide/product-recs-provider', () => ({
  useProductRecommendations: () => ({ getAiRecommendations }),
}));

const SWF_HOME_PAGE_AI_RECS = 'swf-home-page-ai-recs';
const SWE_HOME_PAGE_AI_RECS = 'xb237';

interface FixedHeightContent {
  data?: {
    fixedHeight?: {
      desktop: string;
      mobile: string;
    };
  };
}

describe('Home Marketing', () => {
  (DynamicMarketing as jest.Mock).mockRestore();
  (CmsMarketing as jest.Mock).mockImplementation((props: CmsMetaProps<FixedHeightContent>) => {
    const { _meta } = props;
    return (
      <>
        <h1>CMS Marketing content: {_meta.schema}</h1>
        <h2>{_meta.name}</h2>
      </>
    );
  });
  (JsonMarketing as jest.Mock).mockImplementation((props: JsonProps) => {
    const { instanceName, name } = props;
    return (
      <>
        <h1>Json Marketing content</h1>
        <h2>{instanceName || name}</h2>
      </>
    );
  });

  const windowWidth = window.innerWidth;
  afterEach(() => {
    window.innerWidth = windowWidth;
  });

  it('should render home marketing in mobile breakpoint', async () => {
    window.innerWidth = 456;
    render(
      <>
        <Marketing
          brand={'gap'}
          cid={'home'}
          locale={'en_US'}
          pageType='home'
          slot='sitewide/edfssmall'
          market={'us'}
          defaultMarketing={<h1>Default Edfs msg</h1>}
        />
        <Home />
      </>,
      {
        customRenderProps: {
          content: contentData as MarketingContent<'home'>,
          breakpoint: 'small',
        },
      }
    );

    const homeJsonContent = await screen.findByText('HP Promo 1');
    expect(homeJsonContent).toBeInTheDocument();
    const homeJsonContentContainerStyle = homeJsonContent.parentElement?.style;
    expect(homeJsonContentContainerStyle?.getPropertyValue('--content-min-height')).toEqual('calc(98vw*(100/425))');

    const homeCmsContent = await screen.findByText('Spotlight Variable Height- Harini');
    expect(homeCmsContent).toBeInTheDocument();
    const homeCmsContainerStyle = homeCmsContent.parentElement?.style;
    expect(homeCmsContainerStyle?.getPropertyValue('--content-min-height')).toEqual('1234px');

    const edfsMsg = await screen.findByText('LayoutComponent');
    expect(edfsMsg).toBeInTheDocument();
    const edfsContainerStyle = edfsMsg.parentElement?.style;

    expect(edfsContainerStyle?.getPropertyValue('--content-min-height')).toEqual('40px');
  });

  it('should render home marketing in desktop breakpoint', async () => {
    render(
      <>
        <Marketing
          brand={'gap'}
          cid={'home'}
          locale={'en_US'}
          pageType='home'
          slot='sitewide/edfssmall'
          market={'us'}
          defaultMarketing={<h1>Default Edfs msg</h1>}
        />
        <Home />
      </>,
      {
        customRenderProps: {
          content: contentData as MarketingContent<'home'>,
        },
      }
    );

    const homeJsonContent = await screen.findByText('HP Promo 1');
    expect(homeJsonContent).toBeInTheDocument();

    screen.debug();
    const homeJsonContentContainerStyle = homeJsonContent.parentElement?.style;
    expect(homeJsonContentContainerStyle?.getPropertyValue('--content-min-height')).toEqual('calc(98vw*(165/1440))');

    const homeCmsContent = await screen.findByText('Spotlight Variable Height- Harini');
    expect(homeCmsContent).toBeInTheDocument();
    const homeCmsContainerStyle = homeCmsContent.parentElement?.style;
    expect(homeCmsContainerStyle?.getPropertyValue('--content-min-height')).toEqual('1111px');

    const edfsMsg = await screen.findByText('LayoutComponent');
    expect(edfsMsg).toBeInTheDocument();
    const edfsContainerStyle = edfsMsg.parentElement?.style;

    expect(edfsContainerStyle?.getPropertyValue('--content-min-height')).toEqual('');
  });

  it('should call getAiRecommendations if SWF_HOME_PAGE_AI_RECS is true AND SWE_HOME_PAGE_AI_RECS is active', () => {
    render(<Home />, { appState: { enabledFeatures: { [SWF_HOME_PAGE_AI_RECS]: true }, abSeg: { [SWE_HOME_PAGE_AI_RECS]: 'a' } } });
    expect(getAiRecommendations).toHaveBeenCalledWith(aiRecArgs);
  });

  it('should NOT call getAiRecommendations if SWF_HOME_PAGE_AI_RECS is false, even if SWE_HOME_PAGE_AI_RECS is active', () => {
    render(<Home />, { appState: { enabledFeatures: { [SWF_HOME_PAGE_AI_RECS]: false }, abSeg: { [SWE_HOME_PAGE_AI_RECS]: 'a' } } });
    expect(getAiRecommendations).not.toHaveBeenCalled();
  });

  it('should NOT call getAiRecommendations if SWE_HOME_PAGE_AI_RECS is inactive, even if SWF_HOME_PAGE_AI_RECS is true', () => {
    render(<Home />, { appState: { enabledFeatures: { [SWF_HOME_PAGE_AI_RECS]: true }, abSeg: { [SWE_HOME_PAGE_AI_RECS]: 'x' } } });
    expect(getAiRecommendations).not.toHaveBeenCalled();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });
});
