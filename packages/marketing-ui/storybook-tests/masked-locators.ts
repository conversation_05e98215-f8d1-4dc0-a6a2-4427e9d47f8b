const maskedStories = ['circlenavigation', 'categorycards'];
const maskedLocators: { [key in (typeof maskedStories)[number]]: { locator: string } } = {
  circlenavigation: { locator: 'a>div>div' },
  categorycards: { locator: 'div[data-testid="product-card-image"]' },
};

export const maskLocatorsPosition = (id: string) => maskedStories.findIndex(maskedStory => id.includes(maskedStory));
export const hasMaskedStory = (id: string) => maskedStories.find(maskedStory => id.includes(maskedStory));
export const maskLocator = (id: string): { locator: string } => maskedLocators[maskedStories[maskLocatorsPosition(id)]];
