/* eslint-disable no-console */
import console from 'console';
import process from 'process';
import nodemon from 'nodemon';
import open from 'open';
import fetch from 'node-fetch';
import { setInterval, clearInterval } from 'timers';

const checkServer = (url, interval) => {
  const timer = setInterval(async () => {
    try {
      const response = await fetch(url);
      if (response.ok) {
        clearInterval(timer);
        console.log(`\n\nServer is up. \nOpening browser to: ${url}\n\n`);
        open(url);
      }
    } catch (error) {
      console.log('\nWaiting for server to be up ...\n');
    }
  }, interval);
};

nodemon({
  script: 'storybook build',
  watch: ['src/components', '.storybook'],
  exec: 'NODE_ENV=development npm run build-storybook && npx http-server storybook-static --port 6006 --brotli -c-10 --proxy https://www.gap.com/ --cors',
  ext: 'css,js,json,ts,tsx',
})
  .on('start', () => {
    console.log('Nodemon is watching for changes...');
    if (!process.execArgv.includes('--open=false')) {
      checkServer('http://localhost:6006', 15000); // Check every 15 seconds
    }
  })
  .on('SIGINT', () => {
    console.log('Shutting Down ...');
    process.exit();
  })
  .on('quit', () => {
    console.log('Shutting Down ...');
    process.exit();
  })
  .on('restart', () => {
    // Disable auto-open on restart
    process.execArgv.push('--open=false');
    console.log('\n\nRebuilding storybook ...\n\n');
  })
  .on('error', err => {
    console.error('Error:', err);
  });
