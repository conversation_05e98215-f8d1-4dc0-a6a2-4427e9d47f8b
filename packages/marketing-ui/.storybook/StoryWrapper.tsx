import React, { ReactNode, useEffect } from 'react';
import lazy from 'next/dynamic';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations, MfeKeys } from '@ecom-next/sitewide/localization';
import { PersonalizationContextProvider } from '@ecom-next/sitewide/personalization-provider';
import { PageAppState, PageContextProvider } from '@ecom-next/sitewide/app-state-provider';
import { getPageContext, PageContext, PageContextProps } from '@ecom-next/utils/server';
import { StoryErrorBoundary } from '../src/components/StoryErrorBoundary';

const defaultAppState = ({ displayName, brand }): PageAppState => ({
  market: 'us',
  locale: 'en_US',
  brandName: brand,
  pageType: 'category',
  cid: '',
  targetEnv: undefined,
  engineEndpointConfiguration: undefined,
  topSearchTerms: [],
  brandBarShortcutLinks: [],
  hasInternationalShipping: false,
  contentType: 'wip',
  appConfig: undefined,
  brand: 'at',
  brandId: '',
  brandAbbr: 'at',
  brandCode: 1,
  brandtype: 'ca',
  breakpoint: 'Mobile',
  buildVersion: {
    nextApp: '',
    syncedPackageVersions: {
      'sitewide-app': '',
      'marketing-ui': '',
      'find-components': '',
      'core-ui': '',
      'micro-frontend-app-helpers': '',
      'plp-ui': '',
      'search-page': '',
      'product-page': '',
      seo: '',
    },
  },
  contentApi: undefined,
  ecomApiBaseUrl: '',
  headersList: new Headers(),
  isDesktop: false,
  previewDate: undefined,
  requestType: 'wip',
  displayName,
});

const AtStyle = lazy(import('./styles/at'));
const BrStyle = lazy(import('./styles/br'));
const OnStyle = lazy(import('./styles/on'));
const GapStyle = lazy(import('./styles/gap'));

const LOOKUP_BRANDS = /--brand-name:\s*(at|br|on|gap);/;

type Brands = 'at' | 'br' | 'brfs' | 'on' | 'gap' | 'gapfs';
type Market = 'us' | 'ca';
type Locale = 'en-US' | 'en-CA' | 'fr-CA';

type StoryProps = {
  brand: Brands;
  data: unknown;
  isDesktop: boolean;
  locale: Locale;
  market: Market;
};

export interface Props {
  StoryComponent?: (props: StoryProps) => JSX.Element | JSX.Element[] | null;
  brand: Brands;
  children?: React.ReactNode;
  data: unknown;
  innerCSS?: string;
  isDesktop: boolean;
  locale: Locale;
  market: Market;
  translations?: MfeKeys[];
}

const brandComponent = {
  at: AtStyle,
  br: BrStyle,
  brfs: BrStyle,
  gap: GapStyle,
  gapfs: GapStyle,
  on: OnStyle,
};

const AtStyleComponent = lazy(() => import('../../ecom-next/app/at/style'));
const BrStyleComponent = lazy(() => import('../../ecom-next/app/br/style'));
const gapStyleComponent = lazy(() => import('../../ecom-next/app/gap/style'));
const onStyleComponent = lazy(() => import('../../ecom-next/app/on/style'));

const StyleComponentMap = {
  at: AtStyleComponent,
  br: BrStyleComponent,
  brfs: BrStyleComponent,
  gap: gapStyleComponent,
  gapfs: gapStyleComponent,
  on: onStyleComponent,
};

const ThemeSelector = ({ children, brand }: { brand: Brands; children: ReactNode }) => {
  const BrandComponent = brandComponent[brand];
  const StyleComponent = StyleComponentMap[brand];
  useEffect(() => {
    document.querySelectorAll('style').forEach(tag => {
      if (/tailwindcss v3|tailwindcss\.com/.test(tag.innerHTML)) {
        const [, brandFound] = String(tag.innerHTML).match(LOOKUP_BRANDS) || [];
        const disabled = brandFound !== '' && !brand.includes(brandFound);
        if (brandFound) tag.disabled = disabled;
      }
    }, 500);
    return () => {
      document.querySelectorAll('style[data-tailwind]').forEach(tag => {
        if ('disabled' in tag) {
          tag.disabled = false;
        }
      });
    };
  }, [brand]);

  return (
    <React.Suspense fallback={<>Loading...</>}>
      <BrandComponent />
      <StyleComponent>{children}</StyleComponent>
    </React.Suspense>
  );
};

function StoryWrapper(props: Props) {
  const { StoryComponent, brand, translations: tscopes, ...rest } = props;
  const pageContext: PageContextProps = getPageContext(brand);
  const pageAppState = defaultAppState({ displayName: pageContext.displayName, brand });
  const translations = tscopes?.length ? getLocaleSpecificTranslations(pageContext.locale, tscopes) : {};

  return (
    <ThemeSelector brand={brand}>
      <StitchStyleProvider brand={brand}>
        <PageContextProvider value={{ ...pageContext, ...pageAppState }}>
          <PersonalizationContextProvider>
            <LocalizationProvider locale={pageContext.locale} market={pageContext.market} translations={translations}>
              {props.innerCSS && <style dangerouslySetInnerHTML={{ __html: props.innerCSS }} />}
              {StoryComponent ? (
                <div className={brand}>
                  <StoryErrorBoundary>
                    <StoryComponent brand={brand} {...rest} />
                  </StoryErrorBoundary>
                </div>
              ) : null}
            </LocalizationProvider>
          </PersonalizationContextProvider>
        </PageContextProvider>
      </StitchStyleProvider>
    </ThemeSelector>
  );
}

export default StoryWrapper;
