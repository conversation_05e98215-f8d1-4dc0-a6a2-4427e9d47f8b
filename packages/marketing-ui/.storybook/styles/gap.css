@import 'tailwindcss/base';
@import '../../../core/src/themes/crossbrand/base.css';
@import '../../../core/src/themes/common/base.css';
@import '../../../core/src/themes/gap/base.css';

@import '../../../core/src/themes/tools/figma/brand/primitive/cb.generated.css';
@import '../../../core/src/themes/tools/figma/brand/primitive/global.generated.css';
@import '../../../core/src/themes/tools/figma/brand/primitive/gap.generated.css';

@import '../../../core/src/themes/tools/figma/brand/legacy/cb.legacy.css';
@import '../../../core/src/themes/tools/figma/brand/legacy/base.legacy.css';
@import '../../../core/src/themes/tools/figma/brand/legacy/gap.legacy.css';

@import 'tailwindcss/components';
@import '../../../core/src/themes/crossbrand/components.css';
@import 'slick-carousel/slick/slick-theme.css';
@import '../../../core/src/themes/common/components.css';

@import 'tailwindcss/utilities';
@import 'tailwindcss/variants';
@import '../../../marketing-ui/src/components/rapid-authoring/components.css';

@config "./gap.config.ts";
