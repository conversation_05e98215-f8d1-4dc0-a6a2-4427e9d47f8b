export const translations = {
  mfeKey: 'marketing',
  translationObject: {
    'en-US': {
      translation: {
        'cms.edfs.sign_in_or_join': 'Sign In or Join',
        'cms.carousel.previous': 'Previous',
        'cms.carousel.next': 'Next',
        'cms.carousel.previous_with_slide_count': 'View previous of {{slidesLength}} slides',
        'cms.carousel.next_with_slide_count': 'View next of {{slidesLength}} slides',
        'cms.pauseplay.play': 'play',
        'cms.pauseplay.pause': 'pause',
        'cms.promosticker.close': 'Close',
        'cms.ReviewRatings.ariaLabel': 'Image of 5 stars, {{rating}} out of the 5 stars are filled',
        'cms.ReviewRatings.ariaLabelNoReviews': 'Image of 5 stars, 0 are filled, 0 Reviews',
        'marketing.VideoComponent.ariaLabelPlay': 'Play',
        'marketing.VideoComponent.ariaLabelPause': 'Pause',
        'marketing.VideoComponent.ariaLabelMute': 'Mute',
        'marketing.VideoComponent.ariaLabelUnmute': 'Unmute',
        'marketing.VideoComponent.ariaLabelVolume': 'Volume',
        'cms.promodrawer.tap_to_apply_default_message': 'tap to apply',
        'cms.promodrawer.tap_to_apply_is_tapped_message': 'applied',
        'cms.promodrawer.tap_to_apply_failed': 'Try Again',
      },
    },
    'en-CA': {
      translation: {
        'cms.edfs.sign_in_or_join': 'Sign In or Join',
        'cms.carousel.previous': 'Previous',
        'cms.carousel.next': 'Next',
        'cms.carousel.previous_with_slide_count': 'View previous of {{slidesLength}} slides',
        'cms.carousel.next_with_slide_count': 'View next of {{slidesLength}} slides',
        'cms.pauseplay.play': 'play',
        'cms.pauseplay.pause': 'pause',
        'cms.promosticker.close': 'Close',
        'cms.ReviewRatings.ariaLabel': 'Image of 5 stars, {{rating}} out of the 5 stars are filled',
        'cms.ReviewRatings.ariaLabelNoReviews': 'Image of 5 stars, 0 are filled, 0 Reviews',
        'marketing.VideoComponent.ariaLabelPlay': 'Play',
        'marketing.VideoComponent.ariaLabelPause': 'Pause',
        'marketing.VideoComponent.ariaLabelMute': 'Mute',
        'marketing.VideoComponent.ariaLabelUnmute': 'Unmute',
        'marketing.VideoComponent.ariaLabelVolume': 'Volume',
        'cms.promodrawer.tap_to_apply_default_message': 'tap to apply',
        'cms.promodrawer.tap_to_apply_is_tapped_message': 'applied',
        'cms.promodrawer.tap_to_apply_failed': 'Try Again',
      },
    },
    'fr-CA': {
      translation: {
        'cms.edfs.sign_in_or_join': 'Ouvrir Une Session ou S’inscrire',
        'cms.carousel.previous': 'Précédente',
        'cms.carousel.next': 'Suivante',
        'cms.carousel.previous_with_slide_count': 'Voir les {{slidesLength}} diapositives précédentes',
        'cms.carousel.next_with_slide_count': 'Voir les {{slidesLength}} diapositives suivantes',
        'cms.pauseplay.play': 'lecture',
        'cms.pauseplay.pause': 'pause',
        'cms.promosticker.close': 'Fermer',
        'cms.ReviewRatings.ariaLabel': 'Image de 5 étoiles, {{rating}} sont colorées',
        'cms.ReviewRatings.ariaLabelNoReviews': 'Image de 5 étoiles, 0 sont colorées, 0 Notes',
        'marketing.VideoComponent.ariaLabelPlay': 'Lire',
        'marketing.VideoComponent.ariaLabelPause': 'Mettre en pause',
        'marketing.VideoComponent.ariaLabelMute': 'Activer le son',
        'marketing.VideoComponent.ariaLabelUnmute': 'Désactiver le son',
        'marketing.VideoComponent.ariaLabelVolume': 'Volume',
        'cms.promodrawer.tap_to_apply_default_message': 'Taper pour appliquer',
        'cms.promodrawer.tap_to_apply_is_tapped_message': 'appliqué à la caisse',
        'cms.promodrawer.tap_to_apply_failed': 'Réessayez',
      },
    },
  },
} as const;
