import { serverFetch } from '@ecom-next/utils/serverFetch';
import { Brand, getPageContext, Locale, Market } from '@ecom-next/utils/server';
import logger from '@ecom-next/app/logger';

export interface PmcsEditionData {
  edition: {
    end: string;
    id: string;
    start: string;
  };
}

const getPMCSUrl = (brand: Brand, market: Market, locale: Locale, editionId: string, contentApi?: string) => {
  const baseUrl = process.env.PMCS_EDITIONS_SERVICE_URL || 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com/pmcs/edition';
  const localeFormatted = locale.replace('-', '_');
  const queryParams = `brand=${brand}&locale=${localeFormatted}&market=${market}${contentApi ? `&contentApi=${contentApi}` : ''}`;

  return `${baseUrl}/${editionId}/pages?${queryParams}`;
};
export async function fetchMarketingEditions(editionId: string, contentApi?: string) {
  const { brand, locale, market } = getPageContext();

  const pmcsUrl = getPMCSUrl(brand, market, locale, editionId, contentApi);
  const log = logger.child({ url: pmcsUrl });
  log.info('fetching content...');

  try {
    const res = await serverFetch<PmcsEditionData>(pmcsUrl, {
      mode: 'no-cors',
    });
    return res;
  } catch (err) {
    logger.error('no content for edition: ', editionId, err);
    return {} as Record<string, unknown>;
  }
}

export const getPMCSMarketingEditions = fetchMarketingEditions;
