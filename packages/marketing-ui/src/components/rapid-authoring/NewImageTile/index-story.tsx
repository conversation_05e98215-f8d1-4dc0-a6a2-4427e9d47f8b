// @ts-nocheck
'use client';
import React from 'react';
// @ts-ignore
import { StoryFn } from '@storybook/react';
import { bothImagesData, desktopOnlyData, mobileOnlyData, carouselData } from './__fixtures__/new-image-story-data';
import NewImageTile from './ImageTile';

import Carousel from './index';

export default {
  title: 'Common/JSON Components (Marketing)/Rapid Authoring/NewImageTile',
  component: NewImageTile,
  parameters: {
    knobs: { disable: true },
    layout: 'fullscreen',
  },
  tags: ['exclude'],
};

const dataWithFeatureFlags = data => ({
  ...data,
  featureFlags: {
    'omni-show-hotzone-grid': false,
  },
});

const NewImageTileTemplate: StoryFn<typeof NewImageTile> = ({ data }) => {
  return <NewImageTile {...dataWithFeatureFlags(data)} />;
};

export const BothDesktopAndMobileImages = NewImageTileTemplate.bind({});
BothDesktopAndMobileImages.args = { data: bothImagesData };

export const DesktopImageOnly = NewImageTileTemplate.bind({});
DesktopImageOnly.args = { data: desktopOnlyData };

export const MobileImageOnly = NewImageTileTemplate.bind({});
MobileImageOnly.args = { data: mobileOnlyData };

export const WithDebugOverlays = NewImageTileTemplate.bind({});
WithDebugOverlays.args = {
  data: {
    ...bothImagesData,
    featureFlags: {
      'omni-show-hotzone-grid': true,
    },
  },
};

const NewCarouselTemplate: StoryFn<typeof NewImageTile> = ({ data }) => {
  // TODO: replace with NewImageTile after implementation
  return <Carousel {...dataWithFeatureFlags(data)} />;
};
export const NewCarousel = NewCarouselTemplate.bind({});
NewCarousel.args = { data: carouselData };
