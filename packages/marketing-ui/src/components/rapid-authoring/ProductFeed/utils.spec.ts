import { Brands } from '@ecom-next/core/legacy/utility';
import prepareDataForRecsAggregationServiceAPI from '../../../components/legacy/components/Recommendations/helpers/prepare-for-recs-aggregation-service-api';
import { productListMock, categoryListMock, noVLIProductListMock } from './__fixtures__/aggregation-mock';
import { prepareCategoryProducts, getPreviewData, fetchCategoriesAndProducts, formatReviewCount } from './utils';
import { safeWindow } from '@mui/components/legacy/helper/safeWindow';
import Cookies from 'js-cookie';

// Mocks
jest.mock('@mui/components/legacy/helper/safeWindow');
jest.mock('js-cookie');

describe('prepareCategoryProducts', () => {
  it('should return expected category products', () => {
    const result = prepareCategoryProducts(categoryListMock, productListMock, 'catid123');
    expect(result).toMatchSnapshot();
  });

  it('should have the same output as the legacy prepareDataForRecsAggregationServiceAPI helper when AT brand, AT recommendations remap, and product recs CCID FF', () => {
    const legacyResponse = prepareDataForRecsAggregationServiceAPI({
      ccList: categoryListMock[0].ccList,
      cid: 'catid123',
      pcid: 'catid123',
      products: productListMock,
      brandName: Brands.Athleta,
      enabledFlags: { 'at-recommendations-remap': true, 'product-recommendations-use-ccid': true },
    });
    const appendedLegacyResponse = legacyResponse.map(res => ({
      ...res,
      hasMarkdownPrice: false,
      hasPercentageOff: true,
      hasSUPPrice: false,
      CategoryName: 'New Arrivals',
    }));
    const currentResponse = prepareCategoryProducts(categoryListMock, productListMock, 'catid123');
    expect(currentResponse).toEqual(appendedLegacyResponse);
  });

  it('should return the remapped products with `P01` image type when the `VLI` image type is not available', () => {
    const result = prepareCategoryProducts(categoryListMock, noVLIProductListMock, 'catid123');
    expect(result).toMatchSnapshot();
    result.forEach(res => {
      expect(res.ImageURL.includes('p01')).toBe(true);
      expect(res.LightWeightImageURL.includes('p01')).toBe(true);
    });
  });

  it('should utilize both pcid and cid with the assumption that cid is always passed', () => {
    const result = prepareCategoryProducts(categoryListMock, productListMock, 'catid123');

    expect(result).toMatchSnapshot();
    const url = new URL(result[0].DetailURL, 'http://base-domain.com');
    expect(url.searchParams.get('cid')).toBe('catid123');
    expect(url.searchParams.get('pcid')).toBe('catid123');
  });

  it('should utilize ccId instead of styleId when constructing a pdp url', () => {
    const result = prepareCategoryProducts(categoryListMock, productListMock, 'catid123');

    expect(result).toMatchSnapshot();
    const url = new URL(result[0].DetailURL, 'http://base-domain.com');
    expect(url.searchParams.get('pid')).toBe('756089032');
  });
});

describe('getPreviewData', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        search: '?previewDate=2025-01-01&previewMode=preview',
      },
      writable: true,
    });
  });

  it('returns preview data from cookies when available', () => {
    (safeWindow as jest.Mock).mockReturnValue(true);
    (Cookies.get as jest.Mock).mockReturnValue(
      JSON.stringify({
        previewDate: '2025-06-01',
        previewMode: 'cookie-preview',
      })
    );

    const result = getPreviewData();

    expect(result).toEqual({
      previewDate: '2025-06-01',
      previewMode: 'cookie-preview',
    });
    expect(Cookies.get).toHaveBeenCalledWith('previewButtonValues');
  });

  it('returns preview data from URL params when cookies are not available', () => {
    (safeWindow as jest.Mock).mockReturnValue(true);
    (Cookies.get as jest.Mock).mockReturnValue('{}');

    const result = getPreviewData();

    expect(result).toEqual({
      previewDate: '2025-01-01',
      previewMode: 'preview',
    });
  });

  it('uses empty URLSearchParams when window is not available', () => {
    (safeWindow as jest.Mock).mockReturnValue(false);
    (Cookies.get as jest.Mock).mockReturnValue(null);

    const result = getPreviewData();

    expect(result).toEqual({
      previewDate: '',
      previewMode: '',
    });
  });

  it('handles errors gracefully', () => {
    (safeWindow as jest.Mock).mockReturnValue(true);
    (Cookies.get as jest.Mock).mockImplementation(() => {
      throw new Error('Cookie error');
    });

    const result = getPreviewData();

    expect(result).toEqual({
      previewDate: '2025-01-01',
      previewMode: 'preview',
    });
  });
});

describe('fetchCategoriesAndProducts', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    global.fetch = jest.fn();
  });

  const mockParams = {
    cid: 'category123',
    brand: 'at',
    market: 'us',
    locale: 'en_US',
    previewDate: '',
    previewMode: '',
  };

  it('fetches product data successfully', async () => {
    const mockResponse = {
      categories: [{ id: 1 }],
      products: [{ id: 'prod1' }],
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const result = await fetchCategoriesAndProducts(
      mockParams.cid,
      mockParams.brand,
      mockParams.market,
      mockParams.locale,
      mockParams.previewDate,
      mockParams.previewMode
    );

    expect(result).toEqual(mockResponse);
    expect(global.fetch).toHaveBeenCalledWith(
      `https://api.gap.com/commerce/search/products/v2/cc?brand=${mockParams.brand}&market=${mockParams.market}&locale=${mockParams.locale}&cid=${mockParams.cid}&pageSize=20&pageNumber=0`
    );
  });

  // TODO: this should be uncommented once the preview call is re-enabled
  // it('includes preview parameters when provided', async () => {
  //   const mockResponse = { data: 'preview data' };
  //   const previewParams = { ...mockParams, previewDate: '2025-01-01', previewMode: 'preview' };

  //   (global.fetch as jest.Mock).mockResolvedValueOnce({
  //     ok: true,
  //     json: async () => mockResponse,
  //   });

  //   await fetchCategoriesAndProducts(
  //     previewParams.cid,
  //     previewParams.brand,
  //     previewParams.market,
  //     previewParams.locale,
  //     previewParams.previewDate,
  //     previewParams.previewMode
  //   );

  //   const expectedUrl = `https://api.gap.com/commerce/search/products/v2/cc?brand=${previewParams.brand}&market=${previewParams.market}&locale=${previewParams.locale}&cid=${previewParams.cid}&pageSize=20&pageNumber=0&previewDate=${encodeURIComponent(previewParams.previewDate)}&mode=${previewParams.previewMode}&ignoreInventory=false`;

  //   expect(global.fetch).toHaveBeenCalledWith(expectedUrl);
  // });

  it('handles fetch errors', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    });

    await expect(
      fetchCategoriesAndProducts(mockParams.cid, mockParams.brand, mockParams.market, mockParams.locale, mockParams.previewDate, mockParams.previewMode)
    ).rejects.toThrow('request failed: 404 with Not Found');
  });

  it('handles network errors', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    await expect(
      fetchCategoriesAndProducts(mockParams.cid, mockParams.brand, mockParams.market, mockParams.locale, mockParams.previewDate, mockParams.previewMode)
    ).rejects.toThrow('Error: Network error');
  });
});

describe('formatReviewCount', () => {
  it('returns the count as a string when less than 1000', () => {
    expect(formatReviewCount(0)).toBe('0');
    expect(formatReviewCount(1)).toBe('1');
    expect(formatReviewCount(999)).toBe('999');
  });

  it('returns count in k with 1 decimal, rounded up, for 1000 <= count < 10000', () => {
    expect(formatReviewCount(1000)).toBe('1.0k');
    expect(formatReviewCount(1234)).toBe('1.3k'); // 1234/100=12.34, ceil=13, 13/10=1.3
    expect(formatReviewCount(1999)).toBe('2.0k');
    expect(formatReviewCount(9999)).toBe('10.0k');
  });

  it('returns count in k with no decimal, rounded down, for count >= 10000', () => {
    expect(formatReviewCount(10000)).toBe('10k');
    expect(formatReviewCount(124710)).toBe('124k');
    expect(formatReviewCount(999999)).toBe('999k');
  });

  it('handles edge cases at boundaries', () => {
    expect(formatReviewCount(999)).toBe('999');
    expect(formatReviewCount(1000)).toBe('1.0k');
    expect(formatReviewCount(9999)).toBe('10.0k');
    expect(formatReviewCount(10000)).toBe('10k');
  });
});
