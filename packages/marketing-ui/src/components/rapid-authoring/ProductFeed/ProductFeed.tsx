'use client';
import React, { useContext, useState, useEffect } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { BreakpointContext, XLARGE, LARGE } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { LoadingPlaceholder } from '@ecom-next/core/legacy/loading-placeholder';
import { ProductFeedContent, getSectionLabel, fetchCategoriesAndProducts, prepareCategoryProducts, getPreviewData } from './utils';
import { getStyles } from './styles';
import { CategoryProduct } from './types';
import NewTile from '../NewImageTile/ImageTile';
import ArrowButton from './ArrowButton';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';

/**
 * ProductFeed component displays a list or carousel of product items with optional navigation arrows.
 * It adapts its layout and behavior based on the current breakpoint (mobile or desktop) and feature flags.
 *
 * @param serverResponse - The data object containing product feed information, layout, styling, and feature flags.
 * @returns A React component rendering the product feed as a section, with optional carousel and navigation arrows.
 *
 * @remarks
 * - Uses BreakpointContext to determine responsive behavior.
 * - Supports both stacked and carousel layouts depending on device and configuration.
 * - Handles carousel navigation and scrolling logic.
 * - Renders product items using the NewTile component.
 * - Infinite carousel logic is not yet implemented.
 */
export default function ProductFeed(serverResponse: ProductFeedContent) {
  const { brand, market, locale } = usePageContext();
  const { formatCurrency } = useLocalize();
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE) && smallerThan(LARGE);
  const { accessibilityAltText, categoryId, showPrice, trackingId, title } = serverResponse;
  const sliderTrackRef = React.useRef<HTMLDivElement>(null);

  const [productFeedItems, setProductFeedItems] = useState<Array<CategoryProduct>>([]);
  // Q: should we also add a loading and error state that is usually a best practice?

  useEffect(() => {
    const { previewDate, previewMode } = getPreviewData();

    const getCategoryProducts = async () => {
      try {
        const data = await fetchCategoriesAndProducts(categoryId, brand, market, locale, previewDate, previewMode);
        const categoryProducts = prepareCategoryProducts(data.categories, data.products, categoryId);
        const setCatProducts = [...categoryProducts, ...categoryProducts, ...categoryProducts]; // duplicate products arr twice for infinite carousel
        setProductFeedItems(setCatProducts);
      } catch (e) {
        console.log(e);
      }
    };

    getCategoryProducts();
  }, []);

  //TODO: need to improve and refactor this logic
  const isCarousel = (() => {
    if (!isMobile) return true;
    return true;
  })();
  const itemCount = productFeedItems?.length / 3; // divide by 3 since arrow is duplicated twice
  const arrowDisplayThreshold = 4;
  const minIndex = itemCount; // first element in the middle array
  const maxIndex = itemCount - 1;
  const showArrows = productFeedItems.length > 0 && isCarousel && itemCount > arrowDisplayThreshold;

  const [_, setCarouselIndex] = React.useState(minIndex);

  useEffect(() => {
    // Wait for items to be loaded and DOM to be updated
    if (productFeedItems.length > 0 && sliderTrackRef.current) {
      // Calculate the width of a single item
      const childWidth = sliderTrackRef.current.firstElementChild?.clientWidth || 0;
      const initialScrollPosition = childWidth * minIndex;

      setCarouselIndex(minIndex);

      sliderTrackRef.current?.scrollTo({
        left: initialScrollPosition,
        behavior: 'instant',
      });
    }
  }, [productFeedItems.length, minIndex]);

  const updateCarouselIndex = (direction: 'left' | 'right') => {
    setCarouselIndex(prevIndex => {
      const childWidth = sliderTrackRef?.current?.firstElementChild?.clientWidth || 0;

      if (direction === 'left') {
        sliderTrackRef.current?.scrollTo({
          left: childWidth * (prevIndex - 1),
          behavior: 'smooth',
        });
        if (prevIndex === 1) {
          setTimeout(() => {
            sliderTrackRef.current?.scrollTo({
              left: childWidth * itemCount,
              behavior: 'instant',
            });
          }, 500);
        }
        return prevIndex === 1 ? itemCount : prevIndex - 1;
      }

      // right direction
      sliderTrackRef.current?.scrollTo({
        left: childWidth * (prevIndex + 1),
        behavior: 'smooth',
      });
      const lastElemInCenterArr = itemCount * 2 - 1;
      if (prevIndex === lastElemInCenterArr) {
        setTimeout(() => {
          sliderTrackRef.current?.scrollTo({
            left: childWidth * itemCount,
            behavior: 'instant',
          });
        }, 500);
      }

      return prevIndex === lastElemInCenterArr ? itemCount : prevIndex + 1;
    });
  };

  const handleScroll = (direction: 'left' | 'right') => {
    if (sliderTrackRef.current) {
      updateCarouselIndex(direction);
    }
  };

  const styles = getStyles(isCarousel, itemCount);

  const buildProductFeedResponse = (productFeedItem: any) => {
    return {
      imageTabs: {
        desktopImage: {
          url: productFeedItem?.ImageURL,
        },
        image: {
          url: productFeedItem?.ImageURL,
        },
        desktopAccessibilityAltText: productFeedItem?.ProductName,
        accessibilityAltText: productFeedItem?.ProductName,
      },
    };
  };

  const sectionLabel = getSectionLabel(isMobile, isCarousel, showArrows);

  const handlePFItemClick = (wrapperLink: string) => {
    if (!wrapperLink) return;
    const isAbsolute = /^https?:\/\//i.test(wrapperLink);
    const url = isAbsolute ? `${wrapperLink}&mlink=${trackingId}` : `${window.location.origin}${wrapperLink}&mlink=${trackingId}`;
    if (isAbsolute) {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      window.location.href = url;
    }
  };

  // This will be used three times to make the carousel look like a continuous loop
  const renderProductFeedItems = () => {
    if (!productFeedItems || productFeedItems.length === 0) {
      // Render 4 loading placeholders if no items
      const fixedSize = isMobile ? { width: 279, height: 350 } : { width: 352, height: 500 };
      return (
        <>
          {[...Array(4)].map((_, idx) => (
            <figure key={idx} tabIndex={0} className={'w-3/4 px-2 md:w-[300px] lg:w-1/4'} role='group'>
              <div className='flex h-[500px] w-[250px] cursor-pointer flex-col gap-4 md:w-[300px] lg:w-[352px]'>
                <LoadingPlaceholder loadingComplete={false} className='h-full w-full' />
              </div>
            </figure>
          ))}
        </>
      );
    }

    return (
      <>
        {productFeedItems.map((productFeedItem, index) => (
          <figure tabIndex={0} key={index} className={styles.productFeedCard} role='group'>
            <div className='flex cursor-pointer flex-col gap-1' onClick={() => handlePFItemClick(productFeedItem?.DetailURL)}>
              <NewTile {...(buildProductFeedResponse(productFeedItem) as any)} />
              <div className={styles.cardDescription}>
                <p className='line-clamp-1'>{productFeedItem?.ProductName}</p>
                {showPrice && productFeedItem?.hasMarkdownPrice && (
                  <p className='line-clamp-1 flex gap-1'>
                    <span className='text-[#5E5C5A] line-through'>{formatCurrency(productFeedItem?.OriginalPrice + '')}</span>
                    <span className='font-semibold text-[#D00000]'>{formatCurrency(productFeedItem?.CurrentPrice + '')}</span>
                  </p>
                )}
                {showPrice && !productFeedItem?.hasMarkdownPrice && <p className='line-clamp-1'>{formatCurrency(productFeedItem?.OriginalPrice + '')}</p>}
                {productFeedItem?.MarketingFlag && <p className='line-clamp-1'>{productFeedItem?.MarketingFlag}</p>}
                <StarRatings ratingValue={productFeedItem.ReviewScore} postText={productFeedItem.ReviewCount} showRatingValue={false} />
              </div>
            </div>
          </figure>
        ))}
      </>
    );
  };

  const categoryName = (() => {
    if (productFeedItems && productFeedItems.length > 0) {
      return productFeedItems[0]?.CategoryName || null;
    }
    return title || null;
  })();

  return (
    <section className={styles.productFeedContainer}>
      {categoryName && <header className={styles.header}>{categoryName}</header>}
      <section aria-label={sectionLabel} className={styles.slider}>
        {showArrows && <ArrowButton direction='left' onClick={() => handleScroll('left')} />}
        <article className={styles.sliderTrack} ref={sliderTrackRef}>
          {renderProductFeedItems()}
        </article>
        {showArrows && <ArrowButton direction='right' onClick={() => handleScroll('right')} />}
      </section>
    </section>
  );
}
