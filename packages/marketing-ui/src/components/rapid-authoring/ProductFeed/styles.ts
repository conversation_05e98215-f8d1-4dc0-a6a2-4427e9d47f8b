export const getStyles = (isCarousel: boolean, itemCount: number) => {
  const productFeedContainerPadding = 'pb-[30px]';
  const baseProductFeedContainer = `pt-5 flex flex-col items-start stretch ${productFeedContainerPadding}`;
  const productFeedContainer = isCarousel ? `${baseProductFeedContainer} lg:pl-0 gap-2 lg:gap-[30px]` : `${baseProductFeedContainer} gap-2`;

  const slider = 'relative w-full';

  const sliderTrack = isCarousel
    ? `flex overflow-x-auto overflow-y-hidden lg:overflow-x-hidden snap-x snap-mandatory scroll-smooth h-auto max-w-full [scrollbar-width:none] [&::-webkit-scrollbar]:hidden ml-[2px] lg:m-[0px_12px_12px]`
    : 'grid grid-cols-2 gap-4';

  const productFeedCardWidth = (() => {
    if (itemCount === 2) {
      return 'w-3/4 md:w-1/2';
    }
    if (itemCount === 4) {
      return 'md:w-1/4';
    }
    if (itemCount > 4) {
      return 'w-3/4 md:w-[300px] lg:w-1/4';
    }
    return 'w-full';
  })();

  const productFeedCard = isCarousel
    ? `flex-shrink-0 snap-start snap-always ${productFeedCardWidth} box-border flex px-[6px] md:px-[6px] first:lg:ml-[6px]`
    : `flex flex-col box-border`;

  const header = 'pl-2 lg:pl-[16px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] leading-[18px] font-weight-400';

  const cardDescription = 'flex flex-col flex-start py-2 gap-2 w-[calc(100%-30px)]';

  return {
    cardDescription,
    header,
    productFeedCard,
    productFeedContainer,
    slider,
    sliderTrack,
  };
};

export const arrows = 'invisible lg:visible ch-auto max-w-[50px] w-full z-[1] bottom-0 mb-auto mt-auto top-0 transform-none absolute';
export const arrowOpacity = 'opacity-[0.125] cursor-default';
