import { AggregationServiceProductProps, AggregationServiceCCListItem } from '../../legacy/components/Recommendations/types';
import { ProductAdapterProps, ProductAdapterList } from '../../legacy/components/Recommendations/helpers/prepare-for-recs-aggregation-service-api';

export type AggregationServiceAPIProps = {
  ccList: Array<AggregationServiceCCListItem>;
  cid: string;
  products: Array<AggregationServiceProductProps>;
};

export type Category = {
  categoryId: string;
  categoryName: string;
  ccList: Array<AggregationServiceCCListItem>;
  subCategoryId: string;
  subCategoryName: string;
};

export type CategoryProduct = {
  CategoryName?: string;
  CurrentPrice?: number | string;
  DetailURL: string;
  ImageURL: string;
  LightWeightImageURL: string;
  MarketingFlag: string;
  OriginalPrice?: number | string;
  ProductName?: string;
  PromotionDisplay: string;
  InStock?: string;
  ReviewScore: number;
  ReviewCount: string;
  hasMarkdownPrice: boolean;
  hasSUPPrice: boolean;
  hasPercentageOff: boolean | string;
};

export type { AggregationServiceProductProps, AggregationServiceCCListItem, ProductAdapterProps, ProductAdapterList };
