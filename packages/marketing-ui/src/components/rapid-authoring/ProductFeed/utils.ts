import { AggregationServiceProductProps, ProductAdapterList, Category, CategoryProduct } from './types';
import { safeWindow } from '@mui/components/legacy/helper/safeWindow';
import Cookies from 'js-cookie';
import { Brand, Market } from '@ecom-next/utils/server';

export interface ProductFeedPlatforms {
  android: boolean;
  ios: boolean;
}

export interface ProductFeedMeta {
  name: string;
  schema: string;
  deliveryId: string;
}
export interface ProductFeedContent {
  _meta: ProductFeedMeta;
  showPrice: boolean;
  accessibilityAltText: string;
  type: string;
  title: string;
  productCount: number;
  categoryId: string;
  platforms: ProductFeedPlatforms;
  trackingId: string;
}

export type ArrowButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  direction: 'left' | 'right';
  disabled?: boolean;
  onClick: () => void;
};

const PREFIX = 'Product Feed';
const CAROUSEL = `${PREFIX} carousel`;
const GRID = `${PREFIX} grid`;
const COLLECTION = `${PREFIX} collection`;

export const getSectionLabel = (isMobile: boolean, isCarousel: boolean, showArrows: boolean) => {
  if (isMobile) {
    return isCarousel ? CAROUSEL : GRID;
  }
  return isCarousel && showArrows ? CAROUSEL : COLLECTION;
};

export const productAdapter = (products: AggregationServiceProductProps[]): ProductAdapterList => {
  const productList = {} as ProductAdapterList;
  products.forEach(product => {
    productList[product.styleId] = product;
  });
  return productList;
};

export const formatReviewCount = (count: number) => {
  if (count < 1000) {
    return count.toString();
  }
  if (count < 10000) {
    // Show 1 decimal, round up, max 5 chars (e.g. 1234 -> 1.3k)
    const value = Math.ceil(count / 100) / 10;
    return `${value.toFixed(1)}k`;
  }
  // No decimal, round down (e.g. 124710 -> 124k)
  const value = Math.floor(count / 1000);
  return `${value}k`;
};

export const prepareCategoryProducts = (categories: Category[], products: AggregationServiceProductProps[], cid: string): Array<CategoryProduct> => {
  const productList = productAdapter(products);
  const isAggregationServiceAPI = products?.length > 0 && categories?.length > 0;

  if (isAggregationServiceAPI) {
    const selectedCategory = categories.find(category => category.categoryId === cid);

    return (
      (
        selectedCategory?.ccList.map(({ ccId, styleId }) => {
          const selectedProduct = productList[styleId];
          const selectedStyle = selectedProduct.styleColors.find(style => style.ccId === ccId);

          const marketingFlags = selectedStyle?.ccLevelMarketingFlags?.[0]?.content;

          const PRIMARY_HIGH_RES_IMAGE_TYPE = 'VLI';
          const SECONDARY_HIGH_RES_IMAGE_TYPE = 'P01';

          const selectedImage =
            selectedStyle?.images.find(image => image.type === PRIMARY_HIGH_RES_IMAGE_TYPE) ||
            selectedStyle?.images.find(image => image.type === SECONDARY_HIGH_RES_IMAGE_TYPE);

          return {
            CategoryName: selectedCategory?.categoryName,
            CurrentPrice: selectedStyle?.effectivePrice,
            DetailURL: `/browse/product.do?cid=${cid}&pcid=${cid}&vid=1&pid=${selectedStyle?.ccId}`,
            ImageURL: `https://www4.assets-gap.com/${selectedImage?.path}`,
            LightWeightImageURL: `https://www4.assets-gap.com/${selectedImage?.path}`,
            MarketingFlag: marketingFlags ?? '',
            OriginalPrice: selectedStyle?.regularPrice,
            ProductName: selectedProduct.styleName,
            PromotionDisplay: '',
            InStock: selectedStyle?.inventoryStatus,
            ReviewScore: selectedProduct?.reviewScore || 0,
            ReviewCount: formatReviewCount(selectedProduct?.reviewCount || 0),
            // TODO confirm if this is the correct way to handle priceType. There should be functions in PLP.
            hasMarkdownPrice: selectedStyle?.priceType === '2' || selectedStyle?.priceType === 'P', // Assuming '2' is the code for markdown price
            hasSUPPrice: selectedStyle?.priceType === '3', // Assuming '3' is the code for single unit price
            hasPercentageOff: selectedStyle?.percentageOff ? parseFloat(selectedStyle.percentageOff) > 0 : false,
          };
        }) || []
      )
        // limit to 20 products
        .filter(product => !product.ImageURL?.includes('undefined') && !product.LightWeightImageURL?.includes('undefined'))
    );
  }
  return [];
};

type PreviewDate = string;
type PreviewMode = string;

export const getPreviewData = (): { previewDate: string; previewMode: string } => {
  const sWindow = safeWindow();

  let params;
  let previewDate: PreviewDate;
  let previewMode: PreviewMode;

  try {
    sWindow ? (params = new URLSearchParams(window.location.search)) : (params = new URLSearchParams());
    const previewButtonCookieValues = JSON.parse(Cookies.get('previewButtonValues') || '{}');
    previewDate = previewButtonCookieValues.previewDate || params.get('previewDate') || '';
    previewMode = previewButtonCookieValues.previewMode || params.get('previewMode') || '';
  } catch (e) {
    previewDate = params?.get('previewDate') || '';
    previewMode = params?.get('previewMode') || '';
  }

  return {
    previewDate,
    previewMode,
  };
};

const ecomBaseUS = 'https://api.gap.com';
const ecomBaseCA = 'https://api.gapcanada.ca';
const PRODUCT_CAP_SIZE = '20';
const ecomFactoryBaseUS = 'https://api.gapfactory.com';
const ecomFactoryBaseCA = 'https://api.gapfactory.ca';

const productSearchCCConfigs = {
  'us-ecom': `${ecomBaseUS}/commerce/search/products/v2/cc`,
  'ca-ecom': `${ecomBaseCA}/commerce/search/products/v2/cc`,

  'factory-ecom': `${ecomFactoryBaseUS}/commerce/search/products/v2/cc`,
  'ca-factory-ecom': `${ecomFactoryBaseCA}/commerce/search/products/v2/cc`,
};

export const getCommmerceProductSearchUrl = (brand: Brand, market: Market): string => {
  const marketSelector = brand.includes('fs') ? (market === 'ca' ? `ca-factory` : 'factory') : market;
  return productSearchCCConfigs[`${marketSelector}-ecom`];
};

export const fetchCategoriesAndProducts = async (
  cid: string,
  brand: Brand,
  market: Market,
  locale: string,
  previewDate: PreviewDate,
  previewMode: PreviewMode
) => {
  const baseRequestUrl = getCommmerceProductSearchUrl(brand, market);
  const url = new URL(baseRequestUrl);

  url.searchParams.set('brand', brand);
  url.searchParams.set('market', market);
  url.searchParams.set('locale', locale);
  url.searchParams.set('cid', cid);

  url.searchParams.set('pageSize', PRODUCT_CAP_SIZE);
  url.searchParams.set('pageNumber', '0');

  const fetchUrl = url.toString();
  if (previewDate) {
    // fetchUrl += `&previewDate=${encodeURIComponent(previewDate)}&mode=${previewMode}&ignoreInventory=false`;
  }
  try {
    const res = await fetch(fetchUrl);
    if (!res.ok) {
      throw new Error(`request failed: ${res.status} with ${res.statusText}`);
    }
    const data = await res.json();
    return data;
  } catch (e: unknown) {
    throw new Error(e as string);
  }
};
