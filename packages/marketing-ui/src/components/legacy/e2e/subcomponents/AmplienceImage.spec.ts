// @ts-nocheck
import { expect, test } from '@playwright/test';

test.describe('AmplienceImage', () => {
  test.skip('Visual Regression Test in Gap', async ({ page }) => {
    await page.goto('/iframe.html?args=&id=common-json-components-marketing-cms-amplienceimage--visual-regression-tests&viewMode=story&brand=gap');
    await page.waitForLoadState('networkidle');

    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });
});
