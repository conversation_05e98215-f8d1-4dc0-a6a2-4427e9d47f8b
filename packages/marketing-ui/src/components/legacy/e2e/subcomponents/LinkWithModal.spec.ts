// @ts-nocheck
import { expect, test } from '@playwright/test';
import { getStoryUrl } from '../helpers';

test.describe('LinkWithModal', () => {
  test.skip('default link with modal', async ({ page }) => {
    await page.goto(
      getStoryUrl({
        id: 'common-json-components-marketing-linkwithmodal--default-link-modal',
        brand: 'gap',
        featureFlags: {
          'gap-colors-2022': true,
          'gap-redesign-2024': true,
        },
      })
    );
    await page.waitForLoadState('networkidle');

    await expect(page).toHaveScreenshot();

    const buttonOne = page.getByRole('link');
    await buttonOne.click();

    await expect(page).toHaveScreenshot();
  });
});
