// @ts-nocheck
'use client';
import { Page } from '@playwright/test';
import path from 'path';

type ResourceExclusions = 'image' | 'script' | 'stylesheet' | 'media' | 'font' | 'fetch' | 'xhr' | 'other';

const iframePath = path.join(__dirname, '../fixtures/mock-iframe.html');

const RESOURCE_EXCLUSIONS: ResourceExclusions[] = ['image', 'script', 'stylesheet', 'media', 'font', 'fetch', 'xhr', 'other'];

/**
 * This will help the page route for vimeo content.
 * It will bypass the default iframe method so that the content isn't blocked
 * and content can load as expected.
 */
export const blockVimeoIframe = (page: Page, pattern: string | RegExp): Promise<void> =>
  page.route(pattern, async route => {
    await route.fulfill({
      status: 200,
      contentType: 'text/html',
      path: iframePath,
    });
  });

/**
 * This simulates content being unavailable to the client.
 * This is done by pattern matching on the route (eg. Vimeo).
 * If pattern matches, then the request within page.goTo() is intercepted and aborted, otherwise it continues.
 */
export const blockNetworkResources = (page: Page, pattern: string | RegExp, exclusions = RESOURCE_EXCLUSIONS): Promise<void> =>
  page.route(pattern, route => (exclusions.includes(route.request().resourceType() as ResourceExclusions) ? route.abort() : route.continue()));

/**
 *
 * @param pattern Search pattern for the url
 * @param page Playwright Page
 * @returns Promise<Request>
 */
export const waitForContentUrlResponse = (pattern: string | RegExp, page: Page) => {
  try {
    page.waitForResponse(response => RegExp(pattern).test(response.url()) && response.status() === 200);
  } catch (error) {
    console.error(error);
  }
};
