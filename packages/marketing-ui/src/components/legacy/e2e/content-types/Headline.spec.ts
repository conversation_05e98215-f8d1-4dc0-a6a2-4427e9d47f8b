// @ts-nocheck
import { expect, test } from '@playwright/test';
import { BrandInfo, defaultFlags, getStoryUrl } from '../helpers';

test.describe('Headline', () => {
  const brandsForVR: BrandInfo[] = [
    {
      brand: 'gap',
      description: 'Gap with new colors',
      featureFlags: { 'gap-colors-2022': true },
    },
    {
      brand: 'gap',
      description: 'Gap without new colors',
      featureFlags: { 'gap-colors-2022': false },
    },
    {
      brand: 'gapfs',
      description: 'GapFS with new colors',
      featureFlags: { 'gap-colors-2022': true },
    },
    {
      brand: 'gapfs',
      description: 'GapFS without new colors',
      featureFlags: { 'gap-colors-2022': false },
    },
    {
      brand: 'at',
      description: 'AT without redesign',
      featureFlags: { 'at-redesign-2023': false },
    },
    {
      brand: 'at',
      description: 'AT with redesign',
      featureFlags: { 'at-redesign-2023': true },
    },
    {
      brand: 'br',
      description: 'BR with redesign',
      featureFlags: {},
    },
    {
      brand: 'brfs',
      description: 'BRFS with redesign',
      featureFlags: {},
    },
    { brand: 'on', description: 'ON default settings' },
  ];

  brandsForVR.forEach(({ brand, description, featureFlags }) => {
    test.skip(`Visual Regression for ${description}`, async ({ page }) => {
      const mergedFlags = { ...defaultFlags, ...featureFlags };
      await page.goto(
        getStoryUrl({
          id: 'common-json-components-marketing-content-types-headline--default',
          featureFlags: mergedFlags,
          brand,
        })
      );
      await page.waitForLoadState('networkidle');
      await expect(page).toHaveScreenshot({
        fullPage: true,
      });
    });
    test.skip(`Visual Regression for ${description} with custom appearance`, async ({ page }) => {
      const mergedFlags = { ...defaultFlags, ...featureFlags };
      await page.goto(
        getStoryUrl({
          id: 'common-json-components-marketing-content-types-headline--with-custom-appearance',
          featureFlags: mergedFlags,
          brand,
        })
      );
      await page.waitForLoadState('networkidle');
      await expect(page).toHaveScreenshot({
        fullPage: true,
      });
    });
  });
});
