// @ts-nocheck
import { expect, test } from '@playwright/test';
import { ANDROID_CHROME, IPHONE_SAFARI, getStoryUrl } from '../helpers';

const brand = 'at';
const vrTests = [
  {
    description: 'AT SpotlightVideo Inset with Rebrand',
    featureFlags: { 'gap-colors-2022': false, 'at-redesign-2023': true },
    story: {
      component: 'spotlightvideo-playwright',
      storyName: 'inset-with-prefix',
    },
  },
  {
    description: 'AT SpotlightVideo Full Bleed with Rebrand',
    featureFlags: { 'gap-colors-2022': false, 'at-redesign-2023': true },
    story: {
      component: 'spotlightvideo-playwright',
      storyName: 'fullbleed-vertical-center-center',
    },
  },
  {
    useArgs: true,
    description: 'AT SpotlightVideo Full Bleed Right',
    featureFlags: { 'gap-colors-2022': false, 'at-redesign-2023': true },
    story: {
      component: 'spotlightvideo-playwright',
      storyName: 'fullbleed-vertical-center-center',
    },
  },
];

test.describe('SpotlightVideo', () => {
  vrTests.forEach(({ description, featureFlags, story, useArgs }) => {
    test.skip(`Tests for ${brand} ${description} content-type`, async ({ page }, testInfo) => {
      const { component, storyName } = story;
      let args;

      if (useArgs) {
        if (testInfo.project.name === IPHONE_SAFARI || testInfo.project.name === ANDROID_CHROME) {
          args = 'data.content.mobileContentJustification:right';
        } else {
          args = 'data.content.contentJustification:right';
        }
      }

      await page.route('**/*vimeo*', route => route.abort());
      await page.goto(
        getStoryUrl({
          args,
          brand,
          featureFlags,
          story: {
            subCategory: 'content-types',
            component,
            storyName,
          },
        })
      );
      await page.waitForLoadState('networkidle');
      await expect(page).toHaveScreenshot({
        fullPage: true,
        scale: 'css',
      });
    });
  });
});
