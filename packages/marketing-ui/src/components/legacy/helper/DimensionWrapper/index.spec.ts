// @ts-nocheck
/**
 * @jest-environment jsdom
 */

import { isHeightValid, parsePixelValue } from '.';

describe('invoke isHeightValid function', () => {
  afterAll(() => {
    // @ts-expect-error
    global.CSS = {
      supports: jest.fn(() => true),
    };
  });

  it('should return false if CSS.supports returns false', () => {
    // @ts-expect-error
    global.CSS = {
      supports: jest.fn(() => false),
    };

    expect(isHeightValid('200')).toBeFalse();
  });
  it('should return true if CSS.supports returns true', () => {
    // @ts-expect-error
    global.CSS = {
      supports: jest.fn(() => true),
    };

    expect(isHeightValid('200')).toBeTrue();
  });

  it("should return false if CSS.supports is undefined (in case it's SSR)", () => {
    global.CSS = {
      // @ts-expect-error
      supports: undefined,
    };

    expect(isHeightValid('200')).toBeFalse();
  });
});

describe('invoke parsePixelValue function', () => {
  it('should parse as pixels when it is a string of number', () => {
    const string = parsePixelValue('200');

    expect(string).toBe('200px');
  });
  it('should parse as pixels when it is a string with px', () => {
    const string = parsePixelValue('200px');

    expect(string).toBe('200px');
  });
  it('should parse as VW when it is a string with VW', () => {
    const string = parsePixelValue('200vw');

    expect(string).toBe('200vw');
  });
});
