// @ts-nocheck
import React from 'react';
import { render } from '@testing-library/react';
import wrapWithFallbackContentErrorBoundary from '../FallbackContentErrorBoundary';

describe('FallbackContentErrorBoundary', () => {
  const ErrorComponent = () => {
    throw new Error();
  };
  it('should render fallback content when there is an error', () => {
    const fallbackContentText = 'Fallback Content';
    const WrappedComponent = wrapWithFallbackContentErrorBoundary(ErrorComponent);
    const fallbackContent = <div className='fallbackContentClass'>Fallback Content</div>;
    const wrapper = render(<WrappedComponent fallbackContent={fallbackContent} />);

    const fallbackEl = wrapper.getByText(`${fallbackContentText}`);
    expect(fallbackEl).toBeInTheDocument();
  });
});
