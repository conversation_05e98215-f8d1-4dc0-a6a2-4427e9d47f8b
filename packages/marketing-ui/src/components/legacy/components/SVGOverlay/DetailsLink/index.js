// @ts-nocheck
'use client';
import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Modal } from '@ecom-next/core/legacy/modal';
import { wcdTracking } from '../../../helper/wcdTracking';

class DetailsLink extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalEnabled: false,
    };
    this.toggleModal = this.toggleModal.bind(this);
  }

  toggleModal(trackingId) {
    this.setState(prevState => ({
      modalEnabled: !prevState.modalEnabled,
    }));

    if (trackingId && this.state.modalEnabled) {
      wcdTracking(trackingId);
    }
  }

  render() {
    const { content, style, isLarge, modalIframeStyle } = this.props;
    const normalTextStyle = {}; // defaults to black
    const linkTextStyle = {};

    if (isLarge) {
      normalTextStyle.color = content.normalTextColor;
      linkTextStyle.color = content.linkTextColor;
    }

    return (
      <Fragment>
        <Modal
          closeButtonAriaLabel={content.closeModalAriaLabel}
          isOpen={this.state.modalEnabled}
          modalSize={content.modalSize}
          onClose={this.toggleModal}
          title=''
        >
          <div className='sds_pd'>
            <iframe
              className='mkt-svg-overlay__iframe'
              src={content.url}
              style={isLarge ? modalIframeStyle.desktop : modalIframeStyle.mobile}
              title={content.linkText}
            />
          </div>
        </Modal>
        <div className='mkt-svg-overlay__details-link' style={isLarge ? style.desktop : style.mobile}>
          <a href='#' onClick={() => this.toggleModal(content.tid)} role='button'>
            <span className='mkt-svg-overlay__details-link__normal-text sds_font--primary' style={normalTextStyle}>
              {content.normalText}
            </span>
            <span className='mkt-svg-overlay__details-link__link-text sds_font--primary' style={linkTextStyle}>
              {content.linkText}
            </span>
          </a>
        </div>
      </Fragment>
    );
  }
}

DetailsLink.defaultProps = {
  content: {},
  style: {},
  modalIframeStyle: {},
};

DetailsLink.propTypes = {
  content: PropTypes.shape({
    closeModalAriaLabel: PropTypes.string.isRequired,
    linkText: PropTypes.string.isRequired,
    linkTextColor: PropTypes.string,
    modalSize: PropTypes.oneOf(['mini', 'standard', 'max']),
    normalText: PropTypes.string,
    normalTextColor: PropTypes.string,
    tid: PropTypes.string,
    url: PropTypes.string.isRequired,
  }).isRequired,
  isLarge: PropTypes.bool.isRequired,
  modalIframeStyle: PropTypes.shape({
    desktop: PropTypes.objectOf(PropTypes.string),
    mobile: PropTypes.objectOf(PropTypes.string),
  }),
  style: PropTypes.shape({
    desktop: PropTypes.objectOf(PropTypes.string),
    mobile: PropTypes.objectOf(PropTypes.string),
  }),
};

export default DetailsLink;
