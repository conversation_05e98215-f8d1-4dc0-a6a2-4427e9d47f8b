// @ts-nocheck
import React from 'react';
import { SMALL } from '@ecom-next/core/breakpoint-provider';
import { render, screen, RenderOptions, act } from 'test-utils';
import LinkWithModal from '.';
import { LinkWithModalType } from './types';
import { wcdTracking } from '../../helper/wcdTracking';
import { DefaultModal } from './components/DefaultModal';

jest.mock('../../helper/wcdTracking', () => ({
  wcdTracking: jest.fn(),
}));

const ariaLabelText = 'Close';

const renderLinkWithModal = (props: LinkWithModalType, options: RenderOptions = {}): void => {
  render(<LinkWithModal {...props} />, options);
};

describe('<LinkWithModal />', () => {
  describe('link', () => {
    it('should match snapshot', () => {
      const props = {
        className: 'testClass',
        target: 'https://google.com',
        to: 'https://google.com',
        linkText: 'Link Button',
      };
      const { container } = render(<LinkWithModal {...props} />, {
        breakpoint: SMALL,
      });
      expect(container).toMatchSnapshot();
    });

    it('should be a simple link if there is no type', () => {
      const props = {};
      renderLinkWithModal(props, { breakpoint: SMALL });
      const anchor = screen.getAllByRole('link');
      expect(anchor).toHaveLength(1);
    });

    it('should link to url passed', () => {
      const url = 'www.test.com';
      const props = {
        to: url,
      };
      renderLinkWithModal(props, { breakpoint: SMALL });
      const anchor = screen.getByRole('link');
      expect(anchor).toHaveAttribute('href', url);
    });

    it('should have same classname as the one that is passed', () => {
      const someClass = 'so classy';
      const props = {
        className: someClass,
      };
      renderLinkWithModal(props, { breakpoint: SMALL });
      const anchor = screen.getByRole('link');
      expect(anchor).toHaveClass(someClass);
    });

    it('should have children passed', () => {
      const someChildren = <span>test</span>;
      const props = {};
      render(<LinkWithModal {...props}>{someChildren}</LinkWithModal>, {
        breakpoint: SMALL,
      });
      expect(screen.getByText('test')).toBeInTheDocument();
    });

    it('should execute wcdTracking when link is clicked', async () => {
      const someTrackingId = '12345';
      const props = { tid: someTrackingId };
      renderLinkWithModal(props, { breakpoint: SMALL });
      const anchor = screen.getByRole('link');
      await act(async () => {
        anchor.click();
      });
      expect(wcdTracking).toHaveBeenCalledWith(someTrackingId);
    });
    it('should pass closeButtonAriaButton property to Modal', async () => {
      const props = {
        modalCloseButtonAriaLabel: ariaLabelText,
        type: 'modal',
        linkText: 'click me',
      };
      renderLinkWithModal(props, { breakpoint: SMALL });
      const btn = screen.getByText('click me');
      await act(async () => {
        btn.click();
      });
      const ariaLabel = screen.getByLabelText(ariaLabelText);
      expect(ariaLabel).toBeInTheDocument();
    });
  });
  describe('modal', () => {
    it('should have a modal component if type is modal', async () => {
      const props = {
        modalCloseButtonAriaLabel: ariaLabelText,
        type: 'modal',
        linkText: 'click me',
      };
      renderLinkWithModal(props, { breakpoint: SMALL });
      const btn = screen.getByText('click me');
      await act(async () => {
        btn.click();
      });
      const modal = screen.getByTestId('isolationLayer');
      expect(modal).toBeInTheDocument();
    });

    it('should pass isOpen property to modal', () => {
      const openProps = {
        modalCloseButtonAriaLabel: ariaLabelText,
        type: 'modal',
        isOpen: true,
      };
      renderLinkWithModal(openProps, { breakpoint: SMALL });
      expect(screen.getByTestId('isolationLayer')).toBeInTheDocument();
    });

    describe('default modal', () => {
      it('should match snapshot', () => {
        const props = {
          modalCloseButtonAriaLabel: ariaLabelText,
          type: 'modal',
          url: 'https://google.com',
        };
        const { container } = render(<LinkWithModal {...props} />, {
          breakpoint: SMALL,
        });
        expect(container).toMatchSnapshot();
      });
      it('should have a default modal component', () => {
        const props = {
          modalCloseButtonAriaLabel: ariaLabelText,
          type: 'modal',
          isOpen: true,
        };
        renderLinkWithModal(props, { breakpoint: SMALL });
        const modal = screen.getByTestId('default-modal');
        expect(modal).toBeDefined();
      });
      it('should pass toggleModal property to modal', async () => {
        const toggleModal = jest.fn();
        const props = {
          modalCloseButtonAriaLabel: ariaLabelText,
          title: 'modal',
          isOpen: true,
          toggleModal,
          linkText: 'test',
          contentSrc: 'https://google.com',
        };
        render(<DefaultModal {...props} />);
        const btn = screen.getByLabelText('close');
        await act(async () => {
          btn.click();
        });
        expect(toggleModal).toHaveBeenCalled();
      });
    });

    describe('generic modal', () => {
      it('should match snapshot', () => {
        const toggleModal = jest.fn();
        const props = {
          isOpen: true,
          toggleModal,
          linkText: 'test',
          actionComponentConfig: {
            type: 'builtin',
            name: 'span',
            data: {
              components: ['This is a test'],
            },
          },
        };
        const { container } = render(<LinkWithModal {...props} />, {
          breakpoint: SMALL,
        });
        expect(container).toMatchSnapshot();
      });
      it('should have a genericModal component', () => {
        const toggleModal = jest.fn();
        const props = {
          isOpen: true,
          toggleModal,
          linkText: 'test',
          actionComponentConfig: {
            type: 'builtin',
            name: 'span',
            data: {
              components: ['This is a test'],
            },
          },
        };
        render(<LinkWithModal {...props} />, { breakpoint: SMALL });
        const modal = screen.queryByTestId('generic-modal');
        expect(modal).toBeDefined();
      });

      it("when type equal to 'other'", () => {
        const toggleModal = jest.fn();
        const props = {
          isOpen: true,
          toggleModal,
          linkText: 'test',
          type: 'other',
          actionComponentConfig: {
            type: 'builtin',
            name: 'span',
            data: {
              components: ['This is a test'],
            },
          },
        };
        render(<LinkWithModal {...props} />, { breakpoint: SMALL });
        const modal = screen.queryByTestId('generic-modal');
        expect(modal).toBeDefined();
      });
    });
  });

  afterAll(() => {
    jest.resetAllMocks();
  });
});
