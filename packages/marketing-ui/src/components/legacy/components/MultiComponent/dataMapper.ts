// @ts-nocheck
'use client';
import { ComponentSize, Data, IsSmallType } from './types';

export const mapAttribute = (data: boolean | ComponentSize | undefined, isSmall: IsSmallType, defaultValue: string | boolean) => {
  if (data === undefined) {
    return defaultValue;
  }
  if (typeof data === 'string' || typeof data === 'boolean') {
    return data !== '' ? data : defaultValue;
  }
  const { small, large } = data;
  const value = isSmall ? small : large;
  return typeof value !== 'undefined' ? value : defaultValue;
};

export default (data: Data, isSmall: IsSmallType) => {
  const { components: pageComponentsData, componentDefaultHeight, marginTop, marginBottom } = data;
  return {
    pageComponentsData: Array.isArray(pageComponentsData) && pageComponentsData.map(component => component.output || component),
    componentDefaultHeight: mapAttribute(componentDefaultHeight, isSmall, '300px'),
    marginTop: mapAttribute(marginTop, isSmall, '0px'),
    marginBottom: mapAttribute(marginBottom, isSmall, '0px'),
  };
};
