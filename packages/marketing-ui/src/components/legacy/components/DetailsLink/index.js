// @ts-nocheck
'use client';
/* 
  ATTENTION: THIS COMPONENT HAS BEEN DEPRECATED!
  PLEASE USE 'DetailsLinkWithPrefix'.
*/

import React from 'react';
import PropTypes from 'prop-types';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { Modal } from '@ecom-next/core/legacy/modal';
import { compose } from '@ecom-next/core/legacy/utility';
import { styled } from '@ecom-next/core/react-stitch';
import { wcdTracking } from '../TextOverlay/utils';
import wrapWithFallbackContentErrorBoundary from '../../helper/FallbackContentErrorBoundary';

const IframeContainer = styled.div(({ theme }) => ({
  padding: theme.spacing.medium,
}));

const Iframe = styled.iframe({
  width: '100%',
  border: 'none',
  minHeight: 300,
  height: 'auto',
});

const DetailsLinkContainer = styled.div(({ containerClass }) => {
  if (!containerClass) {
    return {
      '@media (min-width: 768px)': {
        position: 'absolute',
        zIndex: 10,
        fontSize: '0.75rem',
      },
    };
  }
  return null;
});

const NormalText = styled.span(({ hasCustomClass, theme }) => {
  if (!hasCustomClass) {
    return {
      ...theme.font.primary,
      marginRight: '0.4em',
    };
  }
  return null;
});

const LinkText = styled.span((hasCustomClass, theme) => {
  if (!hasCustomClass) {
    return {
      ...theme.font.primary,
      textDecoration: 'underline',
    };
  }
  return null;
});

/* eslint class-methods-use-this: ["error", { "exceptMethods": ["trackLink"] }] */
export class DetailsLink extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalEnabled: false,
    };
    this.toggleModal = this.toggleModal.bind(this);
    this.trackLink = this.trackLink.bind(this);
  }

  toggleModal(trackingId) {
    this.setState(prevState => ({
      modalEnabled: !prevState.modalEnabled,
    }));

    if (this.state.modalEnabled) {
      this.trackLink(trackingId);
    }
  }

  trackLink(trackingId) {
    if (trackingId) {
      wcdTracking(trackingId);
    }
  }

  render() {
    const { data, isLarge } = this.props;
    const { content, style = {}, containerClass } = data;
    const { modalCloseButtonAriaLabel, normalText = {}, linkText, tid = '', url, type = '', modalSize, target } = content;

    return (
      <React.Fragment>
        <Modal closeButtonAriaLabel={modalCloseButtonAriaLabel} isOpen={this.state.modalEnabled} modalSize={modalSize} onClose={this.toggleModal} title=''>
          <IframeContainer>
            <Iframe src={url} title={linkText.label} />
          </IframeContainer>
        </Modal>
        <DetailsLinkContainer hasCustomClass={containerClass} style={isLarge ? style.desktop : style.mobile}>
          {normalText && normalText.label ? (
            <NormalText css={isLarge ? normalText.style.desktop : normalText.style.mobile} hasCustomClass={normalText.textClass}>
              {normalText.label}
            </NormalText>
          ) : null}
          {type && type === 'modal' ? (
            <LinkText
              hasCustomClass={linkText.textClass}
              href='true'
              onClick={() => this.toggleModal(tid)}
              role='button'
              style={isLarge ? linkText.style.desktop : linkText.style.mobile}
            >
              {linkText.label}
            </LinkText>
          ) : (
            <LinkText
              data-testid='noModalLink'
              hasCustomClass={linkText.textClass}
              onClick={() => this.trackLink(tid)}
              style={isLarge ? linkText.style.desktop : linkText.style.mobile}
              target={target}
              to={url}
            >
              {linkText.label}
            </LinkText>
          )}
        </DetailsLinkContainer>
      </React.Fragment>
    );
  }
}

const DetailsLinkWithBreakpointProvider = props => (
  <BreakpointContext.Consumer>
    {({ greaterOrEqualTo }) => (greaterOrEqualTo(LARGE) ? <DetailsLink {...props} isLarge /> : <DetailsLink {...props} isLarge={false} />)}
  </BreakpointContext.Consumer>
);

DetailsLink.defaultProps = {
  data: {
    style: {
      mobile: {},
      desktop: {},
    },
    containerClass: '',
    modalSize: '',
  },
};

DetailsLink.propTypes = {
  data: PropTypes.shape({
    containerClass: PropTypes.string,
    content: PropTypes.shape({
      linkText: PropTypes.shape({
        label: PropTypes.string.isRequired,
        style: PropTypes.shape({
          desktop: PropTypes.objectOf(PropTypes.string),
          mobile: PropTypes.objectOf(PropTypes.string),
        }),
        textClass: PropTypes.string,
      }).isRequired,
      modalCloseButtonAriaLabel: PropTypes.string.isRequired,
      modalSize: PropTypes.string,
      normalText: PropTypes.shape({
        label: PropTypes.string,
        style: PropTypes.shape({
          desktop: PropTypes.objectOf(PropTypes.string),
          mobile: PropTypes.objectOf(PropTypes.string),
        }),
        textClass: PropTypes.string,
      }),
      target: PropTypes.string,
      tid: PropTypes.string,
      type: PropTypes.string,
      url: PropTypes.string.isRequired,
    }).isRequired,
    style: PropTypes.shape({
      desktop: PropTypes.objectOf(PropTypes.string),
      mobile: PropTypes.objectOf(PropTypes.string),
    }),
  }).isRequired,
  isLarge: PropTypes.bool.isRequired,
};

export default compose(wrapWithFallbackContentErrorBoundary)(DetailsLinkWithBreakpointProvider);
