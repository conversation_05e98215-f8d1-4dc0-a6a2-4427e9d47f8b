// @ts-nocheck
import { CarouselProps } from '../types';
import { mockCarouselBase, mockCarouselWithBreakpointsHideOnDesktop, mockCarouselWithBreakpointsHideOnMobile } from '../__fixtures__';
import { filteredCarouselDataArray } from './filteredCarouselDataArray';

describe('filteredCarouselDataArray', () => {
  test('backwards compatible and should return 3 slides on desktop', () => {
    const tileProps = mockCarouselBase.components as CarouselProps['components'];
    const isLarge = true;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);
    expect(componentsFilteredArray.length).toEqual(3);
  });
  test('backwards compatible and should return 3 slides on mobile', () => {
    const tileProps = mockCarouselBase.components as CarouselProps['components'];
    const isLarge = false;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);
    expect(componentsFilteredArray.length).toEqual(3);
  });

  test('should return 2 slides for desktop', () => {
    const tileProps = mockCarouselWithBreakpointsHideOnMobile.components as CarouselProps['components'];
    const isLarge = true;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);

    expect(componentsFilteredArray.length).toEqual(2);
  });

  test('should return 0 slides for mobile', () => {
    const tileProps = mockCarouselWithBreakpointsHideOnMobile.components as CarouselProps['components'];
    const isLarge = false;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);

    expect(componentsFilteredArray.length).toEqual(0);
  });

  test('should return 2 slides for mobile', () => {
    const tileProps = mockCarouselWithBreakpointsHideOnDesktop.components as CarouselProps['components'];
    const isLarge = false;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);

    expect(componentsFilteredArray.length).toEqual(2);
  });

  test('should return 0 slides for desktop', () => {
    const tileProps = mockCarouselWithBreakpointsHideOnDesktop.components as CarouselProps['components'];
    const isLarge = true;
    const componentsFilteredArray = filteredCarouselDataArray(tileProps, isLarge);

    expect(componentsFilteredArray.length).toEqual(0);
  });
});
