// @ts-nocheck
import React from 'react';
import { render, screen, act } from 'test-utils';
import RedpointPlaceholder, { RedpointPlaceholderProps } from '.';

const defaultProps: RedpointPlaceholderProps = {
  type: 'sitewide',
  name: 'RedpointPlaceholder',
  instanceName: 'redpoint-experiment',
};
describe('RedpointPlaceholder', () => {
  test('it should render redpoint-placeholder div when redpointExperimentRunning flag is true ', () => {
    const props: RedpointPlaceholderProps = {
      ...defaultProps,
      redpointExperimentRunning: true,
    };
    render(<RedpointPlaceholder {...props} />);
    expect(screen.getByTestId('redpoint-placeholder')).toBeDefined();
  });

  test("it shouldn't render redpoint-placeholder div when redpointExperimentRunning flag is false ", () => {
    const props: RedpointPlaceholderProps = {
      ...defaultProps,
      redpointExperimentRunning: false,
    };
    render(<RedpointPlaceholder {...props} />);
    const elm = screen.queryByTestId('redpoint-placeholder');
    expect(elm).not.toBeInTheDocument();
  });
});
