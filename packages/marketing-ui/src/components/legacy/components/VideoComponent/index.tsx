'use client';
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import ReactPlayerBase from 'react-player/lazy';
import dynamic from 'next/dynamic';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { CSSObject, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { ColorTheme } from '@ecom-next/core/legacy/icons';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { withAnalytics } from '@ecom-next/core/legacy/analytics';
import { compose } from '@ecom-next/core/legacy/utility';
import { mapDataToProps } from '../../helper';
import { PlayerCustomControls } from './components';
import { VideoComponentProps } from './types';
import { localTranslations } from './components/localTranslations';

export const VideoPlayerContext = createContext({
  useCustomPlayIcon: false,
  useCustomPlayIconSrc: '',
  useCustomPauseIcon: false,
  useCustomPauseIconSrc: '',
});

export const useVideoPlayerContext = () => useContext(VideoPlayerContext);

// @ts-ignore
const ReactPlayer = dynamic(() => import('@mui/components/video-wrapper'), { ssr: false });
// @ts-ignore
ReactPlayer.canPlay = ReactPlayerBase.canPlay;
export const VideoComponent = ({
  containerStyle,
  controlColor,
  customControls: isCustomControls = false,
  customControlStyles,
  config,
  controls,
  fallbackImage,
  forceAsReady: initialReadyToPlay = false,
  onVideoEnded,
  onVideoReady,
  playerStyles,
  playerWrapperStyle,
  playIcon,
  url,
  desktopUrl,
  playing,
  playOnHover = false,
  pauseOnHover = false,
  playsinline,
  playInLoop,
  muted,
  light,
  hideMuteControl = false,
  disableCoreUiControlIcons = false,
  useCustomPlayIcon = false,
  useCustomPlayIconSrc = '',
  useCustomPauseIconSrc = '',
  useCustomPauseIcon = false,
  videoContainerAbsolute = false,
  videoIndex,
  controlsIconsColor,
  onVideoLoadError,
  ...playerProps
}: VideoComponentProps): JSX.Element => {
  const initialPlaySetting: boolean = playing || false;
  const initialControlsSetting: boolean = controls || false;
  const playVideoInLoop: boolean = playInLoop || false;
  const enabledFeatures = useEnabledFeatures();
  const { locale } = useAppState();
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const isLarge = greaterOrEqualTo(LARGE);
  const playButton = playIcon && <img alt={playIcon.alt} src={playIcon.src} />;
  const videoUrl = desktopUrl && isLarge ? desktopUrl : url;
  const fallbackImageSrc = fallbackImage?.desktopSrc && isLarge ? fallbackImage.desktopSrc : fallbackImage?.src;
  /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
  // @ts-ignore url type for method incorrect react-player types
  const isValidUrl = ReactPlayer.canPlay(videoUrl);
  const [hasVideoError, setVideoError] = useState(false);
  const [playState, setPlayState] = useState(initialPlaySetting);
  const [readyToPlay, setReadyToPlay] = useState(initialReadyToPlay);
  const [onBuffer, setOnBuffer] = useState(false);
  const [volumeState, setVolumeState] = useState<number>(0);
  const isPlayIconOrLightNotPresent: boolean = playIcon === undefined && light === undefined && fallbackImage?.src === undefined;
  const muteOnAutoplay = (isPlayIconOrLightNotPresent && playing && true) || null;
  const initialMuteSetting = (muted === undefined && true) || muted;
  const [muteState, setMuteState] = useState<boolean>(muteOnAutoplay || isSelfHostedVideoOrPlayIconEnableMute() || initialMuteSetting);
  const colorTheme = (controlsIconsColor === 'secondary' ? 'light' : 'dark') as ColorTheme;
  const muiVideoFallbackImage2025Enabled = Boolean(enabledFeatures['mui-video-poster-image-2025']);
  const configWithOverridablePosterImage = {
    file: {
      attributes: {
        poster: fallbackImageSrc,
        ...config?.file?.attributes,
      },
      ...config?.file,
    },
    ...config,
  } as const;
  const playerCss = {
    ...playerStyles,
    '&& video': {
      backgroundImage: fallbackImageSrc ? `url('${fallbackImageSrc}')` : 'none',
      backgroundSize: 'cover',
      height: '100%',
      objectFit: 'cover' as const,
      width: '100%',
    },
    zIndex: 0,
  } as const;

  function isSelfHostedVideoOrPlayIconEnableMute(): boolean | null {
    const isPlayIcon = playIcon !== undefined && playIcon?.src.length > 0;
    const isLight = light !== undefined || typeof light === 'boolean' || typeof light === 'string';
    const isPlayingAndNotMuted = playing !== undefined && playing === true && (muted === undefined || muted === false);
    const checkIfPosterOrBtnExist = isPlayIcon || isLight;
    const isPlayingDefined = playing !== undefined && playing === true;

    if (checkIfPosterOrBtnExist && isPlayingAndNotMuted) {
      return muted !== undefined && muted;
    }
    if (checkIfPosterOrBtnExist && isPlayingDefined) {
      return true;
    }
    return null;
  }

  const volumeAfterMuteIsAtZeroRef = useRef<number>(0.13);

  // @ts-ignore
  const playerRef = useRef<ReactPlayer>(null);

  useEffect(() => {
    const isMutedDefined = muted !== undefined || null;
    if (!isCustomControls && isMutedDefined) {
      if (muted !== undefined) setMuteState(muted);
    }
    if (muteOnAutoplay) setMuteState(true);
  }, [muted, muteOnAutoplay]);

  useEffect(() => {
    setPlayState(initialPlaySetting);
  }, [initialPlaySetting]);

  useEffect(() => {
    if (!muteState && volumeState === 0) {
      setVolumeState(0.13);
    }
  }, [muteState, volumeState]);

  useEffect(() => {
    if (hideMuteControl) {
      setMuteState(true);
      setVolumeState(0);
    }
  }, [hideMuteControl]);

  // Give Fallback Image Fetch Priority
  useEffect(() => {
    const preloadLink = document.createElement('link');

    if (fallbackImageSrc) {
      preloadLink.rel = 'preload';
      preloadLink.as = 'image';
      preloadLink.fetchPriority = 'high';
      preloadLink.href = fallbackImageSrc;

      document.head.appendChild(preloadLink);
    }
    return () => {
      if (document.head.contains(preloadLink)) {
        document.head.removeChild(preloadLink);
      }
    };
  }, [fallbackImageSrc]);

  const handlePause = (action?: string) => {
    readyToPlay && setPlayState(false);
    if (!playInLoop && pauseOnHover && action === 'clicked-pause-button') {
      onVideoEnded && onVideoEnded(videoIndex as number, false, false);
    }
  };

  const handlePlay = (action?: string) => {
    readyToPlay && setPlayState(true);
    if (!playInLoop && playOnHover && action === 'clicked-play-button') {
      onVideoEnded && onVideoEnded(videoIndex as number, true, false);
    }
  };

  const handleMute = () => {
    readyToPlay && setMuteState(!muteState);
  };

  const handleVolume = (e: React.ChangeEvent<HTMLInputElement>) => {
    readyToPlay && setVolumeState(Number(e.currentTarget.value) as number);
    if ((Number(e.currentTarget.value) as number) === 0) setMuteState(true);
    if ((Number(e.currentTarget.value) as number) > 0.0) setMuteState(false);
  };

  const isPlayingInline = playState ? true : playsinline;
  !isValidUrl && console.warn(`[MUI Warn - VideoComponent] ${videoUrl} is not valid`);

  const customControlsCSS: CSSObject = {
    width: 'fit-content',
    position: 'relative',
  };

  const handlePlayOnHover = () => {
    if (playOnHover && playInLoop) {
      handlePlay();
    }
    if (!playInLoop && playOnHover) {
      onVideoEnded && onVideoEnded(videoIndex as number, true, false);
    }
  };

  const handlePauseOnHover = () => {
    if (pauseOnHover && playInLoop) {
      handlePause();
    }
    if (!playInLoop && pauseOnHover && playState) {
      onVideoEnded && onVideoEnded(videoIndex as number, false, true);
    }
  };

  const handleEnded = () => {
    if (readyToPlay) {
      setPlayState(false);
      onVideoEnded && onVideoEnded(videoIndex as number);
    }
  };

  return (
    <VideoPlayerContext.Provider
      value={{
        useCustomPlayIcon,
        useCustomPlayIconSrc,
        useCustomPauseIconSrc,
        useCustomPauseIcon,
      }}
    >
      <div
        css={[
          isCustomControls ? customControlsCSS : {},
          isLarge ? containerStyle?.desktop : containerStyle?.mobile,
          videoContainerAbsolute ? { position: 'absolute', height: '100%' } : {},
          controlColor?.setMaxHeight ? { height: playerProps.height } : {},
        ]}
        data-testid='videocomponent-container'
        onMouseEnter={handlePlayOnHover}
        onMouseLeave={handlePauseOnHover}
      >
        {hasVideoError || !isValidUrl ? (
          <img
            alt={fallbackImage?.alt ?? 'video not available'}
            css={{
              height: '100%',
              objectFit: 'cover',
              width: '100%',
            }}
            src={fallbackImageSrc}
          />
        ) : (
          <>
            <div style={{ position: 'relative', zIndex: 0 }}>
              {fallbackImageSrc && (
                <img
                  fetchPriority='high'
                  src={fallbackImageSrc}
                  css={{
                    height: '100%',
                    left: 0,
                    objectFit: 'cover',
                    position: 'absolute',
                    top: 0,
                    width: '100%',
                    zIndex: -1,
                  }}
                />
              )}

              <ReactPlayer
                playerRef={playerRef}
                loop={playVideoInLoop}
                {...playerProps}
                // @ts-ignore
                config={muiVideoFallbackImage2025Enabled ? configWithOverridablePosterImage : config}
                controls={isCustomControls ? false : initialControlsSetting}
                css={playerCss}
                light={light}
                muted={muteState}
                onBuffer={() => setOnBuffer(true)}
                onBufferEnd={() => setOnBuffer(false)}
                onEnded={handleEnded}
                onError={() => {
                  setVideoError(true);
                  onVideoLoadError && onVideoLoadError(true);
                }}
                onPause={handlePause}
                onPlay={handlePlay}
                onReady={() => {
                  setReadyToPlay(true);
                  onVideoReady && onVideoReady(playerRef);
                }}
                playIcon={playButton}
                playing={playState}
                playsinline={isPlayingInline}
                url={videoUrl}
                volume={!muteState && volumeState === 0 ? volumeAfterMuteIsAtZeroRef.current : volumeState}
              />
            </div>
            {readyToPlay && (
              // @ts-ignore
              <LocalizationProvider locale={locale} translations={localTranslations[normalizeLocale(locale)].translation} supportNesting>
                <PlayerCustomControls
                  colorTheme={colorTheme}
                  controlColor={controlColor}
                  customControlStyles={customControlStyles}
                  disableCoreUiControlIcons={disableCoreUiControlIcons}
                  handleMute={handleMute}
                  handlePause={() => handlePause('clicked-pause-button')}
                  handlePlay={() => handlePlay('clicked-play-button')}
                  handleVolume={e => handleVolume(e)}
                  hideMuteControl={hideMuteControl}
                  isCustomControls={isCustomControls}
                  isPlaying={playState}
                  muted={muteState}
                  onBuffer={onBuffer}
                  readyToPlay={readyToPlay}
                />
              </LocalizationProvider>
            )}
          </>
        )}
      </div>
    </VideoPlayerContext.Provider>
  );
};

export default compose(withAnalytics, mapDataToProps)(VideoComponent);
export type { VideoComponentProps } from './types';
