// @ts-nocheck
'use client';
import React from 'react';
import README from '../README.mdx';
import { ButtonDropdown } from '..';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';

const withPadding = Story => (
  <div data-testid='buttondropdownroot' style={{ padding: '1rem' }}>
    <Story />
  </div>
);

const baseProps = {
  startsExpanded: false,
  heading: { text: 'Shop Halloween styles' },
  id: 'sampleID',
  submenu: [
    {
      href: 'girls',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Girls',
      trackingId: 'abc_123_1',
    },
    {
      href: 'boys',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Boys',
      trackingId: 'abc_123_1',
    },
    {
      href: 'toddler girl',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Toddler girl',
      trackingId: 'abc_123_1',
    },
    {
      href: 'toddler boy',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Toddler boy',
      trackingId: 'abc_123_1',
    },
    {
      href: 'baby boy',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Baby boy',
      trackingId: 'abc_123_1',
    },
  ],
  buttonStyle: {
    style: {},
    desktopStyle: {},
  },
  submenuItemStyles: {
    style: {},
    desktopStyle: {},
  },
  submenuListStyles: {
    style: {},
    desktopStyle: {},
  },
  customClasses: '',
};

const openProp = {
  startsExpanded: true,
  id: '2ndID',
};

export default {
  title: 'Common/JSON Components (Marketing)/ButtonDropdown',
  decorators: [withPadding],
  tags: ['autodocs'],
  parameters: {
    docs: {
      page: README,
    },
  },
  component: ButtonDropdown,
};

const ButtonDropdownTemplate = args => (
  <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
    <ButtonDropdown {...args} />
    <div style={{ height: '20px' }} />
    <ButtonDropdown {...args} {...openProp} />
  </OnCtaRedesign2024Context.Provider>
);

export const Default = ButtonDropdownTemplate.bind({});
Default.args = { ...baseProps };

Default.storyName = 'default';
