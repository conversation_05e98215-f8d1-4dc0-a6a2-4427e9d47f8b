// @ts-nocheck
'use client';
import { GetCertonaRecsProps, GetCertonaDataSchemeItem } from '../types';

export const parseNumberOfProducts = ({ numProducts }: Pick<GetCertonaRecsProps, 'numProducts'>) => {
  const numProductsToNumber = Number(numProducts);
  if (!numProductsToNumber) return undefined;

  return numProductsToNumber;
};

export const getRecommendations = ({ schemes, scheme, numProducts }: GetCertonaRecsProps): GetCertonaDataSchemeItem[] => {
  if (!Array.isArray(schemes) || schemes.length === 0) {
    return [];
  }

  const schemeItems = schemes.find(sch => scheme === sch.scheme)?.items || [];

  return schemeItems.slice(0, numProducts).map((product: GetCertonaDataSchemeItem) => product);
};
