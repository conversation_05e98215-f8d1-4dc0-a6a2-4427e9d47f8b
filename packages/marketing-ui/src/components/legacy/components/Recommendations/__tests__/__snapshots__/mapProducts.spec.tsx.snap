// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Recommendations/> mapRecommendations function correctly maps recommendations 1`] = `
[
  {
    "asFragment": [Function],
    "baseElement": .emotion-0 {
  max-width: 256px;
  display: inline-block;
  width: auto;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-3 {
  position: relative;
}

.emotion-5 {
  margin-top: 0.5rem;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<body>
      <div>
        <div
          style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
        >
          <div
            class="emotion-0"
            data-testid="recommended-product-card"
          >
            <div
              class="emotion-1"
            >
              <a
                href="/browse/product.do?pid=495965002&rrec=true&mlink=5001,1,home_scheme_0&clink=1"
                target="_self"
              >
                <div
                  class="emotion-2"
                >
                  <div
                    class="emotion-3"
                  >
                    <img
                      alt="Jeans"
                      aria-hidden="true"
                      class="emotion-4"
                      src="/webcontent/0017/612/471/cn17612471.jpg"
                    />
                  </div>
                  <div
                    class="emotion-5"
                  >
                    <p
                      class="emotion-2"
                    >
                      Jeans
                    </p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
        >
          <div
            class="emotion-0"
            data-testid="recommended-product-card"
          >
            <div
              class="emotion-1"
            >
              <a
                href="/browse/product.do?pid=491343002&rrec=true&mlink=5001,1,home_scheme_1&clink=1"
                target="_self"
              >
                <div
                  class="emotion-2"
                >
                  <div
                    class="emotion-3"
                  >
                    <img
                      alt="Pants"
                      aria-hidden="true"
                      class="emotion-4"
                      src="/webcontent/0017/365/846/cn17365846.jpg"
                    />
                  </div>
                  <div
                    class="emotion-5"
                  >
                    <p
                      class="emotion-2"
                    >
                      Pants
                    </p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </body>,
    "container": .emotion-0 {
  max-width: 256px;
  display: inline-block;
  width: auto;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-3 {
  position: relative;
}

.emotion-5 {
  margin-top: 0.5rem;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<div>
      <div
        style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
          data-testid="recommended-product-card"
        >
          <div
            class="emotion-1"
          >
            <a
              href="/browse/product.do?pid=495965002&rrec=true&mlink=5001,1,home_scheme_0&clink=1"
              target="_self"
            >
              <div
                class="emotion-2"
              >
                <div
                  class="emotion-3"
                >
                  <img
                    alt="Jeans"
                    aria-hidden="true"
                    class="emotion-4"
                    src="/webcontent/0017/612/471/cn17612471.jpg"
                  />
                </div>
                <div
                  class="emotion-5"
                >
                  <p
                    class="emotion-2"
                  >
                    Jeans
                  </p>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>,
    "debug": [Function],
    "findAllByAltText": [Function],
    "findAllByDisplayValue": [Function],
    "findAllByLabelText": [Function],
    "findAllByPlaceholderText": [Function],
    "findAllByRole": [Function],
    "findAllByTestId": [Function],
    "findAllByText": [Function],
    "findAllByTitle": [Function],
    "findByAltText": [Function],
    "findByDisplayValue": [Function],
    "findByLabelText": [Function],
    "findByPlaceholderText": [Function],
    "findByRole": [Function],
    "findByTestId": [Function],
    "findByText": [Function],
    "findByTitle": [Function],
    "getAllByAltText": [Function],
    "getAllByDisplayValue": [Function],
    "getAllByLabelText": [Function],
    "getAllByPlaceholderText": [Function],
    "getAllByRole": [Function],
    "getAllByTestId": [Function],
    "getAllByText": [Function],
    "getAllByTitle": [Function],
    "getByAltText": [Function],
    "getByDisplayValue": [Function],
    "getByLabelText": [Function],
    "getByPlaceholderText": [Function],
    "getByRole": [Function],
    "getByTestId": [Function],
    "getByText": [Function],
    "getByTitle": [Function],
    "queryAllByAltText": [Function],
    "queryAllByDisplayValue": [Function],
    "queryAllByLabelText": [Function],
    "queryAllByPlaceholderText": [Function],
    "queryAllByRole": [Function],
    "queryAllByTestId": [Function],
    "queryAllByText": [Function],
    "queryAllByTitle": [Function],
    "queryByAltText": [Function],
    "queryByDisplayValue": [Function],
    "queryByLabelText": [Function],
    "queryByPlaceholderText": [Function],
    "queryByRole": [Function],
    "queryByTestId": [Function],
    "queryByText": [Function],
    "queryByTitle": [Function],
    "rerender": [Function],
    "unmount": [Function],
  },
  {
    "asFragment": [Function],
    "baseElement": .emotion-0 {
  max-width: 256px;
  display: inline-block;
  width: auto;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-3 {
  position: relative;
}

.emotion-5 {
  margin-top: 0.5rem;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<body>
      <div>
        <div
          style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
        >
          <div
            class="emotion-0"
            data-testid="recommended-product-card"
          >
            <div
              class="emotion-1"
            >
              <a
                href="/browse/product.do?pid=495965002&rrec=true&mlink=5001,1,home_scheme_0&clink=1"
                target="_self"
              >
                <div
                  class="emotion-2"
                >
                  <div
                    class="emotion-3"
                  >
                    <img
                      alt="Jeans"
                      aria-hidden="true"
                      class="emotion-4"
                      src="/webcontent/0017/612/471/cn17612471.jpg"
                    />
                  </div>
                  <div
                    class="emotion-5"
                  >
                    <p
                      class="emotion-2"
                    >
                      Jeans
                    </p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
        >
          <div
            class="emotion-0"
            data-testid="recommended-product-card"
          >
            <div
              class="emotion-1"
            >
              <a
                href="/browse/product.do?pid=491343002&rrec=true&mlink=5001,1,home_scheme_1&clink=1"
                target="_self"
              >
                <div
                  class="emotion-2"
                >
                  <div
                    class="emotion-3"
                  >
                    <img
                      alt="Pants"
                      aria-hidden="true"
                      class="emotion-4"
                      src="/webcontent/0017/365/846/cn17365846.jpg"
                    />
                  </div>
                  <div
                    class="emotion-5"
                  >
                    <p
                      class="emotion-2"
                    >
                      Pants
                    </p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </body>,
    "container": .emotion-0 {
  max-width: 256px;
  display: inline-block;
  width: auto;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-3 {
  position: relative;
}

.emotion-5 {
  margin-top: 0.5rem;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<div>
      <div
        style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
          data-testid="recommended-product-card"
        >
          <div
            class="emotion-1"
          >
            <a
              href="/browse/product.do?pid=491343002&rrec=true&mlink=5001,1,home_scheme_1&clink=1"
              target="_self"
            >
              <div
                class="emotion-2"
              >
                <div
                  class="emotion-3"
                >
                  <img
                    alt="Pants"
                    aria-hidden="true"
                    class="emotion-4"
                    src="/webcontent/0017/365/846/cn17365846.jpg"
                  />
                </div>
                <div
                  class="emotion-5"
                >
                  <p
                    class="emotion-2"
                  >
                    Pants
                  </p>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>,
    "debug": [Function],
    "findAllByAltText": [Function],
    "findAllByDisplayValue": [Function],
    "findAllByLabelText": [Function],
    "findAllByPlaceholderText": [Function],
    "findAllByRole": [Function],
    "findAllByTestId": [Function],
    "findAllByText": [Function],
    "findAllByTitle": [Function],
    "findByAltText": [Function],
    "findByDisplayValue": [Function],
    "findByLabelText": [Function],
    "findByPlaceholderText": [Function],
    "findByRole": [Function],
    "findByTestId": [Function],
    "findByText": [Function],
    "findByTitle": [Function],
    "getAllByAltText": [Function],
    "getAllByDisplayValue": [Function],
    "getAllByLabelText": [Function],
    "getAllByPlaceholderText": [Function],
    "getAllByRole": [Function],
    "getAllByTestId": [Function],
    "getAllByText": [Function],
    "getAllByTitle": [Function],
    "getByAltText": [Function],
    "getByDisplayValue": [Function],
    "getByLabelText": [Function],
    "getByPlaceholderText": [Function],
    "getByRole": [Function],
    "getByTestId": [Function],
    "getByText": [Function],
    "getByTitle": [Function],
    "queryAllByAltText": [Function],
    "queryAllByDisplayValue": [Function],
    "queryAllByLabelText": [Function],
    "queryAllByPlaceholderText": [Function],
    "queryAllByRole": [Function],
    "queryAllByTestId": [Function],
    "queryAllByText": [Function],
    "queryAllByTitle": [Function],
    "queryByAltText": [Function],
    "queryByDisplayValue": [Function],
    "queryByLabelText": [Function],
    "queryByPlaceholderText": [Function],
    "queryByRole": [Function],
    "queryByTestId": [Function],
    "queryByText": [Function],
    "queryByTitle": [Function],
    "rerender": [Function],
    "unmount": [Function],
  },
]
`;
