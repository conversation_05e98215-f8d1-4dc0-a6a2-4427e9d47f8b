// @ts-nocheck
import { LayoutDataProps } from '../types';

export const baseComponentData: LayoutDataProps = {
  mobile: {
    shouldDisplay: true,
    data: {
      style: {
        flexDirection: 'column',
        outline: '1px dashed blue',
      },
      components: [
        {
          name: 'LayoutComponent',
          type: 'sitewide',
          tileStyle: {
            mobile: {
              outline: '1px unset green',
              width: '100%',
            },
            desktop: {},
          },
          data: {
            mobile: {
              shouldDisplay: true,
              data: {
                components: [
                  {
                    name: 'LayoutComponent',
                    type: 'sitewide',
                    tileStyle: {
                      mobile: {
                        outline: '1px unset yellow',
                        width: '100%',
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    },
  },
  desktop: {
    shouldDisplay: true,
    data: {
      style: {
        flexDirection: 'column',
        outline: '1px unset red',
      },
      components: [
        {
          name: 'LayoutComponent',
          type: 'sitewide',
          tileStyle: {
            desktop: {
              outline: '1px dashed orange',
              width: '100%',
            },
            mobile: {},
          },
          data: {
            desktop: {
              shouldDisplay: true,
              data: {
                components: [
                  {
                    name: 'LayoutComponent',
                    type: 'sitewide',
                    tileStyle: {
                      desktop: {
                        outline: '1px dashed purple',
                        width: '100%',
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    },
  },
};

export const desktopAndMobileData: LayoutDataProps = {
  desktopAndMobile: {
    shouldDisplay: true,
    data: {
      style: {
        flexDirection: 'column',
        outline: '1px groove azure',
      },
      components: [
        {
          name: 'LayoutComponent',
          type: 'sitewide',
          tileStyle: {
            desktop: {
              outline: '5px solid aquamarine',
              width: '100%',
            },
            mobile: {
              outline: '10px solid lemonchiffon',
              width: '100%',
            },
          },
          data: {
            desktop: {
              shouldDisplay: true,
              data: {
                components: [
                  {
                    name: 'LayoutComponent',
                    type: 'sitewide',
                    tileStyle: {
                      desktop: {
                        outline: '1px dashed purple',
                        width: '100%',
                      },
                    },
                  },
                ],
              },
            },
            mobile: {
              shouldDisplay: true,
              data: {
                components: [
                  {
                    name: 'LayoutComponent',
                    type: 'sitewide',
                    tileStyle: {
                      mobile: {
                        outline: '10px dashed purple',
                        width: '100%',
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    },
  },
};

const fallbackContentComponent = (): JSX.Element => jest.fn() as any;

export const baseComponentDataFallback = {
  fallbackContent: {
    data: {
      components: [fallbackContentComponent()],
    },
  },
};
