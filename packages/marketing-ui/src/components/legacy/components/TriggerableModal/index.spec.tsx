// @ts-nocheck
import React, { ReactNode, useContext } from 'react';
import { render, screen, fireEvent, act } from 'test-utils';
import { Modal } from '@ecom-next/core/legacy/modal';
import TriggerableModal, { TriggerableModalContext } from '.';

jest.mock('../LayoutComponent');

const layoutChildClass = 'bob';
const content = {
  className: layoutChildClass,
  desktopAndMobile: {
    shouldDisplay: true,
    data: {
      className: layoutChildClass,
      style: {},
      components: [
        {
          name: 'TextHeadline',
          type: 'sitewide',
          data: {
            text: 'BUILT FOR THE EXTREME. DESIGNED FOR EVERYDAY. SOURCED RESPONSIBLY. WELCOME TO HILL CITY.',
            isVisible: {
              large: true,
            },
            className: {
              desktop: 'desktopCLASS',
              mobile: 'mobileCLASS',
              desktopAndMobile: 'desktopAndMobileCLASS',
            },
          },
        },
      ],
    },
  },
};

const TestButton = () => {
  const context = useContext(TriggerableModalContext);
  return <button onClick={() => context?.openModal(content)}>CLICK ME!</button>;
};

describe('TriggerableModal', () => {
  describe('will display children', () => {
    it('when given children directly', () => {
      const childClassname = 'layoutChildClass';
      const ariaLabelText = 'Close';
      const { container } = render(
        <TriggerableModal
          data={{
            modalCloseButtonAriaLabel: ariaLabelText,
          }}
        >
          <button className={childClassname}>CLICK ME</button>
        </TriggerableModal>
      );

      expect(container.getElementsByClassName(childClassname)).toHaveLength(1);
    });
  });

  it('passes the aria label text to Modal', async () => {
    const ariaLabelText = 'Close';
    render(
      <TriggerableModal
        data={{
          modalCloseButtonAriaLabel: ariaLabelText,
          layoutData: content,
          title: 'Test Modal',
        }}
      >
        <TestButton />
      </TriggerableModal>
    );

    expect(screen.queryByLabelText(ariaLabelText)).not.toBeInTheDocument();
    await act(async () => {
      fireEvent.click(screen.getByRole('button'));
    });
    screen.debug();
    expect(screen.getByLabelText(ariaLabelText)).toBeInTheDocument();
  });
});
