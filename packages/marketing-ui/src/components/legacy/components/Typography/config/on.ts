// @ts-nocheck
'use client';
import { BrandTypographyConfigFactory } from '../types';

// (fontSize * (tracking / 100)) / 10

const config: BrandTypographyConfigFactory = () => ({
  eyebrow1: {
    fontSize: [23, 28],
    lineHeight: [23, 28],
    minFontSize: [12, 12],
    letterSpacing: [0.92, 1.12],
  },
  eyebrow2: {
    fontSize: [19, 23],
    lineHeight: [19, 23],
    minFontSize: [12, 12],
    letterSpacing: [0.76, 0.92],
  },
  eyebrow3: {
    fontSize: [14, 17],
    lineHeight: [15, 17],
    minFontSize: [12, 12],
    letterSpacing: [0.56, 0.68],
  },
  headline1: {
    fontSize: [68, 118],
    lineHeight: [68, 118],
    minFontSize: [20, 24],
  },
  headline2: {
    fontSize: [50, 80],
    lineHeight: [50, 80],
    minFontSize: [20, 24],
  },
  headline3: {
    fontSize: [43, 72],
    lineHeight: [43, 72],
    minFontSize: [20, 24],
  },
  headline4: {
    fontSize: [36, 64],
    lineHeight: [36, 64],
    minFontSize: [20, 24],
    letterSpacing: [0.72, 1.28],
  },
  headline5: {
    fontSize: [28, 48],
    lineHeight: [28, 48],
    minFontSize: [20, 24],
    letterSpacing: [1.12, 2.88],
  },
  headline6: {
    fontSize: [24, 40],
    lineHeight: [26, 44],
    minFontSize: [20, 24],
    letterSpacing: [0.96, 2.4],
  },
  headline7: {
    fontSize: [20, 32],
    lineHeight: [20, 32],
    minFontSize: [20, 24],
    letterSpacing: [0.48, 1.28],
  },
  headlineAlt1: {
    fontSize: [64, 114],
    lineHeight: [64, 114],
    letterSpacing: [-0.3, -0.4],
    minFontSize: [13, 14],
  },
  headlineAlt2: {
    fontSize: [54, 84],
    lineHeight: [54, 84],
    letterSpacing: [-0.3, -0.4],
    minFontSize: [13, 14],
  },
  headlineAlt3: {
    fontSize: [34, 64],
    lineHeight: [34, 64],
    letterSpacing: [-0.3, -0.4],
    minFontSize: [13, 14],
  },
  headlineAlt4: {
    fontSize: [24, 34],
    lineHeight: [34, 34],
    letterSpacing: [-0.3, -0.4],
    minFontSize: [13, 14],
  },
  subhead1: {
    fontSize: [20, 24],
    lineHeight: [30, 36],
    minFontSize: [16, 20],
  },
  subhead2: {
    fontSize: [18, 22],
    lineHeight: [27, 33],
    minFontSize: [16, 20],
  },
  subhead3: {
    fontSize: [16, 20],
    lineHeight: [24, 30],
    minFontSize: [16, 20],
  },
  promo1: {
    fontSize: [60, 100],
    lineHeight: [60, 100],
    letterSpacing: [0, 0],
    minFontSize: [13, 14],
  },
  promo2: {
    fontSize: [40, 80],
    lineHeight: [40, 80],
    letterSpacing: [0, 0],
    minFontSize: [13, 14],
  },
  body1: {
    fontSize: [14, 18],
    lineHeight: [21, 27],
    minFontSize: [10, 10],
    letterSpacing: [0.336, 0.72],
  },
  body2: {
    fontSize: [12, 16],
    lineHeight: [18, 24],
    minFontSize: [10, 10],
    letterSpacing: [0.288, 0.64],
  },
  body3: {
    fontSize: [12, 14],
    lineHeight: [18, 21],
    minFontSize: [10, 10],
    letterSpacing: [0.288, 0.56],
  },
  body4: {
    fontSize: [10, 12],
    lineHeight: [15, 18],
    minFontSize: [10, 10],
  },
  body5: {
    fontSize: [10, 10],
    lineHeight: [15, 15],
    minFontSize: [10, 10],
  },
  F2: {
    fontSize: [11, 12],
    lineHeight: [14, 16],
  },
  F1: {
    fontSize: [11, 12],
    lineHeight: [14, 16],
  },
  F0: {
    fontSize: [11, 12],
    lineHeight: [14, 16],
  },
  FN1: {
    fontSize: [11, 12],
    lineHeight: [14, 16],
  },
});

export default config;
