// @ts-nocheck
'use client';
import React from 'react';
// Emotion methods, brand themes and and many common stitch styles come from react-stitch.
// More info: https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/common-reactstitch--default
import { CSSObject, styled, useTheme } from '@ecom-next/core/react-stitch';
import { TemplateProps } from './types';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';

const ComponentTemplate = (props: TemplateProps): JSX.Element => {
  const theme = useTheme();
  const isMobile = !useViewportIsLarge();

  const divStyles: CSSObject = isMobile
    ? {
        color: theme.color.b1,
      }
    : {
        color: theme.color.b2,
      };

  const StyledDiv = styled.div({
    ...theme.brandFont,
    fontWeight: 600,
    ...divStyles,
  });

  return (
    <StyledDiv>
      <p>{props.message}</p>
    </StyledDiv>
  );
};

export default ComponentTemplate;
