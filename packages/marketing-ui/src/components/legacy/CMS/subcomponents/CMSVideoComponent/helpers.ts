// @ts-nocheck
'use client';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { useMemo } from 'react';
import { ReactPlayerProps } from 'react-player';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import { encodeAdvanceImage } from '../../helpers/ImageEncoder';
import { VideoData, VimeoVideoPartialData } from '../../types/amplience';
import { CMSVideoComponentProps } from './types';

export const defaultPlaying = (isDesktop: boolean, video: VimeoVideoPartialData | undefined): boolean =>
  video ? Boolean(getReactPlayerProps(video, isDesktop).playing) : false;

export const getReactPlayerProps = ({ desktop, mobile }: CMSVideoComponentProps['video'], isDesktop: boolean, posterImageUrl?: string): ReactPlayerProps => {
  let urlString = '';
  if (!isDesktop && mobile?.url) {
    urlString = mobile.url;
  } else if (desktop?.url) {
    urlString = desktop?.url;
  }

  let url: URL;
  try {
    url = new URL(urlString);
  } catch {
    return {};
  }

  const hasParam = (paramName: string): boolean | undefined => {
    const paramValue = url.searchParams.get(paramName);
    if (paramValue === null) return undefined;
    return paramValue === '1';
  };

  return {
    url: url.toString(),
    playing: hasParam('autoplay'),
    controls: hasParam('controls'),
    loop: hasParam('loop'),
    muted: hasParam('muted'),
    playsinline: hasParam('playsinline'),
    config: {
      vimeo: {
        playerOptions: {
          autopause: hasParam('autopause'),
          byline: hasParam('byline'),
          portrait: hasParam('portrait'),
          title: hasParam('title'),
          responsive: hasParam('responsive'),
        },
      },
      file: {
        attributes: {
          poster: posterImageUrl,
        },
      },
    },
  };
};

const getImageData = ({ desktop, mobile }: CMSVideoComponentProps['video'], isDesktop: boolean): ImageData => {
  let fallbackImage: VideoData['fallbackImage'] = [];

  fallbackImage = !isDesktop && mobile?.fallbackImage ? mobile?.fallbackImage : desktop?.fallbackImage;

  if (!fallbackImage || fallbackImage.length === 0) return {};
  return {
    src: encodeAdvanceImage(fallbackImage[0], isDesktop ? 'desktop' : 'mobile', {})!,
    alt: fallbackImage[0].altText || '',
  };
};

type ImageData = { alt?: string; src?: string };

type VideoImageData = [ReactPlayerProps, ImageData];

export function useVideoImageData(video: CMSVideoComponentProps['video']): VideoImageData {
  const isDesktop = useViewportIsLarge();
  const enabledFeatures = useEnabledFeatures();
  const muiVideoFallbackImage2025Enabled = Boolean(enabledFeatures['mui-video-poster-image-2025']);

  const getVideoImageData = (): VideoImageData => {
    const imageSrc = getImageData(video, isDesktop);
    const reactPlayerProps = muiVideoFallbackImage2025Enabled ? getReactPlayerProps(video, isDesktop, imageSrc?.src) : getReactPlayerProps(video, isDesktop);

    return [reactPlayerProps, imageSrc];
  };

  return useMemo(getVideoImageData, [video, isDesktop, muiVideoFallbackImage2025Enabled]);
}
