// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { ReactPlayerProps } from 'react-player';
import { NonVimeoVideoControlColors, NonVimeoVideoPartialData, VimeoVideoPartialData } from '../../types/amplience';

export type CMSVideoComponentProps = Omit<ReactPlayerProps, 'playIcon'> & {
  forceVideoAsReady?: boolean;
  playIcon?: {
    alt: string;
    src: string;
  };
  fallbackImageStyles?: CSSObject;
  exposeVideoState?: (state: ReactPlayerProps) => void;
  video: VimeoVideoPartialData | NonVimeoVideoPartialData;
  customControls?: boolean;
  customControlStyles?: CSSObject;
  hideMuteControl?: boolean;
  playInLoop?: boolean;
  playOnHover?: boolean;
  pauseOnHover?: boolean;
  controlColor?: NonVimeoVideoControlColors;
  videoContainerAbsolute?: boolean;
  onVideoEnded?: (videoIndex: number, onMouseHover?: boolean, onMouseLeave?: boolean) => void;
  onVideoReady?: (ref: ReactPlayerProps) => void;
  videoIndex?: number;
  onVideoLoadError?: (error: boolean) => void;
};
