// @ts-nocheck
import { Brand<PERSON>, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { BreakpointProvider, LARGE, Size, XLARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { fireEvent, render, RenderOptions, screen, act } from 'test-utils';
import React from 'react';
import { DetailsButton, getDetailsContent } from './index';
import { DetailLinkProps } from './types';

const LABEL = 'Details';
const LABEL_WITH_ASTERISK = '*Details';
const LABEL_WITH_AT_SIGN = '@Details';
const COLOR = '#f23e95';
const PEMOLE_CODE = '560053';
const HTML_MODAL_URL = 'https://www.gap.com/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0';
const PEMOLE_MODAL_URL = getDetailsContent(Brands.Athleta, PEMOLE_CODE);

const renderDetailsButtonForBrand = ({
  brand = Brands.Gap,
  labelText = LABEL,
  prefixText,
  value = PEMOLE_MODAL_URL,
  color = COLOR,
  prefixColor,
  options,
  useBaseStylesOnlyForBrands = [],
  customStyles = {},
  detailsPrefixWrapperStyles = {},
  css = {},
}: DetailLinkProps) =>
  render(
    <DetailsButton
      color={color}
      prefixColor={prefixColor}
      label={labelText}
      prefix={prefixText}
      value={value}
      useBaseStylesOnlyForBrands={useBaseStylesOnlyForBrands}
      customStyles={customStyles}
      detailsPrefixWrapperStyles={detailsPrefixWrapperStyles}
      css={css}
    />,
    {
      brand,
      ...options,
    }
  );

describe('Details Button', () => {
  describe('should match snapshots', () => {
    [LARGE, SMALL].forEach((size: Size) => {
      [Brands.Gap, Brands.GapFactoryStore, Brands.Athleta, Brands.OldNavy].forEach(brand => {
        it(`for ${brand} with a prefix label on viewport size ${size}`, () => {
          const { container } = renderDetailsButtonForBrand({
            brand,
            prefixText: 'Exclusions Apply',
            options: {
              breakpoint: size,
            },
          });
          expect(container).toMatchSnapshot();
        });
        it(`for ${brand} without a prefix label on viewport size ${size}`, () => {
          const { container } = renderDetailsButtonForBrand({
            brand,
            options: {
              breakpoint: size,
            },
          });
          expect(container).toMatchSnapshot();
        });
      });
    });
  });

  it('should render a button', () => {
    renderDetailsButtonForBrand({});
    const button = screen.getByRole('button', { name: 'Details' });
    expect(button).toBeInTheDocument();
  });

  it('should open a modal when clicked', async () => {
    renderDetailsButtonForBrand({});
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const modal = screen.getByRole('button', { name: /close modal/i });
    expect(modal).toBeInTheDocument();
  });

  describe('athleta configurable colors', () => {
    it('should render white color for the details link by default when no color is passed', () => {
      renderDetailsButtonForBrand({ brand: Brands.Athleta, color: null });
      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).toHaveStyleRule('color', '#FFFFFF');
    });

    it('should render the color for the details link that is passed to the component', () => {
      renderDetailsButtonForBrand({ brand: Brands.Athleta, color: 'green' });
      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).toHaveStyleRule('color', 'green');
    });

    it('should render white color for the prefix label by default when no color or prefixColor passed', () => {
      const { container } = renderDetailsButtonForBrand({ brand: Brands.Athleta, color: null, prefixColor: null, prefixText: 'prefix' });
      const span = container.querySelector('span');
      expect(span).toHaveStyleRule('color', '#FFFFFF');
    });

    it('should render font color for the prefix label color is passed in with no prefixColor', () => {
      const { container } = renderDetailsButtonForBrand({ brand: Brands.Athleta, prefixColor: undefined, prefixText: 'prefix' });
      const span = container.querySelector('span');
      expect(span).toHaveStyleRule('color', '#f23e95');
    });

    it('should render the color for the prefix label that is passed to the component', () => {
      const { container } = renderDetailsButtonForBrand({ brand: Brands.Athleta, prefixColor: 'purple', prefixText: 'prefix' });
      const span = container.querySelector('span');
      expect(span).toHaveStyleRule('color', 'purple');
    });
  });

  describe('useBaseStylesOnlyForBrands', () => {
    const atTestProps = {
      brand: Brands.Athleta,
      prefixText: 'prefix',
      customStyles: { margin: 75 },
      detailsPrefixWrapperStyles: { padding: 100 },
      css: { gap: 200 },
      useBaseStylesOnlyForBrands: [Brands.Athleta],
    };

    it('should ignore any custom style overrides and only use the brands base styles for a brand passed into the array', () => {
      const { container } = renderDetailsButtonForBrand(atTestProps);

      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).not.toHaveStyleRule('margin', '75');
      expect(button).not.toHaveStyleRule('gap', '200');

      const span = container.querySelector('span');
      expect(span).not.toHaveStyleRule('margin', 75);

      expect(span.parentElement).not.toHaveStyleRule('padding', '100');

      expect(container).toMatchSnapshot();
    });

    it('should use custom style overrides when the brand being rendered is not in the array', () => {
      const { container } = renderDetailsButtonForBrand({ ...atTestProps, useBaseStylesOnlyForBrands: ['br'] });

      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).not.toHaveStyleRule('margin', '75');
      expect(button).not.toHaveStyleRule('gap', '200');

      const span = container.querySelector('span');
      expect(span).not.toHaveStyleRule('margin', 75);

      expect(span.parentElement).not.toHaveStyleRule('padding', '100');

      expect(container).toMatchSnapshot();
    });

    it('should use custom style overrides when the prop is empty', () => {
      const { container } = renderDetailsButtonForBrand({ ...atTestProps, useBaseStylesOnlyForBrands: [] });

      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).not.toHaveStyleRule('margin', '75');
      expect(button).not.toHaveStyleRule('gap', '200');

      const span = container.querySelector('span');
      expect(span).not.toHaveStyleRule('margin', 75);

      expect(span.parentElement).not.toHaveStyleRule('padding', '100');

      expect(container).toMatchSnapshot();
    });

    it('should use custom style overrides when the prop is not passed in', () => {
      const newTestProps = { ...atTestProps };
      delete atTestProps.useBaseStylesOnlyForBrands;
      const { container } = renderDetailsButtonForBrand({ ...newTestProps });

      const button = screen.getByRole('button', { name: 'Details' });
      expect(button).not.toHaveStyleRule('margin', '75');
      expect(button).not.toHaveStyleRule('gap', '200');

      const span = container.querySelector('span');
      expect(span).not.toHaveStyleRule('margin', 75);

      expect(span.parentElement).not.toHaveStyleRule('padding', '100');

      expect(container).toMatchSnapshot();
    });
  });

  describe('label contains', () => {
    it('an asterisk, it has no styles', () => {
      const { container } = renderDetailsButtonForBrand({
        labelText: LABEL_WITH_ASTERISK,
      });
      const detailsButtonSpan = container.querySelector('button > span');
      expect(detailsButtonSpan).toHaveStyleRules({});
    });

    it('a special character other than an asterisk, it is styled with an underline', () => {
      const { container } = renderDetailsButtonForBrand({
        labelText: LABEL_WITH_AT_SIGN,
      });
      const detailsButton = container.querySelector('button');
      expect(detailsButton).toHaveStyleRules({ 'text-decoration': 'underline' });
    });
  });
});

describe('Details Button modal', () => {
  it('should return pemole url when provided', async () => {
    renderDetailsButtonForBrand({ value: PEMOLE_MODAL_URL });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringContaining(PEMOLE_MODAL_URL));
  });

  it('should return html url when provided', async () => {
    renderDetailsButtonForBrand({ value: HTML_MODAL_URL });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringContaining(HTML_MODAL_URL));
  });

  it('should return html url when pemole and html are provided', async () => {
    const modalURL = getDetailsContent(Brands.Athleta, PEMOLE_CODE, HTML_MODAL_URL);
    renderDetailsButtonForBrand({ value: modalURL });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringContaining(HTML_MODAL_URL));
  });

  it('should get value with locale', async () => {
    renderDetailsButtonForBrand({ options: { appState: { locale: 'fr-CA' } } });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringMatching('&locale=fr_CA'));
  });

  it('should default to en_US if undefined', async () => {
    renderDetailsButtonForBrand({ options: { appState: { locale: undefined } } });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringContaining('&locale=en_US'));
  });

  it('should not include locale if value is not http link', async () => {
    renderDetailsButtonForBrand({
      value: '123456',
      options: { appState: { locale: 'en_US' } },
    });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.not.stringContaining('&locale='));
  });

  it('should include locale if a pemole code is provided', async () => {
    renderDetailsButtonForBrand({ options: { appState: { locale: 'en_US' } } });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.stringContaining('&locale='));
  });

  it('should not include locale if a pemole code is NOT provided', async () => {
    renderDetailsButtonForBrand({
      value: HTML_MODAL_URL,
      options: { appState: { locale: 'en_US' } },
    });
    const button = screen.getByRole('button', { name: 'Details' });
    await act(async () => {
      fireEvent.click(button);
    });
    const iframe = screen.getByTestId('iframeModal');
    expect(iframe).toHaveAttribute('src', expect.not.stringContaining('&locale='));
  });
});

describe('DetailsButton brand specific styles', () => {
  it('should render the label exactly as provided for Gap (no text-transform applied)', () => {
    renderDetailsButtonForBrand({
      brand: Brands.Gap,
    });

    const button = screen.getByRole('button', { name: 'Details' });
    expect(button).toBeInTheDocument();
  });

  it('should render the prefix exactly as provided for Gap (no text-transform applied)', () => {
    const prefixText = 'exclusions apply';
    const { container } = renderDetailsButtonForBrand({
      brand: Brands.Gap,
      prefixText,
    });

    const prefixElement = container.querySelector('span');
    expect(prefixElement).toBeInTheDocument();
    expect(prefixElement).toHaveTextContent(prefixText);
  });

  it('should render the label exactly as provided for GapFactoryStore (no text-transform applied)', () => {
    renderDetailsButtonForBrand({
      brand: Brands.GapFactoryStore,
    });

    const button = screen.getByRole('button', { name: 'Details' });
    expect(button).toBeInTheDocument();
  });

  it('should render the prefix exactly as provided for GapFactoryStore (no text-transform applied)', () => {
    const prefixText = 'exclusions apply';
    const { container } = renderDetailsButtonForBrand({
      brand: Brands.GapFactoryStore,
      prefixText,
    });

    const prefixElement = container.querySelector('span');
    expect(prefixElement).toBeInTheDocument();
    expect(prefixElement).toHaveTextContent(prefixText);
  });

  it("should have 'none' text-transform for Banana Republic", () => {
    renderDetailsButtonForBrand({
      brand: Brands.BananaRepublic,
    });
    const button = screen.getByRole('button');
    expect(button).toHaveStyle('text-transform: none');
  });

  it("should have 'capitalize' text-transform for Old Navy", () => {
    renderDetailsButtonForBrand({
      brand: Brands.OldNavy,
    });
    const button = screen.getByRole('button');
    expect(button).toHaveStyle('text-transform: capitalize');
  });

  it("should have 'none' text-transform for Athleta", () => {
    renderDetailsButtonForBrand({
      brand: Brands.Athleta,
    });
    const button = screen.getByRole('button');
    expect(button).toHaveStyle('text-transform: none');
  });

  it("should have prefix styled correctly for Banana Republic with 'none'", () => {
    const prefixText = 'exclusions apply';
    const { container } = renderDetailsButtonForBrand({
      brand: Brands.BananaRepublic,
      prefixText,
    });
    const prefixElement = container.querySelector('span');
    expect(prefixElement).toHaveStyle('text-transform: none');
  });
});

describe('getDetailsContent', () => {
  const PEMOLE_CODE = '560053';
  const HTML_MODAL_URL = 'https://www.gap.com/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0';

  it('should return the htmlModalUrl if provided', () => {
    const result = getDetailsContent(Brands.Gap, PEMOLE_CODE, HTML_MODAL_URL);
    expect(result).toBe(HTML_MODAL_URL);
  });

  it('should return the correct pemole URL for Athleta brand', () => {
    const result = getDetailsContent(Brands.Athleta, PEMOLE_CODE);
    expect(result).toBe(`https://secure-athleta.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${PEMOLE_CODE}`);
  });

  it('should return the correct pemole URL for Old Navy brand', () => {
    const result = getDetailsContent(Brands.OldNavy, PEMOLE_CODE);
    expect(result).toBe(`https://secure-oldnavy.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${PEMOLE_CODE}`);
  });

  it('should return the correct pemole URL for Banana Republic brand', () => {
    const result = getDetailsContent(Brands.BananaRepublic, PEMOLE_CODE);
    expect(result).toBe(`https://secure-bananarepublic.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${PEMOLE_CODE}`);
  });

  it('should return the correct pemole URL for Gap brand', () => {
    const result = getDetailsContent(Brands.Gap, PEMOLE_CODE);
    expect(result).toBe(`https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${PEMOLE_CODE}`);
  });

  it('should return the correct pemole URL for GapFS brand', () => {
    const result = getDetailsContent(Brands.GapFactoryStore, PEMOLE_CODE);
    expect(result).toBe(`https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${PEMOLE_CODE}`);
  });
});
