// @ts-nocheck
'use client';
import React from 'react';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { RichTextWrapper } from './components/RichTextWrapper';
import { RichTextProps, isTextValue } from './types';
import HTMLInjectionComponent from '../../../components/HTMLInjectionComponent';
import { useViewportIsLarge } from '../../../hooks';

const parseText = ({
  text,
  isDesktop,
  disableTextAlign,
  disabledFontWeight,
}: Pick<RichTextProps, 'text' | 'disableTextAlign' | 'disabledFontWeight' | 'isDesktop'>): string | null => {
  let richText = '';
  if (isTextValue(text)) {
    const { defaultText, mobileOverride } = text;
    if ((isDesktop || !mobileOverride) && defaultText) {
      richText = defaultText;
    } else if (!isDesktop && mobileOverride) {
      richText = mobileOverride;
    }
  } else if (typeof text === 'string') {
    richText = text;
  } else {
    return null;
  }

  if (disableTextAlign) richText = richText.replace(/text-align:\s{0,1}(center|left|right);/g, '');
  if (disabledFontWeight) richText = richText.replace(/\s*font-weight[^(;|")]+/g, '');
  richText = richText.replace(/style=""/g, '');

  return richText;
};

export const RichText = ({ text, disableTextAlign, disabledFontWeight, isDesktop: isDesktopProp, ...restProps }: RichTextProps): JSX.Element | null => {
  const isDesktopState = useViewportIsLarge();
  const isDesktop = isDesktopProp !== undefined ? isDesktopProp : isDesktopState;
  const enabledFeatures = useEnabledFeatures();
  const richText = parseText({
    text,
    disableTextAlign,
    disabledFontWeight,
    isDesktop,
  });

  return richText ? (
    <RichTextWrapper {...restProps} enabledFeatures={enabledFeatures} isDesktop={isDesktop}>
      <HTMLInjectionComponent data={{ html: richText }} />
    </RichTextWrapper>
  ) : null;
};
export * from './types';
