// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, act } from 'test-utils';
import { LARGE } from '@ecom-next/core/breakpoint-provider';
import { AthletaGirlLogo, AthletaGirlLogoProps } from '.';

const data: AthletaGirlLogoProps = {
  color: '#555553',
  height: '24px',
};

describe('Athleta Girl Logo Component', () => {
  it('should match snapshots', () => {
    const { container } = render(<AthletaGirlLogo {...data} />, {
      breakpoint: LARGE,
    });
    expect(container).toMatchSnapshot();
  });

  it(`should render the svg with color ${data.color}`, async () => {
    const { container } = render(<AthletaGirlLogo {...data} />, {
      breakpoint: LARGE,
    });
    const svgElement = container.querySelector('svg');
    const pathElement = svgElement?.querySelector('path');
    expect(svgElement).toBeInTheDocument();
    expect(pathElement).toHaveAttribute('fill', data.color);
  });

  it('should not have a11y violations', async () => {
    const { container } = render(<AthletaGirlLogo {...data} />, {
      breakpoint: LARGE,
    });
    expect(await axe(container)).toHaveNoViolations();
  });
});
