// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import { LinePaginationContainer } from './LinePaginationContainer';

const brands = [Brands.Athleta, Brands.Gap, Brands.OldNavy, Brands.BananaRepublic];

describe('<LinePaginationContainer />', () => {
  brands.forEach(brand => {
    test(`should match snapshot for ${brand}`, () => {
      const containaer = render(<LinePaginationContainer brand={brand} />);

      expect(containaer).toMatchSnapshot();
    });
  });
});
