// @ts-nocheck
'use client';
import React from 'react';
import { ComponentMeta, Story } from '@storybook/react';
import README from '../../CTADropdown/README.mdx';
import CTADropdownGroup, { CTADropdownGroupType } from '..';
import { defaultData } from '../__fixtures__';

const withBackground = (storyFn: () => JSX.Element) => <div css={{ padding: '10px', background: '#CCCCCC' }}>{storyFn()}</div>;

export default {
  title: 'Common/JSON Components (Marketing)/CMS/CTADropdownGroup',
  decorators: [withBackground],
  parameters: {
    docs: { page: README },
    knobs: { escapeHTML: false },
    sandbox: true,
  },
  tags: ['exclude'],
} as ComponentMeta<typeof CTADropdownGroup>;

export const Default: Story<{
  data: CTADropdownGroupType;
}> = ({ data }) => <CTADropdownGroup {...data} />;

Default.args = {
  data: defaultData,
};
