'use client';

import { CSSObject, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import React from 'react';
import { Size } from '../../../components/ComposableButton/components/index';
import { useSelectedState, useViewportIsLarge } from '../../../hooks/index';
import { CTAContainer } from '../../content-types/CTADropdown/components/CTAContainer';
import { CtaButtonStylingProps, CTADropdownItem, CTAVariant } from '../CTAButton/types';
import CTADropdown from '../CTADropdown/index';

export interface CTADropdownGroupType {
  /* eslint-disable react/require-default-props */
  className?: string;
  contentJustification?: 'left' | 'right' | 'middle';
  ctaButtonStyle?: CTAVariant;
  ctaButtonStyling?: CtaButtonStylingProps;
  ctaDropdownList: CTADropdownItem[];
  ctaSize?: Size;
  customStyles?: CSSObject;
  heading?: string;
  label?: string;
  naturalWidth?: boolean;
  useLinearLayout?: boolean; // This is used to determine the natural width of the dropdown group for styling purposes
}

export const CTADropdownGroup = ({
  label,
  ctaDropdownList,
  contentJustification,
  className,
  customStyles,
  useLinearLayout,
  ctaSize,
  ctaButtonStyling,
  naturalWidth,
}: CTADropdownGroupType) => {
  const isLargeVP = useViewportIsLarge();
  const selectedLink = useSelectedState(ctaDropdownList.map(({ ctaDropdown }) => ctaDropdown).flat());
  const dropdownListComponents = ctaDropdownList.map(({ ctaDropdown, label: ctaDropDownLabel }, i) => {
    const key = `cta-dropdown-item-${label?.toLowerCase().replace(' ', '-') ?? 'label'}-${i}`;
    return (
      <CTADropdown
        key={key}
        ctaButtonStyling={ctaButtonStyling}
        heading={ctaDropDownLabel || label}
        items={ctaDropdown}
        selected={!!ctaDropdown.find(cta => cta.value === selectedLink)}
        size={ctaSize}
        naturalWidth={naturalWidth}
      />
    );
  });

  const placementMap = {
    right: 'flex-end',
    left: 'flex-start',
    middle: 'center',
  };

  const enabledFeatures = useEnabledFeatures();
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'];

  const mobileCtaDropdownContentJustification = isOnCtaRedesign2024Enabled ? placementMap[contentJustification ?? 'middle'] : 'normal';

  if (useLinearLayout) {
    return (
      // eslint-disable-next-line react/no-unknown-property
      <div css={customStyles} data-testid='linear-layout-wrapper'>
        {dropdownListComponents}
      </div>
    );
  }

  return (
    <CTAContainer
      className={className}
      // @ts-ignore
      css={{
        justifyContent: placementMap[contentJustification ?? 'middle'],
        gap: isLargeVP ? 60 : 12,
        alignItems: !isLargeVP ? mobileCtaDropdownContentJustification : 'center',
        ...customStyles,
      }}
    >
      {dropdownListComponents}
    </CTAContainer>
  );
};

export default CTADropdownGroup;
