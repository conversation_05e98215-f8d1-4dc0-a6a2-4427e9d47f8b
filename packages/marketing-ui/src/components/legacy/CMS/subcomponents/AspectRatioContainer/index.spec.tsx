// @ts-nocheck
import React, { createRef } from 'react';
import { axe } from 'jest-axe';
import { render, act } from 'test-utils';
import { backgroundTypeImageData, backgroundTypeImageEmptyData, withoutAltText } from './test-data';
import AspectRatioContainer from '.';
import { AdvanceImageData } from '../../helpers/ImageEncoder';

const refDiv = createRef<HTMLDivElement>();

describe('<AspectRatioContainer />', () => {
  it('should not have a11y violations', async () => {
    const { container } = render(<AspectRatioContainer {...backgroundTypeImageData} ref={refDiv} />);
    expect(await axe(container)).toHaveNoViolations();
  });
  describe('role', () => {
    const imgAltText = backgroundTypeImageData.background?.images?.[0].altText || 'not-found';

    it('should have role of image if background is an image and there is no child content', () => {
      const { getByRole } = render(<AspectRatioContainer {...backgroundTypeImageData} ref={refDiv} />);
      expect(getByRole('img')).toBeInTheDocument();
    });
    it('if the role is img, it should have an aria-label with the image alt text', () => {
      const { getByAltText } = render(<AspectRatioContainer {...backgroundTypeImageData} ref={refDiv} />);
      expect(getByAltText(imgAltText)).toBeInTheDocument();
    });
    it('if the role is img and there is no image altText provided, it should have no aria-label', () => {
      const { queryByAltText } = render(<AspectRatioContainer {...withoutAltText} ref={refDiv} />);
      expect(queryByAltText(imgAltText)).toBeNull();
    });
    it('should not have role of image if there is no image data in the images array', () => {
      const text = 'text';
      const { getByText, queryByRole } = render(
        <AspectRatioContainer {...backgroundTypeImageEmptyData} ref={refDiv}>
          text
        </AspectRatioContainer>
      );
      expect(queryByRole('img')).toBeNull();
      expect(getByText(text)).toBeInTheDocument();
    });
  });
});
