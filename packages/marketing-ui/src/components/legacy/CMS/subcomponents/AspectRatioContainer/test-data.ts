// @ts-nocheck
'use client';
import { BackgroundTypeContainerProps } from '../BackgroundTypeContainer';

export const backgroundTypeImageEmptyData: BackgroundTypeContainerProps = {
  background: {
    type: 'image',
    images: [],
  },
  className: 'test-class-name',
  height: 500,
  width: 500,
  scale: 2,
};

export const backgroundTypeImageData: BackgroundTypeContainerProps = {
  background: {
    type: 'image',
    images: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5b805fe2-9bcb-442c-b7ce-afad2449edc0',
          name: 'left-nav-demo',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'alt text 1',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
      },
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5b805fe2-9bcb-442c-b7ce-afad2449edc0',
          name: 'left-nav-demo-1',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'alt text 2',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
      },
    ],
  },
  className: 'test-class-name',
  height: 500,
  width: 500,
  scale: 2,
};

export const withoutAltText: BackgroundTypeContainerProps = {
  ...backgroundTypeImageData,
  background: {
    type: 'image',
    images: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5b805fe2-9bcb-442c-b7ce-afad2449edc0',
          name: 'left-nav-demo',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
      },
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5b805fe2-9bcb-442c-b7ce-afad2449edc0',
          name: 'left-nav-demo-1',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
      },
    ],
  },
};

export const withContent: BackgroundTypeContainerProps = {
  ...backgroundTypeImageData,
};
