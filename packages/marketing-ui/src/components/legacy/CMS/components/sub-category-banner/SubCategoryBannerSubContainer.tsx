// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import useBrandValue from '../../../hooks/useBrandValue';

interface SubContainerProps {
  hasBackgroundColor: boolean;
}
export const SubCategoryBannerSubContainer = styled.div(({ hasBackgroundColor }: SubContainerProps) => {
  const isDesktop = useViewportIsLarge();
  const padding = useBrandValue(
    {
      gap: {
        padding: isDesktop ? '0px 30px' : '15px 16px 16px',
      },
      gapfs: {
        padding: isDesktop ? '0px 30px' : '15px 16px 16px',
      },
      on: {
        padding: isDesktop ? '0px 30px' : `${hasBackgroundColor ? '17px' : '25px'} 12.5px 0px`,
      },
    },
    {
      padding: isDesktop ? '0px 48px' : '25px 12.5px 0px',
    }
  );

  return {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    height: '100%',
    zIndex: 2,
    ...padding,
  };
});
