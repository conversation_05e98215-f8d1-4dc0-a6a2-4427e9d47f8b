// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { CMSVideoComponent } from '../../subcomponents/CMSVideoComponent';
import { StorytellingAndProductCollectionGenericComponentType } from './types';

export type StorytellingProductCollectionVideoProps = Required<Pick<StorytellingAndProductCollectionGenericComponentType, 'defaultVideo'>> & {
  isDesktop: boolean;
};

const VideoComponentWrapper = styled.div({
  display: 'grid',
  gridArea: 'hero-content',
  aspectRatio: '4/5',
  boxSizing: 'border-box',
  '& div': {
    boxSizing: 'border-box',
    display: 'grid',
    position: 'relative',
  },
});

export const StorytellingAndProductCollectionVideo = ({ isDesktop, defaultVideo }: StorytellingProductCollectionVideoProps): JSX.Element => (
  <VideoComponentWrapper>
    <CMSVideoComponent
      css={
        isDesktop && {
          position: 'absolute',
          zIndex: 0,
          top: '0',
          bottom: '0',
        }
      }
      role='presentation'
      video={defaultVideo}
    />
  </VideoComponentWrapper>
);
