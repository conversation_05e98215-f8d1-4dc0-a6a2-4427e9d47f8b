// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { getAllByText, render, act } from 'test-utils';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { defaultComponentData } from './__fixtures__/test-data';
import { StorytellingAndProductCollectionProductCards } from '.';

describe('StorytellingAndProductCollection product cards', () => {
  beforeEach(() => {
    console.error = jest.fn();
  });
  it('should not have a11y violations', async () => {
    const { container } = render(
      <StorytellingAndProductCollectionProductCards isDesktop productCards={defaultComponentData.storytellingProductCollection.content.productCards} />
    );
    expect(await axe(container)).toHaveNoViolations();
  });
  it('should match snapshots in Desktop', () => {
    const result = render(
      <StorytellingAndProductCollectionProductCards isDesktop productCards={defaultComponentData.storytellingProductCollection.content.productCards} />
    );
    expect(result.asFragment()).toMatchSnapshot();
  });
  it('should match snapshots in Mobile', () => {
    const result = render(
      <StorytellingAndProductCollectionProductCards isDesktop={false} productCards={defaultComponentData.storytellingProductCollection.content.productCards} />,
      { breakpoint: SMALL }
    );
    expect(result.asFragment()).toMatchSnapshot();
  });
  describe('anchor link', () => {
    const cardsArray = defaultComponentData.storytellingProductCollection.content.productCards;
    it('card image should be wrapped in a link if a url is provided', () => {
      const result = render(<StorytellingAndProductCollectionProductCards isDesktop productCards={cardsArray} />, { breakpoint: LARGE });
      const cardImage1 = result.getAllByAltText(cardsArray[0]!.cards!.image[0]!.altText!)[0];
      const cardImage2 = result.getAllByAltText(cardsArray[1]!.cards!.image[0]!.altText!)[0];
      expect(cardImage1.closest('a')).toHaveAttribute('href', cardsArray[0].cards.url!.value);
      expect(cardImage2.closest('a')).toHaveAttribute('href', cardsArray[1].cards.url!.value);
    });
  });
});
