// @ts-nocheck
'use client';
import { Interpolation, Brands, Theme, CSSObject } from '@ecom-next/core/react-stitch';
import { ShoppingBagWebAppearance } from '../../content-types/ShoppingBag/types';
import { BannerLinkType, MediaImageLink, VerticalCtaAlignment } from '../../global/types';
import { AdvanceImageData } from '../../helpers/ImageEncoder';
import { BackgroundTypeExtensionValue } from '../../types/amplience';
import { DesktopImageOrIconSizeType } from '../../subcomponents/ImageOrIcon/types';
import { CustomPlayPauseButtonProps } from '../../subcomponents/PlayPauseButton/types';
import { type CMSMarketingCarouselProps } from '../../subcomponents/CMSMarketingCarousel';

export type ShoppingBagSchema = 'https://cms.gap.com/schema/content/v1/shopping-bag.json';

export interface ShoppingBagProps {
  slide: BackgroundData;
  bannerLink?: BannerLinkType;
  ctaButton?: BannerLinkType;
  details?: DetailsData;
  height?: string;
  webAppearance: ShoppingBagWebAppearance;
  className?: string;
  currentSlide?: number;
  index?: number;
  iconMargin?: string;
  onResize?: (height: number) => void;
  verticalCtaAlignment?: VerticalCtaAlignment;
}
export interface ShoppingBagCarouselProps {
  appendDots?: (dots: JSX.Element) => JSX.Element;
  backgroundSlides: Array<BackgroundData>;
  bannerLink?: BannerLinkType;
  carouselCss?: Interpolation<Theme>;
  ctaButton?: BannerLinkType;
  playButtonSettings: CustomPlayPauseButtonProps;
  details?: DetailsData;
  verticalCtaAlignment?: VerticalCtaAlignment;
  webAppearance: ShoppingBagWebAppearance;
  width?: number;
  carouselClassName?: string;
  customPaging?: ((index: number) => JSX.Element) | undefined;
  className?: string;
  iconMargin?: string;
  wrapperCss?: CSSObject;
}

export interface DetailsData {
  detailsContent?: string;
  detailsLink?: string;
  detailsPrefix?: string;
  customStyles?: CSSObject;
}

export interface BackgroundData {
  background: BackgroundTypeExtensionValue;
  icon?: MediaImageLink | AdvanceImageData[];
  richTextArea1?: string;
  richTextArea2?: string;
}

export interface WrapperProps {
  width?: DesktopImageOrIconSizeType;
  height?: DesktopImageOrIconSizeType;
}

export interface LinePaginationContainerProps {
  paginationAlignment?: CMSMarketingCarouselProps['carouselSettings']['styling']['paginationAlignment'];
  children?: React.ReactNode;
  className?: string;
  brand: Brands;
}

export interface LinePaginationButtonProps {
  delay?: CMSMarketingCarouselProps['carouselSettings']['autoplay']['delay'];
  speed?: CMSMarketingCarouselProps['carouselSettings']['animation']['speed'];
  brand: Brands;
}
