// @ts-nocheck
'use client';
import React, { ReactNode } from 'react';
import { styled } from '@ecom-next/core/react-stitch';

export interface TextNavigationListProps {
  children: ReactNode | ReactNode[];
  minHeight?: number;
  itemGap?: number;
  header?: React.ReactNode;
  borderWidth?: number;
}

const TextNavigationListLabelContainer = styled.div<{ minHeight?: number }>(props => ({
  minHeight: props.minHeight || 'auto',
}));

const TextNavigationListSlideContainer = styled.div<{
  minHeight?: number;
  itemGap?: number;
  borderWidth?: number;
}>(props => ({
  flex: 1,
  gap: props.itemGap || 0,
  display: 'flex',
  '& > button, & > a': {
    minHeight: props.minHeight || 'auto',
    borderWidth: props.borderWidth,
    flex: '1 1 auto',
  },
  '& > button:not(:first-of-type), & > a:not(:first-of-type)': {
    borderLeftColor: !props.itemGap ? 'transparent' : undefined,
  },
}));

const TextNavigationListContainer = styled.div<{ minHeight?: number }>(props => ({
  display: 'flex',
  alignItems: 'center',
  minHeight: props.minHeight || 'auto',
}));

const TextNavigationList: React.FC<TextNavigationListProps> = props => {
  const { borderWidth, children, header, minHeight, itemGap } = props;
  return (
    <TextNavigationListContainer minHeight={minHeight}>
      {header && <TextNavigationListLabelContainer>{header}</TextNavigationListLabelContainer>}
      <TextNavigationListSlideContainer borderWidth={borderWidth} itemGap={itemGap} minHeight={minHeight}>
        {children}
      </TextNavigationListSlideContainer>
    </TextNavigationListContainer>
  );
};

export default TextNavigationList;
