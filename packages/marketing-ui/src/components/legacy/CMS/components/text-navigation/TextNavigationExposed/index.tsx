// @ts-nocheck
'use client';
import React from 'react';
import { styled, useTheme, <PERSON>s, CSSObject } from '@ecom-next/core/react-stitch';
import { Variant } from '../../../../components/ComposableButton/types';
import { RichText } from '../../../subcomponents/RichText';
import { RichTextWrapper } from '../../../subcomponents/RichText/components/RichTextWrapper';
import { useSelectedState, useViewportIsXLarge } from '../../../../hooks';
import { TextNavigationExposedButtonWrapper, TextNavigationExposedWrapper } from './components';
import TextNavigationExposedButton from './components/TextNavigationExposedButton';
import TextNavigationExposedHeader from './components/TextNavigationExposedHeader';
import { PRODUCT_GRID_CLASSNAME } from '../../../constants';

export interface TextNavigationExposedProps {
  readonly navigationItems: Array<{ label: string; value: string }>;
  readonly navigationTitle?: string;
  readonly textHeader?: string;
  readonly stacked?: boolean;
  readonly minHeight?: string | number;
  readonly backgroundColor?: string;
  readonly borderColor?: string;
  readonly fontColor?: string;
  textNavExposedHeaderCustomStyles?: CSSObject;
}

const TextNavigationExposed = ({
  navigationItems,
  navigationTitle,
  textHeader,
  stacked,
  minHeight,
  backgroundColor,
  borderColor,
  fontColor,
  textNavExposedHeaderCustomStyles,
}: TextNavigationExposedProps) => {
  const theme = useTheme();
  const isDesktop = useViewportIsXLarge();
  const selectedLink = useSelectedState(navigationItems);

  const navLabel = navigationTitle || 'Categories';
  const contentLabelToId = navigationTitle?.replace(/\W/g, '');
  const navAriaLabel = navLabel;
  const navAriaLabelledby = navLabel && contentLabelToId;

  const TextNavMobileContainer = styled.div({});
  const ctaArray = navigationItems.map((ctaData, index: number) => (
    <TextNavigationExposedButtonWrapper
      key={`${ctaData.label}-${ctaData.value}`}
      borderColor={borderColor}
      index={index}
      listLength={navigationItems.length}
      minHeight={minHeight}
      stacked={stacked}
    >
      <TextNavigationExposedButton
        aria-hidden={false}
        backgroundColor={backgroundColor}
        borderColor={borderColor}
        fontColor={fontColor}
        id={`${ctaData.label.replace(/\W/g, '')}-${index}`}
        interactiveStyles={false}
        isSelected={ctaData.value === selectedLink}
        linkProps={{
          isAJumplink: ctaData.value.startsWith('#'),
          jumplinkCSSSelector: ctaData.value.startsWith('#') ? PRODUCT_GRID_CLASSNAME : undefined,
          to: ctaData.value,
          title: ctaData.label,
          target: '_self',
        }}
        variant={
          (
            {
              [Brands.Gap]: Variant.border,
              [Brands.GapFactoryStore]: Variant.border,
              [Brands.Athleta]: Variant.border,
              [Brands.OldNavy]: Variant.border,
            } as Record<string, Variant | undefined>
          )[theme.brand]
        }
      >
        {ctaData.label}
      </TextNavigationExposedButton>
    </TextNavigationExposedButtonWrapper>
  ));

  return (
    <TextNavMobileContainer>
      <nav aria-label={navAriaLabel} aria-labelledby={navAriaLabelledby}>
        {textHeader && (
          <RichTextWrapper>
            <RichText text={textHeader} />
          </RichTextWrapper>
        )}
        {navigationTitle && (
          <TextNavigationExposedHeader id={contentLabelToId} isDesktop={isDesktop} textNavExposedHeaderCustomStyles={textNavExposedHeaderCustomStyles}>
            {navigationTitle}
          </TextNavigationExposedHeader>
        )}
        <TextNavigationExposedWrapper stacked={stacked}>{ctaArray}</TextNavigationExposedWrapper>
      </nav>
    </TextNavMobileContainer>
  );
};

export default TextNavigationExposed;
