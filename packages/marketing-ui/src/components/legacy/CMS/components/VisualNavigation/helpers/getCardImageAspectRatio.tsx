// @ts-nocheck
'use client';
import { Brands } from '@ecom-next/core/react-stitch';
import { AspectRatioMapType } from '../types';
import { DeviceType } from '../../../global/types';

export const AspectRatioMap: AspectRatioMapType = {
  gap: {
    desktop: {
      twoCard: '640:457',
      threeCard: '426:457',
      fourCard: '213:457',
      sixCard: '213:457',
    },
    mobile: {
      twoCard: '188:292',
      threeCard: '170:292',
      fourCard: '150:292',
      sixCard: '150:292',
    },
  },
  gapfs: {
    desktop: {
      twoCard: '640:457',
      threeCard: '426:457',
      fourCard: '213:457',
      sixCard: '213:457',
    },
    mobile: {
      twoCard: '188:292',
      threeCard: '170:292',
      fourCard: '150:292',
      sixCard: '150:292',
    },
  },
  at: {
    desktop: {
      twoCard: '632:410',
      threeCard: '416:410',
      fourCard: '308:410',
      sixCard: '200:410',
    },
    mobile: {
      twoCard: '167:240',
      threeCard: '133:240',
      fourCard: '133:240',
      sixCard: '133:240',
    },
  },
  on: {
    desktop: {
      twoCard: '632:320',
      threeCard: '416:320',
      fourCard: '308:320',
      sixCard: '200:320',
    },
    mobile: {
      twoCard: '166:210',
      threeCard: '133:210',
      fourCard: '133:210',
      sixCard: '133:210',
    },
  },
  br: {
    desktop: {
      twoCard: '632:320',
      threeCard: '416:320',
      fourCard: '308:320',
      sixCard: '200:320',
    },
    mobile: {
      twoCard: '166:210',
      threeCard: '133:210',
      fourCard: '133:210',
      sixCard: '133:210',
    },
  },
  brfs: {
    desktop: {
      twoCard: '632:320',
      threeCard: '416:320',
      fourCard: '308:320',
      sixCard: '200:320',
    },
    mobile: {
      twoCard: '166:210',
      threeCard: '133:210',
      fourCard: '133:210',
      sixCard: '133:210',
    },
  },
};

export const getCardImageAspectRatio = (brand: Brands, deviceType: DeviceType, variant: string): string => AspectRatioMap[brand][deviceType][variant] || '';
