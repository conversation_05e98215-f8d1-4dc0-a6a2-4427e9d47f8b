# VisualNavigation (CMS Configurable)

- What is `VisualNavigation`?
  - `VisualNavigation` is a navigation menu made up of images and text.
  - It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience. Note that this format differs from earlier JSON formats.
  - Visual Navigation has an adjustable webAppearance. See [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/marketing-ui/CMS/components/VisualNavigation/types.ts) for all the options
- To see the maturity of this component in terms of Usability, Performance, Citizenship, and Code Quality, refer to the [Visual Navigation Scorecard](https://confluence.gapinc.com/pages/viewpage.action?pageId=607222318)."

## Default Behavior

- `VisualNavigation` creates a pre-styled set of `HoverImage`s with the range of 2-6 cards, with a `header` for the navigation, and `title` and `description` for each card. There are a few configurations like light / dark theme and image size / position, and that will be in a types doc coming soon.

## With Hover

- `VisualNavigation` has options for adding a hover state over the category card tiles. These include, displaying a different image on hover, displaying a color overlay based on hex and opacity defined by the author, and lastly a 1.5 center zoom on the current image.

## With Intro Card

- `VisualNavigation` also has an option to include an intro card that will be displayed first before the navigation cards. This intro card is non clickable and does not link to anything. The authorable fields for the intro card include, background image, foreground icon or image, and rich text area. The author can chose to have the foreground icon or image display above or below the rich text copy. This can be found in the webAppearance object, inside of imageOrIconPlacement, with the values: "above" | "below". If there is no rich text copy the foreground icon or image will be displayed in the center.

## With Size Toggle Variant

- `VisualNavigation` also has an option to have tabs that will change the image of the navigation tiles on click. The button when clicked will change the images of the navigation, but the Title, description, and hover options (if enabled), will remain the same, only the image will change. The user can have a minimum of 2 and a maximum of 5 toggles. Within each toggle, the user can specify the amount of images available for alternative views. Note: the number of category cards should match the number of images within each toggle.

## With Carousel

- `VisualNavigation` also has an option to be a Carousel at Desktop. This uses the `@core-ui` carousel component. At mobile, the Visual Navigation Carousel is not technically a carousel but rather a scrollable flex container.

## Limitations

- `VisualNavigation` accepts JSON keys generated by our Content Management System, Amplience. JSON can be manually written as well, as long as it remains compatible with Amplience data.
- Note that Amplience JSON shape differs from earlier JSON shapes. It is intended to be much simpler, with more styles built into the component, and therefore less configurable via JSON.

## Technical Notes

- The styling in the `VisualNavigation` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md)."

### Cautions

### API

To view documentation about the API for `VisualNavigation`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/marketing-ui/stories/VisualNavigation/index-story.jsx).

- ## Helper functions are available for Visual Navigation for various calculations:
- `categoryCardImageDimensions`, `getCardGutter`, `categoryCardImageDimensions`, `getHoverTextColor`, `introCardDimensions`, `useCardPercentHeight`

## Hooks

- `useMinimumMobileWidth`

## UX Guidelines

Coming soon

## Testing the Component in Storybook

- Changes in the Storybook knobs JSON will be reflected in the visual example.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [MIGRATION.md file](/src/MIGRATION.md).
