// @ts-nocheck
import React from 'react';
import { cleanup, render, act } from 'test-utils';
import { SMALL, LARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider, useTheme } from '@ecom-next/core/react-stitch';
import { CategoryCardsArray } from '../components/Cards/CategoryCardsArray';
import { getHoverTextColor } from './getHoverTextColor';
import { getHoverBackgroundColor } from './getHoverBackgroundColor';
import { useCardPercentHeight } from './useCardPercentHeight';
import { categoryCardImageDimensions } from './categoryCardImageDimensions';
import { introCardDimensions } from './introCardDimensions';
import { introCardBasicData } from '../__fixtures__/test-data';
import { getCardImageAspectRatio } from '.';
import { DeviceType } from '../../../global/types';
import { AspectRatioMap } from './getCardImageAspectRatio';
import { BRAND_IMAGE_DIM, getCardImageDimensions } from './getCardImageDimensions';

describe('Vis-Nav Helpers', () => {
  afterEach(cleanup);

  describe('getCardGutter desktop/mobile', () => {
    it('renders getCardGutter(Desktop) - brand agnostic', () => {
      const DESKTOP_CARD_GUTTER = 16;
      const { getAllByTestId } = render(<CategoryCardsArray {...introCardBasicData} isIntroCard />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });
      const element = getAllByTestId('vis-nav-card-content')[0];
      expect(element).toHaveStyle({ marginRight: `${DESKTOP_CARD_GUTTER}px` });
    });
    it('renders getCardGutter(Mobile) - brand agnostic', () => {
      const MOBILE_CARD_GUTTER = 7;
      const { getAllByTestId } = render(<CategoryCardsArray {...introCardBasicData} isIntroCard />, {
        brand: Brands.OldNavy,
        breakpoint: SMALL,
      });
      const element = getAllByTestId('vis-nav-card-content')[0];
      expect(element).toHaveStyle({ marginRight: `${MOBILE_CARD_GUTTER}px` });
    });
  });

  describe('getHoverTextColor on desktop ', () => {
    const TestComponent = (): JSX.Element => {
      const theme = useTheme();
      const styles = { color: getHoverTextColor(theme) };
      return <a style={styles}>Test Me</a>;
    };
    it('renders Gap as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Gap,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(43, 43, 43)' });
    });

    it('renders GapFS as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.GapFactoryStore,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(43, 43, 43)' });
    });

    it('renders OldNavy', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.OldNavy,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(255, 255, 255)' });
    });

    it('renders Athleta', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(255, 255, 255)' });
    });
  });

  describe('getHoverBackgroundColor on desktop ', () => {
    const TestComponent = (): JSX.Element => {
      const theme = useTheme();
      const styles = { color: getHoverBackgroundColor(theme) };
      return <a style={styles}>Test Me</a>;
    };
    it('renders Gap|Gapfs|BR as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Gap,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(255, 255, 255)' });
    });

    it('renders OldNavy', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.OldNavy,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(0, 55, 100)' });
    });

    it('renders Athleta', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ color: 'rgb(0, 0, 0)' });
    });
  });

  describe('useCardPercentHeight ', () => {
    const TestComponent = (): JSX.Element => {
      const catCardsCount = 3;
      const isCarousel = false;
      const percentHeight = useCardPercentHeight(catCardsCount, isCarousel);
      return <a style={{ width: percentHeight }}>Test Me</a>;
    };
    it('renders percentHeight for Gap|Gapfs|BR as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Gap,
        breakpoint: LARGE,
      });

      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '107.109375%' });
    });

    it('renders OldNavy useCardPercentHeight', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.OldNavy,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '106.25%' });
    });

    it('renders Athleta useCardPercentHeight', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '98.5576923076923%' });
    });
  });

  describe('categoryCardImageDimensions ', () => {
    const TestComponent = (): JSX.Element => {
      const theme = useTheme();
      const dimensionWidth = categoryCardImageDimensions(theme);
      return <a style={{ width: dimensionWidth }}>Test Me</a>;
    };
    it('renders categoryCardImageDimensions for Gap|Gapfs|BR as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Gap,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '292px' });
    });

    it('renders OldNavy categoryCardImageDimensions', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.OldNavy,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '210px' });
    });

    it('renders Athleta categoryCardImageDimensions', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });
      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ width: '240px' });
    });
  });

  describe('introCardDimensions ', () => {
    const TestComponent = (): JSX.Element => {
      const theme = useTheme();
      const dimensionMinWidthHeight = introCardDimensions(theme);
      const { minWidth, minHeight } = dimensionMinWidthHeight;
      return <a style={{ minWidth: `${minWidth}`, minHeight: `${minHeight}` }}>Test Me</a>;
    };

    it('renders introCardDimensions for Gap|Gapfs|BR as default', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Gap,
        breakpoint: LARGE,
      });

      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ minWidth: '205px' });
      expect(element).toHaveStyle({ minHeight: '336px' });
    });

    it('renders OldNavy introCardDimensions', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.OldNavy,
        breakpoint: LARGE,
      });

      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ minWidth: '203px' });
      expect(element).toHaveStyle({ minHeight: '250px' });
    });

    it('renders Athleta introCardDimensions', () => {
      const { container } = render(<TestComponent />, {
        brand: Brands.Athleta,
        breakpoint: LARGE,
      });

      const element = container.firstChild.firstChild;
      expect(element).toHaveStyle({ minWidth: '254px' });
      expect(element).toHaveStyle({ minHeight: '240px' });
    });
  });

  describe('getCardImageAspectRatio', () => {
    Object.values(Brands).forEach(brand => {
      ['mobile', 'desktop'].forEach(deviceType => {
        ['twoCard', 'threeCard', 'fourCard', 'sixCard'].forEach(variant => {
          it(`returns aspect ratio for ${brand.toUpperCase()} with ${variant} in ${deviceType.toUpperCase()} environment`, () => {
            const currentViewPort = deviceType as DeviceType;
            const aspectRatio: string = getCardImageAspectRatio(brand, currentViewPort, variant);
            const aspectRatioMap: string = AspectRatioMap[brand][currentViewPort][variant];
            expect(aspectRatio).toBe(aspectRatioMap);
          });
        });
      });
    });
    describe('getCardImageDimensions ', () => {
      type TestComponentProps = {
        isDesktop: boolean;
      };
      const TestComponent = ({ isDesktop }: TestComponentProps): JSX.Element => {
        const theme = useTheme();
        const cardImageDimensions = getCardImageDimensions(theme.brand, isDesktop ? 'desktop' : 'mobile');
        return <a style={{ height: cardImageDimensions.height }}>Test Me</a>;
      };

      Object.values(Brands)
        .filter(brand => brand !== 'on')
        .forEach(brand => {
          it(`renders default dimensions for ${brand.toUpperCase()}  in a desktop environment`, () => {
            const DEVICE = 'desktop';
            const { container } = render(<TestComponent isDesktop />, {
              brand,
              breakpoint: LARGE,
            });
            const element = container.firstChild.firstChild as Element;
            expect(element).toBeTruthy();
            expect(element).toHaveStyle({
              height: BRAND_IMAGE_DIM[brand][DEVICE].height,
            });
          });
        });

      Object.values(Brands)
        .filter(brand => brand !== 'on')
        .forEach(brand => {
          it(`renders default dimensions for ${brand.toUpperCase()}  in a mobile environment`, () => {
            const DEVICE = 'mobile';
            const { container } = render(<TestComponent isDesktop={false} />, {
              brand: brand,
              breakpoint: SMALL,
            });
            const element = container.firstChild.firstChild as Element;
            expect(element).toBeTruthy();
            expect(element).toHaveStyle({
              height: BRAND_IMAGE_DIM[brand][DEVICE].height,
            });
          });
        });
    });
  });
});
