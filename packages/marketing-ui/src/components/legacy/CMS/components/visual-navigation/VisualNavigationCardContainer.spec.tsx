// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import VisualNavigationCardContainer from './VisualNavigationCardContainer';

describe('VisualNavigationCardContainer', () => {
  it('should render default container', () => {
    const result = render(<VisualNavigationCardContainer />);
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('should render a container with spacing', () => {
    const result = render(<VisualNavigationCardContainer spacing={8} />);
    expect(result.container.firstChild.firstChild).toHaveStyle({ gap: '8px' });
  });
});
