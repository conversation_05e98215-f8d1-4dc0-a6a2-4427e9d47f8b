// @ts-nocheck
'use client';
import React from 'react';
import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge';
import { DefaultContainer, LinkContainer } from './styles';
import { BannerProps } from './types';

export const Banner = ({ children, cta }: BannerProps) => {
  const isLargeVP = useViewportIsLarge();
  const isMobile = !isLargeVP;

  return (
    <>
      {cta ? (
        <LinkContainer href={cta.value} isMobile={isMobile}>
          {children}
        </LinkContainer>
      ) : (
        <DefaultContainer isMobile={isMobile}>{children}</DefaultContainer>
      )}
    </>
  );
};
