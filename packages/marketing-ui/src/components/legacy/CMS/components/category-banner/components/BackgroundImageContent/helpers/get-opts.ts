// @ts-nocheck
'use client';
import { GetOptsProps } from '../types';

export const getOpts = ({ imageRatio, index, isMobile }: GetOptsProps): { aspect: string; height: number } => {
  const CONTAINER_HEIGHT_PX = 400;

  const aspectMappings = {
    '70': '895:342',
    '50': isMobile ? '92:171' : '75:38',
    '30': '4:3',
    '33': '455:342',
    '25': isMobile ? '92:73' : '35:38',
  };

  const defaultAspect = isMobile ? '125:114' : '683:171';
  const imageAspect = aspectMappings[imageRatio[index]] || defaultAspect;

  return {
    aspect: imageAspect,
    height: CONTAINER_HEIGHT_PX,
  };
};
