// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, waitFor, act } from 'test-utils';

import { renderHook } from 'test-utils';
import { styled } from '@ecom-next/core/react-stitch';
import { useTypographyElementWidth, withGridItemStyles } from './helpers';
import { TwoGridItemType, ThreeGridItemType } from './types';

const BaseComponent = styled.div``;

const shortImageDataForTwoGridItems: TwoGridItemType = {
  selectedImage: 'first',
  isShorter: true,
  isOverlapEnabled: false,
  isWider: false,
};

const shortImageDataForThreeGridItems: ThreeGridItemType = {
  selectedImage: 'first',
  isShorter: true,
  isOverlapEnabled: false,
  position: 'left',
};

const wideImageDataForTwoGridItems: TwoGridItemType = {
  selectedImage: 'first',
  isShorter: false,
  isOverlapEnabled: false,
  isWider: true,
};

const rightPositionedDataForThreeGridItems: ThreeGridItemType = {
  selectedImage: 'second',
  isShorter: true,
  isOverlapEnabled: false,
  position: 'right',
};

const overlapImageDataForTwoGridItems: TwoGridItemType = {
  selectedImage: 'second',
  isShorter: false,
  isOverlapEnabled: true,
  isWider: false,
};
const overlapImageDataForThreeGridItems: ThreeGridItemType = {
  selectedImage: 'second',
  isShorter: false,
  isOverlapEnabled: true,
  position: 'left',
};

describe('Helpers HOC', () => {
  describe('Styles for Overlapping 2 Image Banner', () => {
    it('should match snapshots', () => {
      const StyledItem = withGridItemStyles(BaseComponent, shortImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);
      expect(container).toMatchSnapshot();
    });

    it('should have padding-top: 10% if grid item is shorter', () => {
      const StyledItem = withGridItemStyles(BaseComponent, shortImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);

      expect(container.querySelector('div div div')).toHaveStyle({
        paddingTop: '7.8125%',
      });
    });

    it("should have grid-column: '1/span 13` if first image is wider", () => {
      const StyledItem = withGridItemStyles(BaseComponent, wideImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);
      const styles = getComputedStyle(container.querySelector('div div div')!);
      expect(styles.gridColumn).toBe('1/span 13');
    });

    it("should have grid-column: '1/span 13` if second image is wider", () => {
      const StyledItem = withGridItemStyles(BaseComponent, wideImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);
      const styles = getComputedStyle(container.querySelector('div div div')!);
      expect(styles.gridColumn).toBe('1/span 13');
    });

    it('should have z-index : 1 if grid item overlapped', () => {
      const StyledItem = withGridItemStyles(BaseComponent, overlapImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);
      expect(container.querySelector('div div div')).toHaveStyle({ zIndex: 1 });
    });

    it('should not have a11y violations', async () => {
      const StyledItem = withGridItemStyles(BaseComponent, wideImageDataForTwoGridItems, 2);
      const { container } = render(<StyledItem />);
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('Styles for Overlapping 3 Image Banner', () => {
    it('should match snapshots', () => {
      const StyledItem = withGridItemStyles(BaseComponent, shortImageDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      expect(container).toMatchSnapshot();
    });

    it('should have padding-top: 10% if grid item is first image positioned left', () => {
      const StyledItem = withGridItemStyles(BaseComponent, shortImageDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      expect(container.querySelector('div div div')).toHaveStyle({ paddingTop: '10%' });
    });

    it("should have grid-column: '13/span 9` if first image is rectangle", () => {
      const StyledItem = withGridItemStyles(BaseComponent, rightPositionedDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      const styles = getComputedStyle(container.querySelector('div div div')!);
      expect(styles.gridColumn).toBe('13/span 9');
    });

    it("should have grid-column: '13/span 9` if second image is square", () => {
      const StyledItem = withGridItemStyles(BaseComponent, rightPositionedDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      const styles = getComputedStyle(container.querySelector('div div div')!);
      expect(styles.gridColumn).toBe('13/span 9');
    });

    it('should have z-index : 1 if grid item overlapped', () => {
      const StyledItem = withGridItemStyles(BaseComponent, overlapImageDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      expect(container.querySelector('div div div')).toHaveStyle({ zIndex: 1 });
    });

    it('should not have a11y violations', async () => {
      const StyledItem = withGridItemStyles(BaseComponent, rightPositionedDataForThreeGridItems, 3);
      const { container } = render(<StyledItem />);
      expect(await axe(container)).toHaveNoViolations();
    });
  });
});

describe('useTypographyElementWidth hook usage', () => {
  beforeAll(() => {
    window.resizeTo = function resizeTo(width, height) {
      Object.assign(this, {
        innerWidth: width,
        innerHeight: height,
        outerWidth: width,
        outerHeight: height,
      }).dispatchEvent(new this.Event('resize'));
    };
  });

  it('verifies usage of useDebouncedWindowResize hook', async () => {
    const { result, rerender } = renderHook(() => useTypographyElementWidth(true, ''));
    window.resizeTo(500, 900);
    const [, width] = result.current;
    expect(width).toBe('43%');

    await act(async () => {
      rerender();
    });
    await waitFor(() => expect(result.current[1]).toBe(''));
  });
});
