// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import EdfsTextArea from './EdfsTextArea';

const htmlString = '<div>This is an rte test div</div>';

describe.only('EdfsTextArea', () => {
  describe('default component', () => {
    it('in mobile view', () => {
      const { container } = render(<EdfsTextArea rte={htmlString} />, {
        breakpoint: SMALL,
      });
      expect(container).toMatchSnapshot();
    });
    it('in desktop view', () => {
      const { container } = render(<EdfsTextArea rte={htmlString} />, {
        breakpoint: XLARGE,
      });
      expect(container).toMatchSnapshot();
    });
    it('should render rich text', () => {
      const { container, getByText } = render(<EdfsTextArea rte={htmlString} />, {
        breakpoint: SMALL,
      });
      expect(getByText('This is an rte test div')).toBeInTheDocument();
      expect(container.firstElementChild?.firstElementChild?.firstElementChild?.firstElementChild?.innerHTML).toBe(htmlString);
    });
  });
});
