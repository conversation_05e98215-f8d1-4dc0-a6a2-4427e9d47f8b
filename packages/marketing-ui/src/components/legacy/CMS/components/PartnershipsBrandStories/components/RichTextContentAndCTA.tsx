// @ts-nocheck
'use client';
import React from 'react';
import { DESKTOP_SCALE_POINT } from '../../../global/constants';
import { CtaButton } from '../../../subcomponents/CTAButton';
import { RatingContentValue } from '../../../types/amplience';
import { RichText } from '../../../subcomponents/RichText';

export type RichTextContentAndCTAProps = RatingContentValue;

export const RichTextContentAndCTA = ({ isDesktop, text, cta }: WithIsDesktop<RichTextContentAndCTAProps>) => {
  const rt1 = text?.upperText;
  const rt2 = text?.lowerText;

  const hasRT2OrCTA = rt2 || Array.isArray(cta);

  const scalableText = {
    enable: true,
    desktopScalingPoint: DESKTOP_SCALE_POINT,
  };

  return (
    <>
      {rt1 && (
        <RichText
          aria-level={1}
          css={{
            gridArea: 'header',
            marginBottom: isDesktop || hasRT2OrCTA ? undefined : '40px',
          }}
          isDesktop={isDesktop}
          role='heading'
          scalableText={scalableText}
          text={rt1}
        />
      )}
      {hasRT2OrCTA && (
        <div
          css={{
            gridArea: 'richText-cta',
            display: 'flex',
            flexDirection: 'column',
            gap: isDesktop ? '40px' : '20px',
            marginBottom: isDesktop ? undefined : '40px',
            marginTop: !isDesktop && rt1 ? '20px' : undefined,
          }}
        >
          {rt2 && <RichText isDesktop={isDesktop} scalableText={scalableText} text={rt2} />}
          {Array.isArray(cta) && <CtaButton ctaButton={cta[0].cta} ctaButtonStyling={cta[0]?.buttonStyle} />}
        </div>
      )}
    </>
  );
};
