// @ts-nocheck
import { CategoryBannerVHCarouselProps } from '..';

export const cbvhCarouselBaseData: CategoryBannerVHCarouselProps = {
  desktopBannerSize: 'medium',
  mobileBannerSize: 'medium',
  contentConfiguration: false,
  mobileTextTreatment: 'on',
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 0,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'desktopAndMobile',
      hideChevrons: false,
    },
  },
  frames: [
    {
      tiles: [
        {
          backgroundImage: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: 'e7e06d55-cc72-4e5e-b8fd-9e38c2e37a49',
                name: '530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882',
                endpoint: 'athleta',
                defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
              },
              altText: 'hello lady',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
            },
          ],
          webAppearance: {
            desktop: {
              verticalPlacement: 'end',
              horizontalPlacement: 'center',
              ctaVerticalPlacement: 'center',
              ctaHorizontalPlacement: 'right',
              textJustification: 'start',
              ctaJustification: 'center',
              ctaButtonStyling: {
                buttonStyle: 'underline',
                buttonColor: 'dark',
              },
            },
            mobile: {
              textJustification: 'right',
              ctaPlacement: 'start',
            },
          },
          ctaButton: {
            label: 'new arrivals',
            value: 'buyIt',
          },
          bannerLink: {
            label: '',
            value: 'linkToNewArrivals',
          },
          text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1" style="color:#00FF00;font-weight:800">Body font 1</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-2" style="color:#10a6a6;font-style:italic">GOOD EARTH</span></p>',
        },
        {
          backgroundImage: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: '706688cf-2808-4d97-b1b0-e8ad10b2ca3b',
                name: 'woman-black_hat',
                endpoint: 'athleta',
                defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
              },
              altText: 'hello lady',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
            },
          ],
          webAppearance: {
            desktop: {
              verticalPlacement: 'end',
              horizontalPlacement: 'end',
              ctaVerticalPlacement: 'center',
              ctaHorizontalPlacement: 'left',
              textJustification: 'start',
              ctaJustification: 'center',
              ctaButtonStyling: {
                buttonStyle: 'border',
                buttonColor: 'dark',
              },
            },
            mobile: {
              textJustification: 'center',
              ctaPlacement: 'center',
            },
          },
          text: '<h2 class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">OUT MOST LOVED STYLES</span></h2>',
          ctaButton: {
            label: 'new arrivals',
            value: 'buyIt',
          },
        },
      ],
    },
    {
      tiles: [
        {
          webAppearance: {
            desktop: {
              verticalPlacement: 'center',
              horizontalPlacement: 'center',
              ctaVerticalPlacement: 'center',
              ctaHorizontalPlacement: 'center',
              textJustification: 'end',
              ctaJustification: 'end',
              ctaButtonStyling: {
                buttonStyle: 'border',
                buttonColor: 'dark',
              },
            },
            mobile: {
              textJustification: 'right',
              ctaPlacement: 'center',
            },
          },
          ctaButton: {
            label: 'Get a life',
            value: 'ss',
          },
          bannerLink: {
            label: 'Middle link',
            value: 'productsWeLove',
          },
          backgroundImage: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: 'a5c19128-41cd-4212-9f52-1402e1d492fa',
                name: 'HOL2_NA_GiftShop_ISM_XL@2x',
                endpoint: 'athleta',
                defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
              },
              altText: 'image only',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
            },
          ],
        },
      ],
    },
    {
      tiles: [
        {
          backgroundImage: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: '8017a351-26d6-4020-98ed-b3606827d83f',
                name: '985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy',
                endpoint: 'athleta',
                defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
              },
              altText: 'hi',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
            },
          ],
          ctaButton: {
            label: 'Get a life',
            value: 'ss',
          },
          bannerLink: {
            label: 'lastLink',
            value: 'lastLink',
          },
          webAppearance: {
            desktop: {
              verticalPlacement: 'start',
              horizontalPlacement: 'start',
              ctaVerticalPlacement: 'start',
              ctaHorizontalPlacement: 'left',
              textJustification: 'center',
              ctaJustification: 'start',
              ctaButtonStyling: {
                buttonStyle: 'border',
                buttonColor: 'dark',
              },
            },
            mobile: {
              textJustification: 'left',
              ctaPlacement: 'center',
            },
          },
          text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1" style="color:#00FF00;font-weight:800">Body font 1</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-2" style="color:#10a6a6;font-style:italic">GOOD EARTH</span></p>',
        },
      ],
    },
  ],
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
