// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryBannerVariableHeightCarousel CBVHCarouselWrapper StaticFile should match snapshot for persistent below on mobile 1`] = `
.emotion-0 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-1 {
  position: relative;
  aspect-ratio: 375/235;
}

.emotion-2 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(31.333333333333336vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 {
  width: 100%;
  position: relative;
}

.emotion-7 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/235;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/235;
  overflow: hidden;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 375/235;
  object-fit: cover;
}

.emotion-10 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-32 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-32 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 3;
}

.emotion-32 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-34 {
  text-align: right;
  display: inline-block;
}

.emotion-34 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-34 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-34 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-34 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-34 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-34 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-34 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-34 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-34 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-34 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-34 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-34 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-34 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-34 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-35 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: start;
}

.emotion-36 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-36:focus {
  outline: none;
}

.emotion-36>span {
  padding: 1px 0;
}

.emotion-36:hover,
.emotion-36:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-37 {
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <section
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <nav
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#2B2B2B"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="235"
                              width="375"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/linkToNewArrivals"
                                  tabindex="-1"
                                  target="_self"
                                  title=""
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="235"
                              width="375"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="235"
                              width="375"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/productsWeLove"
                                  tabindex="-1"
                                  target="_self"
                                  title="Middle link"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="235"
                              width="375"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/lastLink"
                                  tabindex="-1"
                                  target="_self"
                                  title="lastLink"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#2B2B2B"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
          <div
            class="emotion-32"
          >
            <div
              class="rteWrapperDivs emotion-33"
            >
              <div
                class="emotion-34"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-1"
                      style="color:#00FF00;font-weight:800"
                    >
                      Body font 1
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-2"
                      style="color:#10a6a6;font-style:italic"
                    >
                      GOOD EARTH
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="ctaWrapperDivs emotion-35"
            >
              <a
                class="emotion-36"
                color="dark"
                href="/buyIt"
              >
                <span
                  class="emotion-37"
                >
                  new arrivals
                </span>
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeightCarousel should match snapshot 1`] = `
.emotion-1 {
  position: relative;
  aspect-ratio: 1280/400;
}

.emotion-2 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(15.625vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 {
  width: 100%;
  position: relative;
}

.emotion-7 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/400;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/400;
  overflow: hidden;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 640/400;
  object-fit: cover;
}

.emotion-10 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 3;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: start;
  display: inline-block;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: right;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-17:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-27 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: left;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-28:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-31 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-32 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/400;
  overflow: hidden;
}

.emotion-33 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

.emotion-37 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-37 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-37 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: end;
}

.emotion-38 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: center;
}

.emotion-48 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-48 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-48 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 2;
  text-align: start;
}

.emotion-49 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-50 {
  text-align: center;
  display: inline-block;
}

.emotion-50 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-50 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-50 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-50 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-50 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-50 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-50 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-50 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-50 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-50 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-50 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-50 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-50 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <section
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <nav
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#2B2B2B"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/linkToNewArrivals"
                                  tabindex="-1"
                                  target="_self"
                                  title=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                          style="color:#00FF00;font-weight:800"
                                        >
                                          Body font 1
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-2"
                                          style="color:#10a6a6;font-style:italic"
                                        >
                                          GOOD EARTH
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-8"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-9"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUT MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/productsWeLove"
                                  tabindex="-1"
                                  target="_self"
                                  title="Middle link"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-37"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-38"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-10"
                              >
                                <a
                                  class="emotion-11"
                                  href="/lastLink"
                                  tabindex="-1"
                                  target="_self"
                                  title="lastLink"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-48"
                              >
                                <div
                                  class="rteWrapperDivs emotion-49"
                                >
                                  <div
                                    class="emotion-50"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                          style="color:#00FF00;font-weight:800"
                                        >
                                          Body font 1
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-2"
                                          style="color:#10a6a6;font-style:italic"
                                        >
                                          GOOD EARTH
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#2B2B2B"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;
