// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { useViewportIsMobile } from '../../../../hooks/useViewportIsMobile';
import { GenericCBVHCPlacementProps } from '../types';

interface VerticalGridPosition {
  start: number;
  center: number;
  end: number;
}

interface HorizontalGridPosition {
  left: number;
  center: number;
  right: number;
}

type CtaAndRtePlacementProps = GenericCBVHCPlacementProps;

export const CtaAndRtePlacement = ({
  verticalPlacement,
  ctaVerticalPlacement,
  horizontalPlacement,
  ctaHorizontalPlacement,
  ctaJustification,
  text,
  ctaButton,
  minMaxOption = '20%',
}: CtaAndRtePlacementProps) => {
  const isMobile: boolean = useViewportIsMobile();

  const verticalAndHorizontalPosition: VerticalGridPosition = {
    start: 1,
    center: 2,
    end: 3,
  };
  const ctaHorizontalPosition: HorizontalGridPosition = {
    left: 1,
    center: 2,
    right: 3,
  };

  const rteGridColumn: number = verticalAndHorizontalPosition[horizontalPlacement];
  const rteGridRow: number = verticalAndHorizontalPosition[verticalPlacement];
  const ctaGridColumn: number = ctaHorizontalPosition[ctaHorizontalPlacement];
  let ctaGridRow: number = verticalAndHorizontalPosition[ctaVerticalPlacement];
  let gridTemplateRows = 'repeat(3, minmax(0, max-content))';
  let textAlign = {};

  const shareVerticalPosition = rteGridRow === ctaGridRow;
  const shareHorizontalPosition = rteGridColumn === ctaGridColumn;

  const shareVerticalAndHorizontalPosition = shareVerticalPosition && shareHorizontalPosition;

  if (!isMobile) {
    textAlign = { textAlign: ctaJustification };
    if (shareVerticalAndHorizontalPosition && text && ctaButton) {
      ctaGridRow = rteGridRow + 1;
      gridTemplateRows = '1fr 1fr 1fr';
      if (rteGridRow === 2) {
        gridTemplateRows = '1fr max-content 1fr';
      }
      if (rteGridRow === 1) {
        gridTemplateRows = 'max-content max-content 1fr';
      }
    }
  }

  const CtaAndRtePlacement: CSSObject = {
    flexDirection: 'column',
    display: isMobile ? 'flex' : 'grid',
    gridTemplateColumns: `repeat(3, minmax(${minMaxOption},max-content))`,
    alignContent: 'center',
    gridTemplateRows,
    '.rteWrapperDivs': {
      gridColumn: rteGridColumn,
      gridRow: rteGridRow,
    },
    '.ctaWrapperDivs': {
      gridColumn: ctaGridColumn,
      gridRow: ctaGridRow,
      ...textAlign,
    },
    justifyContent: isMobile ? 'center' : 'space-between',
  };

  return CtaAndRtePlacement;
};
