// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryBannerPriceCard Component should match snapshots in desktop view 1`] = `
.emotion-0 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  height: auto;
  width: auto;
  height: min(19.53125vw, 250px);
}

.emotion-1 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  position: absolute;
  z-index: -1;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.emotion-3 {
  display: grid;
  justify-items: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-template-rows: 20px 20px;
  padding: 0 14px;
  margin-top: auto;
  height: 80%;
}

.emotion-4 {
  margin: 0 auto;
  grid-row: 1;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  justify-self: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-5 {
  grid-row: 2;
  justify-self: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  color: #D0103A;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  grid-row: 3;
}

.emotion-7 {
  line-height: 1;
  font-weight: 800;
  position: relative;
  font-size: min(6.25vw, 80px);
}

.emotion-7::before {
  content: "$";
  position: absolute;
  top: 5px;
  left: 32px;
  font-size: min(2.5vw, 32px);
  font-weight: 800;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  z-index: 2;
  grid-row: 4;
  justify-self: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-bottom: min(1.5625vw, 20px);
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center!important;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9:hover,
.emotion-9:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-9:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10 {
  box-sizing: border-box;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <img
          alt="image"
          class="emotion-2"
          src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=auto&pcrop=159.49367088607596,537.6966321790536,146.83544303797467,212.30336782094594&h=500"
        />
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:center;"
              >
                <span
                  class="amp-cms--subhead-3"
                  style="font-weight:700"
                >
                  TEES & TANKS
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:center;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  from
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <span
              class="emotion-7"
            >
              5
            </span>
          </div>
          <div
            class="emotion-8"
          >
            <a
              class="emotion-9"
              color="dark"
              href="#style=123456"
              target="_self"
            >
              <span
                class="emotion-10"
              >
                Jumping
              </span>
            </a>
          </div>
        </div>
        <a
          class="emotion-11"
          href="#style=7890"
          tabindex="-1"
          target="_self"
          title="Test-AG-Men-Pants-4"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerPriceCard Component should match snapshots in mobile view 1`] = `
.emotion-0 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  height: auto;
  width: auto;
  height: min(48.8vw, 183px);
}

.emotion-1 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  position: absolute;
  z-index: -1;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.emotion-3 {
  display: grid;
  justify-items: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-template-rows: auto 25px 45px;
  padding: 0 14px;
  margin-top: auto;
}

.emotion-4 {
  margin: 0 auto;
  grid-row: 1;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  justify-self: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
  margin: 0 auto;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
  margin: 0 auto;
}

.emotion-5 {
  grid-row: 2;
  justify-self: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  color: #D0103A;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  grid-row: 3;
}

.emotion-7 {
  line-height: 1;
  font-weight: 800;
  position: relative;
  font-size: min(18.133333333333333vw, 68px);
}

.emotion-7::before {
  content: "$";
  position: absolute;
  top: 5px;
  left: 32px;
  font-size: min(7.466666666666668vw, 28px);
  font-weight: 800;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  z-index: 2;
  grid-row: 4;
  justify-self: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-bottom: min(3.733333333333334vw, 14px);
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center!important;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9:hover,
.emotion-9:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-9:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10 {
  box-sizing: border-box;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <img
          alt="image"
          class="emotion-2"
          src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=auto&h=366"
        />
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:center;"
              >
                <span
                  class="amp-cms--subhead-3"
                  style="font-weight:700"
                >
                  TEES & TANKS
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:center;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  from
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <span
              class="emotion-7"
            >
              5
            </span>
          </div>
          <div
            class="emotion-8"
          >
            <a
              class="emotion-9"
              color="dark"
              href="#style=123456"
              target="_self"
            >
              <span
                class="emotion-10"
              >
                Jumping
              </span>
            </a>
          </div>
        </div>
        <a
          class="emotion-11"
          href="#style=7890"
          tabindex="-1"
          target="_self"
          title="Test-AG-Men-Pants-4"
        />
      </div>
    </div>
  </div>
</div>
`;
