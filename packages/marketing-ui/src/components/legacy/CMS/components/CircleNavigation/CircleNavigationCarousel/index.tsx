// @ts-nocheck
'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Carousel, SliderElement } from '@ecom-next/core/legacy/carousel';
import CircleNavigationCategoryCircles from '../CircleNavigationCategory';
import { CircleContainer, CircleNavigationDesktop, CircleSlider } from './styles';
import { CircleCategoryType, CircleNavigationStylesType } from '../types';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge';

export type CircleNavigationCarouselProps = {
  categories: CircleCategoryType[];
  desktopMaxWidth: number | undefined;
  styles?: CircleNavigationStylesType;
};

const CircleNavigationCarousel = (props: CircleNavigationCarouselProps): JSX.Element => {
  const { categories, styles = {}, desktopMaxWidth } = props;
  const isViewportLarge = useViewportIsLarge();
  let { carousel = { columnGap: isViewportLarge ? '10px' : '0.25vw' } } = styles;
  const { localize } = useLocalize();

  if (!isViewportLarge && categories.length < 5 && carousel) {
    carousel = { columnGap: '6px', display: 'flex', justifyContent: 'center' };
  }

  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [previousFocusIndex, setPreviousFocusIndex] = useState<number | null>(null);
  const [currentFocusIndex, setCurrentFocusIndex] = useState<number | null>(null);
  const carouselRef = useRef<SliderElement>(null);
  const lastCardRef = useRef<HTMLAnchorElement>(null);
  const firstCardRef = useRef<HTMLAnchorElement>(null);

  const handleWheel = (e: React.WheelEvent) => {
    if (carouselRef.current) {
      e.deltaX > 0 ? carouselRef.current.slickNext() : carouselRef.current.slickPrev();
    }
  };

  const carouselSettings = {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
    infinite: false,
    nextArrowAlt: localize('cms.carousel.next'),
    prevArrowAlt: localize('cms.carousel.previous'),
    slidesToShow: 8,
    slidesToScroll: categories && categories.length > 8 ? categories.length - 8 : categories.length - 1,
    swipe: true,
    useChevronSquare: true,
  };

  const circleNavigationCardList = () =>
    categories.map((category: CircleCategoryType, index: number) => {
      const isLastVisibleCard = index === 7;
      const isFirstVisibleCard = index === categories.length - 8;
      let ref;
      if (isLastVisibleCard) {
        ref = lastCardRef;
      } else if (isFirstVisibleCard) {
        ref = firstCardRef;
      }

      return (
        <CircleNavigationCategoryCircles
          key={category.key}
          ref={ref}
          category={category}
          desktopMaxWidth={desktopMaxWidth}
          onFocus={() => {
            setPreviousFocusIndex(currentFocusIndex);
            setCurrentFocusIndex(index);
          }}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          styles={styles}
        />
      );
    });

  useEffect(() => {
    const handleLastCardFocus = () => {
      if (previousFocusIndex === 6 && currentFocusIndex === 7 && carouselRef.current) {
        carouselRef.current.slickNext();
      }
    };

    const handleFirstCardFocus = () => {
      if (previousFocusIndex === categories.length - 7 && currentFocusIndex === categories.length - 8 && carouselRef.current) {
        carouselRef.current.slickPrev();
      }
    };

    const lastCard = lastCardRef.current;
    const firstCard = firstCardRef.current;

    if (lastCard) {
      lastCard.addEventListener('focus', handleLastCardFocus);
    }

    if (firstCard) {
      firstCard.addEventListener('focus', handleFirstCardFocus);
    }

    return () => {
      if (lastCard) {
        lastCard.removeEventListener('focus', handleLastCardFocus);
      }

      if (firstCard) {
        firstCard.removeEventListener('focus', handleFirstCardFocus);
      }
    };
  }, [previousFocusIndex, currentFocusIndex, categories.length]);

  return (
    <>
      {isViewportLarge ? (
        <CircleNavigationDesktop>
          <div onWheel={handleWheel}>
            <Carousel ref={carouselRef} {...carouselSettings}>
              {circleNavigationCardList()}
            </Carousel>
          </div>
        </CircleNavigationDesktop>
      ) : (
        <CircleContainer>
          <CircleSlider css={carousel}>{circleNavigationCardList()}</CircleSlider>
        </CircleContainer>
      )}
    </>
  );
};

export default CircleNavigationCarousel;
