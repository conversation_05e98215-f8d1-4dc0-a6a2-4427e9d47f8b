// @ts-nocheck
'use client';
import React from 'react';
import CircleNavigationCategoryCircles from '../CircleNavigationCategory';
import { CircleCategoryType, CircleNavigationStylesType } from '../types';
import { ExposedRows } from '../../../content-types/CircleNavigation/types';
import { ExposedCircle, ExposedContainer } from './styles';

export type CircleNavigationExposedProps = {
  categories: CircleCategoryType[];
  desktopMaxWidth: number | undefined;
  rows: ExposedRows[];
  styles?: CircleNavigationStylesType;
};

const CircleNavigationExposed = (props: CircleNavigationExposedProps): JSX.Element => {
  const { rows = [], categories, styles = {} } = props;
  const {
    exposed = {
      circle: {},
    },
  } = styles;

  function categoryRows(rows: ExposedRows[]) {
    const catRows: JSX.Element[] = [];
    let currIndex = 0;
    rows.forEach((row: ExposedRows) => {
      catRows.push(
        <ExposedContainer key={`row-${currIndex}`} css={row.style}>
          {getRowCategories(currIndex, row.categoriesToShow)}
        </ExposedContainer>
      );
      currIndex += row.categoriesToShow;
    });
    return catRows;
  }

  function getRowCategories(currIndex: number, categoriesToShow: number) {
    const rowCategories: JSX.Element[] = [];
    let catToShow = categoriesToShow;
    for (let i = currIndex; catToShow > 0; i += 1) {
      if (categories[i]) {
        rowCategories.push(
          <ExposedCircle key={categories[i].key} css={exposed.circle}>
            <CircleNavigationCategoryCircles key={categories[i].key} category={categories[i]} desktopMaxWidth={props.desktopMaxWidth} styles={styles} />
          </ExposedCircle>
        );
      }
      catToShow -= 1;
    }
    return rowCategories;
  }

  return <>{categoryRows(rows)}</>;
};

export default CircleNavigationExposed;
