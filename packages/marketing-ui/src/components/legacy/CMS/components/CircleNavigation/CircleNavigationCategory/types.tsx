// @ts-nocheck
'use client';
import { CircleCategoryType, CircleNavigationStylesType } from '../types';
import { ImageVariationType } from '../../../helpers/ImageEncoder/types';

export type CircleNavigationCategoryCirclesType = {
  category: CircleCategoryType;
  desktopMaxWidth: number | undefined;
  selectedCategory?: string;
  setSelectedCategory?: (categoryKey: string | undefined) => void;
  styles?: CircleNavigationStylesType;
  viewport?: ImageVariationType;
};
