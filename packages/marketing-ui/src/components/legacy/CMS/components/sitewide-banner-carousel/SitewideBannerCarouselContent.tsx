// @ts-nocheck
'use client';
import React from 'react';
import { BackgroundTypeExtensionValue } from '../../global/types';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer';
import { defaultBannerMinHeight } from './SitewideBannerCarouselContainer';

export interface SitewideBannerCarouselContentProps {
  background?: BackgroundTypeExtensionValue;
  className?: string;
  children?: React.ReactChild;
  minHeight?: string;
}

const SitewideBannerCarouselContent: React.FC<SitewideBannerCarouselContentProps> = (props): JSX.Element => {
  const { background, className, children } = props;
  const minHeight = props.minHeight ? props.minHeight : defaultBannerMinHeight;

  return (
    <BackgroundTypeContainer
      background={background}
      className={className}
      css={{
        display: 'flex',
        width: '100%',
        height: '100%',
        minHeight,
      }}
    >
      {children}
    </BackgroundTypeContainer>
  );
};

export default SitewideBannerCarouselContent;
