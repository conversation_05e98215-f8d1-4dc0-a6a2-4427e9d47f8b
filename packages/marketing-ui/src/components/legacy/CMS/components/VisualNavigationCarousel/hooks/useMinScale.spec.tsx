// @ts-nocheck
import { renderHook, act } from 'test-utils';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge';
import useMinScale, { useScaleValue } from './useMinScale';

jest.mock('../../../../hooks/useViewportIsLarge', () => ({
  __esModule: true,
  useViewportIsLarge: jest.fn(() => true),
}));

const renderCustomHook = (hook: Function, value: number) => renderHook(() => hook(value));
describe('useScaleValue', () => {
  const mockReturnValueOnce = (fn: Function, value: any) => (fn as unknown as jest.Mock).mockReturnValueOnce(value);

  it('should call useViewportIsLarge hooks', async () => {
    renderCustomHook(useScaleValue, 20);
    expect(useViewportIsLarge).toHaveBeenCalled();
  });

  describe('on desktop', () => {
    beforeEach(() => {
      mockReturnValueOnce(useViewportIsLarge, true);
    });

    it('should returns scalable value', async () => {
      const valueToScale = 20;
      const expectedValue = 1.56;
      const { result } = renderCustomHook(useScaleValue, valueToScale);
      const value = result.current;
      expect(value.toFixed(2)).toBe(expectedValue.toFixed(2));
    });
  });

  describe('on mobile', () => {
    beforeEach(() => {
      mockReturnValueOnce(useViewportIsLarge, false);
    });

    it('should returns scalable value', async () => {
      const valueToScale = 20;
      const expectedValue = 5.33;
      const { result } = renderCustomHook(useScaleValue, valueToScale);
      const value = result.current;
      expect(value.toFixed(2)).toBe(expectedValue.toFixed(2));
    });
  });
});

describe('useMinScale', () => {
  it('should match snapshot', async () => {
    const { result } = renderCustomHook(useMinScale, 20);
    expect(result.current).toMatchSnapshot();
  });
});
