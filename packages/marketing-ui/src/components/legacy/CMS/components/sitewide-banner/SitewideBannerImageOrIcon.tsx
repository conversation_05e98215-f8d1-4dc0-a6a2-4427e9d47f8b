// @ts-nocheck
'use client';
import React from 'react';
import { AdvanceImageData, MediaImageLink } from '../../global/types';
import isAdvanceImageDataArray from '../../helpers/type-guards/isAdvanceImageDataArray';
import AmplienceImage from '../../subcomponents/AmplienceImage';

export interface SitewideBannerImageOrIconProps {
  src: MediaImageLink | AdvanceImageData[] | AdvanceImageData;
  size?: number | string;
  className?: string;
}

const SitewideBannerImageOrIcon: React.FC<SitewideBannerImageOrIconProps> = ({ src, size, className }) => {
  const imageSrc = isAdvanceImageDataArray(src) ? src[0] : src;

  return <AmplienceImage className={className} css={{ maxHeight: size }} src={imageSrc} />;
};

export default SitewideBannerImageOrIcon;
