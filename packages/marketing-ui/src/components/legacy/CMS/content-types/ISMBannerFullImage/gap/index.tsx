'use client';

import React from 'react';
// @ts-ignore
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch';
// @ts-ignore
import classnames from 'classnames';
import { ISMBannerContainer, ISMBannerContent } from '../../../components/ism-banner/index';
import { InSortMarketingBannerFullContentType, Viewport } from '../types';
import { RichText } from '../../../subcomponents/RichText/index';
import { CtaButton, CtaProps, CTATreatmentProp } from '../../../subcomponents/CTAButton/index';
import { DetailsButton, getDetailsContent } from '../../../subcomponents/Details/index';
import { useViewportIsLarge, useViewportIsXLarge } from '../../../../hooks/useViewportIsLarge/index';
import ISMOverlay from '../../../components/ism-banner/ISMOverlay';
import ISMBannerImageOrIcon from '../../../components/ism-banner/ISMBannerImageOrIcon';
import { gridRowVerticalAlignment } from '../../../components/ism-banner/helpers/gridRowVerticalAlignment';
import { VerticalAlignmentProps, VerticalCtaAlignment } from '../../../global/types';
import { CtaButtonStylingType } from '../../ISMBanner/types';
import {
  getDetailsButtonCtaAlignment,
  getDetailsButtonGridRow,
  getDetailsButtonJustifyContent,
  ismGridSize,
  ismProductGridHeight,
} from '../../ISMBanner/styles';
import { BannerContent, CtaContainer } from '../../ISMBanner/index';
import { getContainerHeight } from './helpers';

import '../../ISMBanner/ism-banner.gap.css';

export type InSortMarketingBannerProps = InSortMarketingBannerFullContentType;

const GapInSortMarketingBanner = (props: InSortMarketingBannerProps) => {
  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  const {
    _meta,
    background,
    bannerLink,
    image,
    webAppearance,
    text,
    cta,
    cta2,
    primaryCTA,
    secondaryCTA,
    detailsLink,
    htmlModalUrl,
    pemoleCode,
    detailsLinkLocation,
    detailsPrefix,
  } = props;
  const {
    imageOrIconHorizontalAlignment,
    imageOrIconPlacement,
    ctaButtonStyling,
    ctaHorizontalAlignment,
    detailsLinkFontColor = '',
    desktopImageOrIconSize = '24px',
    mobileImageOrIconSize = '14px',
    verticalTextAlignment = 'top',
    verticalCtaAlignment = 'bottom',
    showHideBasedOnScreenSize,
  } = webAppearance || {};
  const firstCTA = cta || primaryCTA;
  const secondCTA = cta2 || secondaryCTA;
  const isDesktopVP = useViewportIsLarge();
  const isDesktop = useViewportIsXLarge();
  const isTablet = isDesktopVP && !isDesktop;
  const currentViewport = isDesktop ? 'desktop' : isTablet ? 'tablet' : 'mobile';
  const isMobile = !(isDesktop || isTablet);
  const viewport: Viewport = {
    mobile: isMobile,
    tablet: isTablet,
    desktop: isDesktop,
  };

  const { schema } = _meta || {};
  const isDouble = schema?.includes('double');

  const { rteAndIconsGridRow, ctaGridRow, gridTemplateRows } = gridRowVerticalAlignment(
    verticalTextAlignment as VerticalAlignmentProps,
    verticalCtaAlignment as VerticalCtaAlignment
  );
  const imageOrIconSize = isDesktopVP ? desktopImageOrIconSize : mobileImageOrIconSize;
  const imageOrIcon = image ? (
    <div className='mui_ism-full-image__image-or-icon'>
      <ISMBannerImageOrIcon size={imageOrIconSize} src={image} />
    </div>
  ) : undefined;

  const isCtaRteBottom = verticalTextAlignment === 'bottom' && verticalCtaAlignment === 'bottom';

  const isCtaRteMiddle = verticalTextAlignment === 'middle' && verticalCtaAlignment === 'middle';

  const isCtaRteBottomOrMiddle = isCtaRteBottom || isCtaRteMiddle;

  const imageOrIconClassNames = classnames('mui_ism-full-image__image-or-icon-content', {
    'mui_ism__cta-rte-bottom': isCtaRteBottomOrMiddle,
  });
  const imageOrIconContent = (
    <div className={imageOrIconClassNames}>
      {imageOrIconPlacement === 'above' && imageOrIcon}
      {text && <RichText text={text} />}
      {imageOrIconPlacement !== 'above' && imageOrIcon}
    </div>
  );

  const ctaPadding = (() => {
    if (verticalCtaAlignment === 'bottom') {
      if (detailsLink) return '15px 0 30px';
      if (isCtaRteBottom && text) return '15px 0 0';
      return '45px 0 0';
    }
    return '15px 0 0';
  })();

  const ctaAlignSelf = (() => {
    if (!detailsLink && !isCtaRteBottom) {
      return 'end';
    }
    return 'unset';
  })();

  const ctaContainerMaxHeight = (() => {
    if (verticalCtaAlignment === 'bottom' && !detailsLink) {
      return 'max-content';
    }
    return 'unset';
  })();

  const detailsContent = getDetailsContent(Brands.Gap, pemoleCode, htmlModalUrl);
  const detailsLinkContent = (
    <div className='mui_ism-full-image__details-link-button'>
      <DetailsButton color={detailsLinkFontColor} label={detailsLink} prefix={detailsPrefix} value={detailsContent} />
    </div>
  );

  const hasFirstCTA = firstCTA?.label;
  const hasSecondCTA = secondCTA?.label;
  const hasCTAs = hasFirstCTA || hasSecondCTA;
  const ctaProps: Omit<CtaProps & CTATreatmentProp, 'ctaButton'> = {
    alignment: 'center',
    ctaButtonStyling: ctaButtonStyling as CtaButtonStylingType,
    ctaSize: isDesktopVP ? 'large' : 'medium',
    fullWidth: false,
    isDesktop: isDesktopVP,
  };
  const ctaContainerContent = (
    <CtaContainer className='mui_ism-full-image__cta-container' data-testid='cta-container'>
      {hasFirstCTA && <CtaButton {...ctaProps} ctaButton={firstCTA} />}
      {hasSecondCTA && <CtaButton {...ctaProps} ctaButton={secondCTA} />}
    </CtaContainer>
  );

  const fullImageContainerClassnames = classnames('mui_ism-full-image__container', {
    'flex-container': hasPlpGrid2025,
    'single-column': hasPlpGrid2025 && !isDouble,
    'double-column': hasPlpGrid2025 && isDouble,
  });

  const containerHeight = getContainerHeight({ viewport });

  let aspectWidth = isDesktopVP ? 234 : 160;
  if (isDouble) {
    aspectWidth = isDesktopVP ? 484 : 335;
  }

  const useIsmGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] : 0;
  const useIsmProductGridHeight: number = hasPlpGrid2025 ? ismProductGridHeight[currentViewport] : 0;
  const useIsmDoubleGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] * 2 : 0;
  const productGridAspectRatio = hasPlpGrid2025 ? `${aspectWidth} / ${useIsmProductGridHeight}` : '';

  const componentStyles = `
    :root {
      --ism--container-height: ${!hasPlpGrid2025 ? containerHeight : ''};
      --ism--padding: ${isDesktopVP ? '20px' : '15px'};
      --ism--grid-template-rows: ${gridTemplateRows};
      --ism--cta-flex-direction: row;
      --ism--cta-padding: ${ctaPadding};
      --ism--cta--align-self: ${ctaAlignSelf};
      --ism--cta--max-height: ${ctaContainerMaxHeight};
      --ism--flex-basis: ${useIsmGridSizing}%;
      --ism--max-width: ${useIsmGridSizing}%;
      --ism--flex-basis-double: ${useIsmDoubleGridSizing}%;
      --ism--max-width-double: ${useIsmDoubleGridSizing}%;
      --ism--aspect-ratio: ${productGridAspectRatio};
      --ism--grid-item-min-height: ${useIsmProductGridHeight}px;
      --ism--cta-horizontal-alignment: ${ctaHorizontalAlignment};
      --ism--cta-grid-row: ${ctaGridRow};
      --ism--imageOrIcon-padding: ${imageOrIconPlacement === 'above' ? '0 0 20px' : '20px 0 0'};
      --ism--imageOrIcon-text-align: ${imageOrIconHorizontalAlignment}; 
      --ism--imageOrIcon-content-grid-row: ${rteAndIconsGridRow};
      --ism--details-link-grid-row: ${getDetailsButtonGridRow(verticalCtaAlignment, verticalTextAlignment)};
      --ism--details-link-vertical-cta-align: ${getDetailsButtonCtaAlignment(verticalCtaAlignment)};
      --ism--details-link-justify-content: ${getDetailsButtonJustifyContent(detailsLinkLocation)};
    }
  `;

  return (
    <>
      <style>{componentStyles}</style>
      <div className={fullImageContainerClassnames} data-testid='ism-full-image'>
        <ISMBannerContainer aspectRatio='' showHideBasedOnScreenSize={showHideBasedOnScreenSize} useAspectRatio={false}>
          <ISMBannerContent background={background}>
            <BannerContent className='mui_ism-full-image__banner-content'>
              {imageOrIconContent}
              {hasCTAs && ctaContainerContent}
              {detailsLink && detailsLinkContent}
            </BannerContent>
            {bannerLink && <ISMOverlay title={bannerLink.label} to={bannerLink.value} type='link' />}
          </ISMBannerContent>
        </ISMBannerContainer>
      </div>
    </>
  );
};

export default GapInSortMarketingBanner;
