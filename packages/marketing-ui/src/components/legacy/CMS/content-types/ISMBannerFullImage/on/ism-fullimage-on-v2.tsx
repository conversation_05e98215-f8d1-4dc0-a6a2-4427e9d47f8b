'use client';

import React from 'react'; // @ts-nocheck
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch'; // @ts-ignore
import classnames from 'classnames';
import { ISMBannerContainer, ISMBannerContent } from '../../../components/ism-banner';
import { InSortMarketingBannerFullContentType } from '../types';
import { RichText } from '../../../subcomponents/RichText';
import { CtaButton } from '../../../subcomponents/CTAButton';
import { DetailsButton, getDetailsContent } from '../../../subcomponents/Details';
import { useViewportIsLarge, useViewportIsXLarge } from '../../../../hooks/useViewportIsLarge';
import ISMOverlay from '../../../components/ism-banner/ISMOverlay';
import ISMBannerImageOrIcon from '../../../components/ism-banner/ISMBannerImageOrIcon';
import { gridRowVerticalAlignment } from '../../../components/ism-banner/helpers/gridRowVerticalAlignment';
import { VerticalAlignmentProps, VerticalCtaAlignment } from '../../../global/types';
import { CtaButtonStylingType } from '../../ISMBanner/types';
import {
  getDetailsButtonCtaAlignment,
  getDetailsButtonGridRow,
  getDetailsButtonJustifyContent,
  ismGridSize,
  ismProductGridHeight,
} from '../../ISMBanner/styles';
import { BannerContent, CtaContainer } from '../../ISMBanner/index';

import '../../ISMBanner/ism-banner.on.css';

export type InSortMarketingBannerProps = InSortMarketingBannerFullContentType;

const OldNavyInSortMarketingBannerV2 = (props: InSortMarketingBannerProps): JSX.Element | null => {
  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  const {
    _meta,
    background,
    bannerLink,
    image,
    webAppearance,
    text,
    cta,
    cta2,
    primaryCTA,
    secondaryCTA,
    detailsLink,
    htmlModalUrl,
    pemoleCode,
    detailsLinkLocation,
    detailsPrefix,
  } = props;
  const { schema } = _meta || {};
  const isDouble = schema?.includes('double');
  const {
    imageOrIconHorizontalAlignment,
    imageOrIconPlacement,
    ctaButtonStyling,
    ctaHorizontalAlignment,
    detailsLinkFontColor = '',
    desktopImageOrIconSize = '24px',
    mobileImageOrIconSize = '14px',
    verticalTextAlignment = 'top',
    verticalCtaAlignment = 'bottom',
    showHideBasedOnScreenSize,
  } = webAppearance || {};
  const firstCTA = cta || primaryCTA;
  const secondCTA = cta2 || secondaryCTA;
  const isDesktopVP = useViewportIsLarge();
  const isDesktop = useViewportIsXLarge();
  const isTablet = useViewportIsLarge() && !isDesktop;
  const currentViewport = isDesktop ? 'desktop' : isTablet ? 'tablet' : 'mobile';

  const { rteAndIconsGridRow, ctaGridRow, gridTemplateRows } = gridRowVerticalAlignment(
    verticalTextAlignment as VerticalAlignmentProps,
    verticalCtaAlignment as VerticalCtaAlignment
  );

  const isCtaRteBottom =
    (verticalTextAlignment === 'bottom' && verticalCtaAlignment === 'bottom') || (verticalTextAlignment === 'middle' && verticalCtaAlignment === 'middle');
  const imageOrIconClassNames = classnames('mui_ism-full-image__image-or-icon-content', {
    'mui_ism__cta-rte-bottom': isCtaRteBottom,
  });
  const imageOrIconSize = isDesktopVP ? desktopImageOrIconSize : mobileImageOrIconSize;
  const imageOrIcon = image ? (
    <div className='mui_ism-full-image__image-or-icon'>
      <ISMBannerImageOrIcon size={imageOrIconSize} src={image} />
    </div>
  ) : undefined;

  const imageOrIconContent = (
    <div className={imageOrIconClassNames}>
      {imageOrIconPlacement === 'above' && imageOrIcon}
      {text && <RichText text={text} />}
      {imageOrIconPlacement !== 'above' && imageOrIcon}
    </div>
  );

  const detailsContent = getDetailsContent(Brands.OldNavy, pemoleCode, htmlModalUrl);
  const detailsLinkContent = (
    <div className='mui_ism-full-image__details-link-button'>
      <DetailsButton color={detailsLinkFontColor} label={detailsLink} prefix={detailsPrefix} value={detailsContent} />
    </div>
  );

  const isCtaButtonsInline = ['chevron', 'underline', 'flat'].includes(ctaButtonStyling?.buttonStyle || '');
  const hasFirstCTA = firstCTA?.label;
  const hasSecondCTA = secondCTA?.label;
  const hasCTAs = hasFirstCTA || hasSecondCTA;

  const ctaContainerContent = (
    <CtaContainer className='mui_ism-full-image__cta-container' data-testid='cta-container'>
      {hasFirstCTA && (
        <CtaButton
          alignment={'center'}
          ctaButton={firstCTA}
          ctaButtonStyling={ctaButtonStyling as CtaButtonStylingType}
          ctaSize='small'
          fullWidth={!isCtaButtonsInline}
          isDesktop={isDesktopVP}
        />
      )}
      {hasSecondCTA && (
        <CtaButton
          alignment={'center'}
          ctaButton={secondCTA}
          ctaButtonStyling={ctaButtonStyling as CtaButtonStylingType}
          ctaSize='small'
          fullWidth={!isCtaButtonsInline}
          isDesktop={isDesktopVP}
        />
      )}
    </CtaContainer>
  );

  const fullImageContainerClassnames = classnames('mui_ism-full-image__container', {
    'flex-container': hasPlpGrid2025,
    'single-column': hasPlpGrid2025 && !isDouble,
    'double-column': hasPlpGrid2025 && isDouble,
  });

  const ctaPadding = (() => {
    if (verticalCtaAlignment === 'bottom') {
      return detailsLink ? '15px 0 30px' : '45px 0 0';
    }
    return '15px 0 0';
  })();

  let aspectWidth = isDesktopVP ? 234 : 160;
  if (isDouble) {
    aspectWidth = isDesktopVP ? 484 : 335;
  }

  const useIsmGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] : 0;
  const useIsmProductGridHeight: number = hasPlpGrid2025 ? ismProductGridHeight[currentViewport] : 0;
  const useIsmDoubleGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] * 2 : 0;
  const productGridAspectRatio = hasPlpGrid2025 ? `${aspectWidth} / ${useIsmProductGridHeight}` : '';

  const componentStyles = `
    :root {
      --ism--container-height: '';
      --ism--padding: ${isDesktopVP ? '20px' : '15px'};
      --ism--grid-template-rows: ${gridTemplateRows};
      --ism--cta-flex-direction: row;
      --ism--cta-padding: ${ctaPadding};
      --ism--flex-basis: ${useIsmGridSizing}%;
      --ism--max-width: ${useIsmGridSizing}%;
      --ism--flex-basis-double: ${useIsmDoubleGridSizing}%;
      --ism--max-width-double: ${useIsmDoubleGridSizing}%;
      --ism--aspect-ratio: ${productGridAspectRatio};
      --ism--grid-item-min-height: ${useIsmProductGridHeight}px;
      --ism--cta-horizontal-alignment: ${ctaHorizontalAlignment};
      --ism--cta-grid-row: ${ctaGridRow};
      --ism--imageOrIcon-padding: ${imageOrIconPlacement === 'above' ? '0 0 20px' : '20px 0 0'};
      --ism--imageOrIcon-text-align: ${imageOrIconHorizontalAlignment}; 
      --ism--imageOrIcon-content-grid-row: ${rteAndIconsGridRow};
      --ism--details-link-grid-row: ${getDetailsButtonGridRow(verticalCtaAlignment, verticalTextAlignment)};
      --ism--details-link-vertical-cta-align: ${getDetailsButtonCtaAlignment(verticalCtaAlignment)};
      --ism--details-link-justify-content: ${getDetailsButtonJustifyContent(detailsLinkLocation)};
    }
  `;

  return (
    <>
      <style>{componentStyles}</style>
      <div className={fullImageContainerClassnames} data-testid='ism-full-image'>
        <ISMBannerContainer aspectRatio='' showHideBasedOnScreenSize={showHideBasedOnScreenSize} useAspectRatio={false}>
          <ISMBannerContent background={background}>
            <BannerContent className='mui_ism-full-image__banner-content'>
              {imageOrIconContent}
              {hasCTAs && ctaContainerContent}
              {detailsLink && detailsLinkContent}
            </BannerContent>
            {bannerLink && <ISMOverlay title={bannerLink.label} to={bannerLink.value} type='link' />}
          </ISMBannerContent>
        </ISMBannerContainer>
      </div>
    </>
  );
};
export default OldNavyInSortMarketingBannerV2;
