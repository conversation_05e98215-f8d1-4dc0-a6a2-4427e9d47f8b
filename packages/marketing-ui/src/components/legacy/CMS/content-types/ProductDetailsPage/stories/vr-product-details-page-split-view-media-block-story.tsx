'use client';
import { Locale, LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { Meta, StoryFn } from '@storybook/react';
import React from 'react';
import { translations } from '../../../../helper/localTRO';
import ProductDetailsPage from '..';
import { productDetailsPageSplitViewMediaBlockPositionRightForON, productDetailsPageSplitViewMediaBlockPositionLeftForON } from '../__fixtures__/test-data';

const locale: Locale = 'en_US';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/ProductDetailsPage/VisualRegression/SplitViewMediaBlock',
  parameters: {
    knobs: {
      disable: true,
    },
    layout: 'fullscreen',
  },
  tags: ['visual:check', 'exclude-br', 'exclude-gap', 'exclude-at', 'desktop-only'],
  decorators: [
    (StoryFn: StoryFn) => (
      <LocalizationProvider market='us' locale={locale} translations={translations[normalizeLocale(locale)].translation}>
        <StoryFn />
      </LocalizationProvider>
    ),
  ],
} as Meta<typeof ProductDetailsPage>;

export const VisualRegressionTestSplitViewMediaBlockPositionRight: StoryFn = () => (
  <div>
    <div style={{ fontSize: 25 }}>Product Details Page - Split View - Media Block Positioning Right for Desktop - Visual Regression (Old Navy Only)</div>
    <div style={{ paddingBottom: 30 }} />
    <div style={{ fontSize: 20 }}>Split View 50/50 Desktop Layout with Media Block on Right and Mobile Layout with 4/5 aspect ratio</div>
    <div style={{ paddingBottom: 30 }} />
    <ProductDetailsPage {...productDetailsPageSplitViewMediaBlockPositionRightForON} />
  </div>
);

export const VisualRegressionTestSplitViewMediaBlockPositionLeft: StoryFn = () => (
  <div>
    <div style={{ fontSize: 25 }}>Product Details Page - Split View - Media Block Positioning Left for Desktop - Visual Regression (Old Navy Only)</div>
    <div style={{ paddingBottom: 30 }} />
    <div style={{ fontSize: 20 }}>Split View 50/50 Desktop Layout with Media Block on Left and Mobile Layout with 2/1 aspect ratio</div>
    <div style={{ paddingBottom: 30 }} />
    <ProductDetailsPage {...productDetailsPageSplitViewMediaBlockPositionLeftForON} />
  </div>
);
