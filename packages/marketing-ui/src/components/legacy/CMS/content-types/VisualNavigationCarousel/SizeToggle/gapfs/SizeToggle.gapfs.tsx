// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject, css } from '@ecom-next/core/react-stitch';
import VisualNavigationCarousel from '../../../../components/VisualNavigationCarousel';
import { VisualNavigationSizeToggleCarouselProps } from '../types';
import SizeToggleContainer from '../components/SizeTogglesContainer';
import { useToggleCategoryCardState } from '../../../VisualNavigation/VisualNavigationSizeToggle/hooks/useToggleCategoryCardState';
import CategoryCard from '../../../VisualNavigation/components/VisualNavigationCard.gapfs';
import { ShowHideWrapper } from '../../../../subcomponents/ShowHideWrapper';
import { ComponentMaxWidth } from '../../../../subcomponents/ComponentMaxWidth';
import VisualNavigationContainer from '../../../../components/visual-navigation/VisualNavigationContainer';
import useMinScale from '../../../../components/VisualNavigationCarousel/hooks/useMinScale';
import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge';
import { SLICK_CURRENT_CLASSNAME } from '../../../../global/constants/slick-carousel';
import { useThemeColors, useSelectedState } from '../../../../../hooks';

/** [width, height] */
const DESKTOP_ASPECT_RATIO = [213.35, 365];
const MOBILE_ASPECT_RATIO = [150, 235];

const VisualNavigationSizeToggleCarousel = ({ headline, toggles, categoryCards, webAppearance }: VisualNavigationSizeToggleCarouselProps) => {
  const { chevronColor } = webAppearance;
  const isLargeVP = useViewportIsLarge();
  const colors = useThemeColors();
  const [cards, toggleState] = useToggleCategoryCardState(categoryCards, toggles);
  const selectedURL = useSelectedState(cards.map(card => ({ label: '', value: card.url?.value || '' })));
  const [width, height] = isLargeVP ? DESKTOP_ASPECT_RATIO : MOBILE_ASPECT_RATIO;
  const arrowCss: CSSObject = {
    top: useMinScale(height / 2),
  };

  const getCardBorderStyle = (): CSSObject => {
    const cardFooterBorderStyle = `1px solid ${colors.b1}`;
    const CARD_FOOTER_SELECTOR = 'a > div:nth-of-type(2)';
    const firstVisibleCard = `.${SLICK_CURRENT_CLASSNAME} ${CARD_FOOTER_SELECTOR}`;

    return {
      [CARD_FOOTER_SELECTOR]: {
        borderBottom: cardFooterBorderStyle,
        borderLeft: cardFooterBorderStyle,
      },
      [firstVisibleCard]: {
        borderLeft: 'none',
      },
    };
  };

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance.showHideBasedOnScreenSize}>
      <ComponentMaxWidth>
        <VisualNavigationContainer
          headline={headline}
          headlineHorizontalPadding={!isLargeVP ? 13 : undefined}
          headlinePaddingBottom={isLargeVP ? 20 : undefined}
          innerContainerCSS={{ flexDirection: 'column' }}
        >
          <SizeToggleContainer
            onClick={toggleState.set}
            selected={toggleState.value}
            toggleCss={{
              borderBottom: `1px solid ${colors.b1}`,
              padding: useMinScale(8),
              flex: `1 1 calc(100% / ${toggles.length})`,
            }}
            toggles={toggles}
          />
          <VisualNavigationCarousel arrowCss={arrowCss} chevronColor={chevronColor} containerCss={css(getCardBorderStyle())} slideWidth={width}>
            {cards.map(card => (
              <CategoryCard
                key={`category-card-${card.heading}`}
                card={card}
                css={{ height: '100%' }}
                imageAspectRatio={`${width}:${height}`}
                selected={!!selectedURL && selectedURL === card.url?.value}
                showHover={isLargeVP}
              />
            ))}
          </VisualNavigationCarousel>
        </VisualNavigationContainer>
      </ComponentMaxWidth>
    </ShowHideWrapper>
  );
};

export default VisualNavigationSizeToggleCarousel;
