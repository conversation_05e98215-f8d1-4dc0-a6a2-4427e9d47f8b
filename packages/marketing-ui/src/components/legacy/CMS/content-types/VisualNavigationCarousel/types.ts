// @ts-nocheck
'use client';
import { DesktopImageOrIconSizeType, MobileImageOrIconSizeType } from '../../subcomponents/ImageOrIcon/types';
import { ChevronColorType } from '../../components/VisualNavigationCarousel/types';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';

export interface VisualNavigationCarouselWebAppearance {
  imageOrIconPlacement?: 'above' | 'below';
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
  chevronColor: ChevronColorType;
  desktopImageOrIconSize?: DesktopImageOrIconSizeType;
  mobileImageOrIconSize?: MobileImageOrIconSizeType;
}

export type NavigationButtonBackgroundMapType = {
  [key in ChevronColorType]: string;
};
