// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import { Locale, LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { AppState, AppStateProvider, DataLayer } from '@ecom-next/sitewide/app-state-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import { translations } from '../../../../helper/localTRO';
import README from '../README.mdx';
import StorytellingAndProductRatingContentType from '../index';
import { storytellingAndProductRatingVideoCompA, storytellingAndProductRatingVideoCompB } from '../__fixtures__/vr-spr-video-case-ab';
import { storytellingAndProductRatingVideoCompC, storytellingAndProductRatingVideoCompD } from '../__fixtures__/vr-spr-video-case-cd';
import sampleVideo from '../../../../public/video/SampleVideo_1280x720_2mb.mp4';
import sampleWebmVideo from '../../../../public/video/SampleVideo_1280x720_2mb.webm';

const locale: Locale = 'en-US';

type DefaultAppState = AppState & {
  abbrBrand: Brands;
  secrets: Record<string, unknown>;
};

const defaultAppState = {
  brandName: Brands.Gap,
  abbrBrand: Brands.Gap,
  market: 'us',
  locale: 'en_US',
  criticalCss: [],
  criticalResources: [],
  analytics: {},
  apis: {
    promoDrawerApi: 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com',
  },
  datalayer: {
    add: () => {},
    build: () => {},
    link: async () => {},
    builderNames: () => {},
    isTealiumReady: () => {},
    view: async () => {},
  } as unknown as DataLayer,
  pageType: 'sitewide',
  secrets: {
    smsKey: {
      apikey: 'test',
    },
    communicationPreferenceAPI: {
      api: 'https://ecom-oidc-proxy.test.gaptechol.com/commerce/communication-preference',
    },
  },
};

const appState = {
  ...defaultAppState,
  brandName: 'at',
  abbrBrand: 'at',
} as DefaultAppState;

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/StorytellingAndProductRatingVideo',
  parameters: {
    docs: {
      page: README,
    },
    knobs: {
      disable: true,
    },
    layout: 'fullscreen',
    eyes: { include: false },
  },
  tags: ['exclude'],
  decorators: [
    (StoryFn: StoryFn) => (
      <AppStateProvider value={appState}>
        <LocalizationProvider locale={locale} translations={translations[normalizeLocale(locale)].translation}>
          <StoryFn />
        </LocalizationProvider>
      </AppStateProvider>
    ),
  ],
};
export const Playground: StoryFn = args => {
  const { data, useLocalSampleVideo, useWebmVideo } = args;
  const videoSrc = useWebmVideo ? sampleWebmVideo : sampleVideo;

  let updatedProps = { ...data };

  if (useLocalSampleVideo) {
    updatedProps = {
      ...data,
      defaultVideo: {
        ...data.defaultVideo,
        desktop: {
          ...data.defaultVideo!.desktop,
          url: `http://localhost:6006/${videoSrc}`,
          controls: true,
          muted: true,
          playIcon: {
            alt: 'play',
            src: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='100%25' version='1.1' viewBox='0 0 32 32' width='100%25'%3E%3Cpath d='m 12.59,20.34 4.58,-4.59 -4.58,-4.59 1.41,-1.41 6,6 -6,6 z' fill='%23fff'%3E%3C/path%3E%3C/svg%3E",
          },
        },
      },
    };
  }
  return (
    <span data-testid='playwright-vr'>
      <StorytellingAndProductRatingContentType {...updatedProps} />
    </span>
  );
};

Playground.args = {
  data: storytellingAndProductRatingVideoCompA,
  useLocalSampleVideo: false,
  useWebmVideo: false,
  eyes: { include: false },
};

export const VRTestCompA: StoryFn = () => (
  <div
    css={{
      display: 'flex',
      flexDirection: 'column',
      gap: '30px',
      fontSize: '13px',
    }}
  >
    <div style={{ fontSize: 18 }}>StorytellingAndProductRatingVideo - Visual Regression (AT Only)</div>
    <div>SPRvideo-A: Solid Background, Secondary Image, RT1, RT2</div>
    <StorytellingAndProductRatingContentType {...storytellingAndProductRatingVideoCompA} />
  </div>
);

export const VRTestCompB: StoryFn = () => (
  <div
    css={{
      display: 'flex',
      flexDirection: 'column',
      gap: '30px',
      fontSize: '13px',
    }}
  >
    <div style={{ fontSize: 18 }}>StorytellingAndProductRatingVideo - Visual Regression (AT Only)</div>
    <div>SPRvideo-B: Gradient Background, Secondary Image, NO RT1, NO RT2</div>
    <StorytellingAndProductRatingContentType {...storytellingAndProductRatingVideoCompB} />
  </div>
);

export const VRTestCompC: StoryFn = () => (
  <div
    css={{
      display: 'flex',
      flexDirection: 'column',
      gap: '30px',
      fontSize: '13px',
    }}
  >
    <div style={{ fontSize: 18 }}>StorytellingAndProductRatingVideo - Visual Regression (AT Only)</div>
    <div>SPRvideo-C: Image Background, NO Secondary Image, RT1, RT2</div>
    <StorytellingAndProductRatingContentType {...storytellingAndProductRatingVideoCompC} />
  </div>
);

export const VRTestCompD: StoryFn = () => (
  <div
    css={{
      display: 'flex',
      flexDirection: 'column',
      gap: '30px',
      fontSize: '13px',
    }}
  >
    <div style={{ fontSize: 18 }}>StorytellingAndProductRatingVideo - Visual Regression (AT Only)</div>
    <div>SPRvideo-D: Solid Background, light Rating, NO Secondary Image</div>
    <StorytellingAndProductRatingContentType {...storytellingAndProductRatingVideoCompD} />
  </div>
);
