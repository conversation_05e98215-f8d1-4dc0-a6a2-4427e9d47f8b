import React from 'react';
// @ts-ignore
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { axe } from 'jest-axe';
// @ts-ignore
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { render } from 'test-utils';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';
import { setupRender } from '../../../test-helpers/index';
import CircleNavigation from './CircleNavigation.on';
import { circleNavigationCarouselData, circleNavigationExposedData, exampleText } from './__fixtures__/test-data';

const _renderExposed = setupRender(CircleNavigation, circleNavigationExposedData);
const _renderCarousel = setupRender(CircleNavigation, circleNavigationCarouselData);

describe('Circle Navigation', () => {
  describe('ComponentMaxWidth', () => {
    it('should render max-width when mui-new-plp-grid-2025 is false', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'mui-new-plp-grid-2025': false }}>
          <CircleNavigation {...circleNavigationCarouselData} />
        </StitchStyleProvider>
      );

      expect(container.firstChild.firstChild.firstChild).toHaveStyleRule('max-width', '1440px');
    });

    it('should not render max-width when mui-new-plp-grid-2025 is true', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'mui-new-plp-grid-2025': true }}>
          <CircleNavigation {...circleNavigationCarouselData} />
        </StitchStyleProvider>
      );

      expect(container.firstChild.firstChild.firstChild).not.toHaveStyleRule('max-width');
    });
  });

  it('should match the snapshots in Large 768-1023 vw for exposed layout', () => {
    const { container } = _renderExposed(circleNavigationExposedData, Brands.OldNavy, LARGE);
    expect(container).toMatchSnapshot();
  });

  it('should match the snapshots in mobile', () => {
    const { container } = _renderExposed(circleNavigationExposedData, Brands.OldNavy, SMALL);
    expect(container).toMatchSnapshot();
  });

  it('should match the snapshots in desktop for carousel/slider layout', () => {
    const { container } = _renderCarousel(circleNavigationCarouselData, Brands.OldNavy, LARGE);
    expect(container).toMatchSnapshot();
  });

  describe('rich text scaling', () => {
    it('should have a maximum size set by home slot', () => {
      const { getByText } = _renderCarousel(
        {
          ...circleNavigationCarouselData,
          marketingType: 'home',
        },
        Brands.OldNavy,
        LARGE
      );
      expect(getByText(exampleText)).toHaveStyle({
        fontSize: 'max(20px, min(1.5625vw, 22.5px))',
      });
    });
    it('should have a maximum size set by ebb slot', () => {
      const { getByText } = _renderCarousel(
        {
          ...circleNavigationCarouselData,
          marketingType: 'ebb',
        },
        Brands.OldNavy,
        LARGE
      );
      expect(getByText(exampleText)).toHaveStyle({
        fontSize: 'max(20px, min(1.5625vw, 20px))',
      });
    });
  });

  it('should not have a11y violations', async () => {
    const { container } = _renderExposed(circleNavigationExposedData, Brands.OldNavy, LARGE);
    expect(await axe(container)).toHaveNoViolations();
  });

  describe('showHide functionality for breakpoints', () => {
    it("should appear on mobile if showHide is 'alwaysShow'", () => {
      const mobileData = {
        ...circleNavigationExposedData,
        webAppearance: {
          ...circleNavigationExposedData.webAppearance,
          showHide: 'alwaysShow' as ShowHideBasedOnScreenSizeProps,
        },
      };

      const { container } = _renderExposed(mobileData, Brands.OldNavy, SMALL);
      expect(container.firstChild).toBeInTheDocument();
    });

    it("should not appear on mobile if showHide is 'hideOnMobile'", () => {
      const mobileData = {
        ...circleNavigationExposedData,
        webAppearance: {
          ...circleNavigationExposedData.webAppearance,
          showHide: 'hideOnMobile' as ShowHideBasedOnScreenSizeProps,
        },
      };

      const { container } = _renderExposed(mobileData, Brands.OldNavy, SMALL);
      expect(container.firstChild.firstChild).not.toBeInTheDocument();
    });

    it("should appear on desktop if showHide is 'alwaysShow'", () => {
      const desktopData = {
        ...circleNavigationExposedData,
        webAppearance: {
          ...circleNavigationExposedData.webAppearance,
          showHide: 'alwaysShow' as ShowHideBasedOnScreenSizeProps,
        },
      };

      const { container } = _renderExposed(desktopData, Brands.OldNavy, LARGE);

      expect(container.firstChild).toBeInTheDocument();
    });

    it("should not appear on desktop if showHide is 'hideOnDesktop'", () => {
      const desktopData = {
        ...circleNavigationExposedData,
        webAppearance: {
          ...circleNavigationExposedData.webAppearance,
          showHide: 'hideOnDesktop' as ShowHideBasedOnScreenSizeProps,
        },
      };

      const { container } = _renderExposed(desktopData, Brands.OldNavy, LARGE);
      expect(container.firstChild.firstChild).not.toBeInTheDocument();
    });
  });

  describe('card content', () => {
    const breakpointWithViewPort = [
      { breakpoint: LARGE, viewport: 'desktop' },
      { breakpoint: SMALL, viewport: 'mobile' },
    ];
    breakpointWithViewPort.forEach(item => {
      it(`render custom font color for ${item.viewport}`, async () => {
        circleNavigationExposedData.webAppearance.fontColor = '#0000FF';
        const { fontColor } = circleNavigationExposedData.webAppearance;
        const { label } = circleNavigationCarouselData.categoryInfo[0];
        const { getByText } = _renderExposed(circleNavigationExposedData, Brands.OldNavy, item.breakpoint as Size);

        expect(getByText(label)).toHaveStyleRules({ color: fontColor });
      });
    });
  });
});
