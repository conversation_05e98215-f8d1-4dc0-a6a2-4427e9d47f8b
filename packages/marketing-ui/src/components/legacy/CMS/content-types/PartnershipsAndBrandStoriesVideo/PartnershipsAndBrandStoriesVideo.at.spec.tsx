// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { partnershipsAndBrandStoriesVideoData } from './__fixtures__/test-data';
import AthletaPartnershipsAndBrandStories from '../PartnershipsAndBrandStories/PartnershipsAndBrandStories.at';

describe('Athelta PartnershipsAndBrandStoriesVideo', () => {
  [SMALL, LARGE].forEach(size => {
    it(`should render on ${size} viewport`, () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <AthletaPartnershipsAndBrandStories {...partnershipsAndBrandStoriesVideoData} />
        </StitchStyleProvider>,
        { breakpoint: size as Size }
      );
      expect(container).toMatchSnapshot();
    });
  });
});
