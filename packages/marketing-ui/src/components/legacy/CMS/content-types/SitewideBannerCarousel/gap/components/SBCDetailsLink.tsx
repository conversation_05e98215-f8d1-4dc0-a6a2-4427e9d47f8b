// @ts-nocheck
'use client';
import React from 'react';
import { useTheme } from '@ecom-next/core/react-stitch';
import { DetailsButton, getDetailsContent } from '../../../../subcomponents/Details';
import { detailsButtonCustomStyles } from '../../commonStyles';
import { DetailsContainer } from '../styles';

type SBCDetailsLinkProps = {
  detailsLink: string;
  detailsPrefix?: string;
  pemoleCode?: string;
  htmlModalUrl?: string;
  detailsLinkFontColor?: string;
  detailsPrefixFontColor?: string;
  isSingleBanner: boolean;
  hideChevrons?: boolean;
  isAutoplay: boolean;
  isLargeVP: boolean;
  isStacked: boolean;
};

export const SBCDetailsLink = ({
  detailsLink,
  detailsPrefix,
  pemoleCode,
  htmlModalUrl,
  detailsLinkFontColor,
  detailsPrefixFontColor,
  isSingleBanner,
  hideChevrons,
  isAutoplay,
  isLargeVP,
  isStacked,
}: SBCDetailsLinkProps) => {
  const brand = useTheme().brand;
  const detailsContent = getDetailsContent(brand, pemoleCode, htmlModalUrl);

  return (
    <div
      css={{
        position: 'absolute',
        right: isSingleBanner || (hideChevrons && !isAutoplay) ? 10 : 0,
        bottom: !isLargeVP ? 10 : 0,
        height: !isLargeVP ? 'auto' : '100%',
        display: 'flex',
        ...(!isLargeVP ? { width: 'auto' } : {}),
        padding: isSingleBanner || (hideChevrons && !isAutoplay) ? '0' : '0 5px 0 0',
      }}
    >
      {!isLargeVP && isStacked ? <div css={{ flexGrow: 1 }} /> : null}
      <DetailsContainer isLargeVP={isLargeVP} isStacked={!!isStacked}>
        <DetailsButton
          color={detailsLinkFontColor}
          customStyles={detailsButtonCustomStyles}
          label={detailsLink}
          prefix={detailsPrefix}
          prefixColor={detailsPrefixFontColor}
          value={detailsContent}
        />
      </DetailsContainer>
    </div>
  );
};
