// @ts-nocheck
import React from 'react';
import { fireEvent, render, screen, act } from 'test-utils';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { sitewideBannerCarouselBasicData } from '../../__fixtures__/test-data';
import { GapSitewideBannerCarousel, SitewideBannerCarouselProps } from '..';

const renderGapSitewideBannerCarousel = (props: SitewideBannerCarouselProps) =>
  render(
    <StitchStyleProvider brand={Brands.Gap}>
      <GapSitewideBannerCarousel {...props} />
    </StitchStyleProvider>
  );

describe('Details Button', () => {
  it('should render the details button when detailsLink is provided', () => {
    const props: SitewideBannerCarouselProps = {
      ...sitewideBannerCarouselBasicData,
      carouselBanners: [
        {
          ...sitewideBannerCarouselBasicData.carouselBanners[0],
          detailsLink: 'See Details',
        },
      ],
    };

    renderGapSitewideBannerCarousel(props);
    const detailsButton = screen.getByRole('button', { name: 'See Details' });
    expect(detailsButton).toBeInTheDocument();
  });

  it('should not render the details button when detailsLink is not provided', () => {
    const props: SitewideBannerCarouselProps = {
      ...sitewideBannerCarouselBasicData,
      carouselBanners: [
        {
          ...sitewideBannerCarouselBasicData.carouselBanners[0],
          detailsLink: undefined,
        },
      ],
    };

    renderGapSitewideBannerCarousel(props);
    const detailsButton = screen.queryByRole('button', {
      name: 'See Details',
    });
    expect(detailsButton).not.toBeInTheDocument();
  });

  it('should render the details prefix when provided', () => {
    const props: SitewideBannerCarouselProps = {
      ...sitewideBannerCarouselBasicData,
      carouselBanners: [
        {
          ...sitewideBannerCarouselBasicData.carouselBanners[0],
          detailsLink: 'See Details',
          detailsPrefix: 'Exclusions Apply',
        },
      ],
    };

    renderGapSitewideBannerCarousel(props);
    const detailsPrefix = screen.getByText('Exclusions Apply');
    expect(detailsPrefix).toBeInTheDocument();
  });

  it('should open a modal when the details button is clicked', async () => {
    const props: SitewideBannerCarouselProps = {
      ...sitewideBannerCarouselBasicData,
      carouselBanners: [
        {
          ...sitewideBannerCarouselBasicData.carouselBanners[0],
          detailsLink: 'See Details',
          pemoleCode: '123456',
        },
      ],
    };

    renderGapSitewideBannerCarousel(props);
    const detailsButton = screen.getByRole('button', { name: 'See Details' });
    await act(async () => {
      fireEvent.click(detailsButton);
    });

    const modal = screen.getByRole('dialog');
    expect(modal).toBeInTheDocument();
  });
});
