'use client';
// @ts-ignore
import { CSSObject, styled } from '@ecom-next/core/react-stitch';

export const DetailLinkContainer = styled.div<{ customDetailContainerStyles?: CSSObject }>(({ customDetailContainerStyles }) => {
  const baseCss = {
    position: 'absolute',
    display: 'flex',
    width: '100%',
    alignItems: 'end',
    justifyContent: 'center',
    textAlign: 'center',
    bottom: 0,
    zIndex: 3,
    pointerEvents: 'none',
  };
  return { ...baseCss, ...customDetailContainerStyles } as CSSObject;
});

export const DetailsWrapper = styled.div<{ customDetailWrapperStyles?: CSSObject }>(() => ({ customDetailWrapperStyles }) => {
  const baseCss = {
    padding: '0 16px 12px 16px',
    position: 'relative',
    zIndex: 1,
    pointerEvnets: 'none',
    alignItems: 'baseline',
  };
  return { ...baseCss, ...customDetailWrapperStyles } as CSSObject;
});
