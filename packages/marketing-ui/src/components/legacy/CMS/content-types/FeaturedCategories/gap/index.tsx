// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { FeaturedCategoriesContentType } from '../types';
import { FeaturedCategories } from '../../../components/featured-categories/FeaturedCategories';
import { ShowHideWrapper } from '../../../subcomponents/ShowHideWrapper';
import { useViewportIsLarge } from '../../../../hooks';

export type FeaturedCategoriesTypes = FeaturedCategoriesContentType;

const GapFeaturedCategories = (props: FeaturedCategoriesTypes): JSX.Element | null => {
  const isDesktop = useViewportIsLarge();
  const enabledFeatures = useEnabledFeatures();
  const isVideoRedesignEnabled = !!enabledFeatures?.['fui-gap-video-icons-2024'];

  const { layout, mobileLayoutType } = props.general;
  const { showHideBasedOnScreenSize = 'alwaysShow' } = props.showHideBasedOnScreenSize || {
    showHideBasedOnScreenSize: 'alwaysShow',
  };

  const isInsetLayout = layout === 'inset';
  const videoIconSizeRedesign2024 = isDesktop ? '30px' : '28px';

  const extendedProps = {
    customCategoryCardStyles: {
      desktop: {
        boxSizing: 'border-box',
        padding: isInsetLayout ? '30px 20px' : '30px 35px',
        height: 'auto !important',
        width: '100% !important',
        display: 'flex !important',
        flexDirection: 'column',
        gap: '20px',
      } as CSSObject,
      mobile: {
        boxSizing: 'border-box',
        padding: '20px 15px',
        height: 'auto !important',
        width: '100% !important',
        display: 'flex !important',
        flexDirection: 'column',
        gap: '20px',
      } as CSSObject,
    },
    customCategoryCardContentStyles: {
      desktop: {
        boxSizing: 'border-box',
        height: 'auto !important',
        width: '100% !important',
        display: 'flex !important',
        flexDirection: 'row',
        gap: '15px',
      } as CSSObject,
      mobile: {
        boxSizing: 'border-box',
        height: 'auto !important',
        width: '100% !important',
        display: 'flex',
        flexDirection: 'row',
        gap: '15px',
      } as CSSObject,
    },
    customContainerStyles: {
      desktop: {
        ...(isInsetLayout
          ? {
              padding: '30px 20px',
              '& > div > div': {
                gap: 30,
              },
            }
          : {
              padding: '0',
            }),
      },
      mobile: {
        ...(isInsetLayout
          ? {
              padding: '16px 0px',
              '& > div': {
                gap: 30,
                display: 'grid',
                margin: mobileLayoutType === 'exposed' ? '0 16px 0 16px' : '0 0 0 16px',
              },
            }
          : {
              padding: '0',
            }),
      },
    },
    customHeadlineStyles: {
      desktop: {
        marginBottom: 'unset',
        marginLeft: 'unset',
        margin: isInsetLayout ? '0 0 30px 15px' : '0 0 30px 35px',
      },
      mobile: {
        margin: '0 0 15px 16px',
      },
    },
    customVideoControlStyles: isVideoRedesignEnabled
      ? ({
          height: videoIconSizeRedesign2024,
          '& > button': {
            left: 0,
            bottom: 0,
            zIndex: 4,
            position: 'relative',
            width: videoIconSizeRedesign2024,
            height: videoIconSizeRedesign2024,
          },

          '& > div > div:before': {
            left: 0,
            bottom: `-${videoIconSizeRedesign2024}`,
            width: videoIconSizeRedesign2024,
            height: videoIconSizeRedesign2024,
            borderRadius: '0',
          },
        } as CSSObject)
      : ({} as CSSObject),
    hasCategoryCardWrapper: true,
  };

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
      <FeaturedCategories {...props} {...extendedProps} />
    </ShowHideWrapper>
  );
};

export default GapFeaturedCategories;
