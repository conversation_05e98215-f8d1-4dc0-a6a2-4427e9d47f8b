// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { ProductCardTypes } from '../../components/product-cards/types';
import { CtaButtonStylingProps } from '../../subcomponents/CTAButton';
import { CTAContainer } from '../CTADropdown/components/CTAContainer';
import CTADropdown from '../../subcomponents/CTADropdown';

import { CategoryCardsPropsContentType } from './types';
import { mobileCTADropdownSideNegMarginScalingVal } from './CategoryCards.on';
import { getCustomMobileLayoutStyles } from './styles';

export type CategoryCardsProps = CategoryCardsPropsContentType;

const CtaGridWrapper = styled.div(({ rowNumber }: { rowNumber: number }) => ({
  gridArea: `CTAs${rowNumber}`,
}));

/**
 *
 * @description function returns grid areas based upon the value of @param numberOfCards
 * @returns a string eg. " '. .' 'CTAs{number} CTAs{number}' "
 *
 */
export const setGridTemplateAreas = (numberOfCards: number): string => {
  const cardLength = numberOfCards / 2;
  const areas = Array.from({ length: cardLength }, (_, i) => `'. .' 'CTAs${i} CTAs${i}'`).join(' ');

  return areas;
};

export const renderProductCardCtas = (
  categoryItems: ProductCardTypes[],
  size: number,
  mobileLayoutOption: string,
  ctaButtonStyling?: CtaButtonStylingProps,
  isOnCtaRedesign2024Enabled?: boolean
): React.JSX.Element => {
  const ctas = categoryItems.map(({ ctaDropdown }) =>
    ctaDropdown !== undefined ? (
      <CTADropdown key={ctaDropdown?.label} ctaButtonStyling={ctaButtonStyling} heading={ctaDropdown?.label} items={ctaDropdown?.ctaDropdown} width='100%' />
    ) : null
  );
  const ctaLength = Math.ceil(ctas.length / size);
  const renderedCTAs = Array.from({ length: ctaLength }, (_, i) => {
    const start = i * size;
    const end = start + size;
    const sliceCtas = ctas.slice(start, end);
    const filteredCtas = sliceCtas.filter(entry => entry !== null && entry);
    const setCtaMargin = filteredCtas.length === 0 ? '36px 0 0 0' : '24px 0';
    const gridWrapperCss = {
      '&:last-of-type': { margin: '24px 0 0 0' },
      '&:not(last-of-type)': { margin: setCtaMargin },
    };
    const ctaContainerCss = {
      gap: 12,
      margin: `0 ${mobileCTADropdownSideNegMarginScalingVal}vw`,
      alignItems: !isOnCtaRedesign2024Enabled ? 'normal' : 'center',
    };
    return (
      <CtaGridWrapper key={i} css={gridWrapperCss} rowNumber={i}>
        <CTAContainer
          css={mobileLayoutOption === 'linear' ? getCustomMobileLayoutStyles(categoryItems.length, ctaButtonStyling) : ctaContainerCss}
          data-testid='category-cta-container'
        >
          {filteredCtas}
        </CTAContainer>
      </CtaGridWrapper>
    );
  });

  return <>{renderedCTAs}</>;
};
