'use client';

import React, { useState } from 'react'; // @ts-ignore
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { useIsmVideoButtonStyles } from '../../ISMBanner/useVideoButtonStyles/index';
import { ISMBannerContainer, ISMBannerContent } from '../../../components/ism-banner/index';
import { InSortMarketingBannerFullVideoContentType } from '../types';
import { RichText } from '../../../subcomponents/RichText/index';
import { CtaButton } from '../../../subcomponents/CTAButton/index';
import { DetailsButton, getDetailsContent } from '../../../subcomponents/Details/index';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge/index';
import ISMOverlay from '../../../components/ism-banner/ISMOverlay';
import ISMBannerImageOrIcon from '../../../components/ism-banner/ISMBannerImageOrIcon';
import { gridRowVerticalAlignment } from '../../../components/ism-banner/helpers/gridRowVerticalAlignment';
import { VerticalAlignmentProps, VerticalCtaAlignment } from '../../../global/types';
import { defaultPlaying } from '../../../components/ism-banner/helpers/videoParams';
import { customControls, isDisplayOnHover, isNonVimeoVideo } from '../../ISMBanner/videoHelpers';
import { mapPlacementToPosition, usePlayPausePosition } from '../../ISMCarousel/helpers';
import { detailsBtnStyles, detailsPrefixBtnStyles } from '../../ISMBanner/styles';
import OldNavyInSortMarketingBannerVideoV2 from './ism-fullvideo-on-v2';

export type InSortMarketingBannerProps = InSortMarketingBannerFullVideoContentType;

const OldNavyInSortMarketingBannerVideoLegacy = (props: InSortMarketingBannerProps): React.JSX.Element => {
  const {
    _meta,
    vimeoVideo,
    nonVimeoVideo,
    bannerLink,
    image,
    webAppearance,
    text,
    cta,
    primaryCTA,
    secondaryCTA,
    detailsLink,
    detailsPrefix,
    htmlModalUrl,
    pemoleCode,
    videoPlaybackBehavior,
  } = props;
  const {
    imageOrIconHorizontalAlignment,
    imageOrIconPlacement,
    ctaButtonStyling,
    ctaHorizontalAlignment,
    detailsLinkFontColor = '',
    desktopImageOrIconSize = '24px',
    mobileImageOrIconSize = '14px',
    verticalTextAlignment = 'top',
    verticalCtaAlignment = 'bottom',
    showHideBasedOnScreenSize,
  } = webAppearance || {};
  const isDesktopVP = useViewportIsLarge();
  const firstCTA = cta || primaryCTA;
  const video = vimeoVideo || nonVimeoVideo;
  const [playBackgroundVideo, setPlayBackgroundVideo] = useState(defaultPlaying(isDesktopVP, video));
  const toggleVideo = () => {
    setPlayBackgroundVideo(!playBackgroundVideo);
  };
  const { schema } = _meta || {};
  const isDouble = schema?.includes('double');

  const detailsContent = getDetailsContent(Brands.OldNavy, pemoleCode, htmlModalUrl);

  const containerHeight = isDesktopVP ? 430 : 340;

  let aspectWidth = isDesktopVP ? 234 : 160;
  if (isDouble) {
    aspectWidth = isDesktopVP ? 484 : 335;
  }

  const containerAspect = `${aspectWidth}:${containerHeight}`;
  const isButtonsInline = ['chevron', 'underline', 'flat'].includes(ctaButtonStyling?.buttonStyle || '');

  const imageOrIconSize = isDesktopVP ? desktopImageOrIconSize : mobileImageOrIconSize;
  const imageOrIcon = image ? (
    <div
      css={{
        display: 'block',
        width: '100%',
        padding: imageOrIconPlacement === 'above' ? '0 0 20px' : '20px 0 0',
        textAlign: imageOrIconHorizontalAlignment,
      }}
    >
      <ISMBannerImageOrIcon size={imageOrIconSize} src={image} />
    </div>
  ) : undefined;

  const { rteAndIconsGridRow, ctaGridRow, gridTemplateRows } = gridRowVerticalAlignment(
    verticalTextAlignment as VerticalAlignmentProps,
    verticalCtaAlignment as VerticalCtaAlignment
  );

  const hideMuteControl = nonVimeoVideo && nonVimeoVideo?.nonVimeovideoSoundIcons !== true;

  const playPauseButtonPositionStyles = usePlayPausePosition(mapPlacementToPosition('lowerRight'), 12, 12);

  const { toggleButtonStylesOnHover, toggleButtonStylesOnLeave, videoButtonStyles } = useIsmVideoButtonStyles(
    nonVimeoVideo?.nonVimeoVideoControlsDisplaySettings,
    hideMuteControl,
    false,
    Brands.OldNavy,
    playPauseButtonPositionStyles
  );

  return (
    <ISMBannerContainer aspectRatio={containerAspect} showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
      <ISMBannerContent
        aspectRatio={containerAspect}
        customControls={video && customControls(video)}
        customControlStyles={videoButtonStyles}
        hideMuteControl={hideMuteControl}
        onHover={video && isDisplayOnHover(video) ? toggleButtonStylesOnHover : undefined}
        onLeave={video && isDisplayOnHover(video) ? toggleButtonStylesOnLeave : undefined}
        playing={playBackgroundVideo}
        playInLoop={!!videoPlaybackBehavior?.singleLoop}
        video={video}
        videoContainerAbsolute={video && isNonVimeoVideo(video)}
      >
        <div
          css={{
            boxSizing: 'border-box',
            padding: '15px 12px 0px',
            position: 'relative',
            height: '100%',
            paddingBottom: detailsLink ? '48px' : undefined,
          }}
        >
          <div
            css={{
              height: '100%',
              overflowY: 'hidden',
            }}
          >
            <div
              css={{
                height: '100%',
                display: 'grid',
                gridTemplateColumns: '1fr',
                gridTemplateRows,
              }}
            >
              <div css={{ width: '100%', gridRow: rteAndIconsGridRow }}>
                {imageOrIconPlacement === 'above' && imageOrIcon}
                {text && <RichText text={text} />}
                {imageOrIconPlacement !== 'above' && imageOrIcon}
              </div>

              <div
                css={{
                  boxSizing: 'border-box',
                  display: 'flex',
                  gap: isButtonsInline ? '24px' : '10px',
                  justifyContent: ctaHorizontalAlignment,
                  paddingTop: 15,
                  paddingBottom: 15,
                  flexDirection: isButtonsInline ? 'row' : 'column',
                  width: '100%',
                  gridRow: ctaGridRow,
                }}
              >
                {firstCTA && (
                  <CtaButton
                    alignment={ctaHorizontalAlignment}
                    ctaButton={firstCTA}
                    ctaButtonStyling={ctaButtonStyling}
                    ctaSize='medium'
                    customCtaStyles={{
                      pointerEvents: 'all',
                    }}
                    fullWidth={!isButtonsInline}
                    isDesktop={isDesktopVP}
                  />
                )}
                {secondaryCTA && (
                  <CtaButton
                    alignment={ctaHorizontalAlignment}
                    ctaButton={secondaryCTA}
                    ctaButtonStyling={ctaButtonStyling}
                    ctaSize='medium'
                    customCtaStyles={{
                      pointerEvents: 'all',
                    }}
                    fullWidth={!isButtonsInline}
                    isDesktop={isDesktopVP}
                  />
                )}
                {vimeoVideo && <ISMOverlay onClick={toggleVideo} tabIndex={0} type='button' />}
              </div>
            </div>
          </div>
          {detailsLink && (
            <DetailsButton
              color={detailsLinkFontColor}
              css={{
                ...detailsBtnStyles(isDesktopVP, isDouble, detailsPrefix),
                pointerEvents: 'all',
                letterSpacing: isDesktopVP ? '0.24px' : '0.22px',
                fontSize: isDesktopVP ? '12px' : '11px',
                lineHeight: isDesktopVP ? '18px' : '16px',
                fontWeight: 500,
              }}
              detailsPrefixWrapperStyles={detailsPrefixBtnStyles(isDesktopVP, isDouble)}
              label={detailsLink}
              prefix={detailsPrefix}
              value={detailsContent}
            />
          )}
          {bannerLink && <ISMOverlay title={bannerLink.label} to={bannerLink.value} type='link' />}
          {vimeoVideo && <ISMOverlay onClick={toggleVideo} tabIndex={0} type='button' />}
        </div>
      </ISMBannerContent>
    </ISMBannerContainer>
  );
};

const OldNavyInSortMarketingBanner = (props: InSortMarketingBannerProps): React.JSX.Element => {
  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  return hasPlpGrid2025 ? <OldNavyInSortMarketingBannerVideoV2 {...props} /> : <OldNavyInSortMarketingBannerVideoLegacy {...props} />;
};

export default OldNavyInSortMarketingBanner;
