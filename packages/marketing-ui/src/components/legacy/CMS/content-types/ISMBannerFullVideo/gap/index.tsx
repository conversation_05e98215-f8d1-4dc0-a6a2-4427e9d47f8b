'use client';

import React, { useState, JSX } from 'react';
// @ts-ignore
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch';
// @ts-ignore
import classnames from 'classnames';
// @ts-ignore
import { LARGE } from '@ecom-next/core/breakpoint-provider';
import { useIsmVideoButtonStyles } from '../../ISMBanner/useVideoButtonStyles/index';
import { useViewportIsLarge, useViewportIsXLarge } from '../../../../hooks/useViewportIsLarge/index';
import { ISMBannerContent } from '../../../components/ism-banner/index';
import ISMBannerImageOrIcon from '../../../components/ism-banner/ISMBannerImageOrIcon';
import ISMOverlay from '../../../components/ism-banner/ISMOverlay';
import { gridRowVerticalAlignment } from '../../../components/ism-banner/helpers/gridRowVerticalAlignment';
import { defaultPlaying } from '../../../components/ism-banner/helpers/videoParams';
import { VerticalAlignmentProps, VerticalCtaAlignment } from '../../../global/types';
import { CtaButton, CtaProps, CTATreatmentProp } from '../../../subcomponents/CTAButton/index';
import { DetailsButton, getDetailsContent } from '../../../subcomponents/Details/index';
import { RichText } from '../../../subcomponents/RichText/index';
import { InSortMarketingBannerFullVideoContentType } from '../types';
import { customControls, isDisplayOnHover, isNonVimeoVideo } from '../../ISMBanner/videoHelpers';
import { mapPlacementToPosition, usePlayPausePosition } from '../../ISMCarousel/helpers';
import { getPlayPauseButtonStylesForGap, getMuteUnmuteStylesForGap } from '../../ISMBannerPartialVideo/gap/styles';
import {
  getDetailsButtonCtaAlignment,
  getDetailsButtonGridRow,
  getDetailsButtonJustifyContent,
  ismGridSize,
  ismProductGridHeight,
} from '../../ISMBanner/styles';
import { BannerContent, CtaContainer } from '../../ISMBanner/index';
import { ShowHideWrapper } from '../../../subcomponents/ShowHideWrapper/index';

import '../../ISMBanner/ism-banner.gap.css';

export type InSortMarketingBannerProps = InSortMarketingBannerFullVideoContentType;

const GapInSortMarketingBanner = (props: InSortMarketingBannerProps): JSX.Element | null => {
  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  const {
    _meta,
    vimeoVideo,
    nonVimeoVideo,
    bannerLink,
    image,
    webAppearance,
    text,
    cta,
    cta2,
    primaryCTA,
    secondaryCTA,
    detailsLink,
    htmlModalUrl,
    pemoleCode,
    videoPlaybackBehavior,
    detailsLinkLocation,
    detailsPrefix,
  } = props;
  const {
    imageOrIconHorizontalAlignment,
    imageOrIconPlacement,
    ctaButtonStyling,
    ctaHorizontalAlignment,
    detailsLinkFontColor = '',
    desktopImageOrIconSize = '24px',
    mobileImageOrIconSize = '14px',
    verticalTextAlignment = 'top',
    verticalCtaAlignment = 'bottom',
    showHideBasedOnScreenSize,
  } = webAppearance || {};
  const firstCTA = cta || primaryCTA;
  const secondCTA = cta2 || secondaryCTA;
  const isDesktopVP = useViewportIsLarge();
  const isDesktop = useViewportIsXLarge();
  const isTablet = useViewportIsLarge() && !isDesktop;
  const currentViewport = isDesktop ? 'desktop' : isTablet ? 'tablet' : 'mobile';
  const video = vimeoVideo || nonVimeoVideo;

  const controlsIconsColor = nonVimeoVideo ? nonVimeoVideo?.controlsIconsColor : 'primary';
  const [playBackgroundVideo, setPlayBackgroundVideo] = useState(defaultPlaying(isDesktopVP, video));
  const toggleVideo = () => {
    setPlayBackgroundVideo(!playBackgroundVideo);
  };
  const { schema } = _meta || {};
  const isDouble = schema?.includes('double');
  const containerHeight = isDesktopVP ? 430 : 340;

  let aspectWidth = isDesktopVP ? 234 : 160;
  if (isDouble) {
    aspectWidth = isDesktopVP ? 484 : 335;
  }

  const containerAspect = `${aspectWidth}:${containerHeight}`;

  const imageOrIconSize = isDesktopVP ? desktopImageOrIconSize : mobileImageOrIconSize;
  const imageOrIcon = image ? (
    <div className='mui_ism-full-video__image-or-icon'>
      <ISMBannerImageOrIcon size={imageOrIconSize} src={image} />
    </div>
  ) : undefined;

  const { rteAndIconsGridRow, ctaGridRow, gridTemplateRows } = gridRowVerticalAlignment(
    verticalTextAlignment as VerticalAlignmentProps,
    verticalCtaAlignment as VerticalCtaAlignment
  );

  const hideMuteControl = nonVimeoVideo && nonVimeoVideo?.nonVimeovideoSoundIcons !== true;

  const desktopPlayPauseButtonPosition = usePlayPausePosition(mapPlacementToPosition(nonVimeoVideo?.playPauseButtonPlacement || 'lowerLeft'), 20, 20);
  const mobilePlayPauseButtonPosition = usePlayPausePosition(mapPlacementToPosition(nonVimeoVideo?.playPauseButtonPlacement || 'lowerLeft'), 15, 15);

  const { toggleButtonStylesOnHover, toggleButtonStylesOnLeave, videoButtonStyles } = useIsmVideoButtonStyles(
    nonVimeoVideo?.nonVimeoVideoControlsDisplaySettings,
    hideMuteControl,
    false,
    Brands.Gap,
    getPlayPauseButtonStylesForGap(nonVimeoVideo?.playPauseButtonPlacement || 'lowerLeft', desktopPlayPauseButtonPosition, mobilePlayPauseButtonPosition),
    getMuteUnmuteStylesForGap(nonVimeoVideo?.playPauseButtonPlacement || 'lowerLeft', isDesktopVP)
  );

  const ctaPadding = (() => {
    let padding = '15px 0 0';

    if (verticalCtaAlignment === 'bottom') {
      padding = detailsLink ? '15px 0 30px' : '45px 0 0';
      if (
        nonVimeoVideo &&
        (nonVimeoVideo.playPauseButtonPlacement === 'lowerLeft' ||
          nonVimeoVideo.playPauseButtonPlacement === 'lowerRight' ||
          nonVimeoVideo.playPauseButtonPlacement === undefined)
      ) {
        // Icon Size + Spacing
        // Desktop: 30px + 30px = 60px
        // Mobile: 28px + 30px = 58px
        padding = isDesktopVP ? '15px 0 60px' : '15px 0 58px';
        if (detailsLink) {
          padding = isDesktopVP ? '15px 0 44px' : '15px 0 43px';
        }
      }
    }

    return padding;
  })();

  const detailsContent = getDetailsContent(Brands.Gap, pemoleCode, htmlModalUrl);
  const detailsButtonContent = (
    <div className='mui_ism-full-video__details-link-button'>
      <DetailsButton
        color={detailsLinkFontColor}
        className='mui_ism-full-video__details-link-button'
        label={detailsLink}
        prefix={detailsPrefix}
        value={detailsContent}
      />
    </div>
  );

  const isCtaRteBottom =
    (verticalTextAlignment === 'bottom' && verticalCtaAlignment === 'bottom') || (verticalTextAlignment === 'middle' && verticalCtaAlignment === 'middle');
  const imageOrIconClassNames = classnames('mui_ism-full-video__imageOrIcon-content', {
    'mui_ism__cta-rte-bottom': isCtaRteBottom,
  });

  const imageOrIconContent = (
    <div className={imageOrIconClassNames}>
      {imageOrIconPlacement === 'above' && imageOrIcon}
      {text && <RichText text={text} />}
      {imageOrIconPlacement !== 'above' && imageOrIcon}
    </div>
  );
  const ctaProps: Omit<CtaProps & CTATreatmentProp, 'ctaButton'> = {
    alignment: 'center',
    ctaButtonStyling,
    ctaSize: isDesktopVP ? 'large' : 'medium',
    customCtaStyles: { pointerEvents: 'all' },
    fullWidth: false,
    isDesktop: isDesktopVP,
  };
  const ctaContainerContent = (
    <CtaContainer className='mui_ism-full-video__cta-container' data-testid='cta-container'>
      {firstCTA && <CtaButton {...ctaProps} ctaButton={firstCTA} />}
      {secondCTA && <CtaButton {...ctaProps} ctaButton={secondCTA} />}
    </CtaContainer>
  );

  const fullVideoClassNames = classnames('mui_ism-full-video__container', {
    'grid-container': hasPlpGrid2025,
    'single-column': hasPlpGrid2025 && !isDouble,
    'double-column': hasPlpGrid2025 && isDouble,
  });

  const useIsmGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] : 0;
  const useIsmProductGridHeight: number = hasPlpGrid2025 ? ismProductGridHeight[currentViewport] : 0;
  const useIsmDoubleGridSizing: number = hasPlpGrid2025 ? ismGridSize[currentViewport] * 2 : 0;
  const productGridAspectRatio = hasPlpGrid2025 ? `${aspectWidth} / ${useIsmProductGridHeight}` : '';

  const componentStyles = `
    :root {
      --ism--padding: ${isDesktopVP ? '20px' : '15px'};
      --ism--container-aspect-ratio: ${containerAspect.replace(':', '/')};
      --ism--grid-template-rows: ${gridTemplateRows};
      --ism--cta-flex-direction: row;
      --ism--cta-padding: ${ctaPadding};
      --ism--flex-basis: ${useIsmGridSizing}%;
      --ism--max-width: ${useIsmGridSizing}%;
      --ism--flex-basis-double: ${useIsmDoubleGridSizing}%;
      --ism--max-width-double: ${useIsmDoubleGridSizing}%;
      --ism--aspect-ratio: ${productGridAspectRatio};
      --ism--grid-item-min-height: ${useIsmProductGridHeight}px;
      --ism--cta-horizontal-alignment: ${ctaHorizontalAlignment};
      --ism--cta-grid-row: ${ctaGridRow};
      --ism--imageOrIcon-padding: ${imageOrIconPlacement === 'above' ? '0 0 20px' : '20px 0 0'};
      --ism--imageOrIcon-text-align: ${imageOrIconHorizontalAlignment};
      --ism--imageOrIcon-content-grid-row: ${rteAndIconsGridRow};
      --ism--details-link-grid-row: ${getDetailsButtonGridRow(verticalCtaAlignment, verticalTextAlignment)};
      --ism--details-link-vertical-cta-align: ${getDetailsButtonCtaAlignment(verticalCtaAlignment)};
      --ism--details-link-justify-content: ${getDetailsButtonJustifyContent(detailsLinkLocation)};
    }
  `;

  return (
    <>
      <style>{componentStyles}</style>
      <ShowHideWrapper breakpoint={LARGE} showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
        <div className={fullVideoClassNames} data-testid='ism-full-video'>
          <ISMBannerContent
            controlsIconsColor={controlsIconsColor}
            customControls={video && customControls(video)}
            customControlStyles={videoButtonStyles}
            hideMuteControl={hideMuteControl}
            onHover={video && isDisplayOnHover(video) ? toggleButtonStylesOnHover : undefined}
            onLeave={video && isDisplayOnHover(video) ? toggleButtonStylesOnLeave : undefined}
            playerStyles={{ video: { objectFit: 'cover' } }}
            playing={playBackgroundVideo}
            playInLoop={!!videoPlaybackBehavior?.singleLoop}
            video={video}
            videoContainerAbsolute={video && isNonVimeoVideo(video)}
          >
            <BannerContent className='mui_ism-full-video__banner-content'>
              {imageOrIconContent}
              {ctaContainerContent}
              {detailsLink && detailsButtonContent}
              {bannerLink && <ISMOverlay title={bannerLink.label} to={bannerLink.value} type='link' />}
              {vimeoVideo && <ISMOverlay onClick={toggleVideo} tabIndex={0} type='button' />}
            </BannerContent>
          </ISMBannerContent>
        </div>
      </ShowHideWrapper>
    </>
  );
};

export default GapInSortMarketingBanner;
