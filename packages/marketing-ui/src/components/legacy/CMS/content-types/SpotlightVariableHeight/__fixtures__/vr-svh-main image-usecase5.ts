// @ts-nocheck
import { SpotlightVariableHeightContentType } from '../types';

// Image section X-small
export const mainSectionUseCase5: SpotlightVariableHeightContentType = {
  _meta: {
    name: 'Spotlight Variable Height - Test',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json',
    deliveryId: '379da898-ff0e-43f1-841e-90a7e6d62c05',
  },
  image: {
    imageSize: 'xsmall',
    heroImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '759dd7d6-ce44-4d6b-b84a-1ddaa282a423',
          name: 'Spotlight_Lg_1440x800',
          endpoint: 'oldnavy',
          defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileHeroImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '87d22358-2c14-4c72-bd9a-9d5740578195',
          name: '220307_43-M6558_Shorts_Fleece_Size18_W_VIBanner',
          endpoint: 'oldnavy',
          defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlay: {
    cta: {
      buttonStyle: {
        buttonStyle: 'underline',
        buttonColor: 'dark',
      },
    },
    useGradientBackfill: false,
    contentPlacement: {
      horizontal: 'left',
      vertical: 'middle',
    },
    contentJustification: 'left',
    handle: {
      placement: 'right',
      text: ' ',
    },
    useGradientBackfillFooter: false,
    imageText:
      '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="color:#000">Large Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-3" style="color:#000">Lorem ipsum dolor sit amet, consectetur adipiscing elit, </span></p>',
  },
  contentBlocks: {
    aboveImage: {
      cta: {
        buttonStyle: {
          buttonStyle: 'outline',
          buttonColor: 'dark',
        },
      },
      contentPlacement: 'left',
      contentJustification: 'left',
    },
    belowImage: {
      cta: {
        buttonStyle: {
          buttonStyle: 'underline',
          buttonColor: 'dark',
        },
      },
      contentPlacement: 'middle',
      contentJustification: 'middle',
    },
  },
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
