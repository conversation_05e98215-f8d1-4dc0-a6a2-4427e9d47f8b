'use client';

import { CSSObject } from '@ecom-next/core/react-stitch';
import { CTAVariant, CTAColor } from '../../subcomponents/CTAButton/types';
import { AdvanceImageExtensionValue, BackgroundTypeExtensionValue, BaseContentType } from '../../types/amplience';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';
import { CTADropdownItem } from '../../subcomponents/CTAButton/index';
import { VerticalAlignmentProps, CtaProps } from '../../global/types';
import { Size } from '../../../components/ComposableButton/components/index';
import { BRCTAButtons } from '../SpotlightVariableHeightVideo/types';

export interface SpotlightVariableHeightWebAppearance {
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
}

export type SpotlightVariableHeightSchema = 'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json';

export type MediaSize = 'xsmall' | 'small' | 'medium' | 'large';

export type SpotlightImage = {
  heroImage: AdvanceImageExtensionValue;
  imageSize: MediaSize;
  link?: CtaProps;
  mobileHeroImage?: AdvanceImageExtensionValue;
};

export type CTAContent = {
  buttonStyle: { buttonStyle: CTAVariant; buttonColor: CTAColor };
  ctaDropdownList?: CTADropdownItem[];
  mobileLayout?: 'stacked' | 'linear';
  showHideBasedOnScreenSize?: ShowHideBasedOnScreenSizeProps;
};

export type Handle = {
  placement: 'left' | 'right';
  text?: string;
};

export type DetailsLink = {
  fontColor?: string;
  htmlModalUrl?: string;
  label?: string;
  pemoleCode?: string;
  prefixLabel?: string;
};

type HorizontalAlignments = 'left' | 'middle' | 'right';

export type ContentPlacement = {
  horizontal: HorizontalAlignments;
  vertical: VerticalAlignmentProps;
};

export type ImageOverlay = {
  contentJustification: 'left' | 'middle' | 'right';
  contentPlacement: ContentPlacement;
  cta?: CTAContent;
  detailsLink?: DetailsLink;
  handle?: Handle;
  imageText?: string;
  mediaSize?: MediaSize;
  useGradientBackfill: boolean;
  useGradientBackfillFooter: boolean;
};

export type RTEContent = {
  text: string;
};

export type ContentBlock = {
  background?: BackgroundTypeExtensionValue;
  contentJustification?: 'left' | 'middle' | 'right';
  contentPlacement?: 'left' | 'middle' | 'right';
  cta?: CTAContent;
  ctaButtons?: BRCTAButtons; // BR only (it's required for BR, though marked as optional):
  ctaContainerStyles?: CSSObject;
  ctaNaturalWidth?: boolean;
  ctaSize?: Size;
  rte?: string;
  useLinearLayout?: boolean;
  usesVideo?: boolean;
};

export type ContentBlocks = {
  aboveImage?: ContentBlock;
  belowImage?: ContentBlock;
};

export interface SpotlightVariableHeightContentType extends BaseContentType<SpotlightVariableHeightSchema> {
  contentBlocks?: ContentBlocks;
  image: SpotlightImage;
  imageOverlay: ImageOverlay;
  webAppearance?: SpotlightVariableHeightWebAppearance;
}
