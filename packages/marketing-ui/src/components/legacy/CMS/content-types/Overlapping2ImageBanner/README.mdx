# Overlapping2ImageBanner (CMS Configurable)

- What is `Overlapping2ImageBanner`?

  - `Overlapping2ImageBanner` is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience.

- This component is Athleta specific.

## Default Behavior

- `Overlapping2ImageBanner` creates a pre-styled image clickable banner with a `headline` and optional `athleta girl logo`, `details link`, `headlineDivider`, `subhead`, `eyebrow`, and `subcopy` text.

- `Overlapping2ImageBanner` will have only two images and can configure the `shorter height image`, `layout` and `overlapping image`

## Limitations

- `Overlapping2ImageBanner` accepts JSON keys generated by our Content Management System, Amplience. JSON can be manually written as well, as long as it remains compatible with Amplience data.

- Note that Amplience JSON shape differs from earlier JSON shapes. It is intended to be much simpler, with more styles built into the component, and therefore less configurable via JSON.

- This component still need to be converted to TypeScript.

## Technical Notes

- The styling in the `Overlapping2ImageBanner` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md)."

- **Note on mobile flexibility for headline text**: We have the left text section at 30% of the banner width for desktop; however, AT creative asked for these mobile features:

  - flexibility for longer Headline words like "Accessories" or "Combinations", especially for French
  - shorter Headlines would continue to be a smaller width
  - only Headline width should affect the section. Subhead text, or the "lorem ipsum" text below the dividing line at the time of writing, should not make the left section wider, no matter how long it is. It should fit into the space that Headline takes up.

  The solution we have is for useEffects to watch the Headline in mobile, and apply that width to the whole left text section, rather than the whole section growing naturally with the Headline _or_ the Subhead.

### API

To view documentation about the API for `Overlapping2ImageBanner`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/marketing-ui/stories/content-types/Overlapping2ImageBanner/index-story.tsx).

## UX Guidelines

[Overlapping 2 Image Banner - Flow](https://xd.adobe.com/view/16e3c889-e69b-4695-833e-85abc3516cbb-43b0/)
[Overlapping 2 Image Banner - Athleta](https://xd.adobe.com/view/6cd723f3-ef3c-43a9-bbf3-8ff7a5799153-3984/)

## Testing the Component in Storybook

- Changes in the Storybook control JSON should be reflected in the visual example above.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
