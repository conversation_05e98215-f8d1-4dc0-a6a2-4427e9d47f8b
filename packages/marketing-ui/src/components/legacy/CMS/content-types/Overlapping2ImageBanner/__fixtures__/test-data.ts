// @ts-nocheck
import { Overlapping2ImageBannerContentType } from '../types';

export const overlapping2ImageBannerData: Overlapping2ImageBannerContentType = {
  _meta: {
    name: 'Overlapping 2 Image Banner - test',
    schema: 'https://cms.gap.com/schema/content/v1/overlapping-2-image-banner.json',
    deliveryId: '16c0b13a-49b3-4086-bea8-516baa22c6bb',
  },
  images: [
    {
      image: {
        _meta: {
          schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
        },
        id: '48afb127-79ce-4b1a-90bc-7ed176a72843',
        name: 'FALL3_FALL_EDIT_XL@2x',
        endpoint: 'athleta',
        defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
      },
      altText: 'image 2',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
    },
    {
      image: {
        _meta: {
          schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
        },
        id: 'c509cdb7-8198-417f-98f3-9c8a07f09c0b',
        name: 'FALL3_FE_NOLITA_ISM_XL@2x',
        endpoint: 'athleta',
        defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
      },
      altText: 'image1',
      variations: [
        {
          variation: 'desktop',
          crop: {
            x: 3.7701149425287355,
            y: 3.479587765957447,
            width: 972.6896551724138,
            height: 641.6595744680851,
            unit: 'px',
          },
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
    },
  ],
  bannerLink: {
    label: 'Banner Link',
    value: '#link',
  },
  webAppearance: {
    headlineDivider: true,
    athletaGirlLogo: '#ffffff',
    headlineSubheadingFontColorDesktop: '#ffffff',
    headlineSubheadingFontColorMobile: '#000000',
    eyebrowSubcopyFontColorDesktop: '#ffffff',
    eyebrowSubcopyFontColorMobile: '#000000',
    detailsLinkFontColorDesktop: '#ffffff',
    detailsLinkFontColorMobile: '#000000',
    shorterImage: 'first',
    imageRatio: '40:60',
    onTopImage: 'first',
    desktopImageOrIconSize: '64px',
    mobileImageOrIconSize: '48px',
  },
  headline: 'Soutiens‑gorge bonnet',
  subhead: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc semper diam',
  eyebrow: 'Featuring',
  subcopy: 'The sundown sweatshirt',
  detailsLink: 'Link Label here',
  htmlModalUrl: 'http://somecrazy-notreal-url-ofinternet.co.uk/whatever',
};
