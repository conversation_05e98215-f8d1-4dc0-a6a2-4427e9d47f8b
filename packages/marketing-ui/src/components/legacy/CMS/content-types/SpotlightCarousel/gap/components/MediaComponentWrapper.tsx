/* eslint-disable react/require-default-props */
'use client';
import React from 'react';
import { CSSObject } from '@emotion/serialize';
import { CMSMarketingCarouselProps } from '../../../../subcomponents/CMSMarketingCarousel';
import { MediaComponent, MediaSize } from '../../../../subcomponents/MediaComponent';
import { BannerSize, MediaContent, GapBrandBannerSize } from '../../types';
import { constructMediaImgData } from '../../utilities';
import { controlColor, getMediaAspectRatio, playerStyles } from '../styles';
import { MEDIA_CONTROLS_LINE_PAGINATION_GAP, MEDIA_LINE_PAGINATION_HEIGHT } from '../constants';
import BackgroundTypeContainer from '../../../../subcomponents/BackgroundTypeContainer';
import useDeviceValues from '../../../../../helper/hooks/useDeviceValues';
import { isMobileXSmall } from '../utils';
import { FeatureConfig } from '../types';
import { customControlStyles as customControlStylesBase } from '../../commonStyles';

interface MediaComponentWrapperProps {
  autoPlayControl?: boolean;
  carouselSettings: CMSMarketingCarouselProps['carouselSettings'];
  desktopBannerSize?: GapBrandBannerSize;
  isLarge: boolean;
  linePaginationConfig?: FeatureConfig;
  mediaContent?: MediaContent;
  mediaControlsConfig?: FeatureConfig;
  mobileBannerSize?: BannerSize;
}

function defineBottomPositionFix(mediaControlsConfig?: FeatureConfig, linePaginationConfig?: FeatureConfig): number {
  const EXPECTED_PADDING = MEDIA_CONTROLS_LINE_PAGINATION_GAP + MEDIA_LINE_PAGINATION_HEIGHT;

  if (linePaginationConfig?.enabled && mediaControlsConfig?.enabled && mediaControlsConfig?.justification === linePaginationConfig.justification)
    return EXPECTED_PADDING;

  return 0;
}

export const MediaComponentWrapper: React.FC<MediaComponentWrapperProps> = ({
  isLarge,
  desktopBannerSize,
  mobileBannerSize,
  mediaContent,
  autoPlayControl,
  carouselSettings,
  linePaginationConfig,
  mediaControlsConfig,
}) => {
  const mediaImageData = constructMediaImgData(mediaContent?.image, isLarge);
  const mediaImage = mediaContent?.image?.[0];

  const image = isLarge && mediaImage?.mobileOverride ? mediaImage?.mobileOverride : mediaImage?.default;

  const aspectRatio = getMediaAspectRatio({
    isLarge,
    mobileBannerSize,
    desktopBannerSize,
  });

  const mobileMarginTopBottom = isMobileXSmall(mobileBannerSize) ? 50 : 30;

  const bottomPositionFix = defineBottomPositionFix(mediaControlsConfig, linePaginationConfig);

  const { bottom, horizontalMargin } = useDeviceValues({
    horizontalMargin: ['5vw', null, '2.5vw'],
    bottom: [mobileMarginTopBottom + bottomPositionFix, null, 30 + bottomPositionFix],
  });

  const customControlStyles: CSSObject = {
    ...customControlStylesBase,
    margin: 0,
    width: 'auto',
    bottom,
  };
  if (mediaControlsConfig?.justification === 'right') customControlStyles.right = horizontalMargin;
  if (mediaControlsConfig?.justification === 'left') customControlStyles.left = horizontalMargin;

  return (
    <>
      {!isLarge && isMobileXSmall(mobileBannerSize) ? (
        <BackgroundTypeContainer
          background={{
            type: 'image',
            images: image,
          }}
          customCss={{
            aspectRatio,
            width: '100%',
            height: '100%',
            '& > img': {
              height: '100%',
            },
          }}
        />
      ) : (
        <MediaComponent
          aspectRatio={aspectRatio}
          media={{
            video: {
              controlsIconsColor: mediaContent?.controlsIconsColor,
              customControlStyles,
              playerStyles,
              controlColor,
              videoSize: (isLarge ? desktopBannerSize : mobileBannerSize) as MediaSize,
              ...(mediaContent?.video ? mediaContent?.video[0]?.video : {}),
            },
            ...mediaImageData,
            bgImageContainerStyles: { width: '100%', height: '100%' },
            fallbackImageContainerStyles: { width: '100%', height: '100%' },
          }}
          playVideoOnLoad={autoPlayControl ?? true}
          videoControlSettings={carouselSettings.videoControlSettings}
        />
      )}
    </>
  );
};
