import { render, screen } from 'test-utils';
import React from 'react';
import { icon } from '../../__fixtures__/gap-test-data';
import { ImageOrIconComponent } from './ImageOrIconComponent';

describe('ImageOrIconComponent', () => {
  const altTextDesktop = icon.default?.icon?.[0].altText || 'not-found';
  const altTextMobile = icon.mobileOverride?.[0].mobileIcon?.icon?.[0].altText || 'not-found';

  it('should not render anything when icon prop is not provided', () => {
    const { container } = render(<ImageOrIconComponent isLarge />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render anything when icon.default.icon is empty', () => {
    const emptyIcon = { default: { icon: [] } };
    const { container } = render(<ImageOrIconComponent icon={emptyIcon} isLarge />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render desktop icon when isLarge is true', () => {
    render(<ImageOrIconComponent icon={icon} isLarge />);
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', expect.stringContaining(altTextDesktop));
  });

  it('should render mobile icon when isLarge is false and mobile override exists', () => {
    render(<ImageOrIconComponent icon={icon} isLarge={false} />);
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', expect.stringContaining(altTextMobile));
  });

  it('should render desktop icon when isLarge is false and no mobile override exists', () => {
    const modifiedIcon = { ...icon, mobileOverride: undefined };
    render(<ImageOrIconComponent icon={modifiedIcon} isLarge={false} />);
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', expect.stringContaining(altTextDesktop));
  });
});
