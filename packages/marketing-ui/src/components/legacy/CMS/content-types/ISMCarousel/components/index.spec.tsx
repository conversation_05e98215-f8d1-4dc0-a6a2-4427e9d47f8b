// @ts-nocheck
import React from 'react';
import { render, screen, act } from 'test-utils';
import { Size, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { ISMBannerFooterContentProps } from '../../../components/ism-banner';
import { CarouselIconAndRTE } from '.';
import { CarouselIconAndRTEProps } from '../types';
import { Div, ContainerDiv, ISMBannerContainerCarousel } from './styles';

const defaultProps: ISMBannerFooterContentProps = {
  cta1: {
    label: 'Shop now 1',
    value: 'testing cta',
  },
  cta2: {
    label: 'Shop now 2',
    value: 'testing cta2',
  },
  detailsContent: 'detailsContent',
  bodyCopy: '<span>Lorem Ipsum Dolor Sit Amet Con Sectetur Adipiscing 1</span>',
  detailsLink: 'DETAILS',
};

describe('CarouselFooter component', () => {
  const renderComponentCarouselIconAndRTE = (props?: Partial<CarouselIconAndRTEProps>, breakpoint: Size = XLARGE, brand: Brands = Brands.Gap) =>
    render(
      <StitchStyleProvider brand={brand}>
        <CarouselIconAndRTE {...defaultProps} {...props} />
      </StitchStyleProvider>,
      {
        breakpoint,
      }
    );

  describe('CarouselIconAndRTE brands snapshot', () => {
    ['at', 'gap', 'on', 'gapfs'].forEach(brandName => {
      it(`should match brands snapshot for ${brandName} on desktop`, () => {
        const { asFragment } = renderComponentCarouselIconAndRTE({}, XLARGE, brandName as Brands);

        expect(asFragment()).toMatchSnapshot();
      });

      it(`should match brands snapshot for ${brandName} on mobile`, () => {
        const { asFragment } = renderComponentCarouselIconAndRTE({}, SMALL, brandName as Brands);

        expect(asFragment()).toMatchSnapshot();
      });

      it('should have padding CarouselIconAndRTE', () => {
        const { asFragment } = renderComponentCarouselIconAndRTE({ padding: '3px' }, XLARGE, Brands.Athleta);

        expect(asFragment()).toMatchSnapshot();
      });

      it('should have padding + avoid overlapping CarouselIconAndRTE', () => {
        const { asFragment } = renderComponentCarouselIconAndRTE({ avoidOverlapping: true }, XLARGE, Brands.Athleta);

        expect(asFragment()).toMatchSnapshot();
      });

      it('should have padding + avoid overlapping CarouselIconAndRTE mobile', () => {
        const { asFragment } = renderComponentCarouselIconAndRTE({ avoidOverlapping: true }, SMALL, Brands.Athleta);

        expect(asFragment()).toMatchSnapshot();
      });

      it('should have padding + avoid overlapping CarouselIconAndRTE play mode', () => {
        const { asFragment } = renderComponentCarouselIconAndRTE(
          {
            avoidOverlapping: true,
            verticalTextAlignment: 'bottom',
            shouldDisplayPlayPauseButton: true,
          },
          XLARGE,
          Brands.Athleta
        );

        expect(asFragment()).toMatchSnapshot();
      });

      it('should have padding + avoid overlapping CarouselIconAndRTE play mode mobile', () => {
        const { asFragment } = renderComponentCarouselIconAndRTE(
          {
            avoidOverlapping: true,
            verticalTextAlignment: 'bottom',
            shouldDisplayPlayPauseButton: true,
          },
          SMALL,
          Brands.Athleta
        );

        expect(asFragment()).toMatchSnapshot();
      });
    });
  });
});

describe('ISMCarousel styles', () => {
  it('render 100% div', () => {
    const message = 'Hello Element';
    render(<Div>{message}</Div>);

    expect(screen.getByText(message)).toHaveStyleRule('width', '100%');
  });

  it('render justifyContent defaut value ContainerDiv', () => {
    const message = 'Hello Element';
    render(<ContainerDiv justifyContent=''>{message}</ContainerDiv>);

    expect(screen.getByText(message)).toHaveStyleRule('justify-content', 'flex-start');
  });

  it('render ISMBannerContainerCarousel', () => {
    const message = 'Hello Element';
    render(
      <ISMBannerContainerCarousel height={140} width={200}>
        {message}
      </ISMBannerContainerCarousel>
    );

    expect(screen.getByText(message)).toHaveStyleRules({
      height: '140px',
      width: '200px',
    });
  });
});
