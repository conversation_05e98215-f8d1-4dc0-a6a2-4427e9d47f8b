// @ts-nocheck
import { CategoryBannerPriceCardsCarouselContentType } from '../types';
const background1 = require('../../../../assets/background-1.png').default?.src;
const background2 = require('../../../../assets/background-2.png').default?.src;
const jeans = require('../../../../assets/boyfriend-jeans.jpg').default?.src;
const people = require('../../../../assets/gap-people-image.png').default?.src;
const man = require('../../../../assets/man-on-casual-clothes.png').default?.src;
const yoga = require('../../../../assets/person-doing-yoga.png').default?.src;
const playing = require('../../../../assets/person-playing-ball.jpg').default?.src;
const posing = require('../../../../assets/person-posing-or-fighting.png').default?.src;
const glance = require('../../../../assets/woman-glancing-the-view.png').default?.src;

export const CatBannerPriceCardsCarouselData: CategoryBannerPriceCardsCarouselContentType = {
  _meta: {
    name: 'Category Banner Price Cards - sk - 01/19/2022',
    schema: 'https://cms.gap.com/schema/v1/content/category-banner-price-cards-carousel.json',
    deliveryId: '1491c1f9-a1ad-419c-b08f-23533418f30d',
  },
  richTextAreaDesktopBackground: [
    {
      svgPath: background1,
      altText: 'desktop background',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
  ],
  richTextAreaMobileBackground: [
    {
      svgPath: background2,
      altText: 'Mobile Background',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
    },
  ],
  categoryCards: [
    {
      image: [
        {
          svgPath: jeans,
          altText: 'jeans',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 159.**************,
                y: 537.6966321790536,
                width: 146.83544303797467,
                height: 212.30336782094594,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      cardLinkUrl: {
        value: '#style=7890',
      },
      cta: {
        value: '#style=123456',
        label: 'Jumping',
      },
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-3" style="font-weight:700">TEES &amp; TANKS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '5',
      priceTextFieldSymbol: '$',
    },
    {
      image: [
        {
          svgPath: man,
          altText: 'man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      cardLinkUrl: {
        value: 'www.oldnavy.com',
      },
      cta: {
        value: 'www.oldnavy.com/shorts',
        label: 'Absolute',
      },
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">SHORTS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '8',
      priceTextFieldSymbol: '$',
    },
    {
      image: [
        {
          svgPath: yoga,
          altText: 'yoga',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      cardLinkUrl: {
        value: '/sweatshirtssweatpants',
      },
      cta: {
        value: '/sweatshirtssweatpants',
        label: 'Relative',
      },
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">SWEATSHIRTS &amp; SWEATPANTS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '15',
      priceTextFieldSymbol: '$',
    },
    {
      image: [
        {
          svgPath: playing,
          altText: 'playing',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      cardLinkUrl: {
        value: 'www.oldnavy.com',
      },
      cta: {
        value: 'www.oldnavy.com/activeweartops',
        label: 'SHOP NOW',
      },
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">ACTIVEWEAR TOPS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '8',
      priceTextFieldSymbol: '$',
    },
    {
      image: [
        {
          svgPath: posing,
          altText: 'Posing',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">ACTIVEWEAR BOTTOMS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '8',
      priceTextFieldSymbol: '$',
      cta: {
        value: 'www.oldnavy.com/activewearbottoms',
        label: 'MAGASINER MAINTENANT',
      },
      cardLinkUrl: {
        value: 'www.oldnavy.com',
      },
    },
    {
      image: [
        {
          svgPath: glance,
          altText: 'glance',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">PAJAMAS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '10',
      priceTextFieldSymbol: '$',
      cta: {
        value: 'www.oldnavy.com/pajamas',
        label: 'SHOP NOW',
      },
      cardLinkUrl: {
        value: 'www.oldnavy.com',
      },
    },
    {
      image: [
        {
          svgPath: people,
          altText: 'People',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 159.**************,
                y: 537.6966321790536,
                width: 146.83544303797467,
                height: 212.30336782094594,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      cardLinkUrl: {
        value: 'www.oldnavy.com',
      },
      cta: {
        value: 'www.oldnavy.com/teestanks',
        label: 'SHOP NOW',
      },
      categoryName: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-3" style="font-weight:700">TEES &amp; TANKS</span></p>',
      subcopy: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">from</span></p>',
      priceTextFieldValue: '5',
      priceTextFieldSymbol: '$',
    },
  ],
  webAppearance: {
    priceTextSymbolPlacement: 'right',
    ctaButtonStyling: {
      buttonStyle: 'underline',
      buttonColor: 'dark',
    },
    priceTextFontColor: '#D0103A',
    chevronColor: 'light',
  },
  richTextArea: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category Banner Price Cards</span></p>',
};
