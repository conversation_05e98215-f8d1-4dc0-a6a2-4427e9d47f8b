:is([data-brand='on']) {
  & .mui_ism-banner-content,
  & .mui_ism__cms-video-component {
    position: relative;
  }

  & .mui_ism-full-image__container {
    width: 100%;
    height: var(--ism--container-height);
    &.single-column {
      min-height: var(--ism--grid-item-min-height, 430px);
      --columnSize: var(--ism--column-size, 1);
      flex-basis: var(--ism--flex-basis, 25%);
      max-width: var(--ism--max-width, 25%);
    }
    &.double-column {
      flex-basis: var(--ism--flex-basis-double);
      max-width: var(--ism--max-width-double);
    }
    &.flex-container {
      flex-grow: 0;
      padding: 0.5em;
      margin: 0;
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }
    & .mui_ism-full-image__image-or-icon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-full-image__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-full-image__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: var(--ism--grid-template-rows);
    }

    & .mui_ism-full-image__cta-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism-full-image__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align, left);
      justify-content: var(--ism--details-link-justify-content, center);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;

      & button {
        line-height: normal;
      }
    }
  }

  & .mui_ism-full-video__container {
    display: grid;
    grid-template-areas: 'stacked';

    &.single-column {
      min-height: var(--ism--grid-item-min-height, 430px);
      --columnSize: var(--ism--column-size, 1);
      flex-basis: var(--ism--flex-basis, 25%);
      max-width: var(--ism--max-width, 25%);
    }
    &.double-column {
      flex-basis: var(--ism--flex-basis-double);
      max-width: var(--ism--max-width-double);
    }
    &.grid-container {
      flex-grow: 0;
      padding: 0.5em;
      margin: 0;
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }

    & .mui_ism-full-video__imageOrIcon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-full-video__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-full-video__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: var(--ism--grid-template-rows);
      grid-area: stacked;
      aspect-ratio: var(--ism--container-aspect-ratio);
    }

    & .mui_ism-full-video__cta-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism__video-component-wrapper {
      grid-area: stacked;

      & > div {
        position: relative;
      }

      & [data-testid='videocomponent-container'] > div {
        height: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
      }

      & .mui_ism__cms-video-component {
        &.mui_ism__pointer-events-none {
          pointer-events: none;
        }

        &.mui_ism__pointer-events-auto {
          pointer-events: auto;
        }

        & video {
          object-fit: cover;
        }
      }
    }

    & .mui_ism-full-video__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align, left);
      justify-content: var(--ism--details-link-justify-content, center);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;
    }
  }
}
