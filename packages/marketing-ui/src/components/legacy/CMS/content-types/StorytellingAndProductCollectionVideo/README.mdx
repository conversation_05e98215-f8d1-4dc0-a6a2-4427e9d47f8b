# StorytellingAndProductCollectionVideo (CMS Configurable)

- What is `StorytellingAndProductCollectionVideo`?
  - It is a wrapper for `StorytellingAndProductCollection` that displays a video instead of an image in the "hero" section of the component. All functionality is the same.

`StorytellingAndProductCollectionVideo` is intended for use on the Athleta Homepage.

## Default Behavior

- The only required element is the video. It has an optional mobile override.
- The Background, Icon, RichText, CTAs, and product cards are optional.
  - The background can be a color (hex, white by default), gradient, or an image.
  - For the CTAs, there is a minimum of 0 and maximum of 2.

## Limitations

`StorytellingAndProductCollectionVideo` is currently only styled for Athleta.

## Technical Notes

- The styling in the `StorytellingAndProductCollectionVideo` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/react-stitch/README.md)."

### API

Main component: [`StorytellingAndProductCollection`]((https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/StorytellingAndProductCollection)
You can see the props [here]//(https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/StorytellingAndProductCollection/types.ts)

## Testing the Component in Storybook

- Changes in the Storybook controls JSON should be reflected in the visual example above.
- A Playground story has been added to enable/disable optional elements and change background style.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
