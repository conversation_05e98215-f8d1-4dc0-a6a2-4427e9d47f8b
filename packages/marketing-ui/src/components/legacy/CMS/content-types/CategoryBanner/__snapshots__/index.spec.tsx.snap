// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryBanner Component images Render mobile image with correct height 1`] = `"url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_1340?fmt=auto&h=800)"`;

exports[`CategoryBanner Component should match snapshots for AT in default/mobile view 1`] = `
.emotion-0 {
  margin-left: 0;
  margin-right: 0;
  max-width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-2 {
  display: block;
  position: relative;
  height: 0;
  padding-bottom: 79.73333333333333%;
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 7.5px;
  grid-template-rows: 1fr 1fr;
}

.emotion-4 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  grid-row: 1/3;
  grid-column: auto;
  background-image: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_1340?fmt=auto&h=800);
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  height: 100%;
  width: 43%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: right;
}

.emotion-6 {
  height: auto;
  max-width: 100%;
}

.emotion-7 {
  max-height: 48px;
  height: 12.8vw;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.15999999999999998vw, 2.048px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.6;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 40.96px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 4.096px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(2.933333333333333vw, 37.54666666666667px));
  line-height: 2;
  letter-spacing: min(0.29333333333333333vw, 3.754666666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.18133333333333335vw, 2.321066666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.676266666666668px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(0.45333333333333325vw, 5.802666666666667px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: min(0.07999999999999999vw, 1.024px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: min(0.48000000000000004vw, 6.144px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.1818181818181819;
  letter-spacing: min(0.5866666666666667vw, 7.509333333333334px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.375;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  text-transform: none;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.02666666666666667vw, 0.3413333333333334px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 1.0922666666666667px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.18666666666666668vw, 2.3893333333333335px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.4666666666666663vw, 44.373333333333335px));
  line-height: 1;
  letter-spacing: min(0.25866666666666666vw, 3.3109333333333333px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--p>span {
  white-space: normal;
}

.emotion-9 {
  margin: 7% 0 3.5%;
  background-color: #gd6466;
  height: 1px;
}

.emotion-11 {
  position: absolute;
  bottom: 30px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.emotion-12 {
  color: #000000;
  text-align: right;
  margin-right: 15px;
  width: 35.73%;
  margin-left: auto;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.15999999999999998vw, 2.048px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.6;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 40.96px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 4.096px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(2.933333333333333vw, 37.54666666666667px));
  line-height: 2;
  letter-spacing: min(0.29333333333333333vw, 3.754666666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.18133333333333335vw, 2.321066666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.676266666666668px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(0.45333333333333325vw, 5.802666666666667px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: min(0.07999999999999999vw, 1.024px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: min(0.48000000000000004vw, 6.144px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.1818181818181819;
  letter-spacing: min(0.5866666666666667vw, 7.509333333333334px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.375;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  text-transform: none;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.02666666666666667vw, 0.3413333333333334px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 1.0922666666666667px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.18666666666666668vw, 2.3893333333333335px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.4666666666666663vw, 44.373333333333335px));
  line-height: 1;
  letter-spacing: min(0.25866666666666666vw, 3.3109333333333333px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--p>span {
  white-space: normal;
}

.emotion-14 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #ABC;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-15 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <a
          class="emotion-2"
          href="url.example"
        >
          <div
            class="emotion-3"
          >
            <div
              aria-label="image 1"
              class="emotion-4"
              role="img"
            />
          </div>
          <div
            class="emotion-5"
          >
            <span
              class="emotion-6"
            >
              <svg
                aria-labelledby="title"
                class="emotion-7"
                viewBox="0 0 151 88"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title
                  id="title"
                  lang="en"
                >
                  Athleta Girl Logo
                </title>
                <path
                  d="m1780.6 209.4 3.2 7.3h-6.6ZM1770 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm34-18.6V225h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm16.7 5.4h9.3v-8.5h3.3V225h-3.3V215h-9.3V225h-3.2v-21.7h3.2v8.5Zm26.6-8.5V222h6.3v3.1h-9.5v-21.7h3.2Zm27 3h-8.7v5.3h8.4v3h-8.4v7.3h8.7v3.1h-12v-21.7h12v3Zm15.8 0v18.7h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm20.6 3 3.2 7.3h-6.6ZM1900 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm-98.7 34.6h22.9v.4c0 9.6-2.8 17.4-8.2 22.7a25.8 25.8 0 0 1-19 7.2 24 24 0 0 1-18.8-7.7 30.2 30.2 0 0 1-8.3-21.2c0-8.7 3.5-16.3 8.1-21a29.4 29.4 0 0 1 20.9-8.3 29.7 29.7 0 0 1 14.5 3.5 25.3 25.3 0 0 1 9 8.4l-6.8 4.8a22.7 22.7 0 0 0-6.8-6.4 18.5 18.5 0 0 0-9.8-2.4 20 20 0 0 0-14.7 5.8 22.9 22.9 0 0 0-5.9 15.7 22.7 22.7 0 0 0 5.8 15.3c3.8 4 8.5 5.6 13.9 5.6a16.6 16.6 0 0 0 12.4-5 15.1 15.1 0 0 0 4.6-9.5h-13.8v-7.9Zm40.3-27v35h-8.4v-35h8.4Zm20.8 0c6.7 0 11 1 14.6 3.4 6 4 6.6 10.7 6.6 13.2 0 8-5 14-12.3 15.6l17.3 24h-10.3l-15.9-23h-1.5v23h-8.4v-56.1h9.9Zm-1.5 25.8h2.7c2.3 0 11.8-.2 11.8-9.1 0-8-7.4-8.7-11.5-8.7h-3v17.8Zm43.6-25.7v48h16.3v8.1h-24.8v-56.1h8.5Zm-67 40.7a8.3 8.3 0 1 0 8 8.2 8.2 8.2 0 0 0-8-8.2Zm4.3 10.4.6 2.7-5-4.9 2 4.5-1.2 2.4-.7-6.9-1.4 4.7-2.5 1 3.9-5.7-4 2.7-2.6-.8 6.6-1.9-4.9-.5-1.4-2.3 6.3 2.8-3.4-3.6.4-2.7 3 6.3-.3-5 2-1.7-1.7 6.7 2.9-4h2.6l-5.5 4 4.7-1.2 2.1 1.7-6.8-.5Z"
                  data-name="Girl Logo Grey"
                  fill="#gd6466"
                  fill-rule="evenodd"
                  transform="translate(-1770 -202)"
                />
              </svg>
            </span>
            <div>
              <div
                class="emotion-8"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Soutiens 2011 gorge bonnet
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <hr
              aria-hidden="true"
              class="emotion-9"
            />
            <div>
              <div
                class="emotion-8"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc semper diam
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                class="emotion-13"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      The sundown sweatshirt
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </a>
        <div
          class="emotion-14"
        >
          <button
            class="emotion-15"
          >
            Details Links Example
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBanner Component should match snapshots for AT in desktop view 1`] = `
.emotion-0 {
  margin-left: 0;
  margin-right: 0;
  max-width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-2 {
  display: block;
  position: relative;
  height: 0;
  padding-bottom: 31.25%;
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  grid-template-rows: 1fr 1fr;
}

.emotion-4 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  grid-row: 1/3;
  grid-column: auto;
  background-image: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_1340?fmt=auto&h=800);
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  height: 100%;
  width: 30%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: right;
}

.emotion-6 {
  height: auto;
  max-width: 100%;
}

.emotion-7 {
  max-height: 64px;
  height: 5vw;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.25vw, 16px));
  line-height: 1.75;
  letter-spacing: min(0.125vw, 1.6px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.0640625vw, 0.82px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 60px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.4px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.375vw, 56px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.90625vw, 50px));
  line-height: 1;
  letter-spacing: min(0.1953125vw, 2.5px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.1875vw, 28px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.11796875vw, 1.51px);
  text-transform: none;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.5625vw, 20px));
  line-height: 1.2;
  letter-spacing: min(0.1015625vw, 1.3px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 124px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.2px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.028124999999999997vw, 0.36px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.09375vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--p>span {
  white-space: normal;
}

.emotion-9 {
  margin: 5% 0 3%;
  background-color: #gd6466;
  height: 1px;
}

.emotion-11 {
  position: absolute;
  bottom: 30px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.emotion-12 {
  color: #000000;
  text-align: right;
  margin-right: 30px;
  width: 15%;
  margin-left: auto;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.25vw, 16px));
  line-height: 1.75;
  letter-spacing: min(0.125vw, 1.6px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.0640625vw, 0.82px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 60px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.4px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.375vw, 56px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.90625vw, 50px));
  line-height: 1;
  letter-spacing: min(0.1953125vw, 2.5px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.1875vw, 28px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.11796875vw, 1.51px);
  text-transform: none;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.5625vw, 20px));
  line-height: 1.2;
  letter-spacing: min(0.1015625vw, 1.3px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 124px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.2px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.028124999999999997vw, 0.36px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.09375vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--p>span {
  white-space: normal;
}

.emotion-14 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #ABC123;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-15 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <a
          class="emotion-2"
          href="url.example"
        >
          <div
            class="emotion-3"
          >
            <div
              aria-label="image 1"
              class="emotion-4"
              role="img"
            />
          </div>
          <div
            class="emotion-5"
          >
            <span
              class="emotion-6"
            >
              <svg
                aria-labelledby="title"
                class="emotion-7"
                viewBox="0 0 151 88"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title
                  id="title"
                  lang="en"
                >
                  Athleta Girl Logo
                </title>
                <path
                  d="m1780.6 209.4 3.2 7.3h-6.6ZM1770 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm34-18.6V225h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm16.7 5.4h9.3v-8.5h3.3V225h-3.3V215h-9.3V225h-3.2v-21.7h3.2v8.5Zm26.6-8.5V222h6.3v3.1h-9.5v-21.7h3.2Zm27 3h-8.7v5.3h8.4v3h-8.4v7.3h8.7v3.1h-12v-21.7h12v3Zm15.8 0v18.7h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm20.6 3 3.2 7.3h-6.6ZM1900 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm-98.7 34.6h22.9v.4c0 9.6-2.8 17.4-8.2 22.7a25.8 25.8 0 0 1-19 7.2 24 24 0 0 1-18.8-7.7 30.2 30.2 0 0 1-8.3-21.2c0-8.7 3.5-16.3 8.1-21a29.4 29.4 0 0 1 20.9-8.3 29.7 29.7 0 0 1 14.5 3.5 25.3 25.3 0 0 1 9 8.4l-6.8 4.8a22.7 22.7 0 0 0-6.8-6.4 18.5 18.5 0 0 0-9.8-2.4 20 20 0 0 0-14.7 5.8 22.9 22.9 0 0 0-5.9 15.7 22.7 22.7 0 0 0 5.8 15.3c3.8 4 8.5 5.6 13.9 5.6a16.6 16.6 0 0 0 12.4-5 15.1 15.1 0 0 0 4.6-9.5h-13.8v-7.9Zm40.3-27v35h-8.4v-35h8.4Zm20.8 0c6.7 0 11 1 14.6 3.4 6 4 6.6 10.7 6.6 13.2 0 8-5 14-12.3 15.6l17.3 24h-10.3l-15.9-23h-1.5v23h-8.4v-56.1h9.9Zm-1.5 25.8h2.7c2.3 0 11.8-.2 11.8-9.1 0-8-7.4-8.7-11.5-8.7h-3v17.8Zm43.6-25.7v48h16.3v8.1h-24.8v-56.1h8.5Zm-67 40.7a8.3 8.3 0 1 0 8 8.2 8.2 8.2 0 0 0-8-8.2Zm4.3 10.4.6 2.7-5-4.9 2 4.5-1.2 2.4-.7-6.9-1.4 4.7-2.5 1 3.9-5.7-4 2.7-2.6-.8 6.6-1.9-4.9-.5-1.4-2.3 6.3 2.8-3.4-3.6.4-2.7 3 6.3-.3-5 2-1.7-1.7 6.7 2.9-4h2.6l-5.5 4 4.7-1.2 2.1 1.7-6.8-.5Z"
                  data-name="Girl Logo Grey"
                  fill="#gd6466"
                  fill-rule="evenodd"
                  transform="translate(-1770 -202)"
                />
              </svg>
            </span>
            <div>
              <div
                class="emotion-8"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Soutiens 2011 gorge bonnet
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <hr
              aria-hidden="true"
              class="emotion-9"
            />
            <div>
              <div
                class="emotion-8"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc semper diam
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                class="emotion-13"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      The sundown sweatshirt
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </a>
        <div
          class="emotion-14"
        >
          <button
            class="emotion-15"
          >
            Details Links Example
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBanner Component should match snapshots for Gap in default/mobile view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  position: relative;
}

.emotion-2 {
  display: block;
  position: relative;
  height: 0;
  padding-bottom: 79.73333333333333%;
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 7.5px;
  grid-template-rows: 1fr 1fr;
}

.emotion-4 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  grid-row: 1/3;
  grid-column: auto;
  background-image: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_1340?fmt=auto&h=800);
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  height: 100%;
  width: 43%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: right;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 61.440000000000005px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 88.74666666666667px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 187.73333333333335px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 187.73333333333335px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 153.60000000000002px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 119.46666666666667px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 61.440000000000005px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--p>span {
  white-space: normal;
}

.emotion-7 {
  margin: 7% 0 3.5%;
  background-color: #gd6466;
  height: 1px;
}

.emotion-9 {
  position: absolute;
  bottom: 30px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.emotion-10 {
  color: #000000;
  text-align: right;
  margin-right: 15px;
  width: 35.73%;
  margin-left: auto;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 61.440000000000005px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 88.74666666666667px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 187.73333333333335px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 187.73333333333335px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 153.60000000000002px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 119.46666666666667px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 61.440000000000005px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--p>span {
  white-space: normal;
}

.emotion-12 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #ABC;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
  padding: 0;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13>span {
  padding: 1px 0;
}

.emotion-13 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-13 span span {
  padding-left: initial;
}

.emotion-13:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <a
          class="emotion-2"
          href="url.example"
        >
          <div
            class="emotion-3"
          >
            <div
              aria-label="image 1"
              class="emotion-4"
              role="img"
            />
          </div>
          <div
            class="emotion-5"
          >
            <div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Soutiens 2011 gorge bonnet
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <hr
              aria-hidden="true"
              class="emotion-7"
            />
            <div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc semper diam
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      The sundown sweatshirt
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </a>
        <div
          class="emotion-12"
        >
          <button
            class="emotion-13"
          >
            Details Links Example
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBanner Component should match snapshots for Gap in desktop view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  position: relative;
}

.emotion-2 {
  display: block;
  position: relative;
  height: 0;
  padding-bottom: 31.25%;
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  grid-template-rows: 1fr 1fr;
}

.emotion-4 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  grid-row: 1/3;
  grid-column: auto;
  background-image: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_1340?fmt=auto&h=800);
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  height: 100%;
  width: 30%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: right;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--p>span {
  white-space: normal;
}

.emotion-7 {
  margin: 5% 0 3%;
  background-color: #gd6466;
  height: 1px;
}

.emotion-9 {
  position: absolute;
  bottom: 30px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.emotion-10 {
  color: #000000;
  text-align: right;
  margin-right: 30px;
  width: 15%;
  margin-left: auto;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--p>span {
  white-space: normal;
}

.emotion-12 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #ABC123;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
  padding: 0;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13>span {
  padding: 1px 0;
}

.emotion-13 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-13 span span {
  padding-left: initial;
}

.emotion-13:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <a
          class="emotion-2"
          href="url.example"
        >
          <div
            class="emotion-3"
          >
            <div
              aria-label="image 1"
              class="emotion-4"
              role="img"
            />
          </div>
          <div
            class="emotion-5"
          >
            <div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Soutiens 2011 gorge bonnet
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <hr
              aria-hidden="true"
              class="emotion-7"
            />
            <div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc semper diam
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                    >
                      The sundown sweatshirt
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </a>
        <div
          class="emotion-12"
        >
          <button
            class="emotion-13"
          >
            Details Links Example
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBanner Component variations CategoryBannerShopBySize snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  min-height: 366px;
  padding: 1rem;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/1?fmt=auto&pcrop=683.52,171.216,4628,1740.696&h=1296) no-repeat;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  height: unset;
  margin: 0px 50px;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
}

.emotion-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  text-align: left;
  padding-bottom: 24px;
  font-weight: 400;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin-bottom: 8px;
}

.emotion-5 {
  font-size: 20px;
  margin-right: 30px;
  padding-bottom: 0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  white-space: nowrap;
  color: #2B2B2B;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px;
  -webkit-box-pack: unset;
  -ms-flex-pack: unset;
  -webkit-justify-content: unset;
  justify-content: unset;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-8:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <header
        aria-label="Short Sleeve"
        class="emotion-1"
        data-testid="shopBySize"
        role="img"
      >
        <div
          class="emotion-2"
        >
          <h1
            class="emotion-3"
          >
            Shop your size
          </h1>
          <div
            class="emotion-4"
            role="group"
          >
            <p
              aria-level="3"
              class="emotion-5"
              id="Apparel"
              role="heading"
            >
              Apparel
            </p>
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741929751939761949821108678199210411433bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:929|75-1:939|76-1:949|82-1:1086|78-1:992|104-1:1433&banner=vi_sbs_tg"
                  id="department165size741929751939761949821108678199210411433bannervi_sbs_tg"
                  target="_self"
                >
                  12-18M
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741930751940761950821108778199310411434bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:930|75-1:940|76-1:950|82-1:1087|78-1:993|104-1:1434&banner=vi_sbs_tg"
                  id="department165size741930751940761950821108778199310411434bannervi_sbs_tg"
                  target="_self"
                >
                  18-24M
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741931751941761951821108878199510411465bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:931|75-1:941|76-1:951|82-1:1088|78-1:995|104-1:1465&banner=vi_sbs_tg"
                  id="department165size741931751941761951821108878199510411465bannervi_sbs_tg"
                  target="_self"
                >
                  2T
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741932751942761952821108978199610411436bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:932|75-1:942|76-1:952|82-1:1089|78-1:996|104-1:1436&banner=vi_sbs_tg"
                  id="department165size741932751942761952821108978199610411436bannervi_sbs_tg"
                  target="_self"
                >
                  3T
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741933751943761953821109078199710411437bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:933|75-1:943|76-1:953|82-1:1090|78-1:997|104-1:1437&banner=vi_sbs_tg"
                  id="department165size741933751943761953821109078199710411437bannervi_sbs_tg"
                  target="_self"
                >
                  4T
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Apparel department165size741934751944761954821109178199410411438bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&size=74-1:934|75-1:944|76-1:954|82-1:1091|78-1:994|104-1:1438&banner=vi_sbs_tg"
                  id="department165size741934751944761954821109178199410411438bannervi_sbs_tg"
                  target="_self"
                >
                  5T
                </a>
              </div>
            </div>
          </div>
          <div
            class="emotion-4"
            role="group"
          >
            <p
              aria-level="3"
              class="emotion-5"
              id="Shoes"
              role="heading"
            >
              Shoes
            </p>
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311386bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1386&banner=vi_sbs_tg"
                  id="department165style1106367size10311386bannervi_sbs_tg"
                  target="_self"
                >
                  5
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311387bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1387&banner=vi_sbs_tg"
                  id="department165style1106367size10311387bannervi_sbs_tg"
                  target="_self"
                >
                  6
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311388bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1388&banner=vi_sbs_tg"
                  id="department165style1106367size10311388bannervi_sbs_tg"
                  target="_self"
                >
                  7
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311389bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1389&banner=vi_sbs_tg"
                  id="department165style1106367size10311389bannervi_sbs_tg"
                  target="_self"
                >
                  8
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311390bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1390&banner=vi_sbs_tg"
                  id="department165style1106367size10311390bannervi_sbs_tg"
                  target="_self"
                >
                  M (10)
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311391bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1391&banner=vi_sbs_tg"
                  id="department165style1106367size10311391bannervi_sbs_tg"
                  target="_self"
                >
                  XL (10-12)
                </a>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  aria-labelledby="Shoes department165style1106367size10311392bannervi_sbs_tg"
                  class="emotion-8"
                  color="dark"
                  href="#department=165&style=1106367&size=103-1:1392&banner=vi_sbs_tg"
                  id="department165style1106367size10311392bannervi_sbs_tg"
                  target="_self"
                >
                  XXL (12-14)
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
    </div>
  </div>
</div>
`;
