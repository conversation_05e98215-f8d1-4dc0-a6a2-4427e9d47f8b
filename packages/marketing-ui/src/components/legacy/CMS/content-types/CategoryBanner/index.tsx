// @ts-nocheck
'use client';
import React from 'react';
import { Brands } from '@ecom-next/core/react-stitch';
import { BackgroundImageContent, Banner, BottomContent, Container, DetailsLink, LeftContent } from '../../components/category-banner';
import { ComponentMaxWidth } from '../../subcomponents/ComponentMaxWidth';
import { ShowHideWrapper } from '../../subcomponents/ShowHideWrapper';
import { CategoryBannerProps } from './types';
import CategoryBannerShopBySize from './variations/CategoryBannerShopBySize';
import { useIsBrand } from '../../../hooks';

const CategoryBanner = (props: CategoryBannerProps): JSX.Element => {
  const isAthleta = useIsBrand(Brands.Athleta);

  const { desktopImages, mobileImages, cta, detailsLink, pemoleCode, htmlModalUrl, webAppearance } = props;

  const { desktopHeadline, mobileSubcopy, desktopSubcopy, mobileSubheading, mobileHeadline, desktopSubheading } = props?.richTextArea ?? {};

  const { schema } = props._meta;
  const { showHideBasedOnScreenSize } = webAppearance || {};

  if (schema.includes('shop-by-size')) return <CategoryBannerShopBySize {...props} />;

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
      <ComponentMaxWidth customStyles={isAthleta ? { maxWidth: '100%', marginLeft: 0, marginRight: 0 } : {}}>
        <Container>
          <Banner cta={cta}>
            <BackgroundImageContent desktopImages={desktopImages} mobileImages={mobileImages} webAppearance={webAppearance} />
            <LeftContent
              richTextArea={{
                desktopHeadline,
                desktopSubHeadline: desktopSubheading,
                mobileHeadline,
                mobileSubHeadline: mobileSubheading,
              }}
              webAppearance={webAppearance}
            />
            <BottomContent
              richTextArea={{
                desktopSubCopy: desktopSubcopy,
                mobileSubCopy: mobileSubcopy,
              }}
              webAppearance={webAppearance}
            />
          </Banner>
          {detailsLink && <DetailsLink htmlModalUrl={htmlModalUrl} link={detailsLink} pemoleCode={pemoleCode} webAppearance={webAppearance} />}
        </Container>
      </ComponentMaxWidth>
    </ShowHideWrapper>
  );
};

export default CategoryBanner;
