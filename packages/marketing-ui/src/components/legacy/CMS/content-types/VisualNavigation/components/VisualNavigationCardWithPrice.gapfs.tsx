// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { Body } from '../../../../components/Typography';
import { VisualNavigationCategoryCard } from '../VisualNavigation/types';
import { VisualNavigationCard, VisualNavigationFooter } from '../../../components/visual-navigation';
import { RichText } from '../../../subcomponents/RichText';
import { useThemeColors } from '../../../../hooks/useThemeColors';
import useMinScale from '../../../components/VisualNavigationCarousel/hooks/useMinScale';
import { useViewportIsLarge } from '../../../../hooks';

export const StyledCard = styled(VisualNavigationCard)`
  height: 100%;
  & > div:nth-of-type(2) {
    border-top: 1px solid ${props => props.theme.color.b1};
  }
  &:not(:first-of-type) {
    & > div:nth-of-type(2) {
      border-left: 1px solid ${props => props.theme.color.b1};
    }
  }
`;

export interface VisualNavigationCardWithPriceProps {
  className?: string;
  card: VisualNavigationCategoryCard;
  selected?: boolean;
  imageAspectRatio: string;
  fontColor?: string;
}

const VisualNavigationCardWithPrice = (props: VisualNavigationCardWithPriceProps) => {
  const { card, className, selected, imageAspectRatio, fontColor } = props;
  const { heading, price, hoverOptions = [], image = [] } = card;
  const colors = useThemeColors();
  const isLargeVP = useViewportIsLarge();
  const [source] = image;
  const [hoverOption] = hoverOptions;
  const { image: images = [] } = hoverOption || {};
  const [hoverSource] = images;
  const scalableText = { enable: true, disableInfiniteScaling: true };
  const footerPaddingTopBottom = useMinScale(isLargeVP ? 22 : 16);
  const footerPaddingRightLeft = useMinScale(isLargeVP ? 10 : 16);
  const footerGap = useMinScale(7);

  return (
    <StyledCard
      className={className}
      hoverImage={hoverSource}
      image={source}
      imageAspectRatio={imageAspectRatio}
      overlay={hoverOption}
      selected={selected}
      url={card.url?.value}
      zoom={hoverOption?.zoom}
    >
      {({ isHovering, focused }) => {
        const isHovered = !focused && isHovering;
        return (
          <VisualNavigationFooter
            css={{
              flexDirection: isLargeVP ? 'row' : 'column',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: footerGap,
              boxSizing: 'border-box',
              position: 'relative',
              padding: `${footerPaddingTopBottom} ${footerPaddingRightLeft}`,
              backgroundColor: focused ? colors.b1 : colors.wh,
              textAlign: isLargeVP ? undefined : 'center',
              color: focused || selected ? colors.wh : undefined,
              opacity: isHovered ? 0.65 : 1,
            }}
          >
            {heading && (
              <Body
                customStyles={{
                  textTransform: 'uppercase',
                  flex: 2,
                  display: 'flex',
                  alignItems: 'center',
                  ...(fontColor && { color: fontColor }),
                  ...((focused || selected) && { color: 'inherit' }),
                }}
                element='p'
                variant='body4'
              >
                {heading}
              </Body>
            )}
            {price && (
              <RichText
                color={focused || selected ? 'inherit' : undefined}
                css={{ flex: 1, display: 'flex', alignItems: 'center' }}
                disableTextAlign={!isLargeVP}
                scalableText={scalableText}
                text={price}
              />
            )}
          </VisualNavigationFooter>
        );
      }}
    </StyledCard>
  );
};

export default VisualNavigationCardWithPrice;
