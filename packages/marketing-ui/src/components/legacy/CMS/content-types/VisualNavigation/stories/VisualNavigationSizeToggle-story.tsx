// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';

import VisualNavigationSizeToggle from '../VisualNavigationSizeToggle';
import { VisualNavigationSizeToggleContentType } from '../VisualNavigationSizeToggle/types';
import { MockBaseVisualNavSizeToggle } from '../VisualNavigationSizeToggle/__fixtures__/test-data';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/VisualNavigation/SizeToggle',
  decorators: [
    (Story: React.FC) => (
      <div>
        <Story />
      </div>
    ),
  ],
  parameters: {
    layout: 'fullscreen',
    eyes: { include: false },
  },
  tags: ['exclude'],
};

export const Playground: StoryFn<{
  data: VisualNavigationSizeToggleContentType;
}> = args => <VisualNavigationSizeToggle {...args.data} />;
Playground.args = {
  data: MockBaseVisualNavSizeToggle,
};
