// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import { checkbox, NullJSX, object, select } from '../../../../stories/story-helpers';
import VisualNavigation from '../VisualNavigation';
import { VisualNavigationProps } from '../VisualNavigation/types';
import {
  VisualNavigationDefaultData,
  introCardData,
  MockVisualNavigationCategoryCard1,
  MockVisualNavigationCategoryCard2,
  MockVisualNavigationCategoryCard3,
  MockVisualNavigationCategoryCard4,
  MockVisualNavigationCategoryCard5,
  MockVisualNavigationCategoryCard6,
  VisualNavigationNewData,
  MockVisualNavigationProdData,
} from '../VisualNavigation/__fixtures__/test-data';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/VisualNavigation',
  parameters: {
    page: null,
    knobs: {
      disabled: true,
    },
    eyes: { include: false },
    layout: 'fullscreen',
    sandbox: true,
  },
  tags: ['visual:check'],
};

export const Playground: StoryFn<{ data: VisualNavigationProps }> = props => {
  const content = <VisualNavigation {...props.data} />;

  return (
    <NullJSX content={content}>
      <p>Unsupported by brand</p>
    </NullJSX>
  );
};
Playground.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

Playground.args = {
  data: VisualNavigationDefaultData,
};

export const WithHeadlineBackground = Playground.bind({});
WithHeadlineBackground.args = { data: VisualNavigationNewData };
WithHeadlineBackground.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

export const VisualNavigationWithPOIForON = Playground.bind({});
VisualNavigationWithPOIForON.args = { data: MockVisualNavigationProdData };
VisualNavigationWithPOIForON.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

export const VisualNavigationDefault: StoryFn<{
  data: VisualNavigationProps;
  introCard: boolean;
  iconPlacement: 'above' | 'below' | undefined;
  numberOfCards: number;
  headingStyle: 'primary' | 'secondary';
}> = props => {
  let visualNavData = { ...VisualNavigationDefaultData };

  const categoryCardsData = (numberOfCards: number) =>
    [
      MockVisualNavigationCategoryCard1,
      MockVisualNavigationCategoryCard2,
      MockVisualNavigationCategoryCard3,
      MockVisualNavigationCategoryCard4,
      MockVisualNavigationCategoryCard5,
      MockVisualNavigationCategoryCard6,
    ].slice(0, numberOfCards || 6);

  if (props.introCard) {
    visualNavData = { ...introCardData };
  }

  const { iconPlacement, headingStyle } = props;

  visualNavData.webAppearance = {
    imageOrIconPlacement: iconPlacement,
    showHideBasedOnScreenSize: 'alwaysShow',
    desktopImageOrIconSize: '64px',
    mobileImageOrIconSize: '48px',
    headingStyle,
  };

  const visualNavCardData = categoryCardsData(props.numberOfCards);

  visualNavData.categoryCards = [...visualNavCardData];

  const content = <VisualNavigation {...visualNavData} />;

  return (
    <div>
      <NullJSX content={content}>
        <p>Unsupported by brand</p>
      </NullJSX>
      <div className='product-grid' css={{ marginTop: '100em', width: '100%', textAlign: 'center' }}>
        Jump-to-able Section
      </div>
    </div>
  );
};
const cardCountArray = [1, 2, 3, 4, 5, 6];
VisualNavigationDefault.argTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  data: object('ISM Amplience Data'),
  introCard: checkbox('Display Intro Card'),
  iconPlacement: select('Icon Placement', ['above', 'below']),
  headingStyle: select('Heading Style', ['primary', 'secondary']),
  numberOfCards: select('Number of Category Cards', cardCountArray),
};

VisualNavigationDefault.args = {
  data: VisualNavigationDefaultData,
  introCard: false,
  iconPlacement: 'above',
  numberOfCards: 4,
  headingStyle: 'primary',
};
