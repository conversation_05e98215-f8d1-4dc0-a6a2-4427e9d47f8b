# Meta

The following features are available on all MUI components

- [Lazy Loading](#lazy-loading)
  - [JSON Structure Explained](#json-structure-explained)
  - [Example JSON](#example-json)
- [Subset Page Types](#subset-page-types)
  - [Exclude Page Types](#exclude-page-types)
  - [Include Page Types](#include-page-types)
  - [Backwards Compatibility](#backwards-compatibility)
- [Exclude on Specified CIDs](#exclude-on-specified-cids)
- [Include on Specified CIDs](#include-on-specified-cids)
- [Out of Stock Fallback](#out-of-stock-fallback)
  - [Caution](#caution)
  - [JSON Structure Explained](#json-structure-explained-1)
  - [Example JSON](#example-json-1)
- [Display on Hover](#display-on-hover)

## Lazy Loading

MUI components can be configured to be lazy-loaded. Lazy loading is the act of loading a component only when it's needed (eg. like when a component scrolls into view); it's a technique used to reduce loading times.

### JSON Structure Explained

`name`: string - name of the in-stock component to use

`data`: object - component data for in-stock experience

`meta`: object - configuration of meta features

- `lazy`: boolean - if `true`, enables lazy loading for component

### Example JSON

Under the `meta` key, set the `lazy` key to `true` to have a component load lazily.
While the component is loading, it will create an empty div with a default height. You can set that value using the `defaultHeight` object under `data`. Otherwise, it will use a default value of `300px`.
The `lazy` key is also accepted under `data`.

```json
{
  "name": "LayeredContentModule",
  "meta": {
    "lazy": true
  },
  "data": {
    "className": "exampleClassName",
    "style": {},
    "desktopStyle": {},
    "ctaList": {
      "className": "exampleCTAListClassName",

      "style": {},
      "desktopStyle": {},
      "ctas": [
        {
          "buttonDropdownData": {
            // Accepts any props for `ButtonDropdown`
            "heading": {
              "text": "Shop Winter styles"
            },
            "submenu": [
              {
                "href": "girls",
                "text": "Girls",
                "target": "_blank"
              },
              {
                "href": "boys",
                "text": "Boys"
              },
              {
                "href": "toddler girl",
                "text": "Toddler girl"
              },
              {
                "href": "toddler boy",
                "text": "Toddler boy"
              },
              {
                "href": "baby boy",
                "text": "Baby boy"
              }
            ],
            "customClasses": "exampleCustomClass"
          }
        }
      ]
    }
  }
}
```

## Subset Page Types

### Exclude Page Types

All MUI components can now be excluded from pages by passing in page types to `meta.excludePageTypes` in the JSON. Example with Marketing Sticker below:

```json
   "promorover": {
      "instanceName": "rover-12-13",
      "name": "MktSticker",
      "type": "sitewide",
      "meta": {
        "excludePageTypes": ["category","division"]
      },
      "data": {
        "modalCloseButtonAriaLabel": "Close",
        "href": "link goes here",
        "hrefTarget": "_blank",
        "largeImg": "/Asset_Archive/BRWeb/content/0016/469/084/assets/BRSP_EDPU_Rover.svg",
        "altText": "some alt text here",
        "localStorageKey": "wcd_brRoverStorage_121318",
        "localStorageVal": "BRroverHasBeenClosed_121318",
        "stickerAriaLabel": "stickerAriaLabel",
        "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel",
      }
    }
```

### Include Page Types

Conversely, All MUI components can now be included _only_ in pages by passing in page types to `meta.includePageTypes` in the JSON. Example with Marketing Sticker below:

```json
   "promorover": {
      "instanceName": "rover-12-13",
      "name": "MktSticker",
      "type": "sitewide",
      "meta": {
        "includePageTypes": ["category","division"]
      },
      "data": {
        "modalCloseButtonAriaLabel": "Close",
        "href": "link goes here",
        "hrefTarget": "_blank",
        "largeImg": "/Asset_Archive/BRWeb/content/0016/469/084/assets/BRSP_EDPU_Rover.svg",
        "altText": "some alt text here",
        "localStorageKey": "wcd_brRoverStorage_121318",
        "localStorageVal": "BRroverHasBeenClosed_121318",
        "stickerAriaLabel": "stickerAriaLabel",
        "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel",
      }
    }
```

**Note:** If you use `includePageTypes` it will override `excludePageTypes` logic, thus if you put `"excludePageTypes": ["category"]` in the example above, the category page would still show.

### Backwards Compatibility

Both `includePageTypes` and `excludePageTypes` can be added flexibly within the JSON data structure. The following are some of the alternative data structures supported:

#### In `meta` (recommended)

```json
{
  "meta": {
    "includePageTypes": ["home", "category"]
  }
}
```

#### In `data`

```json
{
  "data": {
    "includePageTypes": ["home", "category"]
  }
}
```

#### In `data.meta`

```json
{
  "data": {
    "meta": {
      "includePageTypes": ["home", "category"]
    }
  }
}
```

#### In `data.options` (deprecated)

```json
{
  "data": {
    "options": {
      "includePageTypes": ["home", "category"]
    }
  }
}
```

## Include on Specified CIDs

All Sitewide MUI components can now be conditionally rendered on any page specified in the array: `data.meta.includeCIDs` in the JSON. Example with Marketing Sticker below:

\*NOTE: Adding this feature to your component forces it to render client side and uses the `placeholderSettings` value as a placeholder until url is checked.

Additionally, for marketing `sitewide` marketing content, this will affect category pages and disable rendering on any category page not listed in the `includeCIDs` array.

```json
   "promorover": {
      "instanceName": "rover-12-13",
      "name": "MktSticker",
      "type": "sitewide",
      "data": {
        "placeholderSettings": {
          "useGreyLoadingEffect": true,
          "desktop": {
            "width": "100%",
            "height": "100px",
            "margin": "0px auto"
          },
          "mobile": {
            "width": "0px",
            "height": "50px"
          }
        },
        "meta": {
          "includeCIDs": ["10018", "5360"]
        }
      }
    }
```

## Exclude on Specified CIDs

All Sitewide MUI components can now be conditionally rendered on any page specified in the array: `data.meta.excludeCIDs` in the JSON. Example with Marketing Sticker below:

\*NOTE: Adding this feature to your component forces it to render client side and uses the `placeholderSettings` value as a placeholder until url is checked.

Additionally, for marketing `sitewide` marketing content, this will affect category pages and disable rendering on any category page listed in the `excludeCIDs` array.

```json
   "promorover": {
      "instanceName": "rover-12-13",
      "name": "MktSticker",
      "type": "sitewide",
      "data": {
        "placeholderSettings": {
          "useGreyLoadingEffect": true,
          "desktop": {
            "width": "100%",
            "height": "100px",
            "margin": "0px auto"
          },
          "mobile": {
            "width": "0px",
            "height": "50px"
          }
        },
        "meta": {
          "excludeCIDs": ["10018", "5360"]
        }
      }
    }
```

## Out of Stock Fallback

This feature allows you to define an in-stock experience and a fallback experience in cases where an experience is dependent on a product's availability.

On initial render, a `LoadingPlaceholder` will be rendered.

If the product is available, then it will show the in-stock experience, else, it will switch to the fallback experience.

On errors, a `LoadingPlaceholder` will be rendered.

### Caution

#### Possible Performance Impacts

Be aware that this feature could negatively impact the performance of the site if used too many times on a single page. This is because each component that uses this feature will need to make an API request before rendering a component, resulting in a slower render.

#### Known Issues with MultiComponent

If this feature is used by a component located within a `MultiComponent`, then the component will initially render a blank space that is `300px` tall. This is due to a default height being set by the `MultiComponent`. To work around this, add a `defaultHeight` object under `data` of the in-stock experience. An example of this can be seen [below](#example-json).

### JSON Structure Explained

`name`: string - name of the in-stock component to use

`type`: `"builtin"` - can be omitted if component is not a "builtin" component

`data`: object - component data for in-stock experience

`meta`: object - configuration of meta features

- `outOfStockFallback`: object - configration for `outOfStockFallback` feature
  - `apiKey`: string - needed to send a request to the product style service
  - `pid`: string - the product ID to check availability
  - `productStyleServiceUrl`: string - the product style service endpoint. Changes based on environment. Example: `https://stage.api.azeus.gaptech.com/ux/web/product-style-preview-web-experience/product-styles/`
    - Additional docs for product style service: https://confluence.gapinc.com/display/EO/Swagger+Docs+for+PSS
  - `loadingPlaceholderData`: object - configuration for the `LoadingPlaceholder`
    - `mobile`: object - CSS to apply on mobile experiences
    - `desktop`: object - CSS to apply on desktop experiences
    - `useGreyLoadingEffect`: boolean - if `false`, will render a blank space where the component will load
  - `component`: object - configuration of the "out of stock" experience
    - `name`: string - name of the component to use for the "out of stock" experience
    - `type`: `"builtin"` - can be omitted if component is not a "builtin" component
    - `data`: object - component data for "out of stock" experience

### Example JSON

**NOTE**: This example requires you to add your own `pid`, `apiKey`, and `productStyleServiceUrl` (under `meta.outOfStockFallback`).

```json
{
  "name": "HoverImage",
  "data": {
    "defaultHeight": {
      "small": "0px",
      "large": "0px"
    },
    "containerStyle": {
      "height": "300px"
    },
    "background": {
      "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny.jpg",
      "altText": "Super Skinny"
    },
    "svgOverlay": {
      "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny.svg",
      "altText": "Super Skinny"
    },
    "backgroundHover": {
      "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny.jpg",
      "altText": "Super Skinny"
    },
    "svgOverlayHover": {
      "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny_hover.svg",
      "altText": "Super Skinny"
    }
  },
  "meta": {
    "outOfStockFallback": {
      "pid": "*SUPPLY YOUR OWN PID*",
      "apiKey": "*SUPPLY YOUR API KEY*",
      "productStyleServiceUrl": "*SUPPLY YOUR OWN PRODUCT STYLE SERVICE URL*",
      "loadingPlaceholderData": {
        "desktop": {
          "height": "300px"
        }
      },
      "component": {
        "name": "LayeredContentModule",
        "data": {
          "container": {
            "style": {
              "height": "300px"
            }
          },
          "background": {
            "linkData": {
              "target": "_blank",
              "to": "https://gap.com",
              "title": "Go to gap.com"
            },
            "image": {
              "alt": "A child",
              "srcUrl": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0017/477/619/assets/190729_008A_Part1_Active_2_HP_US_XL.jpg",
              "desktopStyle": {
                "height": "300px",
                "width": "auto"
              }
            }
          }
        }
      }
    }
  }
}
```

## Display on Hover

This feature enables `onMouseEnter` and `onMouseLeave` events that trigger a defined component
to fade in and fade out respectively.

### JSON Structure Explained

`containerStyle`: CSSObject - The default styles for the container that contains both the hoverable component and the component that will fade in. These styles are meant to be mobile first. To override these styles for larger viewports use `desktopContainerStyle`.

`desktopContainerStyle`: CSSObject - The desktop styles for the container that contains the hoverable component and the component that will fade in. These styles will be applied on top of the `containerStyle` styles for larger device sizes.

`componentContainerStyle`: CSSObject - The default, mobile first, styles for the container of the component. They will be displayed when the mouseEnter event is triggered. These can be overridden for large breakpoints by applying styles to `desktopComponentContainerStyle`.

`desktopComponentContainerStyle`: CSSObject - Desktop styles for the container of the component. Include overrides for mobile first `componentContainerStyle` styles here.

`animation`: celebratory | expressive | performance - Optional. Defaults to `expressive`. The time it takes fot the transitions to fade in and fade out.
Each motion token represents the following speed: Celebratory is slow speed, expressive is medium speed and performance is fast speed.

`component`: Component that will fade in. It requires the common MUI configuration.
{
`name`, `type`, `data`
}

### Example JSON

```json
{
  "displayOnHover": {
    "containerStyle": {
      "display": "flex",
      "position": "relative"
    },
    "desktopContainerStyle": {
      "display": "block"
    },
    "componentContainerStyle": {
      "position": "absolute",
      "bottom": "100%",
      "left": "-200px",
      "width": "500px"
    },
    "desktopComponentContainerStyle": {
      "bottom": "50%",
      "left": "-50px",
      "width": "700px"
    },
    "animation": "expressive",
    "component": {
      "name": "LayeredContentModule",
      "type": "sitewide",
      "data": {
        "container": {
          "style": {
            "height": "300px"
          }
        },
        "background": {
          "linkData": {
            "target": "_blank",
            "to": "https://gap.com",
            "title": "Go to gap.com"
          },
          "image": {
            "alt": "A child",
            "srcUrl": "static/media/background.be7c0d50.jpg",
            "desktopStyle": {
              "height": "300px",
              "width": "auto"
            }
          }
        }
      }
    }
  }
}
```

### Limitations

**Hover is inaccessible on mobile devices**, therefore it should not be used to display information that is necessary to navigate the site.
