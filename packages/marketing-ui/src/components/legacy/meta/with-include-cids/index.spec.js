// @ts-nocheck
import React from 'react';
import { render, screen } from 'test-utils';
import withIncludeCids from '.';

const testChildText = 'Test Component';
const MarketingComponent = () => <div>{testChildText}</div>;

const MarketingComponentWrapper = withIncludeCids(MarketingComponent);

describe('With Include CIDs', () => {
  describe('includeCIDs', () => {
    const renderWithIncludedPages = includeCIDs =>
      render(
        <MarketingComponentWrapper
          data={{
            includeCIDs,
          }}
        />
      );

    beforeAll(() => {
      delete window.location;
      window.location = {
        search: '',
      };
    });

    afterAll(() => {
      window.location = location;
    });

    [
      {
        searchStr: '10018',
        includeCIDs: ['10018'],
        desc: 'should render marketing component for include cids',
      },
      {
        searchStr: '00000',
        includeCIDs: [],
        desc: 'should render marketing component for empty include array',
      },
      {
        searchStr: '00000',
        includeCIDs: ['10018'],
        desc: 'should NOT render marketing component for included cids',
      },
    ].forEach(test =>
      it.skip(test.desc, () => {
        window.location.search = `?cid=${test.searchStr}`;
        renderWithIncludedPages(test.includeCIDs);
        if (test.includeCIDs.length < 1 || test.includeCIDs.includes(test.searchStr)) {
          expect(screen.queryByText(testChildText)).toBeInTheDocument();
        } else {
          expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
        }
      })
    );
  });
});
