// @ts-nocheck
import React from 'react';
import { renderHook, act } from 'test-utils';
import { BreakpointContext, Size, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { useIsViewport, useOrientation, useViewport } from '.';

const MockContextWrapper: React.FC<{
  size: Size;
  orientation: 'portrait' | 'landscape';
}> = ({ children, size, orientation }) => (
  <BreakpointContext.Provider
    value={{
      media: '',
      size,
      greaterOrEqualTo: jest.fn(),
      maxWidth: jest.fn(),
      minWidth: (bp: Size) => true || bp,
      orientation,
      smallerThan: jest.fn(),
    }}
  >
    {children}
  </BreakpointContext.Provider>
);

const render = (hook: 'useViewPort' | 'useIsViewport' | 'useOrientation', size: Size, orientation: 'portrait' | 'landscape') => {
  switch (hook) {
    case 'useIsViewport': {
      return renderHook(() => useIsViewport(size), {
        wrapper: MockContextWrapper,
        initialProps: { size, orientation },
      });
    }
    case 'useOrientation': {
      return renderHook(() => useOrientation(), {
        wrapper: MockContextWrapper,
        initialProps: { size, orientation },
      });
    }
    default: {
      return renderHook(() => useViewport(), {
        wrapper: MockContextWrapper,
        initialProps: { size, orientation },
      });
    }
  }
};

describe('useViewPort', () => {
  it(`view port should be ${XLARGE}`, () => {
    expect(render('useViewPort', XLARGE, 'landscape').result.current).toBe(XLARGE);
  });
  it(`view port should be ${SMALL}`, () => {
    expect(render('useViewPort', SMALL, 'landscape').result.current).toBe(SMALL);
  });
});
describe('useIsViewport', () => {
  it(`view port should be ${XLARGE}`, () => {
    expect(render('useIsViewport', XLARGE, 'landscape').result.current).toBe(true);
  });
});

describe('useOrientation', () => {
  it("should return 'landscape'", () => {
    expect(render('useOrientation', XLARGE, 'landscape').result.current).toBe('landscape');
  });
});
