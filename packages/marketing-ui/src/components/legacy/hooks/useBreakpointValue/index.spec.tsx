// @ts-nocheck
import React from 'react';
import { renderHook, act } from 'test-utils';
import { BreakpointContext, LARGE, MEDIUM, Size, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import useBreakpointValue, { UseBreakpointValueOpts } from '.';

const MockContextWrapper: React.FC<{ size: Size }> = ({ children, size = XLARGE }) => (
  <BreakpointContext.Provider
    value={{
      media: '',
      size,
      greaterOrEqualTo: jest.fn(),
      maxWidth: jest.fn(),
      minWidth: jest.fn(),
      orientation: 'landscape',
      smallerThan: jest.fn(),
    }}
  >
    {children}
  </BreakpointContext.Provider>
);

const render = (size: Size, obj: UseBreakpointValueOpts<number>) =>
  renderHook(() => useBreakpointValue(obj), {
    wrapper: MockContextWrapper,
    initialProps: { size },
  });

describe('useBreakpointValue', () => {
  it('should render the default value', () => {
    const hook = renderHook(() =>
      useBreakpointValue({
        default: 1,
      })
    );

    expect(hook.result.current).toBe(1);
  });

  it('should render the correct value', () => {
    const mockValues = {
      'x-large': 5,
      large: 4,
      medium: 3,
      small: 2,
      default: 1,
    };

    expect(render(XLARGE, mockValues).result.current).toBe(5);
    expect(render(LARGE, mockValues).result.current).toBe(4);
    expect(render(MEDIUM, mockValues).result.current).toBe(3);
    expect(render(SMALL, mockValues).result.current).toBe(2);
  });

  it('should use the default value when breakpoint is not found', () => {
    expect(render(MEDIUM, { default: 1, small: 2, 'x-large': 3 }).result.current).toBe(1);
  });
});
