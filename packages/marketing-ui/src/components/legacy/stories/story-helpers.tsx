// @ts-nocheck
'use client';
import React from 'react';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { ArgTypes, Decorator } from '@storybook/react';
import { FakeProductGridItem, ProductGridItem } from './FakeProductGrid';

export type { StoryFn } from '@storybook/react';

type ControlType = 'boolean' | 'text' | 'color' | 'object' | 'array' | 'radio' | 'select' | 'range' | 'number';

type ControlOpts<T extends ControlType = ControlType> = {
  name: string;
  description?: string;
  type: T;
  category?: string;
  defaultValue?: unknown;
  disable?: boolean;
} & (
  | { type: 'text' | 'object' | 'array' | 'boolean' }
  | { type: 'radio'; options: Array<unknown> }
  | { type: 'select'; options: Array<unknown> }
  | {
      type: 'range';
      min: number;
      max: number;
      step: number;
    }
  | {
      type: 'number';
      min?: number;
      max?: number;
      step?: number;
    }
  | {
      type: 'color';
      presetColors?: Array<{ title: string; color: string } | string>;
    }
);

export type StoryControl = ArgTypes & { table: Record<string, unknown> };

export function control(opts: ControlOpts): StoryControl {
  const { name, category, disable, description, defaultValue } = opts;
  const base = {
    name,
    defaultValue,
    description,
    control: {
      type: opts.type,
    },
    table: {},
  } as unknown as StoryControl;

  if (category) {
    base.table.category = category;
  }

  if (disable) {
    base.table.disable = disable;
  }

  switch (opts.type) {
    case 'select':
    case 'radio':
      return {
        ...base,
        options: opts.options,
      };
    case 'color':
      return {
        ...base,
      };
    case 'number':
    case 'range':
      return {
        ...base,
        control: {
          type: opts.type,
          min: opts.min,
          max: opts.max,
          step: opts.step,
        },
      } as StoryControl;
    default:
      return base;
  }
}

export function select<T>(name: string, options: Array<T>, opts: Omit<ControlOpts, 'name' | 'type'> = {}) {
  return control({
    name,
    options,
    type: 'select',
    ...opts,
  });
}

export function text(name: string, opts: Omit<ControlOpts, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'text',
    ...opts,
  });
}

export function color(name: string, opts: Omit<ControlOpts, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'color',
    ...opts,
  });
}

export function checkbox(name: string, opts: Omit<ControlOpts, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'boolean',
    ...opts,
  });
}

export function number(name: string, opts: Omit<ControlOpts<'number'>, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'number',
    ...opts,
  });
}

export function range(name: string, opts: Omit<ControlOpts<'range'>, 'name' | 'type'>): StoryControl {
  return control({
    name,
    type: 'range',
    ...opts,
  });
}

export function object(name: string, opts: Omit<ControlOpts, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'object',
    ...opts,
  });
}

export function radio<T>(name: string, options: Array<T>, opts: Omit<ControlOpts, 'name' | 'type'> = {}): StoryControl {
  return control({
    name,
    type: 'radio',
    options,
    ...opts,
  });
}

export function category<T extends string>(category: string, controls: Record<T, StoryControl>): Record<T, StoryControl> {
  const keys: T[] = Object.keys(controls) as T[];
  if (!keys || !keys.length) {
    return controls;
  }

  return keys.reduce(
    (res: Record<T, StoryControl>, key: T) => {
      const item = res[key];
      res[key] = {
        ...item,
        table: {
          ...item.table,
          category,
        },
      };
      return res;
    },
    controls as Record<T, StoryControl>
  );
}

export function makeControls<T extends string>(args: Record<T, StoryControl>): (...args: T[]) => Record<T, StoryControl> {
  return (...controls: T[]) => {
    const result: Record<T, StoryControl> = {} as Record<T, StoryControl>;
    if (controls && controls.length) {
      return controls.reduce((res, key: T) => {
        res[key] = { ...args[key] };
        return res;
      }, result);
    }
    return result;
  };
}

export function AddFakeProductGridItem(size: number) {
  const children: JSX.Element[] = [];
  const enabledFeatures = useEnabledFeatures();
  const isNewPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  const productGridItem = isNewPlpGrid2025 ? <ProductGridItem className='fake-product-grid-item' /> : <FakeProductGridItem />;
  for (let item = 0; item < size; item += 1) {
    children.push(productGridItem);
  }
  return <>{children}</>;
}

export const localizationPicker = {
  options: ['en-US', 'en-CA', 'fr-CA'],
  control: {
    type: 'select',
  },
};

/**
 * Story also component,
 * Dive deeper to inspect,
 * Is content now null?
 */
const nullCheck = (content: JSX.Element): boolean =>
  content.type() === null || typeof content.type().type !== 'function' || content.type().type(content.props) === null;

const unsupportedBrandCheck = (content: JSX.Element): boolean => {
  const { type } = content;
  const isTypeAFunction = typeof type === 'function';
  return (content && isTypeAFunction && type() === null) || !isTypeAFunction;
};

/**
 * Shows `content` if `content.type()` is not `null`,
 * e.g., usually used with components wrapped by `withBrandComponent`:
 * @example
 * const content = <ComponentWrappedByWithBrandComponent {...props} />;
 * <NullJSX content={content}>Alternative unsupported text (children)</NullJSX>
 * @param children The alternative children and/or unsupported text
 * @param content Usually a component wrapped by `withBrandComponent`
 * @returns The `content` or the `children` wrapped by a `Fragment`
 */
export const NullJSX = ({ children, content }: { children: React.ReactNode; content: JSX.Element }): JSX.Element => {
  if (unsupportedBrandCheck(content)) return <>{children}</>;

  if (nullCheck(content)) return <p>Need implementation or feature unsupported</p>;

  return <>{content}</>;
};

/**
 * Renders `Unsupported by brand` message
 */
export const UnsupportedByBrand = (props: { customMessage?: string }) => <p>{props?.customMessage ?? 'Unsupported by brand'} </p>;

/**
 * This decorator is used to hide stories that are not supported by the brand
 *
 * https://storybook.js.org/docs/writing-stories/decorators
 */
export const brandSupportDecorator: Decorator = Story => {
  if (nullCheck(<Story />)) return <UnsupportedByBrand />;
  return (
    <>
      <style>
        {`
        .sb-main-padded {
          padding: 0 !important;
        }
      `}
      </style>
      <Story />
    </>
  );
};

export const NotImplemented = () => <p>Not implemented</p>;
