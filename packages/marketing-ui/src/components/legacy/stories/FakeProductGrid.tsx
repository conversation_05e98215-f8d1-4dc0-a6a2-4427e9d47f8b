// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import classnames from 'classnames';
import { styled, Interpolation, Theme, Brands } from '@ecom-next/core/react-stitch';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { useIsBrand } from '../hooks/useBrand';

export const ProductGridItem: React.FC<React.PropsWithChildren & React.HTMLAttributes<HTMLDivElement>> = ({ children, ...restProps }) => (
  <div {...restProps}>{children}</div>
);

export const ProductGrid: React.FC<React.PropsWithChildren & React.HTMLAttributes<HTMLDivElement>> = ({ children, ...restProps }) => {
  const { className = '' } = restProps;
  const productGridClassNames = classnames('fake-product-grid', className, {});

  return (
    <div {...restProps} className={productGridClassNames}>
      <section className='fake-product-grid-section'>{children}</section>
    </div>
  );
};

export const FakeProductGridItem = styled.div<{
  isDouble?: boolean;
  noOfCards?: number;
  width?: string;
}>(props => {
  const isBROrBRFactory = useIsBrand(Brands.BananaRepublic, Brands.BananaRepublicFactoryStore);
  const noOfCards = props.noOfCards || (isBROrBRFactory ? 3 : 4);

  const isGapOrGapFactory = useIsBrand(Brands.Gap, Brands.GapFactoryStore);

  const ctx = useContext(BreakpointContext);

  const baseStyles: Interpolation<Theme> = {
    width: props.width,
    padding: isGapOrGapFactory ? 0 : 8,
    boxSizing: 'border-box',
    position: 'relative',
    '& > *': {
      background: '#FFFFFF',
    },
  };

  if (props.children) {
    return baseStyles;
  }

  // "Mobile" < 767
  const width = {
    minHeight: '100%',
    flexBasis: '50%',
    maxWidth: '50%',
  };

  // lg < 1024
  if (ctx.minWidth('large')) {
    width.flexBasis = `${100 / noOfCards}%`;
    width.maxWidth = `${100 / noOfCards}%`;
  }

  // xl 1024+
  if (ctx.minWidth('x-large')) {
    width.flexBasis = `${100 / noOfCards}%`;
    width.maxWidth = `${100 / noOfCards}%`;
  }

  return {
    ...baseStyles,
    ...width,

    '&::after': {
      content: "'Product Item'",
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#AAA',
      textTransform: 'uppercase',
      fontWeight: 'bold',
      background: '#EEE',
      border: '1px dashed black',
      height: isGapOrGapFactory ? '100%' : 'calc(100% - 16px)',
      width: isGapOrGapFactory ? '100%' : 'calc(100% - 16px)',
      position: 'absolute',
      top: isGapOrGapFactory ? 0 : 8,
      left: isGapOrGapFactory ? 0 : 8,
    },
  };
});

const Container = styled.div`
  box-sizing: border-box;
  background: #ddd;
  min-height: 100vh;
`;
const GridContainer = styled.div(({ isBROrBRFactory, isGapOrGapFactory }: { isBROrBRFactory: boolean; isGapOrGapFactory: boolean }) => ({
  boxSizing: 'border-box',
  background: '#FFFFFF',
  overflowX: 'hidden',
  margin: '0 auto',
  display: 'flex',
  flexWrap: 'wrap',
  gap: isGapOrGapFactory ? 8 : 0,
  width: '100vw',
  maxWidth: isBROrBRFactory || isGapOrGapFactory ? 'unset' : 1024,
  paddingBlock: '8px',
  paddingInline: '12px',
}));

const Grid: React.FC = ({ children }) => {
  const isBROrBRFactory = useIsBrand(Brands.BananaRepublic, Brands.BananaRepublicFactoryStore);
  const isGapOrGapFactory = useIsBrand(Brands.Gap, Brands.GapFactoryStore);

  return (
    <Container>
      <GridContainer isBROrBRFactory={isBROrBRFactory} isGapOrGapFactory={isGapOrGapFactory}>
        {children}
      </GridContainer>
    </Container>
  );
};

export default Grid;
