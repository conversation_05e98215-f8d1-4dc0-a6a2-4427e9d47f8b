// @ts-nocheck
'use client';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import type { StoryObj } from '@storybook/react';
import React from 'react';
import ISMBannerFull from '../../../CMS/content-types/ISMBannerFullImage';
import ISMBannerFullVideo from '../../../CMS/content-types/ISMBannerFullVideo';
import { ProductGrid } from '../../FakeProductGrid';
import { AddFakeProductGridItem, category } from '../../story-helpers';
import { FullImage, FullImageBottomCtaRteData, FullWithNonVimeoVideo, FullWithNonVimeoVideoBottomCtaRteData } from './data';
import { useGridItemWidth } from './helpers';
import { getISMBannerControls, ISMBannerControlArgTypes, usePropsFromControls } from './story-controls.config';

import './assets/ism-product-grid.css';
import ISMBannerFullImage from '../../../CMS/content-types/ISMBannerFullImage/index';

const IconControls = category(
  'Image/Icon',
  getISMBannerControls(
    'showHideBasedOnScreenSize',
    'showIcon',
    'iconPlacement',
    'iconHorizontalAlignment',
    'iconLabelFontColor',
    'desktopImageOrIconSize',
    'mobileImageOrIconSize',
    'verticalTextAlignment',
    'verticalCtaAlignment'
  )
);

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/InSortMarketingBanner/v2/Full',
  parameters: {
    layout: 'fullscreen',
    sandbox: true,
  },
  tags: ['exclude-br', 'visual:check'],
  decorators: [
    (Story: React.FC) => (
      <BreakpointProvider>
        <Story />
      </BreakpointProvider>
    ),
  ],
};

export const ISMFullImage: StoryObj<ISMBannerControlArgTypes> = props => {
  const { productGridSize = 3 } = props;
  const [_, isDouble] = useGridItemWidth(props);
  const ismProps = usePropsFromControls(
    props,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-image.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-image.json'
  );
  return (
    <>
      <ProductGrid>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFull {...(ismProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
      </ProductGrid>
    </>
  );
};
ISMFullImage.argTypes = {
  ...category(
    'ISM',
    getISMBannerControls(
      'productGridSize',
      'ismSize',
      'bannerLink',
      'backgroundType',
      'text',
      'detailsLink',
      'detailsPrefix',
      'detailsLinkLocation',
      'detailsLinkFontColor'
    )
  ),
  ...IconControls,
  ...category(
    'CTA',
    getISMBannerControls('primaryCTALabel', 'secondaryCTALabel', 'ctaButtonVariant', 'ctaColor', 'customCTAPrimary', 'ctaHorizontalAlignment', 'customCTAccent')
  ),
};
ISMFullImage.args = {
  ...FullImage,
};

export const ISMFullImageBottomCtaWithRte: StoryObj<ISMBannerControlArgTypes> = props => {
  const { productGridSize = 3 } = props;
  const [_, isDouble] = useGridItemWidth(props);
  const ismProps = usePropsFromControls(
    props,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-image.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-image.json'
  );
  return (
    <>
      <ProductGrid>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFull {...(ismProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
      </ProductGrid>
    </>
  );
};
ISMFullImageBottomCtaWithRte.argTypes = {
  ...category(
    'ISM',
    getISMBannerControls(
      'productGridSize',
      'ismSize',
      'bannerLink',
      'backgroundType',
      'text',
      'detailsLink',
      'detailsPrefix',
      'detailsLinkLocation',
      'detailsLinkFontColor'
    )
  ),
  ...IconControls,
  ...category(
    'CTA',
    getISMBannerControls('primaryCTALabel', 'secondaryCTALabel', 'ctaButtonVariant', 'ctaColor', 'customCTAPrimary', 'ctaHorizontalAlignment', 'customCTAccent')
  ),
};
ISMFullImageBottomCtaWithRte.args = {
  ...FullImageBottomCtaRteData,
};

export const ISMFullWithVideo: StoryObj<ISMBannerControlArgTypes> = props => {
  const { productGridSize = 3 } = props;
  const [_, isDouble] = useGridItemWidth(props);
  const ismProps = usePropsFromControls(
    props,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-video.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-video.json'
  );

  return (
    <>
      <ProductGrid>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFullVideo {...(ismProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
      </ProductGrid>
    </>
  );
};
ISMFullWithVideo.tags = ['exclude'];
ISMFullWithVideo.argTypes = {
  ...category('ISM', getISMBannerControls('productGridSize', 'ismSize', 'vimeoVideo', 'text', 'detailsLink', 'detailsLinkLocation', 'detailsLinkFontColor')),
  ...IconControls,
  ...category(
    'CTA',
    getISMBannerControls('primaryCTALabel', 'secondaryCTALabel', 'ctaButtonVariant', 'ctaColor', 'customCTAPrimary', 'ctaHorizontalAlignment', 'customCTAccent')
  ),
};
ISMFullWithVideo.args = {
  ...FullWithNonVimeoVideo,
};

export const ISMFullWithVideoBottomCtaWithRte: StoryObj<ISMBannerControlArgTypes> = props => {
  const { productGridSize = 3 } = props;
  const [width, isDouble] = useGridItemWidth(props);
  const ismProps = usePropsFromControls(
    props,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-video.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-video.json'
  );
  if (isDouble) {
    FullWithNonVimeoVideo!.nonVimeoVideo!.desktop!.url =
      'https://cdn.static.amplience.net/oldnavyprod/_vid/ism_doublefull-test/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/3cf2f02e-7bbc-4928-98d0-fc77520633f9.mp4';
    FullWithNonVimeoVideo!.nonVimeoVideo!.mobile!.url =
      'https://cdn.static.amplience.net/oldnavyprod/_vid/ism_doublefull-test/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/3cf2f02e-7bbc-4928-98d0-fc77520633f9.mp4';
  } else {
    FullWithNonVimeoVideo!.nonVimeoVideo!.desktop!.url =
      'https://cdn.static.amplience.net/oldnavyprod/_vid/ism_singlefull_test/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/f5e29bb9-2d63-4c28-8fbb-f9035a139194.mp4';
    FullWithNonVimeoVideo!.nonVimeoVideo!.mobile!.url =
      'https://cdn.static.amplience.net/oldnavyprod/_vid/ism_singlefull_test/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/f5e29bb9-2d63-4c28-8fbb-f9035a139194.mp4';
  }
  return (
    <>
      <ProductGrid>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFullVideo {...(ismProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
      </ProductGrid>
    </>
  );
};
ISMFullWithVideoBottomCtaWithRte.tags = ['exclude'];
ISMFullWithVideoBottomCtaWithRte.argTypes = {
  ...category(
    'ISM',
    getISMBannerControls(
      'productGridSize',
      'ismSize',
      'nonVimeoVideo',
      'text',
      'detailsLink',
      'detailsLinkLocation',
      'detailsLinkFontColor',
      'videoPlaybackBehavior'
    )
  ),
  ...IconControls,
  ...category(
    'CTA',
    getISMBannerControls('primaryCTALabel', 'secondaryCTALabel', 'ctaButtonVariant', 'ctaColor', 'customCTAPrimary', 'ctaHorizontalAlignment', 'customCTAccent')
  ),
};
ISMFullWithVideoBottomCtaWithRte.args = {
  ...FullWithNonVimeoVideoBottomCtaRteData,
};

export const MultiISM: StoryObj<ISMBannerControlArgTypes> = props => {
  const { productGridSize = 7 } = props;
  const [_, isDouble] = useGridItemWidth(props);
  const ismVideoProps = usePropsFromControls(
    props,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-video.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-video.json'
  );
  const ismImageProps = usePropsFromControls(
    FullImage,
    isDouble ? 'https://cms.gap.com/schema/content/v1/ism-double-full-image.json' : 'https://cms.gap.com/schema/content/v1/ism-single-full-image.json'
  );
  return (
    <>
      <ProductGrid>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFullVideo {...(ismVideoProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ISMBannerFullImage {...(ismImageProps as any)} />
        {AddFakeProductGridItem(productGridSize)}
      </ProductGrid>
    </>
  );
};
MultiISM.tags = ['exclude'];
MultiISM.argTypes = {
  ...category(
    'ISM',
    getISMBannerControls(
      'productGridSize',
      'ismSize',
      'vimeoVideo',
      'text',
      'detailsLink',
      'detailsLinkLocation',
      'detailsLinkFontColor',
      'bannerLink',
      'backgroundType',
      'text',
      'detailsPrefix'
    )
  ),
  ...IconControls,
  ...category(
    'CTA',
    getISMBannerControls('primaryCTALabel', 'secondaryCTALabel', 'ctaButtonVariant', 'ctaColor', 'customCTAPrimary', 'ctaHorizontalAlignment', 'customCTAccent')
  ),
};
MultiISM.args = {
  ...FullWithNonVimeoVideo,
};
