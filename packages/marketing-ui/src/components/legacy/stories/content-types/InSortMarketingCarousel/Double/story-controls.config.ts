// @ts-nocheck
'use client';
import { useMemo } from 'react';
import { ISMCarouselDoublePartialContentType, ISMCarouselSchema } from '../../../../CMS/content-types/ISMCarousel/types';
import { color, select, text, checkbox, object, makeControls, control, number } from '../../../story-helpers';

import { CarouselSettings, Autoplays, ControlColorTypes, Transitions } from '../../../../CMS/subcomponents/CMSMarketingCarousel/types';
import { BannerLinkType } from '../../../../CMS/global/types';
import { CTAVariant } from '../../../../CMS/subcomponents/CTAButton';
import { ShowHideBasedOnScreenSizeProps } from '../../../../CMS/subcomponents/ShowHideWrapper/types';

const iconSizeArray = ['1x', '2x', '3x', '4x', '5x'];
const verticalAlignmentArray = ['top', 'center', 'bottom'];

interface ExtendedISMCarouselContentType extends ISMCarouselDoublePartialContentType {
  productGridSize?: number;
}

export type ISMCarouselControlArgTypes = Pick<
  ExtendedISMCarouselContentType,
  | 'frames'
  | 'contentConfiguration'
  | 'cta1'
  | 'bodyCopy'
  | 'cta2'
  | 'bannerLink'
  | 'detailsLink'
  | 'detailsPrefix'
  | 'detailsLinkLocation'
  | 'pemoleCode'
  | 'htmlModalUrl'
  | 'instanceName'
  | 'rowPosition'
  | 'webAppearance'
> & {
  ismSize?: 'single' | 'double';
  ism?: ISMCarouselDoublePartialContentType;
  carouselSettings: CarouselSettings;
  bannerLink?: BannerLinkType;
  transition?: Transitions;
  type?: Autoplays;
  cta1Label?: string;
  cta2Label?: string;
  continuousLoop?: boolean;
  autoplayDelay?: string;
  autoplayPauseOnHover?: boolean;
  animationSpeed?: string;
  animationEase?: boolean;
  stylingControlsIconsColor?: ControlColorTypes;
  stylingHideChevrons?: boolean;
  rowPositionDesktop?: number;
  rowPositionMobile?: number;
  detailsLinkFontColor?: string;
  cta1ButtonVariant?: string;
  cta2ButtonVariant?: string;
  productGridSize?: number;
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
};

export const getISMCarouselControls = makeControls({
  iconLabelFontColor: color('Icon Label Color'),
  contentConfiguration: select('Content Configuration', ['unique', 'persistent']),
  bodyCopy: control({ name: 'Body Copy', type: 'text' }),
  backgroundType: select('ISM Background', ['image', 'solid', 'gradient']),
  cta1Label: text('Primary CTA'),
  cta2Label: text('Secondary CTA'),
  cta1ButtonVariant: select('Primary CTA Variant', ['chevron', 'underline', 'outline', 'flat', 'solid', 'border']),
  cta2ButtonVariant: select('Secondary CTA Variant', ['chevron', 'underline', 'outline', 'flat', 'solid', 'border']),
  cta1Color: select('CTA 1 Color', ['light', 'dark', 'custom']),
  cta2Color: select('CTA 2 Color', ['light', 'dark', 'custom']),
  customCTAPrimary: color('CTA Custom Color (Primary)'),
  secondaryCustomCTAPrimary: color('CTA Custom Color (Secondary)'),
  customCTAccent: color('Primary CTA Custom Color (Accent)'),
  secondaryCustomCTAccent: color('Secondary CTA Custom Color (Accent)'),
  bannerLink: text('Banner Link Text'),
  showIcon: checkbox('Show Icon'),
  iconHorizontalAlignment: select('Icon Horizontal Control', ['left', 'center', 'right']),
  carouselSettings: object('carouselSettings'),
  image: object('Image / Icon'),
  iconPlacement: select('Image/Icon Placement', ['above', 'below']),
  ctaHorizontalAlignment: select('CTA Horizontal Control', ['left', 'center', 'right']),
  detailsLink: text('Details Link Text'),
  detailsPrefix: text('Details Prefix'),
  detailsLinkFontColor: color('Details Link Custom Color'),
  ism: object('ISM Amplience Data'),
  ismSize: select('Single or Double', ['single', 'double']),
  preserveSpace: checkbox('Hide icons on mobile'),
  vimeoVideo: object('ISM Background Video'),
  desktopImageOrIconSize: select('Desktop Image/Icon Size', iconSizeArray),
  mobileImageOrIconSize: select('Mobile Image/Icon Size', iconSizeArray),
  verticalTextAlignment: select('Vertical Text Alignment', verticalAlignmentArray),
  transition: select('Transitions', ['slide', 'fade', 'pan']),
  type: select('Type', ['autoplay', 'clickThrough']),
  continuousLoop: checkbox('Continuous Loop'),
  autoplayDelay: text('Delay'),
  autoplayPauseOnHover: checkbox('Pause On Hover'),
  animationSpeed: text('Speed'),
  animationEase: checkbox('Ease'),
  stylingControlsIconsColor: select('Controls Icons Color', ['primary', 'secondary']),
  stylingPagination: select('Pagination', ['hide', 'desktopAndMobile', 'desktop', 'mobile']),
  stylingHideChevrons: checkbox('Hide Chevrons'),
  rowPositionDesktop: text('Desktop Row Position'),
  rowPositionMobile: text('Mobile Row Position'),
  instanceName: text('Instance Name'),
  productGridSize: number('Product Grid Size', { min: 3, max: 99, step: 1 }),
});

export function usePropsFromControls(args: ISMCarouselControlArgTypes, schema: ISMCarouselSchema): ISMCarouselDoublePartialContentType {
  const props: ExtendedISMCarouselContentType = useMemo(() => {
    const {
      bannerLink,
      contentConfiguration,
      cta1Label,
      bodyCopy,
      cta2Label,
      ism,
      detailsLink,
      detailsPrefix,
      transition,
      type,
      instanceName,
      frames,
      continuousLoop,
      autoplayDelay,
      autoplayPauseOnHover,
      animationSpeed,
      animationEase,
      stylingControlsIconsColor,
      stylingHideChevrons,
      rowPositionDesktop,
      rowPositionMobile,
      detailsLinkFontColor,
      cta1ButtonVariant,
      cta2ButtonVariant,
      productGridSize,
      showHideBasedOnScreenSize,
    } = args;

    if (ism) {
      return ism;
    }
    const base: ExtendedISMCarouselContentType = {
      _meta: {
        name: 'ISM Banner',
        schema,
        deliveryId: '',
      },
      cta1: {
        label: 'Shop now 1',
        value: 'testing cta',
      },
      bodyCopy: '<p class"amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1">This is RTE Body Copy</span></p>',
      webAppearance: {
        detailsLinkFontColor: '#003764',
        showHideBasedOnScreenSize: 'alwaysShow',
      },
      carouselSettings: {
        transition: 'slide',
        type: 'clickThrough',
        continuousLoop: false,
        autoplay: {
          delay: 3000,
          pauseOnHover: false,
        },
        animation: {
          speed: 500,
          ease: false,
        },
        styling: {
          controlsIconsColor: 'primary',
          pagination: 'hide',
          hideChevrons: false,
        },
      },
      frames,
    };

    if (bodyCopy) {
      base.bodyCopy = bodyCopy;
    }

    if (bannerLink) {
      base.bannerLink = {
        label: bannerLink?.label,
        value: '#banner-link',
      };
    }

    if (detailsLink) {
      base.detailsLink = detailsLink;
    }

    if (detailsPrefix) {
      base.detailsPrefix = detailsPrefix;
    }

    if (detailsLinkFontColor) {
      base.webAppearance = {
        ...base?.webAppearance,
        detailsLinkFontColor,
        showHideBasedOnScreenSize: 'alwaysShow',
      };
    }

    if (base._meta.schema.includes('partial')) {
      if (!cta1Label) {
        base.cta1 = undefined;
      } else {
        base.cta1 = {
          label: cta1Label,
          value: '#primary-button',
        };
      }
      if (!cta2Label) {
        base.cta2 = undefined;
      } else {
        base.cta2 = {
          label: cta2Label,
          value: '#primary-button',
        };
      }
    }

    if (base._meta.schema.includes('partial')) {
      if (cta1ButtonVariant) {
        base.webAppearance = {
          ...base?.webAppearance,
          showHideBasedOnScreenSize: 'alwaysShow',

          ctaButton1Styling: {
            buttonStyle: cta1ButtonVariant as CTAVariant,
          },
        };
      }
      if (cta2ButtonVariant) {
        base.webAppearance = {
          ...base?.webAppearance,
          ctaButton2Styling: {
            buttonStyle: cta2ButtonVariant as CTAVariant,
          },
          showHideBasedOnScreenSize: 'alwaysShow',
        };
      }
    }

    if (showHideBasedOnScreenSize) {
      base.webAppearance!.showHideBasedOnScreenSize = showHideBasedOnScreenSize;
    }

    if (contentConfiguration) {
      base.contentConfiguration = contentConfiguration;
    }

    if (transition) {
      base.carouselSettings.transition = transition;
    }

    if (type) {
      base.carouselSettings.type = type;
    }

    base.carouselSettings.continuousLoop = Boolean(continuousLoop);

    if (!isNaN(Number(autoplayDelay))) {
      base.carouselSettings.autoplay.delay = Number(autoplayDelay);
    } else {
      base.carouselSettings.autoplay.delay = undefined;
    }

    if (autoplayPauseOnHover) {
      base.carouselSettings.autoplay.pauseOnHover = autoplayPauseOnHover;
    }
    if (!isNaN(Number(animationSpeed))) {
      base.carouselSettings.animation.speed = Number(animationSpeed);
    } else {
      base.carouselSettings.animation.speed = undefined;
    }

    base.carouselSettings.animation.ease = Boolean(animationEase);

    if (stylingControlsIconsColor) {
      base.carouselSettings.styling.controlsIconsColor = stylingControlsIconsColor;
    }

    base.carouselSettings.styling.hideChevrons = Boolean(stylingHideChevrons);

    if (rowPositionDesktop && rowPositionMobile) {
      base.rowPosition = {
        desktop: rowPositionDesktop,
        mobile: rowPositionMobile,
      };
    }

    if (instanceName) {
      base.instanceName = instanceName;
    }

    if (productGridSize) {
      base.productGridSize = productGridSize;
    }

    return base;
  }, [args, schema]);

  return props;
}
