import { PluginAPI } from 'tailwindcss/types/config';
// import { MEDIA_QUERY_LG, SCALING_TEXT, makeFontSize, makeLetterSpacing, makeLineHeight, makeScalingFontSize, makeScalingLetterSpacing } from '../utils';
import { CSSRuleObject } from '../typography-plugin';

const BODY_1 = '.amp-cms--body-1';
const BODY_2 = '.amp-cms--body-2';
const BODY_3 = '.amp-cms--body-3';
const BODY_4 = '.amp-cms--body-4';
const BODY_5 = '.amp-cms--body-5';
const ALL_BODY = [BODY_1, BODY_2, BODY_3, BODY_4, BODY_5].join(', ');

export const bodyTypographyStyles = (theme: PluginAPI['theme']): CSSRuleObject => ({
  [ALL_BODY]: {
    fontFamily: theme('fontFamily.brand'),
    // fontWeight: theme('fontWeight.medium'),
    // color: theme('colors.bk'),
  },
  // [BODY_1]: {
  //   fontSize: makeFontSize(16),
  //   lineHeight: makeLineHeight(26 / 16),
  //   letterSpacing: makeLetterSpacing(0.8),
  // },
  // [BODY_2]: {
  //   fontSize: makeFontSize(14),
  //   lineHeight: makeLineHeight(20 / 14),
  //   letterSpacing: makeLetterSpacing(0),
  // },
  // [BODY_3]: {
  //   fontSize: makeFontSize(12),
  //   lineHeight: makeLineHeight(18 / 12),
  //   letterSpacing: makeLetterSpacing(0.6),
  // },
  // [BODY_4]: {
  //   fontSize: makeFontSize(12),
  //   lineHeight: makeLineHeight(16 / 10),
  //   letterSpacing: makeLetterSpacing(0),
  // },
  // [BODY_5]: {
  //   fontSize: makeFontSize(10),
  //   lineHeight: makeLineHeight(15 / 10),
  //   letterSpacing: makeLetterSpacing(0.5),
  //   color: theme('colors.wh'),
  // },
  // [SCALING_TEXT]: {
  //   [BODY_1]: {
  //     fontSize: makeScalingFontSize(16, 10),
  //     letterSpacing: makeScalingLetterSpacing(0.8),
  //   },
  //   [BODY_2]: {
  //     fontSize: makeScalingFontSize(14, 10),
  //     letterSpacing: makeScalingLetterSpacing(0),
  //   },
  //   [BODY_3]: {
  //     fontSize: makeScalingFontSize(12, 10),
  //     letterSpacing: makeScalingLetterSpacing(0.6),
  //   },
  //   [BODY_4]: {
  //     fontSize: makeScalingFontSize(10, 10),
  //     letterSpacing: makeScalingLetterSpacing(0),
  //   },
  //   [BODY_5]: {
  //     fontSize: makeScalingFontSize(10, 10),
  //     letterSpacing: makeScalingLetterSpacing(0.5),
  //   },
  // },
  // [MEDIA_QUERY_LG]: {
  //   [BODY_1]: {
  //     fontSize: makeFontSize(18),
  //     lineHeight: makeLineHeight(28 / 18),
  //     letterSpacing: makeLetterSpacing(0.9),
  //   },
  //   [BODY_2]: {
  //     fontSize: makeFontSize(16),
  //     lineHeight: makeLineHeight(20 / 16),
  //     letterSpacing: makeLetterSpacing(0),
  //   },
  //   [BODY_3]: {
  //     fontSize: makeFontSize(14),
  //     lineHeight: makeLineHeight(22 / 14),
  //     letterSpacing: makeLetterSpacing(0.7),
  //   },
  //   [BODY_4]: {
  //     fontSize: makeFontSize(12),
  //     lineHeight: makeLineHeight(18 / 12),
  //     letterSpacing: makeLetterSpacing(0),
  //   },
  //   [BODY_5]: {
  //     fontSize: makeFontSize(10),
  //     lineHeight: makeLineHeight(15 / 10),
  //     letterSpacing: makeLetterSpacing(0.5),
  //   },
  //   [SCALING_TEXT]: {
  //     [BODY_1]: {
  //       fontSize: makeScalingFontSize(18, 10),
  //       letterSpacing: makeScalingLetterSpacing(0.9),
  //     },
  //     [BODY_2]: {
  //       fontSize: makeScalingFontSize(16, 10),
  //       letterSpacing: makeScalingLetterSpacing(0),
  //     },
  //     [BODY_3]: {
  //       fontSize: makeScalingFontSize(14, 10),
  //       letterSpacing: makeScalingLetterSpacing(0.7),
  //     },
  //     [BODY_4]: {
  //       fontSize: makeScalingFontSize(12, 10),
  //       letterSpacing: makeScalingLetterSpacing(0),
  //     },
  //     [BODY_5]: {
  //       fontSize: makeScalingFontSize(10, 10),
  //       letterSpacing: makeScalingLetterSpacing(0.5),
  //     },
  //   },
  // },
});
