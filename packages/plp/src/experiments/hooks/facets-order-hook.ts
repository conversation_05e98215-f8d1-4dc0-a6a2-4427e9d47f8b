import { usePLPState } from '@ecom-next/plp';
import { Brands } from '@ecom-next/core/legacy/utility';
import { ExperimentStatus, useExperiment } from '../features';
import { EXP_FACET_ORDER, FF_FACET_ORDER } from '../glossary';
import { manualBrandsConfig } from '../collaborators/brand-manual-facet-order';
import { FacetsOrderConfig } from './types';

const getFacetConfig = (brand: Brands) => {
  return manualBrandsConfig?.[brand]?.manualFacets;
};

export const useFacetsOrder = (): FacetsOrderConfig => {
  const { isExperimentActive, experimentStatus } = useExperiment(FF_FACET_ORDER, EXP_FACET_ORDER);
  const { brand } = usePLPState();
  const isDynamicFacetsEnabled = experimentStatus === ExperimentStatus.Active;
  const isManualFacetsEnabled = experimentStatus === ExperimentStatus.Alternative;
  const manualFacetConfig = isManualFacetsEnabled ? getFacetConfig(brand) : undefined;

  return {
    isFacetsOrderingEnabled: isExperimentActive,
    isDynamicFacetsEnabled,
    isManualFacetsEnabled,
    manualFacetConfig,
  };
};
