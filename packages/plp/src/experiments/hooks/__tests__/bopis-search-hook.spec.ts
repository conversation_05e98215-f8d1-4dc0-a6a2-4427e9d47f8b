import { renderHook } from 'test-utils';
import { usePLPState } from '../../../data';
import { useBopisSearch } from '../bopis-search-hook';

jest.mock('../../../data');
jest.mock('@ecom-next/sitewide/hooks/usePageContext');

describe('useBopisSearch', () => {
  it('should return true if correct values are informed', () => {
    (usePLPState as jest.Mock).mockReturnValue({ brand: 'gap', pageType: 'search', abSeg: { gap235: 'a' }, enabledFeatures: { 'plp-bopis': true } });
    const { result } = renderHook(() => useBopisSearch());
    expect(result.current).toBe(true);
  });
});
