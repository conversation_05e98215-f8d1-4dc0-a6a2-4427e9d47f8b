import { CSSObject } from '@ecom-next/core/components/react-stitch/src';

export enum Direction {
  Ascending = 'asc',
  Descending = 'desc',
  Unset = 'unset',
}

export enum Field {
  BestSellers = 'bestsellers',
  Featured = 'featured',
  New = 'new',
  Price = 'price',
  ReviewScore = 'reviewScore',
}

export interface SortByValue {
  css?: CSSObject;
  direction: Direction;
  field: Field;
  isActive?: boolean;
  label?: string;
  selectWidth?: number;
  value: string;
}

export type SortingByValue = {
  sortByDir: string | undefined;
  sortByField: string | undefined;
};

export interface SortByValue {
  css?: CSSObject;
  direction: Direction;
  field: Field;
  isActive?: boolean;
  label?: string;
  selectWidth?: number;
  value: string;
}

export type SortByChange = (option: SortByValue) => void;
