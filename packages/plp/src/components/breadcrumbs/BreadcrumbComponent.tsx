'use client';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useIsBreadcrumbRewrite } from '@ecom-next/plp';
import { ProductBreadcrumbs } from './ProductBreadcrumbs';

interface BreadcrumbProps {
  breadcrumbs: BreadCrumbs;
  isBottom?: boolean;
}

export const BreadcrumbComponent = (props: BreadcrumbProps): JSX.Element => {
  const { breadcrumbs, isBottom = false } = props;
  const { brand } = usePageContext();
  const isBreadcrumbRewrite = useIsBreadcrumbRewrite();
  const showOnlyBottombreadcrumbsForBR = brand === 'br' || brand === 'brfs';
  const shouldShowTopBreadcrumbs = isBreadcrumbRewrite && !isBottom;
  const shouldShowBottomBreadcrumbs = !isBreadcrumbRewrite && (!showOnlyBottombreadcrumbsForBR || isBottom);
  const shouldShowBreadcrumbs = shouldShowTopBreadcrumbs || shouldShowBottomBreadcrumbs;

  return <>{shouldShowBreadcrumbs && <ProductBreadcrumbs breadcrumbs={breadcrumbs} />}</>;
};
