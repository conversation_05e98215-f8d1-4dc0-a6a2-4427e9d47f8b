import { 
    transitionTimeout, 
    trimSection, 
    getSectionLength, 
    getShowToggleButton, 
    getSections, 
    getToggleButtonText, 
    getTransitionCSS, 
    getCollapsedFirstSection, 
    getCollapsedSecondSection 
  } from "../Collaborators";
  import { COLLAPSED_TOGGLE_BUTTON, EXPANDED_TOGGLE_BUTTON } from "../Localization-tokens";
  
  const mockTransitions = {
    expressive: { in: { transitionDuration: "0.3s", transitionTimingFunction: "ease-in" } },
    performance: { out: { transitionDuration: "0.2s", transitionTimingFunction: "ease-out" } },
    userTriggeredDelay: "0.1s",
  };
  
  const mockLocalize = (key: string) => key; // Mock localize function
  
  const mockSection = (childrenCount: number) => ({
    header: "Mock Header",
    children: Array.from({ length: childrenCount }, (_, i) => ({ name: `Item ${i + 1}` })),
  });
  
  describe("CategoryNav Utilities", () => {
    test("calculates transition timeout correctly", () => {
        expect(transitionTimeout.enter).toBe(350);      
    });
  
    test("trims section correctly", () => {
      const section = mockSection(5);
      const trimmed = trimSection(section, 3);
      expect(trimmed.children.length).toBe(3);
    });
  
    test("returns section length", () => {
      expect(getSectionLength(mockSection(4))).toBe(4);
      expect(getSectionLength(mockSection(0))).toBe(0);
    });
  
    test("determines if toggle button should be shown", () => {
      expect(getShowToggleButton([mockSection(2), mockSection(3)], 2, 3, 5)).toBe(false);
      expect(getShowToggleButton([mockSection(2), mockSection(4)], 2, 4, 5)).toBe(true);
    });
  
    test("returns correct section based on expansion state", () => {
      const allSections = [mockSection(2), mockSection(3)];
      const collapsedSections = [mockSection(2)];
      expect(getSections(true, allSections, collapsedSections)).toBe(allSections);
      expect(getSections(false, allSections, collapsedSections)).toBe(collapsedSections);
    });
  
    test("returns correct toggle button text", () => {
      expect(getToggleButtonText(true, mockLocalize)).toBe(EXPANDED_TOGGLE_BUTTON);
      expect(getToggleButtonText(false, mockLocalize)).toBe(COLLAPSED_TOGGLE_BUTTON);
    });
  
    test("returns correct transition CSS", () => {
      expect(getTransitionCSS(true, mockTransitions)).toBe("0.3s ease-in 0.1s");
      expect(getTransitionCSS(false, mockTransitions)).toBe("0.2s ease-out 0.1s");
    });
  
    test("returns correct collapsed first section", () => {
      const firstSection = mockSection(5);
      expect(getCollapsedFirstSection(5, 5, firstSection).children.length).toBe(5); 
      expect(getCollapsedFirstSection(4, 4, firstSection).children.length).toBe(4);
    });
  
    test("returns correct collapsed second section", () => {
      const firstSection = mockSection(3);
      const secondSection = mockSection(4);
      expect(getCollapsedSecondSection(3, 6, 4, secondSection).children.length).toBe(3);
      expect(getCollapsedSecondSection(6, 6, 4, secondSection)).toEqual({});
    });
  });
  