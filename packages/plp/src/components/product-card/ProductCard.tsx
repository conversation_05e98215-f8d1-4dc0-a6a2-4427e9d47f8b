import { useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { ColorSwatches } from './color-swatches';
import { ProductImage } from './product-image';
import { Name } from './product-details/Name';
import { Price } from './product-details/Price';
import { StarRatings } from './star-ratings';
import { MarketingFlag } from './product-details/MarketingFlags';
import { Clickable, IsolatedClickable } from './product-details/Clickable';

type ProductCardProps = { gridItemId: string; lazy?: boolean; productId: string };

export function ProductCard({ productId: initProductId, gridItemId, lazy }: ProductCardProps) {
  const [productId, setProductId] = useState(initProductId);
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin: '250px 0px',
  });
  const shouldRender = !lazy || inView;

  return (
    <div ref={ref} className='plp_product-card' data-testid='plp_product-card' id={`product${initProductId}`}>
      {!shouldRender ? null : (
        <>
          <ProductImage productId={productId} />
          <IsolatedClickable productId={productId}>
            <ColorSwatches gridItemId={gridItemId} onChange={setProductId} productId={initProductId} />
          </IsolatedClickable>
          <Clickable data-testid='plp_product-info' className='plp_product-info' productId={productId}>
            <Name productId={productId} />
            <Price productId={productId} />
            <MarketingFlag productId={productId} />
          </Clickable>
          <IsolatedClickable productId={productId}>
            <StarRatings productId={initProductId} />
          </IsolatedClickable>
        </>
      )}
    </div>
  );
}
