import React from 'react';
import { ComposableButton } from '@ecom-next/marketing-ui/legacy/composable-button';
import { Variant } from '@ecom-next/core/legacy/fixed-button';
import { Link } from '@ecom-next/core/migration/link';
import { NewArrivalsOptionsProps } from '../../types';

const NewArrivalsOptionsDesktop = ({ categories, title, id, isBRRedesignEnabled, isDesktop = true }: NewArrivalsOptionsProps) => {
  const listMarginRight = isBRRedesignEnabled ? (isDesktop ? 'mr-[40px]' : 'mr-[8px]') : 'mr-[8px]';
  const listMarginTop = isBRRedesignEnabled ? (isDesktop ? 'mt-[8px]' : 'mt-[16px]') : 'mt-[8px]';
  const newArrivalsTitle = !isBRRedesignEnabled && (
    <h2 id={id} className='plp_search__new-arrivals-section-title'>
      {title}
    </h2>
  );

  return (
    <>
      {newArrivalsTitle}
      <ul className='plp_search__new-arrivals-list-ul'>
        {categories.map(category => (
          <li key={category.name} className={`${listMarginTop} ${listMarginRight}  plp_search__new-arrivals-list-li`}>
            <ComposableButton
              as={Link}
              className='plp_search__new-arrivals-list-link'
              href={`${category.link}&nullSearch=null_search_new_arrivals`}
              interactiveStyles
              variant={Variant.outline}
            >
              {category.name}
            </ComposableButton>
          </li>
        ))}
      </ul>
    </>
  );
};

export default NewArrivalsOptionsDesktop;
