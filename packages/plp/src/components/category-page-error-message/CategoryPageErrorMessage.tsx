import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePsDataContext } from '@ecom-next/plp';

const ERROR_MESSAGE = 'product_grid_region.error_description';

export const CategoryPageErrorMessage = () => {
  const { localize } = useLocalize();
  const { state } = usePsDataContext();
  const { isLoading, isSuccess } = state;
  const shouldShowErrorMessage = !isLoading && !isSuccess;

  return shouldShowErrorMessage ? <div className='plp_product-list--error-message-container flex w-full flex-col'>{localize(ERROR_MESSAGE)}</div> : null;
};
