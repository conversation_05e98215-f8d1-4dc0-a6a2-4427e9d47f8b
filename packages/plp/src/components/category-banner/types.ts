import { BreakpointProviderState } from '@ecom-next/core/breakpoint-provider';
import { MarketingContent } from '@mui/fetchMarketing';
import { SelectedNodes } from '../../../../category/src/components/legacy/components/category-page/types';

export interface MarketingContextData {
  PageMarketing: React.ComponentType<{
    cid?: string;
    defaultContent: JSX.Element;
    did?: string;
    marketingType: string;
    position: string;
  }>;
  contentData?: { [key: string]: unknown };
  pageSpecificMarketingData?: MarketingContent;
}

export type CategoryBannerProps = {
  breakpointData: BreakpointProviderState;
  categoryName: string | undefined;
  marketingData: MarketingContextData;
  mktingType?: string;
  position: string;
  selectedNodes?: SelectedNodes;
  subcategoryName?: string | null;
};

export interface CategoryBannerContainerProps {
  categoryName?: string;
  selectedNodes?: SelectedNodes | undefined;
  subcategoryName?: string;
}
