import { SelectorDropdown } from '@ecom-next/core/fabric/selector-dropdown';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useModelSizeToggle } from '@ecom-next/plp';
import { useEffect, useRef, useState } from 'react';
import { useBreakpoint } from '../../hooks';
import { ModelSizeOption } from '../../../../experiments/hooks/types';

const MODEL_SIZE_TEXT = 'model_toggle.model_size_text';

export const ModelSize = () => {
  const { localize } = useLocalize();
  const { isMobile } = useBreakpoint();
  const { isExperimentActive, onModelSizeChange, modelSizeOptions } = useModelSizeToggle();
  const [selected, setSelected] = useState<ModelSizeOption | null>();
  const targetRef = useRef<HTMLDivElement | null>(null);
  const label = localize(MODEL_SIZE_TEXT);

  useEffect(() => {
    // Only scroll to the end if selected option is not the default one
    if (selected && selected.label !== label) {
      scrollToTheEnd();
    }
  }, [selected]);

  const onChange = (option: ModelSizeOption) => {
    setSelected(option);
    onModelSizeChange(option);
  };

  const shouldDisplayModelSize = isExperimentActive && modelSizeOptions && modelSizeOptions.length > 0;
  if (!shouldDisplayModelSize) return null;

  const modalDefaultOption = {
    label: label,
    value: label,
    disabled: true,
    selected: true,
  };

  const scrollToTheEnd = () => {
    targetRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest',
    });
  };

  // We need to add Model Size label to the selected option in mobile view
  // as there is no placeholder in native select
  const getMobileLabel = (option: ModelSizeOption) => (selected?.value === option.value ? `${label} ${option.label}` : option.label);

  const selectedValueFirst = (a: ModelSizeOption, b: ModelSizeOption) => (b.selected === a.selected ? 0 : b.selected ? 1 : -1);

  const modelSizeOptionsWithExtraValues = (option: ModelSizeOption) => ({
    label: getMobileLabel(option),
    value: option.value,
    // Extra values for the selector
    selected: selected?.value === option.value,
    disabled: selected?.value === option.value,
  });

  const getOptions = () => {
    if (isMobile) {
      if (selected) {
        return modelSizeOptions.map(modelSizeOptionsWithExtraValues).sort(selectedValueFirst);
      }
      return [modalDefaultOption, ...modelSizeOptions];
    }
    return modelSizeOptions;
  };

  return (
    <div className='plp-grid-header__model-size' ref={targetRef}>
      <SelectorDropdown controlId='plp_grid-header__model-size' label={label} keepLabel={true} options={getOptions()} onChange={onChange} />
    </div>
  );
};
