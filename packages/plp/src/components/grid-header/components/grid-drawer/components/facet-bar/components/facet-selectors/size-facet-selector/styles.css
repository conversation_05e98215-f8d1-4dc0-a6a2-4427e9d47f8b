.plp_grid-drawer__size-selector {
  padding-top: theme('padding.padding-stack-large');

  .fds_tabs__list-container .fds_tabs__tab-button {
    color: theme('colors.component-tab-font-color');
    text-align: center;
    font-family: theme('fontFamily.font-family-base');
    font-size: theme('fontSize.font-size--1');
    font-style: normal;
    font-weight: theme('fontWeight.component-selector-font-weight');
    line-height: theme('lineHeight.font-size-b-s-line-height');
  }

  .fds_tabs__tab-button--active {
    border-bottom: theme('borderWidth.global-border-width-1') solid theme('colors.component-tab-selected-border-color');
    color: theme('colors.component-selector-font-color');
  }
}

.at .plp_grid-drawer__size-selector {
  .fds_tabs__list-container .fds_tabs__tab-button {
    font-size: theme('fontSize.font-size--1');
  }
}

.plp_grid-drawer__size-selector-tab {
  padding: theme('padding.padding-stack-large') 0;
}

.plp_grid-drawer__size-selector-options {
  display: flex;
  flex-direction: column;
  gap: theme('padding.padding-stack-large');
  align-items: flex-start;
}

.plp_grid-drawer__size-selector-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.plp_grid-drawer__size-selector-option-title {
  color: theme('colors.color-font-default');
  font-size: theme('fontSize.font-size-0');
  font-weight: theme('fontWeight.font-weight-bold-font-weight');
  padding-bottom: theme('padding.global-spacing-100');
  line-height: theme('lineHeight.font-size-b-l-line-height');
  letter-spacing: theme('letterSpacing.font-tracking-b-regular-letter-spacing');
}

.plp_grid-drawer__size-selector-option-values {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: theme('spacing.global-spacing-150');
  margin-left: 0.2em;
}
