import { Switch } from '@ecom-next/core/fabric/switch';
import { CloseIcon } from '@ecom-next/core/fabric/icons';
import type { FilterDrawerPanelProps } from './types';

const FilterDrawerPanel = (props: FilterDrawerPanelProps) => {
  const { panelName, subTitle, title, withSwitch, switchCallback, isSwitchChecked } = props;

  const switchId = panelName ? `${panelName}-panel-switch` : 'panel-switch';

  return (
    <div className='plp_filter-drawer-panel__container'>
      <div className='plp_filter-drawer-panel__info'>
        <div className='plp_filter-drawer-panel__title'>{title}</div>
        {subTitle ? <div className='plp_filter-drawer-panel__subtitle'>{subTitle}</div> : null}
      </div>
      <div className='plp_filter-drawer-panel__switch'>
        {withSwitch ? <Switch id={switchId} onChange={switchCallback} checked={isSwitchChecked} /> : <CloseIcon height={24} width={24} />}
      </div>
    </div>
  );
};

export { FilterDrawerPanel };
