@import '../components/grid-drawer/styles/GridDrawer.on.css';
@import '../filter-button/styles/FilterButton.on.css';
@import '../item-count/styles/ItemCount.on.css';

.plp_grid-header-filter-icon {
  transform: rotate(90deg);
  display: inline-block;
}

/* 
Override for 
./components/facet-bar/components/facet-selectors/bopis-selector/styles.css
*/

.plp_filter-drawer-header {
  overflow: hidden;
  color: theme('colors.color-type-copy');
  text-overflow: ellipsis;
  font-size: theme('fontSize.font-size-1');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-heavier');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
  text-transform: capitalize;
}

.plp_filter-drawer-panel__title {
  color: theme('colors.color-type-copy');
  font-size: theme('fontSize.font-size-0');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-heavier');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
  text-transform: capitalize;
}

.plp_filter-drawer-panel__subtitle {
  color: theme('colors.color-type-copy');
  font-size: theme('fontSize.font-size--1');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-default');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
}

#plp_filter-drawer-bopis__store {
  font-size: theme('fontSize.font-size--1');
}

.plp_grid-header__filters-button-text {
  line-height: normal;
}

.plp_grid-header__items-count {
  font-size: theme('fontSize.font-size--2', 10px);
  font-family: theme('fontFamily.font-family-base'), 'ON Sans Text', serif;
  font-weight: theme('fontWeight.font-weight-base-heavier', 400);
}

.plp_grid-drawer-show-more__text {
  text-transform: capitalize;
  font-size: theme('fontSize.font-size-0');
  color: theme('colors.color-gray-gray-4');
}

.plp_grid-drawer-results__cta {
  text-transform: capitalize;
}

.plp_grid-drawer .plp_grid-drawer__footer .plp_grid-drawer-clear-filters__cta {
  text-transform: capitalize;
}

.plp_grid_drawer_radio-option {
  color: theme('colors.color-type-copy');
  font-size: theme('fontSize.font-size-0');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-default');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
  text-transform: capitalize;
}

.fds_radio.plp_grid_drawer_radio-option .fds_radio__emphasis-label {
  color: theme('colors.color-type-copy');
  font-size: theme('fontSize.font-size-0');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-default');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
}
.plp_chips-and-clear-all .plp_clear-all-chips-btn.fds_link.cb {
  color: theme('colors.color-type-link', #003764);
  border-bottom: 1px solid theme('colors.color-type-link', #003764);
}
