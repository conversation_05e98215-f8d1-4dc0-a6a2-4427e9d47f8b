import { render } from '@testing-library/react';
import { Brands, Market, PLPStateProviderProps } from '@ecom-next/plp';
import { Locale } from '@ecom-next/sitewide/localization-provider';
import { PLPStateProvider } from '../PLPStateProvider';
import { PLPStateContext } from '../PLPStateContext';

describe('PLPStateProvider', () => {
  const defaultProps = {
    abSeg: { segment: 'segment' } as { segment: string },
    brand: Brands.Gap as Brands | undefined,
    locale: 'en-US' as Locale | undefined,
    market: 'us' as Market | undefined,
    cid: '12345' as string | undefined,
    isPreviewEnabled: false,
    pageType: 'category',
    enabledFeatures: {
      feature1: true,
      feature2: false,
    },
  };

  const renderWithContext = (props: PLPStateProviderProps, children: React.ReactNode = null) => {
    return render(
      <PLPStateProvider {...(props as PLPStateProviderProps)}>
        <PLPStateContext.Consumer>
          {value => (
            <div>
              {value && (
                <>
                  {value.abSeg && <span>{value.abSeg.segment}</span>}
                  <span>{value.brand}</span>
                  <span>{value.locale}</span>
                  <span>{value.market}</span>
                  <span>{value.pageType}</span>
                  {value.cid && <span>{value.cid}</span>}
                </>
              )}
              {children}
            </div>
          )}
        </PLPStateContext.Consumer>
      </PLPStateProvider>
    );
  };

  it('renders children correctly', () => {
    const children = <span>Child Component</span>;
    const props = { ...(defaultProps as unknown as PLPStateProviderProps) };
    const { getByText } = renderWithContext(props, children);

    expect(getByText('Child Component')).toBeInTheDocument();
  });

  it('provides the correct context values', () => {
    const props = { ...(defaultProps as unknown as PLPStateProviderProps) };
    const segment = 'segment';
    const brand = Brands.Gap;
    const locale = 'en-US';
    const market = 'us';
    const cid = '12345';
    const pageType = 'category';

    const { getByText } = renderWithContext(props);

    expect(getByText(segment)).toBeInTheDocument();
    expect(getByText(brand)).toBeInTheDocument();
    expect(getByText(locale)).toBeInTheDocument();
    expect(getByText(market)).toBeInTheDocument();
    expect(getByText(cid)).toBeInTheDocument();
    expect(getByText(pageType)).toBeInTheDocument();
  });

  it('renders correctly when the cid value is not provided', () => {
    const brand = Brands.Athleta;
    const cid = defaultProps.cid;
    const props = { ...defaultProps, brand, cid: undefined } as unknown as PLPStateProviderProps;

    const { getByText, queryByText } = renderWithContext(props);

    expect(getByText(brand)).toBeInTheDocument();
    expect(queryByText(cid!)).not.toBeInTheDocument();
  });

  it('renders correctly when pageType is set to search', () => {
    const brand = Brands.BananaRepublic;
    const pageType = 'search';
    const props = { ...defaultProps, pageType, brand, cid: undefined } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(brand)).toBeInTheDocument();
    expect(getByText(pageType)).toBeInTheDocument();
  });

  it('renders correctly when isPreviewEnabled is true', () => {
    const brand = Brands.OldNavy;
    const isPreviewEnabled = true;
    const props = { ...defaultProps, brand, isPreviewEnabled } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(brand)).toBeInTheDocument();
  });

  it('renders correctly when abSeg is not provided', () => {
    const brand = Brands.Gap;
    const props = { ...defaultProps, brand, abSeg: undefined } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(brand)).toBeInTheDocument();
  });

  it('renders correctly when the brand is not provided', () => {
    const brand = Brands.Gap;
    const props = { ...defaultProps, brand: undefined } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(brand)).toBeInTheDocument();
  });

  it('renders correctly when the locale is not provided', () => {
    const locale = 'en-US';
    const props = { ...defaultProps, locale: undefined } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(locale)).toBeInTheDocument();
  });

  it('renders correctly when the market is not provided', () => {
    const market = 'us';
    const props = { ...defaultProps, market: undefined } as unknown as PLPStateProviderProps;

    const { getByText } = renderWithContext(props);

    expect(getByText(market)).toBeInTheDocument();
  });
});
