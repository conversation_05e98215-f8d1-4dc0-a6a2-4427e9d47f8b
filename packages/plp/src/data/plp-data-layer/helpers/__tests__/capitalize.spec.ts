import { capitalize } from '../capitalize';

describe('Capitalize', () => {
  it('Should capitalize text', () => {
    expect(capitalize('test')).toBe('Test');
  });
  it('Should capitalize text with multiple words', () => {
    expect(capitalize('test with multiple words')).toBe('Test With Multiple Words');
  });
  it('Should lowercase all text before capitalization', () => {
    expect(capitalize('TEXT WITH ALL UPPERCASE')).toBe('Text With All Uppercase');
  });
});
