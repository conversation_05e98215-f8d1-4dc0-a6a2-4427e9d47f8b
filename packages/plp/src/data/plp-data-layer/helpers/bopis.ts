import {
  BopisDataType,
  ProductStore,
} from '../../../components/grid-header/components/grid-drawer/components/facet-bar/components/facet-selectors/bopis-selector/types';
import { ActiveFeatureTogglesProps } from '../types';

export const getSelectedStore = (bopisData: BopisDataType) => {
  return bopisData?.selectedStore ?? null;
};

export const getActiveFeatureToggles = (selectedStore: ProductStore | null) => {
  return selectedStore?.activeFeatureToggles ?? null;
};

export const isInStorePickup = (activeFeatureToggles: ActiveFeatureTogglesProps | null) => {
  return activeFeatureToggles?.inStorePickup ?? null;
};

export const isCurbsidePickup = (activeFeatureToggles: ActiveFeatureTogglesProps | null) => {
  return activeFeatureToggles?.curbsidePickup ?? null;
};
