import { useCallback } from 'react';
import { usePLPDataLayerContext } from '@ecom-next/plp';
import { AppliedFacets, AppliedFacetsOption } from '../../../types';
import { usePsDataContext } from '../context/PsDataContext';
import { addFacetToAppliedFacets, removeFacetFromAppliedFacets } from '../reducer/applied-facets-update';
import facetChangeHandler from './helpers/facet-change-handler';
import { facetOptionsAdapter } from './helpers/facet-option-adapter';
import { getAppliedFacetsCount } from './helpers/get-applied-facet-count';
import pushHashToURL from './helpers/hash-change-handler';
import { combineSortByAndFacets, formatSortBySelectionAsFacet } from './helpers/facet-engagement-adapter';
import { useBopis } from './use-bopis';
import { updateScrollPosition } from './helpers/update-scroll-position';

export const useFacets = () => {
  const { appliedFacetsQuery, state, setAppliedFacetsQuery, appliedSortOptions, setAppliedSortOptions, setPageIndex } = usePsDataContext();
  const { facetEngagement } = usePLPDataLayerContext();
  const { selectedStoreOnChange } = useBopis();
  const isSuccess = state?.isSuccess ?? false;
  const { facets, totalItemCount } = state?.data ?? { facets: {}, totalItemCount: 0 };

  const handleHashToUrl = (appliedFacets: AppliedFacets) => {
    const sortByOptionAsFacet = formatSortBySelectionAsFacet(appliedSortOptions?.sortByField ?? '', appliedSortOptions?.sortByDir ?? '');
    const facetsIncludingSortByOptions = combineSortByAndFacets(sortByOptionAsFacet, appliedFacets);
    pushHashToURL(facetsIncludingSortByOptions);
  };

  const applyFacetOption = useCallback((appliedFacet: AppliedFacetsOption) => {
    setAppliedFacetsQuery?.((currentAppliedFacets: AppliedFacets) => {
      if (!appliedFacet) {
        return currentAppliedFacets;
      }

      if (appliedFacet.name === 'department') {
        const updatedFacets = addFacetToAppliedFacets({}, appliedFacet);
        handleHashToUrl(updatedFacets);
        updateScrollPosition();
        return updatedFacets;
      }

      const updatedFacets = addFacetToAppliedFacets(currentAppliedFacets, appliedFacet);
      handleHashToUrl(updatedFacets);
      setPageIndex(0);
      updateScrollPosition();
      return updatedFacets;
    });
  }, []);

  const removeFacetOption = useCallback((appliedFacet: AppliedFacetsOption) => {
    setAppliedFacetsQuery?.((currentAppliedFacets: AppliedFacets) => {
      const updatedFacets = removeFacetFromAppliedFacets(currentAppliedFacets, appliedFacet);
      handleHashToUrl(updatedFacets);
      setPageIndex(0);
      updateScrollPosition();
      return updatedFacets;
    });
  }, []);

  const clearFacetOption = useCallback(() => {
    setAppliedFacetsQuery?.({});
    setAppliedSortOptions?.({});
    selectedStoreOnChange?.(null);
    setPageIndex(0);
    facetEngagement({});
    pushHashToURL({});
    updateScrollPosition();
  }, []);

  const listOfAppliedFacets = facetOptionsAdapter(appliedFacetsQuery ?? {});
  const appliedFacetsCount = getAppliedFacetsCount(appliedFacetsQuery ?? {});
  const onFacetChangeHandler = facetChangeHandler;

  return {
    appliedFacetsCount,
    appliedFacetsQuery,
    applyFacetOption,
    clearFacetOption,
    facets,
    isSuccess,
    listOfAppliedFacets,
    onFacetChangeHandler,
    removeFacetOption,
    totalItemCount,
  };
};
