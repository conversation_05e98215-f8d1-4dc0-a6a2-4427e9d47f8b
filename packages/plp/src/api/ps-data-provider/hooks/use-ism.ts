import { usePLPState } from '@ecom-next/plp';
import { useContent } from '@ecom-next/marketing-ui/legacy-marketing-provider';
import { MarketingContent } from '@mui/fetchMarketing';

interface MarketingContentData {
  contentData: MarketingContent;
  pageSpecificMarketingData: MarketingContent;
}

export const useIsm = (subcategoryId?: string) => {
  const { cid } = usePLPState();
  const { contentData } = useContent() as unknown as MarketingContentData;

  return Object.entries(contentData)
    .filter(([key, value]) => (key.includes(`${cid}/ism`) || key.includes(`${subcategoryId}/ism`)) && value.contentItems.length > 0)
    .flatMap(([_, value]) => value.contentItems);
};
