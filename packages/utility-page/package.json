{"name": "@ecom-next/utility-page", "version": "1.0.0", "description": "", "exports": {"./pages/*": "./src/pages/*.tsx", "./layouts/*": "./src/layouts/*.tsx", "./translations": "./src/translations/index.ts"}, "scripts": {"clean": "rm -rf dist", "format": "prettier . --write --ignore-unknown", "lint": "eslint ./src", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "dependencies": {"@ecom-next/core": "1.0.0", "@ecom-next/marketing-ui": "1.0.0", "@ecom-next/sitewide": "1.0.0", "dangerously-set-html-content": "^1.1.0"}, "devDependencies": {"@types/react-slick": "^0.23.10", "htmltojsx": "^0.3.0", "next": "14.2.25", "react": "18.3.1", "react-dom": "18.3.1", "react-slick": "^0.29.0", "slick-carousel": "^1.8.1", "tailwindcss": "^3.4.3"}, "author": "", "license": "ISC"}