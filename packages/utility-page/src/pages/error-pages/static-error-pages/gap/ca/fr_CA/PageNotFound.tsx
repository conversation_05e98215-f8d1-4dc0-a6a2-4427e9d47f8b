export default function StaticPageNotFound() {
  return (
    <div className='sds_sp_xl'>
      <div className='sds_display-a sds_pd sds_pd_bottom' style={{ textAlign: 'center' }}>
        Nous sommes désolés.
      </div>
      <div className='sds_heading-d sds_pd'>
        <div style={{ textAlign: 'center' }} className='sds_sp'>
          Une erreur est survenue qui a empêché cette page d&#39;être affichée. Cela pourrait être dû à l&#39;une des raisons suivantes:
        </div>
        <div style={{ display: 'flex', justifyContent: 'center' }} className='sds_sp'>
          - Une erreur interne sur Gap.com s&#39;est produite.
          <br />
          - Une URL a été mal saisie.
          <br />
          - Le fichier ñ&#39;existe plus.
          <br />
        </div>
        <div style={{ textAlign: 'center' }} className='sds_sp_lg'>
          Pour continuer vos achats, retournez maintenant à la page d&#39;accueil de{' '}
          <a href='/' style={{ textDecoration: 'underline' }}>
            gapcanada.ca
          </a>
          .<br />
          Si ce problème persiste, veuillez communiquer avec nous au <em>1.800.427.7895</em>.
        </div>
      </div>
    </div>
  );
}
