import { getPageContext } from '@ecom-next/utils/server';
import { type SeoContent } from '@mui/fetchMarketing';
import { ICAPIResponse } from './types';

export const getRobotsIndexData = (non_indexable: boolean | undefined) => {
  return non_indexable
    ? {
        robots: {
          index: false,
        },
      }
    : {};
};

const generateBaseMetaData = (content: SeoContent | undefined, cid: string, defaultPath: string, useCAPIsFF: boolean, capisMeta: ICAPIResponse) => {
  const { headersList, locale } = getPageContext();
  const host = headersList.get('request-host') || headersList.get('host');
  const canonicalPath = headersList.get('x-canonical-slug-path') || headersList.get('x-pathname') || defaultPath;
  const seo_data = capisMeta?.web_category?.seo_data;
  const hasSeoData = (seo_data && seo_data?.page_title) || (content && ('titleOverride' in content || 'pageTitle' in content));
  const title = hasSeoData && useCAPIsFF ? seo_data?.page_title : (content?.pageTitle || content?.titleOverride);
  const description = hasSeoData && useCAPIsFF ? seo_data?.meta_description : (content?.pageDescription || content?.metaDescription || '');

  return {
    ...(title && { title }),
    ...(description && { description }),
    metadataBase: new URL(`https://${host}`),
    alternates: {
      canonical: `${canonicalPath}?cid=${cid}${locale === 'fr_CA' ? '&locale=fr_CA' : ''}`,
    },
    ...(useCAPIsFF && getRobotsIndexData(seo_data?.non_indexable)),
  };
};

export default generateBaseMetaData;
