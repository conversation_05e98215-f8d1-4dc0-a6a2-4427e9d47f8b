import { serverFetch } from '@ecom-next/utils/serverFetch';
import { ICAPIResponse } from './types';

const targetEnv = process.env.TARGET_ENV || 'prod';

const BASE_URL = process.env.CATALOG_INTERNAL_API_BASE_URL || 'https://ws-catalog-api-service.stage.azeus.gaptech.com';

const HEADERS = {
  'X-Client-Application-Name': 'ecomnext',
};

const getRequestType = (requestType: string) => {
  return requestType === 'ecom' ? 'LIVE' : `PREVIEW_${requestType.toUpperCase()}`;
};

const formatEffectiveDate = (effectiveDate: string = '') => {
  const date = new Date(effectiveDate);
  date.setHours(date.getHours() - 5);
  return date.toISOString().replace('Z', '-05:00');
};

const getMarket = (market: string) => {
  return market === 'ca' ? 'CAN' : market.toUpperCase();
}

export const getCAPIsUrl = (
  cid: string,
  baseUrl: string = '',
  brand: string = '',
  market: string = '',
  locale: string = '',
  effectiveDate: string = '',
  requestType: string = ''
) => {
  const shouldAddEffectiveDate = targetEnv === 'preview' && effectiveDate;
  return `${baseUrl}/v2/categories/${cid}?channel=ONL&market=${getMarket(market)}&brand=${brand.toUpperCase()}&locale=${locale}${shouldAddEffectiveDate ? `&effective_date=${formatEffectiveDate(effectiveDate)}&request_type=${getRequestType(requestType)}` : ''}`;
};

export const fetchMetaDataFromCAPIs = async (
  cid: string,
  brand: string = '',
  market: string = '',
  locale: string = '',
  effectiveDate: string = '',
  requestType: string = ''
): Promise<ICAPIResponse> => {
  const url = getCAPIsUrl(cid, BASE_URL, brand, market, locale, effectiveDate, requestType);
  return await serverFetch(url, { headers: HEADERS });
};
