export const getPageType = (pathName: string) => {
  if (pathName.endsWith('/category.do')) {
    return 'category';
  } else if (pathName.endsWith('/product.do')) {
    return 'product';
  } else if (pathName.endsWith('/division.do')) {
    return 'division';
  } else if (pathName.endsWith('/shopping-bag')) {
    return 'shoppingbag';
  } else if (
    pathName.endsWith('/browse/OutOfStockNoResults.do') ||
    pathName.includes('/browse/GeneralNoResults.do') ||
    pathName.includes('/browse/InvalidIdNoResults.do') ||
    pathName.includes('/CookieFailure.do') ||
    pathName.includes('/PageNotFound.do') ||
    pathName.includes('/InternalServerError.do') ||
    pathName.includes('/BadRequest.do')
  ) {
    return 'otherpages';
  } else if (pathName.endsWith('/')) {
    return 'home';
  }
  return undefined;
};

export const getCid = (queryString: string): string | null => {
  const params = new URLSearchParams(queryString);
  return params.get('cid');
}

export const formatLocale = (locale: string = 'en_US') => {
  const [language, country] = locale.split('_');
  return `${language?.toLowerCase()}-${country?.toLowerCase()}`;
};

export const formatTargetEnv = (targetEnv: string = 'local') => targetEnv === 'preview' ? 'prev' : targetEnv;
