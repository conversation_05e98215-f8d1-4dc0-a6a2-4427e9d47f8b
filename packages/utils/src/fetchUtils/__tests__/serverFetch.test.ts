import logger from '@ecom-next/app/logger';
import { serverFetch, type ApiError } from '../serverFetch';

jest.mock('server-only', () => ({
  __esModule: true,
  default: true,
}));

jest.mock('next/headers', () => {
  const headers = new Map();
  return {
    headers: jest.fn().mockReturnValue(headers),
    cookies: jest.fn().mockReturnValue({
      get: jest.fn(key => {
        switch (key) {
          case 'unknownShopperId':
            return { name: 'unknownShopperId', value: 'ABCD|||' };
          case 'x-b3-parentspandd':
            return { name: 'x-b3-parentspandd', value: '' };
          case 'x-b3-spanid':
            return { name: 'x-b3-spanid', value: '' };
          case 'x-b3-traceid':
            return { name: 'x-b3-traceid', value: '' };
          default:
            return undefined;
        }
      }),
    }),
  };
});

const loggerSpy = jest.spyOn(logger, 'error');
const abortSpy = jest.spyOn(AbortSignal, 'timeout');
const fetchMock = jest.spyOn(global, 'fetch');

const MAX_RETRIES = 3 as const;
const FIVE_SECONDS = 5000 as const;
const TEN_SECONDS = 10000 as const;
const REQUEST_URL = 'https://example.com' as const;

const status200 = () =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: async () => {},
  } as Response);

const status500 = () =>
  Promise.reject({
    ok: false,
    status: 500,
  } as ApiError);

describe('serverFetch', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const errorLoggerArgs = [
    {
      req: {
        headers: {
          'content-type': 'application/json',
          unknownShopperId: 'ABCD',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        url: REQUEST_URL,
      },
      res: {
        status: 500,
        ok: false,
      },
      err: {
        status: 500,
        ok: false,
      },
    },
    `Error fetching url: ${REQUEST_URL} with retry count: 0 and status: 500`,
  ];
  it('should be called once when no retries are passed in and a 200 is returned', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL);
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it('should be called once when no retries are passed in and a 500 is returned', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL);
    } catch (e) {
      expect(loggerSpy).toHaveBeenLastCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 0,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(fetchMock).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
    }
  });

  it('should be called twice when 1 retry is passed in and a 500 is returned', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { retries: 1 });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 1,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(2);
      expect(fetchMock).toHaveBeenCalledTimes(2);
    }
  });

  it('should be called three times when 2 retries are passed in and a 500 is returned', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { retries: 2 });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 2,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(3);
      expect(fetchMock).toHaveBeenCalledTimes(3);
    }
  });

  it('should be called four times when 3 retries are passed in and a 500 is returned', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { retries: 3 });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 3,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(4);
      expect(fetchMock).toHaveBeenCalledTimes(4);
    }
  });

  it('should override the retries to MAX_RETRIES 3 and be called 4 times when 8 retries are passed in and a 500 is returned', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { retries: 8 });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: MAX_RETRIES,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(4);
      expect(fetchMock).toHaveBeenCalledTimes(4);
    }
  });

  it('should be able to override the default timeout', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { timeout: TEN_SECONDS });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: true,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 0,
        signal: AbortSignal.timeout(TEN_SECONDS),
        timeout: TEN_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(TEN_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledTimes(1);
    }
  });

  it('should be able to override the default keepalive', async () => {
    fetchMock.mockImplementationOnce(status500);
    try {
      await serverFetch(REQUEST_URL, { keepalive: false });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: false,
        method: 'GET',
        next: {
          revalidate: 0,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 0,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledTimes(1);
    }
  });

  it('should be able to override the default revalidate', async () => {
    fetchMock.mockImplementationOnce(status200);
    try {
      await serverFetch(REQUEST_URL, { next: { revalidate: 10 } });
    } catch (e) {
      expect(loggerSpy).toHaveBeenCalledWith(...errorLoggerArgs);
      expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
        keepalive: false,
        method: 'GET',
        next: {
          revalidate: 10,
        },
        headers: {
          'content-type': 'application/json',
          'X-B3-ParentSpanId': '',
          'X-B3-SpanId': '',
          'X-B3-TraceId': '',
        },
        retries: 0,
        signal: AbortSignal.timeout(FIVE_SECONDS),
        timeout: FIVE_SECONDS,
      });
      expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledTimes(1);
    }
  });

  it('should work with POST requests and not retry on a 200', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      retries: 2,
      method: 'POST',
      body: JSON.stringify({ test: true }),
    });
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      body: '{"test":true}',
      keepalive: true,
      method: 'POST',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 2,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it('should work with PUT requests', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      method: 'PUT',
      body: JSON.stringify({ test: true }),
    });
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      body: '{"test":true}',
      keepalive: true,
      method: 'PUT',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it('should work with PATCH requests', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      method: 'PATCH',
      body: JSON.stringify({ test: true }),
    });
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      body: '{"test":true}',
      keepalive: true,
      method: 'PATCH',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it('should work with DELETE requests', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      method: 'DELETE',
    });
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      keepalive: true,
      method: 'DELETE',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it('should override default headers with passed in headers of the same key', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      headers: {
        'content-type': 'text/plain',
      },
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      keepalive: true,
      method: 'GET',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'text/plain',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
  });

  it('should correctly merge headers from default headers and passed in headers', async () => {
    fetchMock.mockImplementationOnce(status200);
    await serverFetch(REQUEST_URL, {
      headers: {
        'x-test': 'test',
      },
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      keepalive: true,
      method: 'GET',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'x-test': 'test',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
  });

  it('should correctly merge headers when using a headers class', async () => {
    fetchMock.mockImplementationOnce(status200);
    const headers = new Headers();
    headers.append('x-test', 'test');
    await serverFetch(REQUEST_URL, {
      headers,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      keepalive: true,
      method: 'GET',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'application/json',
        'x-test': 'test',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
  });

  it('should override default headers with passed in headers of the same key with a headers class', async () => {
    fetchMock.mockImplementationOnce(status200);
    const headers = new Headers();
    headers.append('content-type', 'text/plain');
    await serverFetch(REQUEST_URL, {
      headers,
    });
    expect(abortSpy).toHaveBeenCalledWith(FIVE_SECONDS);
    expect(loggerSpy).toHaveBeenCalledTimes(0);
    expect(fetchMock).toHaveBeenCalledTimes(1);
    expect(fetchMock).toHaveBeenCalledWith(REQUEST_URL, {
      keepalive: true,
      method: 'GET',
      next: {
        revalidate: 0,
      },
      headers: {
        'content-type': 'text/plain',
        'X-B3-ParentSpanId': '',
        'X-B3-SpanId': '',
        'X-B3-TraceId': '',
      },
      retries: 0,
      signal: AbortSignal.timeout(FIVE_SECONDS),
      timeout: FIVE_SECONDS,
    });
  });
});
