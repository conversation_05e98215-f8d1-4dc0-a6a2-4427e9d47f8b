import { DEFAULT_CLIENT_HEADERS, DEFAULT_SERVER_HEADERS } from './constants';

export const mergeHeaders = (headers: HeadersInit): HeadersInit => {
  const mergedHeaders: HeadersInit = typeof window === 'undefined' ? { ...DEFAULT_SERVER_HEADERS } : { ...DEFAULT_CLIENT_HEADERS };
  if (headers instanceof Headers) {
    const arr = Array.from(headers.entries());
    arr.forEach(([key, value]) => {
      mergedHeaders[key] = value;
    });
  } else {
    Object.entries(headers).forEach(([key, value]) => {
      mergedHeaders[key] = value;
    });
  }
  return mergedHeaders;
};
