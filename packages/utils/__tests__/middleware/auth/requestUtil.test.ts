import { NextRequest } from 'next/server';
import { getHost } from '../../../src/middleware/auth/common';
import type { TokenInfoResponse } from '../../../src/middleware/auth/types';
import {
  isPrefetch,
  isAuthenticated,
  isAuthenticatedOrGuest,
  isBarclaysPostBack,
  isBarclaysPostBackAutoRedirect,
  isBarclaysPostIn,
  isCheckout,
  isForceSignup,
  isNavParamRedirect,
  isRedirectError,
  isSecureRedirect,
} from '../../../src/middleware/auth/requestUtil';
import {
  ANONYMOUS,
  AUTHENTICATED,
  BARCLAYS,
  BARCLAYS_CREDIT_OFFER,
  BARCLAYS_LOYALTY_STATUS,
  BARCLAYS_POST_BACK_TYPES,
  CHECKOUT_PATH,
  ERROR_CODE,
  FORCE_SIGNUP_PARAM,
  GUEST,
  REDIRECT_AUTHENTICATE_ERROR,
  REDIRECT_BARCLAYS_GUEST_ERROR,
  REDIRECT_BARCLAYS_SIGN_IN_ERROR,
  REDIRECT_CONTINUE_ERROR,
  REDIRECT_CREATE_ACCOUNT_ERROR,
  REDIRECT_EMAIL_VALIDATION_ERROR,
  REDIRECT_EXPIRED_SESSION_ERROR,
  REDIRECT_GUEST_CHECKOUT_ERROR,
  REDIRECT_REENTRY_ERROR,
  REDIRECT_SIGN_IN_ERROR,
  RETRY,
  TARGET_URL,
} from '../../../src/middleware/auth/constants';
import { getBrand, getMarket } from '../../../src/server';
import { ALLOWED_HOSTS_BY_ENV_MARKET_BRAND } from './mocks/allowedHosts';

jest.mock('server-only', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('next/server', () => ({
  NextRequest: jest.fn().mockImplementation(url => ({
    url,
    nextUrl: new URL(url),
  })),
}));

jest.mock('../../../src/middleware/auth/common', () => ({
  ...jest.requireActual('../../../src/middleware/auth/common'),
  getHost: jest.fn(),
}));

const HOME_PATH = '/my-account/home' as const;
const SESSION_TIMEOUT = 'sessionTimeout' as const;
const SIGN_IN_PATH = '/my-account/signin' as const;
const ORIGIN = 'https://secure-www.gap.com' as const;

const setTargetEnv = (value: string): void => {
  Object.defineProperty(process.env, 'TARGET_ENV', {
    value,
    writable: true,
    configurable: true,
  });
};

/**
 * @name requestUtil Test Suite
 * @description Tests for the requestUtil module. Clears mocks and resets modules before each test.
 */
describe('requestUtil', () => {
  let originalTargetEnv: string | undefined;
  beforeAll(() => (originalTargetEnv = process.env.TARGET_ENV));
  afterAll(() => {
    Object.defineProperty(process.env, 'TARGET_ENV', {
      value: originalTargetEnv,
      writable: true,
      configurable: true,
    });
  });
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  /**
   * @name isPrefetch Tests
   * @description Test suite for the isPrefetch function.
   */

  describe('isPrefetch()', () => {
    it('should return true when the request is a prefetch request', () => {
      const request = {
        headers: new Headers({
          'sec-fetch-mode': 'cors',
          'sec-fetch-dest': 'empty',
          'next-url': 'https://secure-www.gap.com/order-details/12345',
        }),
      } as NextRequest;
      expect(isPrefetch(request)).toBe(true);
    });

    it('should return false when the request is not a prefetch request', () => {
      let request = {
        headers: new Headers({
          'sec-fetch-mode': 'navigate',
          'sec-fetch-dest': 'document',
          'next-url': 'https://secure-www.gap.com/order-details/12345',
        }),
      } as NextRequest;
      expect(isPrefetch(request)).toBe(false);

      request = {
        headers: new Headers({
          'sec-fetch-mode': 'cors',
          'sec-fetch-dest': 'empty',
        }),
      } as NextRequest;
      expect(isPrefetch(request)).toBe(false);
    });
  });

  /**
   * @name isSecureRedirect Tests
   * @description Test suite for the isSecureRedirect function.
   */
  describe('isSecureRedirect()', () => {
    describe('stage', () => {
      beforeAll(() => {
        setTargetEnv('stage');
      });

      it('should return false when the host starts with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage[market]).forEach(([_brand, { noRedirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(noRedirect);
            expect(getBrand(noRedirect)).toBe(brand);
            expect(getMarket(noRedirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(false);
          });
        });
      });

      it('should return true when the host does not start with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage[market]).forEach(([_brand, { redirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.stage)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(redirect);
            expect(getBrand(redirect)).toBe(brand);
            expect(getMarket(redirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(true);
          });
        });
      });
    });

    describe('preview', () => {
      beforeAll(() => {
        setTargetEnv('preview');
      });

      it('should return false when the host starts with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app[market]).forEach(([_brand, { noRedirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(noRedirect);
            expect(getBrand(noRedirect)).toBe(brand);
            expect(getMarket(noRedirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(false);
          });
        });
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip[market]).forEach(([_brand, { noRedirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(noRedirect);
            expect(getBrand(noRedirect)).toBe(brand);
            expect(getMarket(noRedirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(false);
          });
        });
      });

      it('should return true when the host does not start with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app[market]).forEach(([_brand, { redirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.app)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(redirect);
            expect(getBrand(redirect)).toBe(brand);
            expect(getMarket(redirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(true);
          });
        });
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip[market]).forEach(([_brand, { redirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.preview.wip)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(redirect);
            expect(getBrand(redirect)).toBe(brand);
            expect(getMarket(redirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(true);
          });
        });
      });
    });

    describe('prod', () => {
      beforeAll(() => {
        setTargetEnv('prod');
      });

      it('should return false when the host starts with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production[market]).forEach(([_brand, { noRedirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(noRedirect);
            expect(getBrand(noRedirect)).toBe(brand);
            expect(getMarket(noRedirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(false);
          });
        });
      });

      it('should return true when the host does not start with secure-', () => {
        Object.keys(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production).forEach(_market => {
          const market = _market as keyof typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production;

          Object.entries(ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production[market]).forEach(([_brand, { redirect }]) => {
            const brand = _brand as keyof (typeof ALLOWED_HOSTS_BY_ENV_MARKET_BRAND.production)[typeof market];

            jest.mocked(getHost).mockReturnValueOnce(redirect);
            expect(getBrand(redirect)).toBe(brand);
            expect(getMarket(redirect)).toBe(market);
            expect(isSecureRedirect({})).toBe(true);
          });
        });
      });
    });
  });

  /**
   * @name isNavParamRedirect Tests
   * @description Test suite for the isNavParamRedirect function.
   */
  describe('isNavParamRedirect', () => {
    it('should return true when the nav param is present', () => {
      const request = {
        nextUrl: {
          searchParams: new URLSearchParams('nav=meganav:Today%25E2%2580%2599s+Deals!:40%25+off+Purchase;+50%25+off+Cardmembers!:Women'),
        },
      } as NextRequest;

      expect(isNavParamRedirect(request)).toBe(true);
    });

    it('should return false when the nav param is not present', () => {
      const request = {
        nextUrl: {
          searchParams: new URLSearchParams('targetURL=/checkout'),
        },
      } as NextRequest;

      expect(isNavParamRedirect(request)).toBe(false);
    });
  });
  /**
   * @name isRedirectError Tests
   * @description Test suite for the isRedirectError function.
   */
  describe('isRedirectError()', () => {
    const errorCodes = [
      REDIRECT_REENTRY_ERROR,
      REDIRECT_SIGN_IN_ERROR,
      REDIRECT_CONTINUE_ERROR,
      REDIRECT_AUTHENTICATE_ERROR,
      REDIRECT_CREATE_ACCOUNT_ERROR,
      REDIRECT_GUEST_CHECKOUT_ERROR,
      REDIRECT_EXPIRED_SESSION_ERROR,
      REDIRECT_EMAIL_VALIDATION_ERROR,
      REDIRECT_BARCLAYS_SIGN_IN_ERROR,
      REDIRECT_BARCLAYS_GUEST_ERROR,
    ] as const;

    for (const errorCode of errorCodes) {
      it(`should return true when the errorCode is ${errorCode} and the retry param is not 0`, () => {
        const request = new NextRequest(`${ORIGIN}?${ERROR_CODE}=${errorCode}`);
        expect(isRedirectError(request)).toBe(true);
      });

      it(`should return false when the errorCode is ${errorCode} and the retry param is 0`, () => {
        const request = new NextRequest(`${ORIGIN}${SIGN_IN_PATH}?${ERROR_CODE}=${errorCode}${RETRY}=0`);
        expect(isRedirectError(request)).toBe(false);
      });
    }

    it(`should return false when the errorCode is sessionTimeout and the retry param is 0`, () => {
      const request = new NextRequest(`${ORIGIN}${SIGN_IN_PATH}?${ERROR_CODE}=${SESSION_TIMEOUT}&${RETRY}=0`);
      expect(isRedirectError(request)).toBe(false);
    });

    it(`should return false when the errorCode is sessionTimeout and the retry param is not 0`, () => {
      const request = new NextRequest(`${ORIGIN}${SIGN_IN_PATH}?${ERROR_CODE}=${SESSION_TIMEOUT}`);
      expect(isRedirectError(request)).toBe(false);
    });
  });

  /**
   * @name isForceSignup Tests
   * @description Test suite for the isForceSignup function.
   */
  describe('isForceSignup()', () => {
    it('should return true if FORCE_SIGNUP_PARAM is true in params', () => {
      const queryParams = {
        [FORCE_SIGNUP_PARAM]: 'true',
      };
      expect(isForceSignup(queryParams)).toBe(true);
    });

    it('should return false if FORCE_SIGNUP_PARAM is false in queryParams', () => {
      const queryParams = {
        [FORCE_SIGNUP_PARAM]: 'false',
      };
      expect(isForceSignup(queryParams)).toBe(false);
    });
  });

  /**
   * @name isBarclaysPostIn Tests
   * @description Test suite for the isBarclaysPostIn function.
   */
  describe('isBarclaysPostIn', () => {
    it('should return true when barclaysCreditOffer is BARCLAYS', () => {
      const queryParams = { [BARCLAYS_CREDIT_OFFER]: BARCLAYS };
      expect(isBarclaysPostIn(queryParams)).toBe(true);
    });

    it('should return false when barclaysCreditOffer is not BARCLAYS', () => {
      const queryParams = { [BARCLAYS_CREDIT_OFFER]: 'NOT_BARCLAYS' };
      expect(isBarclaysPostIn(queryParams)).toBe(false);
    });
  });

  /**
   * @name isBarclaysPostBackAutoRedirect Tests
   * @description Test suite for the isBarclaysPostBackAutoRedirect function.
   */
  describe('isBarclaysPostBack()', () => {
    it('should return true if barclaysLoyaltyStatus is included in BARCLAYS_POST_BACK_TYPES', () => {
      let queryParams = { [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[0] };
      expect(isBarclaysPostBack(queryParams)).toBe(true);
      queryParams = { [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[1] };
      expect(isBarclaysPostBack(queryParams)).toBe(true);
      queryParams = { [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[2] };
      expect(isBarclaysPostBack(queryParams)).toBe(true);
    });

    it('should return false if barclaysLoyaltyStatus is not included in BARCLAYS_POST_BACK_TYPES', () => {
      const queryParams = { [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[4] };
      expect(isBarclaysPostBack(queryParams)).toBe(false);
    });

    it('should return false if barclaysLoyaltyStatus is not provided', () => {
      const queryParams = {};
      expect(isBarclaysPostBack(queryParams)).toBe(false);
    });
  });

  describe('isBarclaysPostBackAutoRedirect()', () => {
    it('should return false if user is not authenticated, targetURL includes CHECKOUT_PATH, and isBarclaysPostBack is true', () => {
      const queryParams = {
        [TARGET_URL]: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[0],
      };
      const tokenInfoResponse = { token_type: ANONYMOUS };
      expect(isBarclaysPostBackAutoRedirect(queryParams, tokenInfoResponse)).toBe(false);
    });

    it('should return false if user is authenticated, targetURL includes CHECKOUT_PATH and isBarclaysPostBack is false', () => {
      const queryParams = {
        [TARGET_URL]: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: 'Invalid',
      };
      const tokenInfoResponse = { token_type: AUTHENTICATED };
      expect(isBarclaysPostBackAutoRedirect(queryParams, tokenInfoResponse)).toBe(false);
    });

    it('should return true if user is authenticated, targetURL includes CHECKOUT_PATH, and isBarclaysPostBack is true', () => {
      const queryParams = {
        [TARGET_URL]: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[0],
      };
      const tokenInfoResponse = { token_type: AUTHENTICATED };
      expect(isBarclaysPostBackAutoRedirect(queryParams, tokenInfoResponse)).toBe(true);
    });

    it('should return true if user is not authenticated, targetURL does not include CHECKOUT_PATH, and isBarclaysPostBack is true', () => {
      const queryParams = {
        [TARGET_URL]: `${ORIGIN}/${HOME_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: BARCLAYS_POST_BACK_TYPES[0],
      };
      const tokenInfoResponse = { token_type: GUEST };
      expect(isBarclaysPostBackAutoRedirect(queryParams, tokenInfoResponse)).toBe(true);
    });
  });

  /**
   * @name isAuthenticated Tests
   * @description Test suite for the isAuthenticated function.
   */
  describe('isAuthenticated()', () => {
    it('should return true if token_type is AUTHENTICATED', () => {
      const response = { token_type: AUTHENTICATED };
      expect(isAuthenticated(response)).toBe(true);
    });

    it('should return false if token_type is not AUTHENTICATED', () => {
      const response = { token_type: GUEST };
      expect(isAuthenticated(response)).toBe(false);
    });
  });

  /**
   * @name isAuthenticatedOrGuest Tests
   * @description Test suite for the isAuthenticatedOrGuest function.
   */
  describe('isAuthenticatedOrGuest()', () => {
    it('should return true when token_type is AUTHENTICATED', () => {
      const response = { token_type: AUTHENTICATED };
      expect(isAuthenticatedOrGuest(response as TokenInfoResponse)).toBe(true);
    });

    it('should return true when token_type is GUEST', () => {
      const response = { token_type: GUEST };
      expect(isAuthenticatedOrGuest(response as TokenInfoResponse)).toBe(true);
    });

    it('should return false when token_type is neither AUTHENTICATED nor GUEST', () => {
      const response = { token_type: ANONYMOUS };
      expect(isAuthenticatedOrGuest(response as TokenInfoResponse)).toBe(false);
    });

    it('should return false when response is undefined', () => {
      const response = undefined;
      // @ts-expect-error - Testing for undefined response
      expect(isAuthenticatedOrGuest(response as TokenInfoResponse)).toBe(false);
    });
  });

  /**
   * @name isCheckout Tests
   * @description Test suite for the isCheckout function.
   */
  describe('isCheckout()', () => {
    it('should return true when targetURL includes /checkout and not FORCE_SIGNUP_PARAM is false', () => {
      const queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [FORCE_SIGNUP_PARAM]: 'false',
      };
      expect(isCheckout(queryParams)).toBe(true);
    });

    it('should return false when targetURL includes /checkout and FORCE_SIGNUP_PARAM is true', () => {
      const queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [FORCE_SIGNUP_PARAM]: 'true',
      };
      expect(isCheckout(queryParams)).toBe(false);
    });

    it('should return false when targetURL includes /checkout and barclaysCreditOffer is barclays', () => {
      const queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_CREDIT_OFFER]: BARCLAYS,
      };
      expect(isCheckout(queryParams)).toBe(false);
    });

    it('should return true when targetURL includes /checkout and barclays postback loyalty status is invalid', () => {
      const queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: 'INVALID',
      };
      expect(isCheckout(queryParams)).toBe(true);
    });

    it('should return false when targetURL includes /checkout and barclays postback loyalty status is valid', () => {
      let queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: 'TEMP',
      };
      expect(isCheckout(queryParams)).toBe(false);
      queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: 'BANNER',
      };
      expect(isCheckout(queryParams)).toBe(false);
      queryParams = {
        targetURL: `${ORIGIN}/${CHECKOUT_PATH}`,
        [BARCLAYS_LOYALTY_STATUS]: '',
      };
      expect(isCheckout(queryParams)).toBe(false);
    });

    it('should return false when targetURL does not include /checkout', () => {
      const queryParams = { targetURL: `${ORIGIN}/my-account/home` };
      expect(isCheckout(queryParams)).toBe(false);
    });
  });
});
