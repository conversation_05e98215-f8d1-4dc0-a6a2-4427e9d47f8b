import { NextRequest, NextResponse } from 'next/server';
import { handleStoresPage } from '../../src/middleware/storesPageMiddleware';

describe('storesPageMiddleware', () => {
  test('should set x-pathname header for customer info pages', () => {
    const nextRequest = new NextRequest(new URL('http://www.local.gaptechol.com:3000/stores/bc/burnaby/metropolis-3183'));
    const nextResponseSpy = jest.spyOn(NextResponse, 'next');

    handleStoresPage(nextRequest);

    expect(nextResponseSpy).toHaveBeenCalledTimes(1);
    expect(nextResponseSpy.mock.calls[0][0]?.request?.headers?.get('x-pathname')).toEqual('/stores/bc/burnaby/metropolis-3183');
  });
});
