import { act, renderHook, waitFor } from 'test-utils';
import { useTmxProfiling } from '../useTmxProfiling';

describe('useTmxProfiling()', () => {
  beforeAll(() => jest.useFakeTimers());
  afterAll(() => jest.useRealTimers());
  beforeEach(() => jest.clearAllMocks());
  afterEach(() => jest.restoreAllMocks());

  it('should return the expected values when bypassTmx is true and isLLSIEnabled is true', () => {
    const { result } = renderHook(() => useTmxProfiling(true, true));
    expect(result.current).toEqual({ isTmxLoaded: false, isMaxTimeEllapsed: false });
  });

  it('should not change return values when tmx_profiling_complete is invoked when bypassTmx is true and isLLSIEnabled is true', async () => {
    const hook = renderHook(() => useTmxProfiling(true, true));
    await waitFor(() => window.tmx_profiling_complete());
    expect(hook.result.current).toEqual({ isTmxLoaded: false, isMaxTimeEllapsed: false });
  });

  it('should not change return values when tmx_profiling_complete is not invoked within 5 seconds and bypassTmx is true and isLLSIEnabled is true', async () => {
    const hook = renderHook(() => useTmxProfiling(true, true));
    act(() => jest.runAllTimers());
    expect(hook.result.current).toEqual({ isTmxLoaded: false, isMaxTimeEllapsed: false });
  });

  it('should return the expected values when bypassTmx is false and isLLSIEnabled is false', () => {
    const { result } = renderHook(() => useTmxProfiling(false, false));
    expect(result.current).toEqual({ isTmxLoaded: false, isMaxTimeEllapsed: false });
  });

  it('should set isTmxLoaded to true and isMaxTimeEllapsed to false when tmx_profiling_complete is invoked and bypassTmx is false and isLLSIEnabled is true', async () => {
    const hook = renderHook(() => useTmxProfiling(false, true));
    const spy = jest.spyOn(window, 'tmx_profiling_complete');
    await waitFor(() => window.tmx_profiling_complete());
    await waitFor(() => {
      expect(spy).toHaveBeenCalledTimes(1);
      expect(hook.result.current.isMaxTimeEllapsed).toBe(false);
      expect(hook.result.current.isTmxLoaded).toBe(true);
    });
  });

  it('should set isMaxTimeEllapsed to true and isTmxLoaded to false when tmx_profiling_complete is not invoked within 5 seconds', async () => {
    const spy = jest.spyOn(global, 'setTimeout');
    const hook = renderHook(() => useTmxProfiling(false, true));
    act(() => jest.runAllTimers());
    expect(spy).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(hook.result.current.isMaxTimeEllapsed).toBe(true);
      expect(hook.result.current.isTmxLoaded).toBe(false);
    });
  });
});
