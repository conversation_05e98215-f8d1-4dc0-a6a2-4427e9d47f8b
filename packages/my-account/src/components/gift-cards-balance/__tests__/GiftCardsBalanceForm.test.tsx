import { render, screen, fireEvent, waitFor } from 'test-utils';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { GiftCardsBalanceForm } from '../GiftCardsBalanceForm';
import { GiftCardsProvider } from '../../../context/gift-cards/GiftCardsContext';
import { GlobalProvider } from '../../../context/global/GlobalContext';
import { fireTealiumLinkTag, fireTealiumViewTag } from '../../../utils/tealium/tealiumUtil';
import { useFeatureFlags } from '../../../hooks/useFeatureFlags';
import * as headlessWrapper from '../../../utils/api/headlessApi';
import * as xApiWrapper from '../../../utils/api/xApi';

const caFactoryBrands: ReadonlyArray<string> = ['gpfs'] as const;
const usFactoryBrands: ReadonlyArray<string> = ['gpfs', 'brfs'] as const;
const specialtyBrands: ReadonlyArray<string> = ['gp', 'br', 'on', 'at'] as const;

type Props = {
  isGiftCardDataError: boolean;
  isVaultError: boolean;
  locale: 'en_US' | 'en_CA' | 'fr_CA';
};

type TranslationLocale = 'en-US' | 'en-CA' | 'fr-CA';

jest.mock('@ecom-next/utils/clientFetch');
jest.mock('@ecom-next/sitewide/hooks/usePageContext');

jest.mock('../../../utils/api/xApi', () => ({
  xApi: jest.fn(),
}));

jest.mock('../../../utils/api/headlessApi', () => ({
  headlessApi: jest.fn(),
}));

jest.mock('../../../hooks/useFeatureFlags', () => ({
  useFeatureFlags: jest.fn(),
}));

jest.mock('../../../utils/tealium/tealiumUtil', () => ({
  fireTealiumLinkTag: jest.fn(),
  fireTealiumViewTag: jest.fn(),
}));

jest.mock('../../../hooks/useGiftCards', () => ({
  useGiftCards: jest.fn().mockReturnValue({
    giftCardDispatch: jest.fn(),
    giftCardState: {
      getGiftCardsBalanceReq: {
        isFail: false,
        isSuccess: false,
      },
      giftCardInfo: {
        cardsLastFourDigits: null,
        balance: null,
        giftCardError: false,
      },
    },
  }),
}));

const setFeatureFlag = (flagValue: boolean): void => {
  const flags = { 'profile-ui-gift-cards-headless': flagValue };
  (useFeatureFlags as jest.Mock).mockReturnValue(flags);
};

const initialProps: Props = {
  locale: 'en_US',
  isVaultError: false,
  isGiftCardDataError: false,
};

const translationLocaleMap: Readonly<Record<string, string>> = {
  en_US: 'en-US',
  en_CA: 'en-US',
  fr_CA: 'fr-CA',
} as const;

const renderComponent = (props: Props) => {
  const { locale } = props;
  const market = locale === 'en_US' ? 'us' : 'ca';
  const translations = getLocaleSpecificTranslations(locale, ['profileui']);
  const translationsLocale = translationLocaleMap[locale] as TranslationLocale;
  return render(
    <LocalizationProvider locale={translationsLocale} translations={translations} market={market}>
      <GlobalProvider isGuestView={false}>
        <GiftCardsProvider>
          <GiftCardsBalanceForm {...props} />
        </GiftCardsProvider>
      </GlobalProvider>
    </LocalizationProvider>
  );
};

describe('Gift Card Balance Form', () => {
  let headlessApiSpy: jest.SpyInstance;
  let xApiSpy: jest.SpyInstance;
  beforeEach(() => {
    jest.clearAllMocks();
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'gap',
      brandAbbr: 'gp',
      market: 'us',
      locale: 'en_US',
      ecomApiBaseUrl: 'https://api.gap.com',
      brandCode: 1,
      brandId: 'gap',
      targetEnv: 'prod',
    });
    xApiSpy = jest.spyOn(xApiWrapper, 'xApi').mockResolvedValue({ balance: 100 });
    headlessApiSpy = jest.spyOn(headlessWrapper, 'headlessApi').mockResolvedValue([{ vaultId: '1234567890321212313' }, { vaultId: '3424567890321212313' }]);
  });

  it('should able to enter the Gift card number and the Pin code', () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, {
      target: { value: '1234' },
    });
    expect(cardNumberInput.value).toBe('6003868849900539');
    expect(cardPinNumberInput.value).toBe('1234');
  });

  it('should able to enter the Gift card number and the Pin code and show error if the inputs value are not correct', () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    const checkCardBalanceButton = screen.getByText('Check Balance');
    fireEvent.change(cardNumberInput, { target: { value: '12344' } });
    fireEvent.change(cardPinNumberInput, {
      target: { value: '12' },
    });
    expect(cardNumberInput.value).toBe('12344');
    expect(cardPinNumberInput.value).toBe('12');
    fireEvent.click(checkCardBalanceButton);
    const cardErrorMsg = screen.getByText('Enter a valid gift card number');
    const cardPinErrorMsg = screen.getByText('Enter a valid pin number');
    expect(cardErrorMsg).toBeInTheDocument();
    expect(cardPinErrorMsg).toBeInTheDocument();
  });

  it('should able to show error msg in blur', () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    fireEvent.blur(cardNumberInput);
    fireEvent.blur(cardPinNumberInput);
    const cardErrorMsg = screen.getByText('Enter a valid gift card number');
    const cardPinErrorMsg = screen.getByText('Enter a valid pin number');
    expect(cardErrorMsg).toBeInTheDocument();
    expect(cardPinErrorMsg).toBeInTheDocument();
  });

  it('should able to submit a request after filling to form', () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    expect(cardNumberInput).toBeInTheDocument();
    expect(cardPinNumberInput).toBeInTheDocument();
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, {
      target: { value: '1234' },
    });
    expect(cardNumberInput.value).toBe('6003868849900539');
    expect(cardPinNumberInput.value).toBe('1234');
  });

  it('should able to submit a request after filling to form and click submit', async () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    expect(cardNumberInput).toBeInTheDocument();
    expect(cardPinNumberInput).toBeInTheDocument();
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, {
      target: { value: '1234' },
    });
    expect(cardNumberInput.value).toBe('6003868849900539');
    expect(cardPinNumberInput.value).toBe('1234');
    const button = screen.getByText('Check Balance');
    await waitFor(() => {
      button.click();
    });
    await waitFor(() => {
      expect(headlessApiSpy).toHaveBeenCalledTimes(1);
      expect(headlessApiSpy).toHaveBeenCalledWith('https://api.gap.com/commerce/vault/vault-entries', {
        method: 'POST',
        body: JSON.stringify([
          {
            index: 0,
            plaintext: '6003868849900539',
            type: 'GIFT_CARD_NUMBER',
          },
          {
            index: 1,
            plaintext: '1234',
            type: 'GIFT_CARD_PIN',
          },
        ]),
      });
      expect(xApiSpy).toHaveBeenCalledTimes(1);
      expect(xApiSpy).toHaveBeenCalledWith('/gift-card-balance', {
        method: 'POST',
        body: JSON.stringify({
          giftCardIdentifier: '1234567890321212313',
          giftCardIdentifierFormat: 'VaultId',
          pinIdentifier: '3424567890321212313',
          pinIdentifierFormat: 'VaultId',
        }),
      });
    });
  });

  it('should render vault error msg', () => {
    setFeatureFlag(false);
    const modifiedProps = { ...initialProps, isVaultError: true };
    renderComponent(modifiedProps);
    const errorMsg = screen.getByText('A technical error has unfortunately occurred. Please refresh to try again.');
    expect(errorMsg).toBeInTheDocument();
  });

  it('should render vault error msg', () => {
    setFeatureFlag(false);
    const modifiedProps = { ...initialProps, isGiftCardDataError: true };
    renderComponent(modifiedProps);
    const errorMsg = screen.getByText('We don’t recognize that gift card information. Please try again.');
    expect(errorMsg).toBeInTheDocument();
  });

  it('should fire tealuim view event', async () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    expect(fireTealiumViewTag).toHaveBeenCalledTimes(1);
  });

  it('should fire tealuim link event', async () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const button = screen.getByText('Check Balance');
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    expect(cardNumberInput).toBeInTheDocument();
    expect(cardPinNumberInput).toBeInTheDocument();
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, {
      target: { value: '1234' },
    });
    expect(cardNumberInput.value).toBe('6003868849900539');
    expect(cardPinNumberInput.value).toBe('1234');
    await waitFor(() => {
      button.click();
    });
    await waitFor(() => {
      expect(fireTealiumLinkTag).toHaveBeenCalledTimes(1);
    });
  });

  it('should show loader while fetching gift card balance', async () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const button = screen.getByText('Check Balance');
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
    await waitFor(() => {
      button.click();
    });
    await waitFor(() => {
      expect(screen.getByText('loading...')).toBeInTheDocument();
    });
  });

  it('should make the get gift card balance request with the correct payload for xApi', async () => {
    setFeatureFlag(false);
    renderComponent(initialProps);
    const button = screen.getByText('Check Balance');
    const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
    const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
    fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
    fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
    await waitFor(() => {
      button.click();
    });
    await waitFor(() => {
      expect(screen.queryByText('loading...')).toBeInTheDocument();
      expect(headlessApiSpy).toHaveBeenCalledTimes(1);
      expect(headlessApiSpy).toHaveBeenCalledWith('https://api.gap.com/commerce/vault/vault-entries', {
        method: 'POST',
        body: JSON.stringify([
          {
            index: 0,
            plaintext: '6003868849900539',
            type: 'GIFT_CARD_NUMBER',
          },
          {
            index: 1,
            plaintext: '1234',
            type: 'GIFT_CARD_PIN',
          },
        ]),
      });
      expect(xApiSpy).toHaveBeenCalledTimes(1);
      expect(xApiSpy).toHaveBeenCalledWith('/gift-card-balance', {
        method: 'POST',
        body: JSON.stringify({
          giftCardIdentifier: '1234567890321212313',
          giftCardIdentifierFormat: 'VaultId',
          pinIdentifier: '3424567890321212313',
          pinIdentifierFormat: 'VaultId',
        }),
      });
    });
  });

  describe('Gift Card Balance Form Headless US Specialty', () => {
    it.each(specialtyBrands)(
      'should make the get gift card balance request with the correct payload for headless for %s',
      async (brandAbbr: (typeof specialtyBrands)[number]) => {
        const brand = brandAbbr === 'gp' ? 'gap' : brandAbbr;
        (usePageContext as jest.Mock).mockReturnValue({
          brand: brand,
          brandAbbr,
          market: 'us',
          locale: 'en_US',
          ecomApiBaseUrl: 'https://api.gap.com',
          brandCode: 1,
          targetEnv: 'prod',
        });
        setFeatureFlag(true);
        renderComponent(initialProps);
        const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
        const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
        const button = screen.getByText('Check Balance');
        fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
        fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
        fireEvent.click(button);
        await waitFor(() => {
          expect(headlessApiSpy).toHaveBeenCalledTimes(2);
          expect(headlessApiSpy).toHaveBeenNthCalledWith(1, 'https://api.gap.com/commerce/vault/vault-entries', {
            method: 'POST',
            body: JSON.stringify([
              {
                index: 0,
                plaintext: '6003868849900539',
                type: 'GIFT_CARD_NUMBER',
              },
              {
                index: 1,
                plaintext: '1234',
                type: 'GIFT_CARD_PIN',
              },
            ]),
          });
          expect(headlessApiSpy).toHaveBeenNthCalledWith(2, 'https://api.gap.com/gift_cards/v2/balances', {
            body: JSON.stringify({
              header: {
                market_code: 'US',
                country_code: 'US',
                brand: brand?.toUpperCase(),
                channel: 'WEB',
                currency: 'USD',
              },
              gift_cards: [
                {
                  gift_card_identifier: '1234567890321212313',
                  gift_card_identifier_format: 'VaultId',
                  pin_identifier: '3424567890321212313',
                  pin_identifier_format: 'VaultId',
                },
              ],
              audit_data: {
                customer_id: '',
                client_app: 'ECOM',
              },
            }),
            method: 'POST',
          });
          expect(xApiSpy).toHaveBeenCalledTimes(0);
        });
      }
    );
  });

  describe('Gift Card Balance Form Headless CA Specialty', () => {
    it.each(specialtyBrands)(
      'should make the get gift card balance request with the correct payload for headless for %s',
      async (brandAbbr: (typeof specialtyBrands)[number]) => {
        const brand = brandAbbr === 'gp' ? 'gap' : brandAbbr;
        (usePageContext as jest.Mock).mockReturnValue({
          brand: brand,
          brandAbbr,
          market: 'ca',
          locale: 'en_CA',
          ecomApiBaseUrl: 'https://api.gapcanada.ca',
          brandCode: 1,
          targetEnv: 'prod',
        });
        setFeatureFlag(true);
        renderComponent({ ...initialProps, locale: 'en_CA' });
        const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
        const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
        const button = screen.getByText('Check Balance');
        fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
        fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
        fireEvent.click(button);
        await waitFor(() => {
          expect(headlessApiSpy).toHaveBeenCalledTimes(2);
          expect(headlessApiSpy).toHaveBeenNthCalledWith(1, 'https://api.gapcanada.ca/commerce/vault/vault-entries', {
            method: 'POST',
            body: JSON.stringify([
              {
                index: 0,
                plaintext: '6003868849900539',
                type: 'GIFT_CARD_NUMBER',
              },
              {
                index: 1,
                plaintext: '1234',
                type: 'GIFT_CARD_PIN',
              },
            ]),
          });
          expect(headlessApiSpy).toHaveBeenNthCalledWith(2, 'https://api.gapcanada.ca/gift_cards/v2/balances', {
            body: JSON.stringify({
              header: {
                market_code: 'CA',
                country_code: 'CA',
                brand: brand?.toUpperCase(),
                channel: 'WEB',
                currency: 'CAD',
              },
              gift_cards: [
                {
                  gift_card_identifier: '1234567890321212313',
                  gift_card_identifier_format: 'VaultId',
                  pin_identifier: '3424567890321212313',
                  pin_identifier_format: 'VaultId',
                },
              ],
              audit_data: {
                customer_id: '',
                client_app: 'ECOM',
              },
            }),
            method: 'POST',
          });
          expect(xApiSpy).toHaveBeenCalledTimes(0);
        });
      }
    );
  });

  describe('Gift Card Balance Form Headless US Factory', () => {
    it.each(usFactoryBrands)(
      'should make the get gift card balance request with the correct payload for headless for %s',
      async (brandAbbr: (typeof specialtyBrands)[number]) => {
        const brand = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
        (usePageContext as jest.Mock).mockReturnValue({
          brand: brand,
          brandAbbr,
          market: 'us',
          locale: 'en_US',
          ecomApiBaseUrl: 'https://api.gapfactory.com',
          brandCode: 1,
          targetEnv: 'prod',
        });
        setFeatureFlag(true);
        renderComponent({ ...initialProps, locale: 'en_US' });
        const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
        const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
        const button = screen.getByText('Check Balance');
        fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
        fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
        fireEvent.click(button);
        await waitFor(() => {
          expect(headlessApiSpy).toHaveBeenCalledTimes(2);
          expect(headlessApiSpy).toHaveBeenNthCalledWith(1, 'https://api.gapfactory.com/commerce/vault/vault-entries', {
            method: 'POST',
            body: JSON.stringify([
              {
                index: 0,
                plaintext: '6003868849900539',
                type: 'GIFT_CARD_NUMBER',
              },
              {
                index: 1,
                plaintext: '1234',
                type: 'GIFT_CARD_PIN',
              },
            ]),
          });
          expect(headlessApiSpy).toHaveBeenNthCalledWith(2, 'https://api.gapfactory.com/gift_cards/v2/balances', {
            body: JSON.stringify({
              header: {
                market_code: 'US',
                country_code: 'US',
                brand: brand?.toUpperCase(),
                channel: 'WEB',
                currency: 'USD',
              },
              gift_cards: [
                {
                  gift_card_identifier: '1234567890321212313',
                  gift_card_identifier_format: 'VaultId',
                  pin_identifier: '3424567890321212313',
                  pin_identifier_format: 'VaultId',
                },
              ],
              audit_data: {
                customer_id: '',
                client_app: 'ECOM',
              },
            }),
            method: 'POST',
          });
          expect(xApiSpy).toHaveBeenCalledTimes(0);
        });
      }
    );
  });

  describe('Gift Card Balance Form Headless CA Factory', () => {
    it.each(caFactoryBrands)(
      'should make the get gift card balance request with the correct payload for headless for %s',
      async (brandAbbr: (typeof specialtyBrands)[number]) => {
        const brand = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
        (usePageContext as jest.Mock).mockReturnValue({
          brand: brand,
          brandAbbr,
          market: 'ca',
          locale: 'en_CA',
          ecomApiBaseUrl: 'https://api.gapfactory.ca',
          brandCode: 1,
          targetEnv: 'prod',
        });
        setFeatureFlag(true);
        renderComponent({ ...initialProps, locale: 'en_US' });
        const cardNumberInput = screen.getByLabelText<HTMLInputElement>('16-digit card number');
        const cardPinNumberInput = screen.getByLabelText<HTMLInputElement>('4-digit PIN number');
        const button = screen.getByText('Check Balance');
        fireEvent.change(cardNumberInput, { target: { value: '6003868849900539' } });
        fireEvent.change(cardPinNumberInput, { target: { value: '1234' } });
        await waitFor(() => fireEvent.click(button));
        await waitFor(() => {
          expect(headlessApiSpy).toHaveBeenCalledTimes(2);
          expect(headlessApiSpy).toHaveBeenNthCalledWith(1, 'https://api.gapfactory.ca/commerce/vault/vault-entries', {
            method: 'POST',
            body: JSON.stringify([
              {
                index: 0,
                plaintext: '6003868849900539',
                type: 'GIFT_CARD_NUMBER',
              },
              {
                index: 1,
                plaintext: '1234',
                type: 'GIFT_CARD_PIN',
              },
            ]),
          });
          expect(headlessApiSpy).toHaveBeenNthCalledWith(2, 'https://api.gapfactory.ca/gift_cards/v2/balances', {
            body: JSON.stringify({
              header: {
                market_code: 'CA',
                country_code: 'CA',
                brand: brand?.toUpperCase(),
                channel: 'WEB',
                currency: 'CAD',
              },
              gift_cards: [
                {
                  gift_card_identifier: '1234567890321212313',
                  gift_card_identifier_format: 'VaultId',
                  pin_identifier: '3424567890321212313',
                  pin_identifier_format: 'VaultId',
                },
              ],
              audit_data: {
                customer_id: '',
                client_app: 'ECOM',
              },
            }),
            method: 'POST',
          });
          expect(xApiSpy).toHaveBeenCalledTimes(0);
        });
      }
    );
  });

  describe('Gift Card Balance Form Headless Errors', () => {
    it('should show the correct error message when the request fails', async () => {
      setFeatureFlag(true);
      headlessApiSpy.mockRejectedValue(new Error('error'));
      renderComponent({ ...initialProps, isVaultError: true });
      await waitFor(() => {
        expect(screen.getByText('A technical error has unfortunately occurred. Please refresh to try again.')).toBeInTheDocument();
      });
    });

    it('should show the correct error message when the last four digits are missing', async () => {
      setFeatureFlag(true);
      headlessApiSpy.mockRejectedValue(new Error('error'));
      renderComponent({ ...initialProps, isGiftCardDataError: true });
      await waitFor(() => {
        expect(screen.getByText('We don’t recognize that gift card information. Please try again.')).toBeInTheDocument();
      });
    });
  });
});
