import { useLocalize } from '@sitewide/providers/localization';
import { Modal } from '@ecom-next/core/legacy/modal';

import { MERGE_FORM_VIEW, REVIEW_FORM_VIEW, View } from './types';
import { BdssMergeForm } from './BdssMergeForm';
import { BdssReview } from './BdssReview';
import './BdssMergeDrawer.css';

export type bdssMergeDrawerProps = {
  currentEmail: string;
  dateOfBirth: string;
  isOpen: boolean;
  onClose: () => void;
  phoneNumber: string;
  secondaryEmailAddress: string;
  setView: (view: View) => void;
  view: string;
};

export const BdssMergeDrawer = (props: bdssMergeDrawerProps): JSX.Element | null => {
  const { localize } = useLocalize();
  const { isOpen, onClose, currentEmail, dateOfBirth, phoneNumber, secondaryEmailAddress, view, setView } = props;

  const title = view === MERGE_FORM_VIEW ? localize('home.bdss.drawerHeader') : localize('home.bdss.mergeReview.headerStateOne');

  const handleOnClose = () => {
    onClose();
  };

  return isOpen ? (
    <Modal id='bdss-merge-drawer-modal' isOpen={isOpen} onClose={handleOnClose} hasRoundedCorners title={title} closeButtonAriaLabel='bdss-modal-close-button'>
      <div className='font-sourcesans p-4 text-[1rem]'>
        {view === MERGE_FORM_VIEW && <BdssMergeForm view={view} setView={setView} currentEmail={currentEmail} maskedVictimEmail={secondaryEmailAddress} />}
        {view === REVIEW_FORM_VIEW && <BdssReview currentEmail={currentEmail} dateOfBirth={dateOfBirth} phoneNumber={phoneNumber} onClose={handleOnClose} />}
      </div>
    </Modal>
  ) : null;
};
