import React, { useEffect, useState } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Paginator } from '@ecom-next/core/legacy/paginator';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useValueCenter } from '../../../hooks/useValueCenter';
import { triggerValueCenterPointsActivityReq } from '../../../requests/value-center/valueCenterRequests';
import { chunkArray } from '../../../utils/value-center/valueCenterUtils';
import { checkUseMockData } from '../../../utils/order-history/orderHistoryUtils';
import { PageUnavailable } from '../../page-unavailable/PageUnavailable';
import { WarningNotification } from '../../common/legacy/Notifications';
import { fireValueCenterLinkTag } from '../../../utils/tealium/tealiumUtil';
import { PointsSummary } from './PointsSummary';
import { PointsListItem } from './PointsListItem';
import { TrackPointsLoading } from './TrackPointsLoading';

type TrackPointsProps = {
  activetabSection: string;
};

export const TrackPoints = (props: TrackPointsProps): JSX.Element => {
  const { activetabSection } = props;
  const { valueCenterState, valueCenterDispatch } = useValueCenter();
  const { getPointsActivityReq } = valueCenterState;
  const { localize } = useLocalize();
  const { brandCode, brandAbbr } = usePageContext();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
  // setup refs
  const refPointsActivity = React.useRef<HTMLDivElement | null>(null);

  const activePoints = valueCenterState?.membership?.customerDetail?.activePoints || 0;
  const activePointsAmount = valueCenterState?.membership?.customerDetail?.activePointsAmount || 0;
  const points = valueCenterState?.trackPoints?.points || [];
  const [isLoading, setIsLoading] = useState(true);
  const isGetPointsActivityFail = valueCenterState.getPointsActivityReq.isFail;
  const isGetUserRewardsFail = valueCenterState?.getMembershipReq?.isGetUserRewardsFail || false;
  const [pageIndex, setPageIndex] = useState(0);
  const listPoints = chunkArray(points, 20);
  const listItems =
    listPoints?.[pageIndex]?.map((item, index) => {
      const key = index * 100;
      return (
        <div key={key} className='border-[#d8d8d8 border-b'>
          <PointsListItem {...item} />
        </div>
      );
    }) || [];
  const isEmpty = listItems.length === 0;
  const showPageUnavailable = isGetUserRewardsFail && isGetPointsActivityFail;

  useEffect(() => {
    // make call only once onload
    if (!getPointsActivityReq?.initialCallMade) {
      const payload = {
        valueCenterDispatch,
        useMockData: checkUseMockData(window),
      };
      triggerValueCenterPointsActivityReq(payload);
      const tealiumPayload = {
        brandCode,
        brandTealium,
        eventName: 'value-center-click',
        value_center_interaction: 'Track Points',
      };
      fireValueCenterLinkTag(tealiumPayload);
    }
  }, []);

  useEffect(() => {
    // check for auto-scroll
    if (activetabSection === 'pointsActivity') {
      refPointsActivity?.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [activetabSection]);

  useEffect(() => {
    if (isGetPointsActivityFail) {
      const tealiumPayload = {
        brandCode,
        brandTealium,
        eventName: 'value_center_error',
        vc_error_location: 'track-points-tab',
      };
      fireValueCenterLinkTag(tealiumPayload);
    }
  }, [isGetPointsActivityFail]);

  useEffect(() => {
    if (valueCenterState.getPointsActivityReq.isFail || valueCenterState.getPointsActivityReq.isSuccess) {
      setIsLoading(false);
    }
  }, [valueCenterState.getPointsActivityReq]);

  const renderWarningMessage = (): JSX.Element => {
    if (isGetPointsActivityFail || isGetUserRewardsFail) {
      return (
        <div className='[&_*]:!font-sourcesans m-auto max-w-[90%]'>
          <WarningNotification message={localize('valueCenter.errors.infoUnavailable')} />
        </div>
      );
    }
    return <></>;
  };

  if (showPageUnavailable) {
    return <PageUnavailable />;
  }

  return (
    <div id='value-center-track-points-hui' className='font-sourcesans m-auto mt-4 flex max-w-[623px] flex-col justify-center'>
      {renderWarningMessage()}
      {!isGetUserRewardsFail && <PointsSummary activePoints={activePoints} activePointsAmount={activePointsAmount} />}
      <div className='my-4 flex-col rounded text-center normal-case shadow-[0_1px_4px_rgba(0,0,0,0.25)]' data-testid='track-points' ref={refPointsActivity}>
        <div className='flex w-full justify-between bg-[#7e868426] p-4 text-[0.8125rem] font-semibold	text-black'>
          <p>{localize('valueCenter.trackPoints.table.activity')}</p>
          <p>{localize('valueCenter.trackPoints.table.points')}</p>
        </div>
        {!isGetPointsActivityFail && (
          <div>
            {isLoading && <TrackPointsLoading />}
            {!isLoading && <div className='w-full'>{listItems}</div>}
          </div>
        )}
        {!isLoading && !isGetPointsActivityFail && (
          <div className='[&_*]:font-sourcesans pb-4 pt-8 text-[#666]'>
            {!isEmpty && (
              <Paginator
                alignment='center'
                index={pageIndex}
                numberOfPages={listPoints?.length}
                goToPage={setPageIndex}
                pageText={localize('valueCenter.trackPoints.pagination.page')}
                ofText={localize('valueCenter.trackPoints.pagination.of')}
                leftArrowLabel={localize('valueCenter.trackPoints.pagination.left')}
                rightArrowLabel={localize('valueCenter.trackPoints.pagination.right')}
                paginationNavLabel={localize('valueCenter.trackPoints.pagination.pagination')}
              />
            )}
            {isEmpty && <p className='text-black'>{localize('valueCenter.trackPoints.table.empty')}</p>}
            <p className='pt-6'>{localize('valueCenter.trackPoints.table.history')}</p>
          </div>
        )}
      </div>
    </div>
  );
};
