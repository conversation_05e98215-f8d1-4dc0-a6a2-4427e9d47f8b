import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { OrganizationModal, OrganizationModalProps } from '../../../earn-redeem/pay-it-forward/OrganizationModal';

const initProps: OrganizationModalProps = {
  charityList: [],
  isOpen: true,
  toggleOrganizationModal: jest.fn(),
};

const renderComponent = (props: OrganizationModalProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <OrganizationModal {...props} />
    </LocalizationProvider>
  );
};

describe('OrganizationModal component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders OrganizationModal component properly', () => {
    renderComponent(initProps);
    expect(screen.getByText('ORGANIZATION DETAILS')).toBeInTheDocument();
    expect(screen.getByText('Do Good')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Pay it forward when you donate your rewards to a good cause! Select from the organizations below, in partnership with our family of brands, to do good.'
      )
    ).toBeInTheDocument();
  });
});
