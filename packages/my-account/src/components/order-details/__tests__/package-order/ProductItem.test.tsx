import { render, screen } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { GlobalProvider } from '../../../../context/global/GlobalContext';
import { ProductItem, ProductItemProps } from '../../package-order/ProductItem';

const initProps: ProductItemProps = {
  productImageURL: '#',
  brandCode: 'ON',
  marketCode: 'US',
  productName: 'product-name',
  productNumber: 'product-number',
  productURL: '#',
  color: 'red',
  size: 'large',
  unitPrice: 10,
  promoCodes: [],
  discount: 0,
  quantity: 2,
  finalPrice: 20,
  finalSale: false,
  itemStyleNumber: '',
  itemStyleDescription: '',
  lineId: 1,
  taxAmount: 5,
  sellerId: 'seller-id',
  remorseExpiryMinutes: 0,
};

const renderComponent = (props: ProductItemProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <GlobalProvider isGuestView={false}>
        <ProductItem {...props} />
      </GlobalProvider>
    </LocalizationProvider>
  );
};

describe('ProductItem component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders ProductItem component properly', () => {
    renderComponent(initProps);
    expect(screen.queryByText('product-name', { exact: false })).toBeInTheDocument();
  });
});
