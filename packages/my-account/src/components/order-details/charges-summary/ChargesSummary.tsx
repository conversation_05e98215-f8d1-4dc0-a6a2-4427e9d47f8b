import React from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import * as utils from '../../../utils/order-details/orderDetailsUtils';

/* eslint-disable typescript-sort-keys/interface, typescript-sort-keys/string-enum */
export type ChargesSummaryProps = {
  orderType: string;
  isShippingTo: boolean;
  isInStorePickup: boolean;
  subTotal: number;
  itemCount: number;
  shippingCost: number;
  shippingOption: string;
  promotions?: number;
  totalTax: number;
  totalCost: number;
  returnItemCharges: number[];
  returnItemCount: number;
  exchangeCredit: number;
  exchangeItemCount: number;
  coloradoTax: number;
  returnShippingFee: number;
  returnShippingFeeAppliedCount: number;
  merchInstantCredit: number;
  returnMerchInstantCredit: number;
  returnMerchInstantCreditItemCount: number;
  rewardsTotal: number;
  savingsTotal: number;
};

export const ChargesSummary = (props: ChargesSummaryProps): JSX.Element => {
  const {
    orderType,
    subTotal = 0,
    itemCount = 0,
    savingsTotal = 0,
    rewardsTotal = 0,
    shippingCost = 0,
    isInStorePickup,
    totalTax = 0,
    exchangeCredit = 0,
    exchangeItemCount = 0,
    coloradoTax = 0,
    merchInstantCredit = 0,
    totalCost = 0,
    returnItemCharges,
    returnItemCount = 0,
    returnShippingFee = 0,
    returnShippingFeeAppliedCount = 0,
    returnMerchInstantCredit = 0,
    returnMerchInstantCreditItemCount = 0,
  } = props;
  const { localize, formatCurrency } = useLocalize();
  const { market } = usePageContext();

  // setup variables
  const isOrderTypeStore = orderType === 'STORE';
  const subTotalLabelText = utils.setupSubTotalLabelText(itemCount, localize);
  const shippingCostText = utils.setupShippingCostText(shippingCost, formatCurrency, localize);
  const returnProductLabelText = utils.setupReturnProductLabelText(returnItemCharges, localize, returnItemCount);
  const totalReturnItemCharges = utils.getTotalReturnItemCharges(returnItemCharges);
  const exchangeProductLabelText = utils.setupExchangeProductLabelText(localize, exchangeItemCount);
  const homePickupFeeLabelText = utils.setupHomePickupFeeLabelText(localize, returnShippingFeeAppliedCount);
  const merchInstantCreditLabelText = utils.setupMerchInstantCreditLabelText(localize, 0); // instant credit for top section (summary)
  const returnMerchInstantCreditLabelText = utils.setupMerchInstantCreditLabelText(localize, returnMerchInstantCreditItemCount); // instant credit for bottom section (returns)

  // toggle params
  const showReturnProductLabelText = returnProductLabelText && totalReturnItemCharges > 0;
  const showExchangeProductLabelText = market === 'us' && exchangeProductLabelText && orderType === 'SALES' && exchangeCredit !== 0;
  const showHomePickupFeeLabelText = homePickupFeeLabelText && Math.abs(returnShippingFee) > 0;
  const showReturnMerchInstantCredit = returnMerchInstantCreditLabelText && Math.abs(returnMerchInstantCredit) > 0;
  const showBottomHR = showReturnProductLabelText || showExchangeProductLabelText || showHomePickupFeeLabelText || showReturnMerchInstantCredit;

  // setup elements
  const renderSavingsTotal = (): JSX.Element => {
    return savingsTotal !== 0 ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.savings')}</div>
        <div className='basis-[30%] text-right'>{formatCurrency(savingsTotal)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderRewardsTotal = (): JSX.Element => {
    return rewardsTotal !== 0 ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.rewards')}</div>
        <div className='basis-[30%] text-right'>{formatCurrency(rewardsTotal)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderShippingCost = (): JSX.Element => {
    return shippingCostText && !isOrderTypeStore ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.shippingOnlyLabel')}</div>
        <div className='basis-[30%] text-right'>{shippingCostText}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderInStorePickupCost = (): JSX.Element => {
    return isInStorePickup ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.inStorePickup')}</div>
        <div className='basis-[30%] text-right'>{localize('orderDetails.chargesSummary.free')}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderExchangeCredit = (): JSX.Element => {
    return market === 'us' && orderType === 'EXCHANGE' && exchangeCredit !== 0 ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.exchangeCreditLabel')}</div>
        <div className='basis-[30%] text-right'>{formatCurrency(exchangeCredit)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderColoradoStateTax = (): JSX.Element => {
    return coloradoTax > 0 ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.stateTaxLabel')}</div>
        <div className='basis-[30%] text-right'>{formatCurrency(coloradoTax)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderMerchInstantCredit = (): JSX.Element => {
    return merchInstantCredit !== 0 ? (
      <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
        <div className='basis-[70%]'>{merchInstantCreditLabelText}</div>
        <div className='basis-[30%] text-right text-[red]'>{formatCurrency(merchInstantCredit)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderBottomHR = (): JSX.Element => {
    return showBottomHR ? <hr className='mx-0 my-4 box-border h-0.5 w-full border-[none] bg-[#CBCACA] p-0 text-[#C9C9C9]' /> : <></>;
  };
  const renderReturnProductLabelText = (): JSX.Element => {
    return showReturnProductLabelText ? (
      <>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[#666666]'>{returnProductLabelText}</div>
          <div className='basis-[30%] text-right text-[#666666]'>{formatCurrency(totalReturnItemCharges)}</div>
        </div>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[0.75rem] text-[#666666]'>{localize('orderDetails.chargesSummary.returnsTaxDisclaimer')}</div>
        </div>
      </>
    ) : (
      <></>
    );
  };
  const renderExchangeProductLabelText = (): JSX.Element => {
    return showExchangeProductLabelText ? (
      <>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[#666666]'>{exchangeProductLabelText}</div>
          <div className='basis-[30%] text-right text-[#666666]'>{formatCurrency(Math.abs(exchangeCredit))}</div>
        </div>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[0.75rem] text-[#666666]'>{localize('orderDetails.chargesSummary.exchangeTaxDisclaimer')}</div>
        </div>
      </>
    ) : (
      <></>
    );
  };
  const renderHomePickupFeeLabelText = (): JSX.Element => {
    return showHomePickupFeeLabelText ? (
      <>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[#666666]'>{homePickupFeeLabelText}</div>
          <div className='basis-[30%] text-right text-[#666666]'>{formatCurrency(Math.abs(returnShippingFee))}</div>
        </div>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[0.75rem] text-[#666666]'>{localize('orderDetails.chargesSummary.exchangeTaxDisclaimer')}</div>
        </div>
      </>
    ) : (
      <></>
    );
  };
  const renderReturnMerchInstantCredit = (): JSX.Element => {
    return showHomePickupFeeLabelText ? (
      <>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[#666666]'>{returnMerchInstantCreditLabelText}</div>
          <div className='basis-[30%] text-right text-[#666666]'>{formatCurrency(Math.abs(returnMerchInstantCredit))}</div>
        </div>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] text-[0.75rem] text-[#666666]'>{localize('orderDetails.chargesSummary.exchangeTaxDisclaimer')}</div>
        </div>
      </>
    ) : (
      <></>
    );
  };

  return (
    <div className='m-0 mb-4 box-border flex flex-col border border-solid border-[#d8d8d8] p-0 align-baseline'>
      <h1 className='m-0 box-border border-0 px-4 pb-0 pt-4 align-baseline text-base font-bold leading-6 text-[#333]'>
        {localize('orderDetails.chargesSummary.summaryChargesHeader')}
      </h1>
      <div className='m-0 box-border flex flex-col border-0 p-4 align-baseline text-sm font-normal'>
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%]'>{subTotalLabelText}</div>
          <div className='basis-[30%] text-right'>{formatCurrency(subTotal)}</div>
        </div>
        {renderSavingsTotal()}
        {renderRewardsTotal()}
        {renderShippingCost()}
        {renderInStorePickupCost()}
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%]'>{localize('orderDetails.chargesSummary.taxLabel')}</div>
          <div className='basis-[30%] text-right'>{formatCurrency(totalTax)}</div>
        </div>
        {renderExchangeCredit()}
        {renderColoradoStateTax()}
        {renderMerchInstantCredit()}
        <hr className='mx-0 my-4 box-border h-0.5 w-full border-[none] bg-[#CBCACA] p-0 text-[#C9C9C9]' />
        <div className='m-0 box-border flex flex-row border-0 p-0 align-baseline leading-6 text-[#333]'>
          <div className='basis-[70%] font-bold'>{localize('orderDetails.chargesSummary.totalLabel')}</div>
          <div className='basis-[30%] text-right font-bold'>{formatCurrency(totalCost)}</div>
        </div>
        {renderBottomHR()}
        {renderReturnProductLabelText()}
        {renderExchangeProductLabelText()}
        {renderHomePickupFeeLabelText()}
        {renderReturnMerchInstantCredit()}
      </div>
    </div>
  );
};
