import React, { forwardRef, ChangeEvent, FocusEvent } from 'react';
import { TextInput } from '@ecom-next/core/legacy/text-input';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { validateEmail } from '../../../utils/validation/email/emailValidation';

export type Props = {
  autoComplete?: string;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  hasError: boolean;
  helpText?: string;
  id?: string;
  label?: string;
  name?: string;
  onBlur: (email: string, errorMessage: string) => void;
  onChange?: (email: string, errorMessage: string) => void;
  readOnly?: boolean;
  value?: string;
}

export const EmailInput = forwardRef<HTMLInputElement, Props>((props, ref) => {
  const {
    id,
    name,
    value,
    label,
    onBlur,
    onChange,
    autoComplete = 'on',
    className = 'text-[1.0625rem]',
    hasError,
    helpText,
    errorMessage,
    disabled,
    readOnly,
  } = props;
  const { localize } = useLocalize();

  const onEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    const error = validateEmail(email);
    onChange && onChange(email, error);
  };

  const onEmailBlur = (e: FocusEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    const error = validateEmail(email);
    onBlur(email, error);
  };

  return (
    <TextInput
      id={id}
      ref={ref}
      name={name}
      type='email'
      value={value}
      required={true}
      crossBrand={true}
      onBlur={onEmailBlur}
      onChange={onEmailChange}
      className={className}
      label={label}
      autoComplete={autoComplete}
      hasError={hasError}
      errorMessage={localize(errorMessage || '')}
      helpText={helpText}
      disabled={disabled}
      readOnly={readOnly}
    />
  );
});

EmailInput.displayName = 'EmailInput';
