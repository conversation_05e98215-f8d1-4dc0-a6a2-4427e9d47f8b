/* eslint-disable typescript-sort-keys/interface, typescript-sort-keys/string-enum */
import * as ActionTypes from '../../constants/account-security/accountSecurityActionTypes';

export type Session = {
  creationTime?: string;
  id?: string;
  ipAddress?: string;
  lastActivityTime?: string;
  sri?: string;
  status?: string;
  type?: string;
  userAgent?: string;
};

export type userSessions = {
  sessions: Session[];
};

export type Action =
  | { payload: userSessions; type: typeof ActionTypes.GET_USER_SESSIONS_SUCCESS }
  | { type: typeof ActionTypes.GET_USER_SESSIONS_FAIL }
  | { type: typeof ActionTypes.POST_REVOKE_USER_SESSIONS_SUCCESS }
  | { type: typeof ActionTypes.POST_REVOKE_USER_SESSIONS_FAIL }
  | { type: typeof ActionTypes.RESET_USER_SESSIONS_REQ };

export type State = {
  userSessionsGetReq: {
    hasSessions: boolean;
    isSuccess: boolean;
    isFail: boolean;
  };
  userSessionsPostReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
};

export type ContextProps = {
  accountSecurityDispatch: React.Dispatch<Action>;
  accountSecurityState: State;
};
