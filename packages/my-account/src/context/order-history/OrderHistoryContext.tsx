'use client';
import React, { createContext, useReducer, ReactNode } from 'react';
import { RESET_ORDER_HISTORY_STATE, POST_ORDER_HISTORY_SUCCESS, POST_ORDER_HISTORY_FAIL } from '../../constants/order-history/orderHistoryActionTypes';
import * as utils from './reducerUtil';
import type { Action, State, ContextProps } from './types';

const initialState: State = {
  orderPackages: [],
  offset: 1,
  totalCount: 0,
  postOrderHistory: {
    isSuccess: false,
    isFail: false,
  },
};

const OrderHistoryContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case RESET_ORDER_HISTORY_STATE: {
      return initialState;
    }
    case POST_ORDER_HISTORY_SUCCESS: {
      return {
        ...state,
        postOrderHistory: {
          isSuccess: true,
          isFail: false,
        },
        ...utils.processOrderHistoryResponse(state, action),
      };
    }
    case POST_ORDER_HISTORY_FAIL: {
      return {
        ...state,
        postOrderHistory: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const OrderHistoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [orderHistoryState, orderHistoryDispatch] = useReducer(reducer, initialState);
  return <OrderHistoryContext.Provider value={{ orderHistoryState, orderHistoryDispatch }}>{children}</OrderHistoryContext.Provider>;
};

export { OrderHistoryProvider, OrderHistoryContext };
