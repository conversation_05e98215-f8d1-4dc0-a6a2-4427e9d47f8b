import { Dispatch } from 'react';
import { Action } from '../../context/communication-preferences/types';

export type TextNotificationsReqPayload = {
  body: {
    brand?: string;
    locale?: string;
    mainSource?: string;
    market?: string;
    phoneNumber: string;
    program?: string;
    selectedBrand?: string;
    subSource?: string;
  };
  communicationPreferencesDispatch: Dispatch<Action>;
  ecomApiBaseUrl?: string;
};

export type SubscribeReqPayload = {
  [k: string]: unknown;
  communicationPreferencesDispatch: Dispatch<Action>;
  ecomApiBaseUrl?: string;
};
