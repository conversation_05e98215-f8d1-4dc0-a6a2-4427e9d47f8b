import type { Metadata } from 'next';
import { getPageContext } from '@ecom-next/utils/server';
import { ResetPassword } from '../components/reset-password/ResetPassword';
import { MetaDataMap } from '../utils/metadata/metadataUtil';
import { ResetPasswordProvider } from '../context/reset-password/ResetPasswordContext';

export const getMetaData = async (): Promise<Metadata> => {
  const pageName = 'resetPassword';
  const { brand, market, locale } = getPageContext();
  return {
    title: MetaDataMap[brand][market][locale][pageName].title,
  };
};

export default function ResetPasswordBase(): JSX.Element {
  return (
    <ResetPasswordProvider>
      <ResetPassword />
    </ResetPasswordProvider>
  );
}
