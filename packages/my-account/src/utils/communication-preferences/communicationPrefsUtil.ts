import { CommunicationPreferencesCategories } from '../../components/communication-preferences/types';

export const communicationPreferencesCategories: CommunicationPreferencesCategories = {
  gp: {
    us: [
      { category: 'gap_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'gap_men', label: 'communicationPreferences.category.categoryMens' },
      { category: 'gap_maternity', label: 'communicationPreferences.category.categoryMaternity' },
      { category: 'gap_kids', label: 'communicationPreferences.category.categoryKids' },
      { category: 'gap_baby', label: 'communicationPreferences.category.categoryBaby' },
    ],
    ca: [
      { category: 'gap_ca_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'gap_ca_men', label: 'communicationPreferences.category.categoryMens' },
      {
        category: 'gap_ca_maternity',
        label: 'communicationPreferences.category.categoryMaternity',
      },
      { category: 'gap_ca_kids', label: 'communicationPreferences.category.categoryKids' },
      { category: 'gap_ca_baby', label: 'communicationPreferences.category.categoryBaby' },
    ],
  },
  at: {
    us: [
      { category: 'ath_women', label: 'communicationPreferences.category.at' },
      { category: 'ath_girls', label: 'communicationPreferences.category.atGirl' },
    ],
    ca: [
      { category: 'ath_women', label: 'communicationPreferences.category.at' },
      { category: 'ath_girls', label: 'communicationPreferences.category.atGirl' },
    ],
  },
  on: {
    ca: [
      { category: 'on_ca_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'on_ca_maternity', label: 'communicationPreferences.category.categoryMaternity' },
      { category: 'on_ca_men', label: 'communicationPreferences.category.categoryMens' },
      { category: 'on_ca_girls', label: 'communicationPreferences.category.categoryGirls' },
      { category: 'on_ca_boys', label: 'communicationPreferences.category.categoryBoys' },
      {
        category: 'on_ca_toddler_girls',
        label: 'communicationPreferences.category.categoryToddlerGirls',
      },
      {
        category: 'on_ca_toddler_boys',
        label: 'communicationPreferences.category.categoryToddlerBoys',
      },
      { category: 'on_ca_baby', label: 'communicationPreferences.category.categoryBabyWithAges' },
    ],
  },
  gpfs: {
    us: [
      { category: 'gap_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'gap_men', label: 'communicationPreferences.category.categoryMens' },
      { category: 'gap_kids', label: 'communicationPreferences.category.categoryKids' },
      { category: 'gap_baby', label: 'communicationPreferences.category.categoryBaby' },
    ],
    ca: [
      { category: 'gap_ca_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'gap_ca_men', label: 'communicationPreferences.category.categoryMens' },
      { category: 'gap_ca_kids', label: 'communicationPreferences.category.categoryKids' },
      { category: 'gap_ca_baby', label: 'communicationPreferences.category.categoryBaby' },
    ],
  },
  brfs: {
    us: [
      { category: 'brfs_womens', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'brfs_mens', label: 'communicationPreferences.category.categoryMens' },
    ],
    ca: [
      { category: 'brfs_womens', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'brfs_mens', label: 'communicationPreferences.category.categoryMens' },
    ],
  },
  br: {
    us: [
      { category: 'br_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'br_men', label: 'communicationPreferences.category.categoryMens' },
      { category: 'br_home', label: 'communicationPreferences.category.categoryHome' },
    ],
    ca: [
      { category: 'br_ca_women', label: 'communicationPreferences.category.categoryWomens' },
      { category: 'br_ca_men', label: 'communicationPreferences.category.categoryMens' },
    ],
  },
};

export const brandTermsURL: Record<string, string> = {
  br: 'https://attnl.tv/t/8b8',
  on: 'https://attnl.tv/t/pKx',
  gpfs: '#', // will update when new link is available
};

export const brandMapping: Record<string, string> = {
  at: 'Athleta',
  gp: 'Gap',
  br: 'Banana Republic',
  on: 'Old Navy',
  gpfs: 'Gap Factory',
  brfs: 'Banana Republic Factory',
};
