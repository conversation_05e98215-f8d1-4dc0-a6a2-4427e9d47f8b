import type {
  BarclaysParams,
  BarclaysPostInUris,
  BarclaysGuestPayload,
  PostInRedirectPayload,
  BarclaysAuthenticatedPayload,
} from '../../requests/sign-in/types';
import { generateValidHostAndSanitize } from './validateHosts';

export const barclaysPostInUris: BarclaysPostInUris = {
  qa: 'https://qa03-cards.barclaycardus.com/credit-card',
  prod: 'https://cards.barclaycardus.com/credit-card',
} as const;

export const logInvalidSiteCode = (siteCode: string | undefined): void => {
  const errMessage = `Profile UI Debug: Barclays invalid sitecode ${siteCode}`;
  const siteCodeSuffix = typeof window.sitecodeSuffix === 'string' ? window.sitecodeSuffix : '';
  const errInfo = {
    errorType: 'debug',
    appType: 'HUI',
    siteCode: siteCode || '',
    siteCodeSuffix,
    queryParams: window.location.search,
    host: window.location.host,
  };
  try {
    window.newrelic?.noticeError(errMessage, {
      ...errInfo,
    });
  } catch (_e) {
    // Prevent error propagation if logging the error fails
  }
};

export const getBarclaysPostInUri = (targetEnv: string): string => {
  return targetEnv === 'prod' ? barclaysPostInUris['prod'] : barclaysPostInUris['qa'];
};

export const getSourceType = (retUrl: string): string => {
  const isCheckout = !!retUrl && retUrl.includes('/checkout');
  const isShoppingBag = !!retUrl && retUrl.includes('/shopping-bag');
  return isCheckout || isShoppingBag ? 'CARD_PURCH_PATH' : 'CARD_BANNER';
};

export const sanitizeValue = (str: string = ''): string => {
  // eslint-disable-next-line no-useless-escape
  const specialChars = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  const hasSpecialChar = specialChars.test(str);
  const result = hasSpecialChar ? '' : str;
  return result;
};

export const generateSiteCode = (siteCode: string | undefined): string | undefined => {
  let output: string = '';
  if (!siteCode) {
    logInvalidSiteCode(siteCode);
    return undefined;
  }
  const siteCodeSuffix = window.sitecodeSuffix || '';
  const isValidSiteCode = siteCode && typeof siteCode === 'string' && siteCode.length > 0;
  const isValidSiteCodeSuffix = siteCodeSuffix && typeof siteCodeSuffix === 'string' && siteCodeSuffix.length > 0;
  if (isValidSiteCode && isValidSiteCodeSuffix) {
    output = siteCode + siteCodeSuffix;
  } else if (isValidSiteCode) {
    output = siteCode;
  }
  const sanitizedValue = sanitizeValue(output);
  if (!sanitizedValue) {
    logInvalidSiteCode(siteCode);
  }
  return sanitizedValue;
};

export const getRetUrl = (retUrl: string | undefined = ''): string => {
  const protocol = window.location.protocol;
  const hostName = window.location.host;
  const orderConfirmationPath = '/checkout/thankyou';
  const ecomBaseHome = `${protocol}//${hostName}`;
  if (retUrl?.includes(orderConfirmationPath)) {
    return ecomBaseHome;
  } else if (retUrl?.trim().length) {
    return retUrl;
  }
  return ecomBaseHome;
};

export const hiddenFormPost = (path: string, params: BarclaysParams, method = 'post'): void => {
  const form = document.createElement('form');
  form.method = method;
  form.action = path;
  if (Object.keys(params).length) {
    for (const key in params) {
      const hiddenField = document.createElement('input');
      hiddenField.type = 'hidden';
      hiddenField.name = key;
      hiddenField.value = params[key] as string;
      form.appendChild(hiddenField);
    }
  }
  document.body.appendChild(form);
  form.submit();
};

export const barclaysPostInRedirect = (payload: PostInRedirectPayload): void => {
  const { response, postUrl, retUrl, siteCode, targetEnv, payloadType = 'sign-in' } = payload;
  const sourceTypeInfo: string = getSourceType(retUrl);

  if (payloadType === 'sign-in') {
    const {
      cardProviderInfo: { offerId },
      encryptionInfo,
      sessionId,
      memberId,
      encryptedCustomerInfo,
      preScreenInfo,
    } = response as BarclaysAuthenticatedPayload['response'];

    const { sessionKey, encryptionKeyId, signature, keyVersion, signKeyVersion, signatureKeyId } = encryptionInfo;
    const preScreenId = preScreenInfo?.preScreenId || '';
    const requestPayload = {
      SESSION: sessionId,
      RETURL: postUrl,
      SESSIONSTATE: retUrl,
      CONFIRMATIONCODE: '',
      PRESCREENID: preScreenId,
      MEMBERNUMBER: memberId,
      SOURCETYPE: sourceTypeInfo,
      SITECODE: siteCode,
      SECUREDDETAILS: encryptedCustomerInfo,
      KEYID: encryptionKeyId,
      SESSIONKEY: sessionKey,
      KEYVERSION: keyVersion,
      SIGNKEYVERSION: signKeyVersion,
      SIGNATURE: signature,
      SIGNKEYID: signatureKeyId,
    };
    hiddenFormPost(`${getBarclaysPostInUri(targetEnv)}/${offerId}`, requestPayload, 'post');
  } else {
    const { offerId, signature, SESSION, signatureKeyId } = response as BarclaysGuestPayload['response'];
    const requestPayload = {
      SESSIONSTATE: retUrl,
      CONFIRMATIONCODE: '',
      RETURL: postUrl,
      SESSION,
      SITECODE: siteCode,
      SOURCETYPE: sourceTypeInfo,
      SIGNATURE: signature,
      SIGNKEYID: signatureKeyId,
    };
    hiddenFormPost(`${getBarclaysPostInUri(targetEnv)}/${offerId}`, requestPayload, 'post');
  }
};

export const barclaysPostBackRedirect = (queryParams?: Record<string, string>): void => {
  const targetURL = queryParams?.targetURL;
  const barclaysLoyaltyStatus = queryParams?.barclaysLoyaltyStatus;
  if (targetURL) {
    const status: string | undefined = barclaysLoyaltyStatus?.toLowerCase() === 'banner' ? 'SUCCESS' : barclaysLoyaltyStatus?.toLowerCase();
    const reqUrl = `${targetURL}#barclaysOffer=${status || ''}`;
    const sanitizedAbsUrl = generateValidHostAndSanitize(reqUrl);
    window.location.href = sanitizedAbsUrl;
  } else {
    const { host } = window.location;
    window.location.href = `https://${host}`;
  }
};
