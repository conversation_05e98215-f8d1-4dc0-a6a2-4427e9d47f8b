import { clientFetch } from '@ecom-next/utils/clientFetch';
import { xApi } from '../xApi';

jest.mock('@ecom-next/utils/clientFetch');

type ErrorStatus = number[] | { status: number; userMessage?: string }[];

const BASE_API_PATH = '/my-account/xapi/v2' as const;
const ORDER_LOOKUP_PATH = '/my-account/order-lookup' as const;
const PAGE_UNAVAILABLE_PATH = '/my-account/page-unavailable' as const;
const SESSION_TIMEOUT_PATH = '/my-account/sign-in?errorCode=sessionTimeout&targetURL=' as const;

const getTimeoutRedirect = (): string => `${SESSION_TIMEOUT_PATH}${window.location.pathname}`;

describe('API', () => {
  describe('xApi request interceptor', () => {
    const path = '/create-account' as const;
    xit('should transform a delete request to a post and add method override headers', async () => {
      (clientFetch as jest.Mock).mockResolvedValue({ status: 200 });
      await xApi(path, { method: 'DELETE' });
      expect(clientFetch).toHaveBeenCalledWith(`${BASE_API_PATH}${path}`, {
        method: 'POST',
        timeout: 15000,
        headers: {
          'x-http-method-override': 'DELETE',
        },
      });
    });

    xit('should transform a put request to a post and add method override headers', async () => {
      (clientFetch as jest.Mock).mockResolvedValue({ status: 200 });
      await xApi(path, { method: 'PUT' });
      expect(clientFetch).toHaveBeenCalledWith(`${BASE_API_PATH}${path}`, {
        method: 'POST',
        timeout: 15000,
        headers: {
          'x-http-method-override': 'PUT',
        },
      });
    });

    xit('should not transform a get request or add method override headers', async () => {
      (clientFetch as jest.Mock).mockResolvedValue({ status: 200 });
      await xApi(path, { method: 'GET' });
      expect(clientFetch).toHaveBeenCalledWith(`${BASE_API_PATH}${path}`, {
        timeout: 15000,
        method: 'GET',
      });
    });

    xit('should not transform a post request or add method override headers', async () => {
      (clientFetch as jest.Mock).mockResolvedValue({ status: 200 });
      await xApi(path, { method: 'POST' });
      expect(clientFetch).toHaveBeenCalledWith(`${BASE_API_PATH}${path}`, {
        method: 'POST',
        timeout: 15000,
      });
    });
  });

  describe('xApi response interceptor success', () => {
    xit('should return the response when the promise is fulfilled and status is 200', async () => {
      const response = {
        status: 200,
        data: 'foo',
      };
      (clientFetch as jest.Mock).mockResolvedValue(response);
      const result = await xApi('/some-path', { method: 'GET' });
      expect(result).toBe(response);
    });
  });
});

describe('xApi response interceptor reset password 4xx, 5xx', () => {
  xit('should intercept reset password 4xx and 5xx errors and return a rejected promise or redirect', async () => {
    const path = '/my-account/reset-password' as const;
    const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
    Object.defineProperty(window, 'location', {
      configurable: true,
      enumerable: true,
      value: {
        ...location,
        protocol: 'https:',
        host: 'secure-www.gap.com',
        pathname: path,
      },
    });
    for (const status of errorStatuses) {
      const err = { status };
      (clientFetch as jest.Mock).mockRejectedValue(err);
      try {
        await xApi(path, { method: 'GET' });
      } catch (error) {
        if (status === 401) {
          expect(window.location.href).toEqual(getTimeoutRedirect());
        } else {
          expect(error).toMatchObject(err);
        }
      }
    }
  });

  describe('xApi response interceptor change password 4xx, 5xx', () => {
    xit('should intercept change password 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/change-password' as const;
      const errorStatuses: ErrorStatus = [
        { status: 400 },
        { status: 401 },
        { status: 401, userMessage: 'Old Password is not correct.' },
        { status: 404 },
        { status: 500 },
        { status: 502 },
        { status: 504 },
      ];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const err of errorStatuses) {
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (err.status === 401 && !err.userMessage) {
            expect(window.location.href).toEqual(getTimeoutRedirect());
          } else {
            expect(error).toMatchObject(err);
          }
        }
      }
    });
  });
  describe('xApi response interceptor shipping addresses 4xx, 5xx', () => {
    xit('should intercept shipping addresses 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/shipping-addresses' as const;
      const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const status of errorStatuses) {
        const err = { status };
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (status === 401) {
            expect(window.location.href).toEqual(getTimeoutRedirect());
          } else {
            expect(error).toMatchObject(err);
          }
        }
      }
    });
  });

  describe('xApi response interceptor account settings 4xx, 5xx', () => {
    xit('should intercept account settings 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/name-and-email' as const;
      const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const status of errorStatuses) {
        const err = { status };
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (status === 401) {
            expect(window.location.href).toEqual(getTimeoutRedirect());
          } else {
            expect(error).toMatchObject(err);
          }
        }
      }
    });
  });

  describe('xApi response interceptor shipping addresses 4xx, 5xx', () => {
    xit('should intercept shipping addresses 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/shipping-addresses' as const;
      const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const status of errorStatuses) {
        const err = { status };
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (status === 401) {
            expect(window.location.href).toEqual(getTimeoutRedirect());
          } else {
            expect(error).toMatchObject(err);
          }
        }
      }
    });
  });

  describe('xApi response interceptor account settings 4xx, 5xx', () => {
    xit('should intercept account settings 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/name-and-email' as const;
      const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const status of errorStatuses) {
        const err = { status };
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (status === 401) {
            expect(window.location.href).toEqual(getTimeoutRedirect());
          } else {
            expect(error).toMatchObject(err);
          }
        }
      }
    });
  });

  describe('xApi response interceptor order history 4xx, 5xx', () => {
    xit('should intercept order history 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/order-history' as const;
      const errorStatuses: ErrorStatus = [400, 401, 404, 500, 502, 504];
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
        },
      });
      for (const status of errorStatuses) {
        const err = { status };
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          if (status === 401) {
            expect(window.location.href).toEqual(ORDER_LOOKUP_PATH);
          } else {
            expect(window.location.href).toEqual(PAGE_UNAVAILABLE_PATH);
          }
        }
      }
    });
  });

  describe('xApi response interceptor order details 4xx, 5xx', () => {
    xit('should intercept order details 4xx and 5xx errors and return a rejected promise or redirect', async () => {
      const path = '/my-account/order-details/JTJNWU';
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
          href: 'https://secure-www.gap.com/my-account/order-details/JTJNWU',
        },
      });
      const errorResponses = [
        { status: 400, expectedRedirect: '/my-account/order-lookup?errorCode=globalMessage' },
        { status: 401, expectedRedirect: getTimeoutRedirect() },
        { status: 404, expectedRedirect: '/my-account/order-lookup?errorCode=globalMessage' },
        {
          status: 404,
          userMessage: 'Order Not Found 404',
          expectedRedirect: `${ORDER_LOOKUP_PATH}?errorCode=orderNotFound`,
        },
        { status: 500, expectedRedirect: PAGE_UNAVAILABLE_PATH },
        { status: 502, expectedRedirect: PAGE_UNAVAILABLE_PATH },
        { status: 504, expectedRedirect: PAGE_UNAVAILABLE_PATH },
      ];
      for (const err of errorResponses) {
        (clientFetch as jest.Mock).mockRejectedValue(err);
        try {
          await xApi(path, { method: 'GET' });
        } catch (error) {
          expect(window.location.href).toEqual(err.expectedRedirect);
        }
      }
    });

    xit('should intercept order details 404 and pass url params: tid / EV / DI / CD / mweb, into the order-lookup redirect', async () => {
      const err = { status: 404 };
      const path = '/my-account/order-details/1234567' as const;
      const search = '?tid=111&ev=222&di=333&cd=444&mweb=555';
      (clientFetch as jest.Mock).mockRejectedValue(err);
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
          search,
        },
      });
      try {
        await xApi(path, { method: 'GET' });
      } catch (error) {
        expect(window.location.href).toEqual('/my-account/order-lookup?errorCode=globalMessage&tid=111&EV=222&DI=333&CD=444&mweb=555');
      }
    });

    xit('check url params for lower and uppercase', async () => {
      const err = { status: 404 };
      const path = '/my-account/order-details/1234567' as const;
      const search = '?tId=1&ev=2&DI=3&cD=4&mWeb=5&';
      (clientFetch as jest.Mock).mockRejectedValue(err);
      Object.defineProperty(window, 'location', {
        configurable: true,
        enumerable: true,
        value: {
          ...location,
          protocol: 'https:',
          host: 'secure-www.gap.com',
          pathname: path,
          search,
        },
      });
      try {
        await xApi(path, { method: 'GET' });
      } catch (error) {
        expect(window.location.href).toEqual('/my-account/order-lookup?errorCode=globalMessage&tid=1&EV=2&DI=3&CD=4&mweb=5');
      }
    });
  });
});
