{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "dist",
    "target": "ESNext",
    "declaration": true,
    "resolveJsonModule": true,
    "declarationDir": "dist/types",
    "paths": {
      "@/*": ["./*"],
      "@mui/*": ["../marketing-ui/src/*"],
      "@ecom-next/core/*": ["../core/src/*"],
      "@find/*": ["./src/*"],
      "test-utils": ["../core/tests/test-utils/index.tsx"],
      "@mfe-api-types/*": ["../../types/generated/*"]
    }
  },
  "include": ["src"],
}
