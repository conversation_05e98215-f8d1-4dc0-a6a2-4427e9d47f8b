'use client';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { ProductBreadcrumbs } from './ProductBreadcrumbs';

interface BreadcrumbProps {
  breadcrumbs: BreadCrumbs;
  isBottom?: boolean;
}

export const BreadcrumbComponent = (props: BreadcrumbProps): JSX.Element => {
  const { breadcrumbs, isBottom = false } = props;
  const { brand } = usePageContext();
  const showOnlyBottombreadcrumbsForBR = brand === 'br' || brand === 'brfs';
  const isShowBottomBreadcrums = !showOnlyBottombreadcrumbsForBR || isBottom;

  return <>{isShowBottomBreadcrums && <ProductBreadcrumbs breadcrumbs={breadcrumbs} />}</>;
};
