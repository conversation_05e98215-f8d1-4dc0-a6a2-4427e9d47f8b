import { render } from '@testing-library/react';
import { Locale } from '@ecom-next/sitewide/localization-provider';
import { Market, usePLPState } from '@ecom-next/plp-ufa';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { ProductList } from '../index';

interface ContentData {
  brand: string | null;
  category: string | null;
  contentType: string | null;
  country: string | null;
  data: unknown | null;
  division: string | null;
  'meta.description': string | null;
  'meta.title.override': string | null;
  sitewide: unknown | null;
}

interface ProductListProps {
  abbrBrand: string;
  categoryName: string;
  contentData: ContentData;
  locale: Locale;
  market: Market;
  params: Record<string, unknown>;
  selectedNodes: { name: string; type: string }[];
  subcategoryName: string;
}

jest.mock('@ecom-next/plp-ufa', () => ({
  usePLPState: jest.fn(),
  translations: {
    translationObjects: {
      en_US: {
        translation: { someKey: 'someValue' },
      },
    },
  },
  Grid: () => <div>Grid</div>,
  GridHeader: () => <div>GridHeader</div>,
  CategoryBannerContainer: () => <div>CategoryBannerContainer</div>,
  SearchTextHeader: ({ searchText }: { searchText: string }) => <div>{searchText}</div>,
}));

jest.mock('@ecom-next/sitewide/app-state-provider', () => ({
  useAppState: jest.fn(),
}));

describe('ProductList', () => {
  const mockUsePLPState = usePLPState as jest.Mock;
  const mockUseAppState = useAppState as jest.Mock;
  const defaultProps = {
    categoryName: 'Suits',
    contentData: {
      brand: 'br',
      category: 'cat1',
      contentType: 'ebb',
      country: 'us',
      data: null,
      division: null,
      'meta.description': null,
      'meta.title.override': null,
      sitewide: null,
    },
    params: {},
    selectedNodes: [],
    subcategoryName: '',
    abbrBrand: 'br',
    market: 'us' as Market,
    locale: 'en_US' as Locale,
  } as ProductListProps;

  beforeEach(() => {
    mockUsePLPState.mockReturnValue({
      searchText: 'Search Text',
      pageType: 'category',
    });

    mockUseAppState.mockReturnValue({
      locale: 'en_US',
      market: 'us',
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders CategoryBannerContainer when pageType is "category"', () => {
    const { getByText } = render(<ProductList categoryName='Category' subcategoryName='Subcategory' />);

    expect(getByText('CategoryBannerContainer')).toBeInTheDocument();
  });

  it('does not render CategoryBannerContainer when pageType is not "category"', () => {
    mockUsePLPState.mockReturnValueOnce({
      searchText: 'Search Text',
      pageType: 'search',
    });

    const { queryByText } = render(<ProductList {...defaultProps} />);

    expect(queryByText('CategoryBannerContainer')).not.toBeInTheDocument();
  });

  it('renders SearchTextHeader with the correct searchText', () => {
    const { getByText } = render(<ProductList {...defaultProps} />);

    expect(getByText('Search Text')).toBeInTheDocument();
  });

  it('renders Grid and GridHeader', () => {
    const { getByText } = render(<ProductList {...defaultProps} />);

    expect(getByText('Grid')).toBeInTheDocument();
    expect(getByText('GridHeader')).toBeInTheDocument();
  });
});
