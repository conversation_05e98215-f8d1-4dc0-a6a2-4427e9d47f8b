@import '../components/grid-drawer/styles/GridDrawer.on.css';
@import '../item-count/styles/ItemCount.on.css';

.plp_grid-header-filter-icon {
  transform: rotate(90deg);
  display: inline-block;
}

/* 
Override for 
./components/facet-bar/components/facet-selectors/bopis-selector/styles.css
*/
.plp_filter-drawer-panel__title {
  font-weight: 600;
}

.plp_grid-header__filters-button-text {
  line-height: normal;
}

.plp_grid-header__items-count {
  color: theme('colors.color-type-link', #003764);
  font-size: theme('fontSize.font-size--2', 10px);
  font-family: theme('fontFamily.font-family-base'), 'ON Sans Text', serif;
  font-weight: theme('fontWeight.font-weight-base-heavier', 400);
}
