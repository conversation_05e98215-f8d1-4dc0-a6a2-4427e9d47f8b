import { render, screen } from '@testing-library/react';
import { GridHeaderPlaceholder } from '../GridHeaderPlaceholder';

describe('GridHeaderPlaceholder', () => {
  it('should render the grid header placeholder container', () => {
    render(<GridHeaderPlaceholder />);

    const gridHeader = screen.getByTestId('plp-grid-header-placeholder');
    expect(gridHeader).toBeInTheDocument();
  });

  it('should render the filters section with placeholders', () => {
    render(<GridHeaderPlaceholder />);

    const filtersSection = screen.getByTestId('plp-grid-header-filters');
    expect(filtersSection).toBeInTheDocument();

    const filterPlaceholders = filtersSection.querySelectorAll('.plp_grid-header__filters-placeholder');
    expect(filterPlaceholders.length).toBe(4);
  });

  it('should render the chips and clear all placeholder', () => {
    render(<GridHeaderPlaceholder />);

    const { container } = render(<GridHeaderPlaceholder />);
    const chipsAndClearAllPlaceholder = container.querySelector('.plp_chips-and-clear-all-placeholder');

    expect(chipsAndClearAllPlaceholder).toBeInTheDocument();
  });

  it('should render the items count placeholder', () => {
    render(<GridHeaderPlaceholder />);

    const { container } = render(<GridHeaderPlaceholder />);
    const itemsCountPlaceholder = container.querySelector('.plp_grid-header__items-count-placeholder');

    expect(itemsCountPlaceholder).toBeInTheDocument();
  });
});
