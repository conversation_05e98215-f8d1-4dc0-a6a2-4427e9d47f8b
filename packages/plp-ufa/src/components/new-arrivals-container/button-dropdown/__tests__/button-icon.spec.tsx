import { render } from '@testing-library/react';
import { usePLPState } from '@ecom-next/plp-ufa';
import { PlusIcon, MinusIcon } from '@ecom-next/core/legacy/icons';
import { ButtonIcon } from '../components/button-icon';

jest.mock('@ecom-next/plp-ufa');
jest.mock('@ecom-next/core/legacy/icons');
describe('ButtonIcon', () => {
  const mockUsePLPState = usePLPState as jest.Mock;
  const mockPlusIcon = PlusIcon as jest.Mock;
  const mockMinusIcon = MinusIcon as jest.Mock;
  beforeEach(() => {
    mockUsePLPState.mockReturnValue({
      brand: 'at',
    });
    mockPlusIcon.mockReturnValue(<div data-testid='PlusIcon' />);
    mockMinusIcon.mockReturnValue(<div data-testid='MinusIcon' />);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it.each`
    isExpanded | brand   | expectedIcon
    ${true}    | ${'at'} | ${mockMinusIcon}
    ${false}   | ${'at'} | ${mockPlusIcon}
    ${true}    | ${'on'} | ${mockMinusIcon}
    ${false}   | ${'on'} | ${mockPlusIcon}
  `('should render $expectedIcon when isExpanded is $isExpanded and brand is $brand', ({ isExpanded, brand, expectedIcon }) => {
    mockUsePLPState.mockReturnValue({
      brand,
    });
    render(<ButtonIcon isExpanded={isExpanded} />);
    expect(expectedIcon).toHaveBeenCalledWith(
      expect.objectContaining({
        className: 'plp_search__new-arrivals-section-mobile-toggle-icon',
        fillColor: 'inherit',
        size: {
          height: isExpanded ? '5px' : '10px',
          width: '10px',
        },
      }),
      {}
    );
  });
  it.each`
    isExpanded | brand      | expectedIcon
    ${true}    | ${'gap'}   | ${'-'}
    ${false}   | ${'gap'}   | ${'+'}
    ${true}    | ${'gapfs'} | ${'-'}
    ${false}   | ${'gapfs'} | ${'+'}
    ${true}    | ${'br'}    | ${'-'}
    ${false}   | ${'br'}    | ${'+'}
    ${true}    | ${'brfs'}  | ${'-'}
    ${false}   | ${'brfs'}  | ${'+'}
  `('should render $expectedIcon when isExpanded is $isExpanded and brand is $brand', ({ isExpanded, brand, expectedIcon }) => {
    mockUsePLPState.mockReturnValue({
      brand,
    });
    const { container } = render(<ButtonIcon isExpanded={isExpanded} />);
    expect(container.innerHTML).toContain(expectedIcon);
    expect(container.querySelector('span')).toHaveClass('plp_search__new-arrivals-section-mobile-glyph-icon');
    expect(container.querySelector('span')).toHaveAttribute('aria-hidden', 'true');
  });
});
