.plp_related-categories {
  &--container {
    .tag-link-group {
      margin-right: 0;
      margin-left: 0;
      padding-left: 4rem;
      padding-right: 4rem;
      @media (min-width: theme('screens.xl')) {
        max-width: 100%;
      }
      @media (max-width: theme('screens.md')) {
        max-width: 100%;
        padding-left: 0;
        padding-right: 0;
      }
      [data-testid='tag-link-group-heading'],
      [data-testid='tag-link-group-footer'] > button {
        color: theme('colors.bk');
      }
    }
    .tag-link-group__text {
      color: theme('colors.b1');
      background-color: theme('colors.inverse.b2');
      &:hover {
        color: theme('colors.wh');
        background-color: theme('colors.inverse.g2');
      }
    }
  }
}
