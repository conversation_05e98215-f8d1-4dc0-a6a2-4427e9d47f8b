import { renderHook } from 'test-utils';
import { usePLPState } from '../../../data';
import { useStarRatingsWithFacets } from '../star-ratings-hook';

jest.mock('../../../data');

describe('useStarRatings', () => {
  it('should return true if correct values are informed - category', () => {
    (usePLPState as jest.Mock).mockReturnValue({ pageType: 'category', brand: 'gap', abSeg: { gap227: 'a' }, enabledFeatures: { 'plp-star-ratings': true } });
    const { result } = renderHook(() => useStarRatingsWithFacets());
    expect(result.current).toBe(true);
  });

  it('should return true if correct values are informed - search', () => {
    (usePLPState as jest.Mock).mockReturnValue({ pageType: 'search', brand: 'gap', abSeg: { gap228: 'a' }, enabledFeatures: { 'plp-star-ratings': true } });
    const { result } = renderHook(() => useStarRatingsWithFacets());
    expect(result.current).toBe(true);
  });
});
