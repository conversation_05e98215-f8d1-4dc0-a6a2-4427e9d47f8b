import { renderHook } from '@testing-library/react';
import { useModelSize } from '../model-size-hook';
import { useExperimentWithVariables } from '../../features';
import { usePLPState } from '../../../data';

const translations = {
  'model_toggle.model_size_text': 'Model Size',
  'model_toggle.model_size_all_text': 'All Model Sizes',
  'model_toggle.size_model_small': 'S',
  'model_toggle.size_model_large': 'L',
};

jest.mock('../../features');
jest.mock('../../../data');
jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: () => ({
    localize: (key: keyof typeof translations) => translations[key],
  }),
}));

describe('useModelSize', () => {
  beforeEach(() => {
    (useExperimentWithVariables as jest.Mock).mockReturnValue({
      isExperimentActive: true,
      experimentVariables: {
        'gap-us-alphanumeric-model-size-cids': '1',
        'gap-us-alpha-model-size-cids': '2',
        'gap-us-model-size-config': {
          S: { placement: 'p2', numeric: '4 (27)', alpha: 'model_toggle.size_model_small' },
          M: { placement: 'av4', numeric: '14 (32)', alpha: 'model_toggle.size_model_large' },
        },
      },
    });
  });

  it('should return correct values for alphanumeric configured cid', () => {
    (usePLPState as jest.Mock).mockReturnValue({ cid: '1', brand: 'gap', market: 'us' });
    const { result } = renderHook(() => useModelSize());
    expect(result.current.isExperimentActive).toBe(true);
    expect(result.current.stringOptions).toStrictEqual(['4 (S)', '14 (L)']);
  });

  it('should return correct values for alpha configured cid', () => {
    (usePLPState as jest.Mock).mockReturnValue({ cid: '2', brand: 'gap', market: 'us' });
    const { result } = renderHook(() => useModelSize());
    expect(result.current.isExperimentActive).toBe(true);
    expect(result.current.stringOptions).toStrictEqual(['S', 'L']);
  });

  it('should return correct values for non configured cid', () => {
    (usePLPState as jest.Mock).mockReturnValue({ cid: '3', brand: 'gap', market: 'us' });
    const { result } = renderHook(() => useModelSize());
    expect(result.current.isExperimentActive).toBe(true);
    expect(result.current.stringOptions).toStrictEqual(['4 (27)', '14 (32)']);
  });

  it('should return default values if none is configured', () => {
    (usePLPState as jest.Mock).mockReturnValue({ cid: '999', brand: 'gap', market: 'us' });
    (useExperimentWithVariables as jest.Mock).mockReturnValue({
      isExperimentActive: true,
      experimentVariables: {
        'gap-us-model-size-config': {
          S: {},
        },
      },
    });

    const { result } = renderHook(() => useModelSize());
    expect(result.current.stringOptions).toStrictEqual([]);
  });

  it('should return default values if empty', () => {
    (usePLPState as jest.Mock).mockReturnValue({ cid: '999', brand: 'gap', market: 'us' });
    (useExperimentWithVariables as jest.Mock).mockReturnValue({
      isExperimentActive: true,
      experimentVariables: {},
    });

    const { result } = renderHook(() => useModelSize());
    expect(result.current.stringOptions).toStrictEqual([]);
  });
});
