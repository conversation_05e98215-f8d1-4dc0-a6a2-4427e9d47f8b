import { usePLPState } from '../../data';
import { useExperiment } from '../features';
import { EXP_STAR_RATINGS_CAT, EXP_STAR_RATINGS_SEARCH, FF_STAR_RATINGS } from '../glossary';

export const useStarRatingsWithFacets = (): boolean => {
  const { pageType } = usePLPState();
  const experimentId = pageType === 'category' ? EXP_STAR_RATINGS_CAT : EXP_STAR_RATINGS_SEARCH;
  const { isExperimentActive } = useExperiment(FF_STAR_RATINGS, experimentId);

  return isExperimentActive;
};
