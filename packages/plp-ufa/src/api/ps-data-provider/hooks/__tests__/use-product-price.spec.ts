import { renderHook } from '@testing-library/react-hooks';
import { useProductPrice } from '../use-product-price';
import { usePsDataContext } from '../../context/PsDataContext';

jest.mock('../../context/PsDataContext');

describe('useProductPrice', () => {
  it('should return product price data and isSuccess as true when context is successful', () => {
    const mockContext = {
      state: {
        isSuccess: true,
        data: {
          productPrices: {
            '123': { price: 100, currency: 'USD' },
          },
        },
      },
    };

    (usePsDataContext as jest.Mock).mockReturnValue(mockContext);

    const { result } = renderHook(() => useProductPrice('123'));

    expect(result.current).toEqual({
      price: 100,
      currency: 'USD',
      isSuccess: true,
    });
  });

  it('should return undefined product price data and isSuccess as false when context is not successful', () => {
    const mockContext = {
      state: {
        isSuccess: false,
        data: {
          productPrices: {},
        },
      },
    };

    (usePsDataContext as jest.Mock).mockReturnValue(mockContext);

    const { result } = renderHook(() => useProductPrice('123'));

    expect(result.current).toEqual({
      isSuccess: false,
    });
  });

  it('should return undefined product price data and isSuccess as false when context is null', () => {
    (usePsDataContext as jest.Mock).mockReturnValue(null);

    const { result } = renderHook(() => useProductPrice('123'));

    expect(result.current).toEqual({
      isSuccess: false,
    });
  });

  it('should return undefined product price data and isSuccess as false when productId does not exist', () => {
    const mockContext = {
      state: {
        isSuccess: true,
        data: {
          productPrices: {},
        },
      },
    };

    (usePsDataContext as jest.Mock).mockReturnValue(mockContext);

    const { result } = renderHook(() => useProductPrice('999'));

    expect(result.current).toEqual({
      isSuccess: true,
    });
  });
});
