const okList: Record<string, string[]> = {
  sortByField: ['price', 'new', 'bestsellers', 'reviewScore'],
  sortByDir: ['asc', 'desc'],
};

const sanitize = ([key, value]: [string, string]) => okList[key]?.includes(value) || /^[0-9,|-]+$/.test(value);

export const overrideFromHash = () => {
  const newParams = new URLSearchParams(window.location.hash.replace('#', ''));
  const hashKeyValues = Object.entries(Object.fromEntries(newParams.entries()));
  // Here we do not want to push the hash to the URL again, as it is already there
  return hashKeyValues.filter(sanitize).reduce(
    (appliedFacets, [key, value]) => ({
      ...appliedFacets,
      [key]: value.split(',').map(id => ({ id, name: '', applied: true })),
    }),
    {}
  );
};
