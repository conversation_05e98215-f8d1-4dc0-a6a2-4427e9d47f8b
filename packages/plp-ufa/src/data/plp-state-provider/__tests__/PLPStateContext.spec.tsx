import { usePLPState } from '../PLPStateContext';

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn(() => {
    return null;
  }),
}));

describe('PLPStateContext', () => {
  it('should throw an error if used outside of PLPStateProvider', () => {
    expect(() => usePLPState()).toThrowWithMessage(Error, 'usePLPState must be used within a PLPStateProvider');
  });
});
