import React from 'react';
import { Button } from '@ecom-next/core/migration/button';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useActiveBagContext } from '../../../../contexts/ActiveBagProvider';
import { useEnabledFeatures } from '../../../../hooks/useEnabledFeatures';
import { useDatalayer } from '../../../../hooks/useDataLayer';
import { UserStatus } from '../../../../types/bag-types';

type RemoveButtonProps = {
  itemId: string;
  setIsRemoved?: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
};

export const RemoveButton = ({ itemId, title, setIsRemoved }: RemoveButtonProps) => {
  const {
    outofStockCarouselState,
    asyncDispatch,
    bagState: { data },
  } = useActiveBagContext();
  const { publishDataLayerEvent, getRecognitionStatus } = useDatalayer();
  const setIsOOSActive = outofStockCarouselState?.setIsOOSActive;
  const { localize } = useLocalize();
  const { enabledFeatures } = useEnabledFeatures();
  const isOutofStockV2Enabled = enabledFeatures['bag-ui-oos-v2'];
  const recognition_status = getRecognitionStatus(data?.bagAttributes?.userStatus as UserStatus);

  const itemDelete = async (item: string, productName: string) => {
    await asyncDispatch({
      action: 'DELETE_BAG_ITEM',
      requestPayload: {
        lineItemId: item,
        productName: productName,
        bagType: 'SAVED',
        outofstockitem: true,
      },
    });
  };

  const handleClick = () => {
    setIsRemoved?.(true);
    setIsOOSActive?.(false);
    isOutofStockV2Enabled && itemDelete(itemId, title);
    publishDataLayerEvent({
      type: 'link',
      name: 'oos_item_remove',
      source: 'ShoppingBag',
      extraAttrs: { recognition_status },
    });
  };

  return (
    <div
      className='cursor-pointer px-1 py-2.5'
      onClick={handleClick}
      role='button'
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick();
        }
      }}
    >
      <Button aria-label={`Remove ${title}`} id='oos-remove-button' kind='text-underline-small' className='cb-base-compact flex font-normal'>
        {localize('product.remove')}
      </Button>
    </div>
  );
};
