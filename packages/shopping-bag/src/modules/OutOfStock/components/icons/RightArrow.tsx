import React from 'react';

export const RightArrow = ({onClick, className}: {className?: string, onClick?: React.MouseEventHandler}) => {
  return (
    <button className={`slick-next ${className}`} style={{transform: "none"}} onClick={onClick}>
      <svg xmlns="http://www.w3.org/2000/svg" width="59" height="61" viewBox="0 0 59 61" fill="none">
        <g opacity="0.75" filter="url(#filter0_d_2202_1963)">
          <circle cx="30.5" cy="30.9004" r="22" fill="white" />
        </g>
        <path fillRule="evenodd" clipRule="evenodd" d="M35.3522 31.6043C35.7445 31.2155 35.7445 30.5852 35.3522 30.1965C35.3521 30.1964 35.352 30.1963 35.3519 30.1962L28.9596 23.8613C28.5673 23.4726 27.9313 23.4726 27.539 23.8613C27.1467 24.2501 27.1467 24.8804 27.539 25.2691L33.2214 30.9004L27.539 36.5316C27.1467 36.9204 27.1467 37.5507 27.539 37.9395C27.9313 38.3282 28.5673 38.3282 28.9596 37.9395L35.3522 31.6043Z" fill="#020202" />
        <defs>
          <filter id="filter0_d_2202_1963" x="0.5" y="0.900391" width="60" height="60" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="4" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2202_1963" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2202_1963" result="shape" />
          </filter>
        </defs>
      </svg>
    </button>
  )
};
