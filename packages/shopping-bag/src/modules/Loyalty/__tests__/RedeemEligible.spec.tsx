import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react';
import { render } from '../../../utils/test-utils';
import { Panel } from '../../../components/Panel/Panel';
import { Rewards, type RewardsProps } from '../Rewards';

jest.mock('@ecom-next/utils/server', () => ({
  getPageContext: jest.fn().mockReturnValue({
    locale: 'en-US',
  }),
}));

const Component = (props: RewardsProps) => {
  return (
    <Panel>
      <Panel.Title>Points & Rewards</Panel.Title>
      <Panel.Body>
        <Rewards {...props} />
      </Panel.Body>
    </Panel>
  );
};

describe('AuthenticatedUser - un-redeemed points flow', () => {
  const mockApplyRewards = jest.fn();
  const mockRemoveRewards = jest.fn();

  const mockDetails = {
    activePoints: 100,
    pointsToValueList: [
      { amount: 1, points: 100 },
      { amount: 2, points: 200 },
      { amount: 3, points: 300 },
    ],
    minPoints: 100,
  };

  const mockProps = {
    applyRewards: mockApplyRewards,
    removeRewards: mockRemoveRewards,
    details: mockDetails,
    isAuthenticated: true,
    isMTLMember: true,
    hasAppliedRewards: false,
    appliedPoints: 0,
    appliedAmount: 0,
    hasError: false,
    merchandiseSubTotal: 100,
    notificationErrorMessage: '',
  };

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('should show error message when redeem fails', () => {
    const { getByText } = render(<Component {...mockProps} hasError />);
    expect(getByText('Unable to redeem rewards. Please try again.')).toBeInTheDocument();
  });

  it('renders Points Balance header', () => {
    const { getByText } = render(<Component {...mockProps} />);
    expect(getByText('Points Balance: 100 points')).toBeInTheDocument();
  });

  it('renders dropdown and option value on select', async () => {
    const { container, getByRole } = render(<Component {...mockProps} />);

    const initialDropdownSelection = getByRole('button', { name: 'Rewards $1 (100 points)' });
    expect(initialDropdownSelection).toBeInTheDocument();

    const dropdownButton = container.querySelector('svg') as Element;
    expect(dropdownButton).toBeInTheDocument();

    fireEvent.click(dropdownButton);

    const option = container.querySelector('#dropdown-option1');
    expect(option).toBeInTheDocument();

    if (option) {
      fireEvent.click(option);
    }

    const dropdownSelection = getByRole('button', { name: 'Rewards $2 (200 points)' });
    expect(dropdownSelection).toBeInTheDocument();
  });

  it('calls applyRewards when button is clicked', async () => {
    const { getByText } = render(<Component {...mockProps} />);

    const redeemButton = getByText('Redeem');
    expect(redeemButton).toBeInTheDocument();

    fireEvent.click(redeemButton);

    await waitFor(() => {
      expect(mockProps.applyRewards).toHaveBeenCalled();
    });
  });

  it('does not call applyRewards when button is clicked if applied amount is 0', async () => {
    const { getByText, getByRole, container } = render(
      <Component
        {...{
          ...mockProps,
          details: {
            ...mockDetails,
            pointsToValueList: [
              { amount: 1, points: 100 },
              { amount: 0, points: 0 },
            ],
          },
        }}
      />
    );

    const initialDropdownSelection = getByRole('button', { name: 'Rewards $1 (100 points)' });
    expect(initialDropdownSelection).toBeInTheDocument();

    const dropdownButton = container.querySelector('svg') as Element;
    expect(dropdownButton).toBeInTheDocument();

    fireEvent.click(dropdownButton);

    const option = container.querySelector('#dropdown-option1');
    await waitFor(() => expect(option).toBeInTheDocument());

    if (option) {
      fireEvent.click(option);
    }

    const redeemButton = getByText('Redeem');
    expect(redeemButton).toBeInTheDocument();

    fireEvent.click(redeemButton);

    await waitFor(() => {
      expect(getByText('Unable to redeem rewards. Please try again.')).toBeInTheDocument();
      expect(mockProps.applyRewards).not.toHaveBeenCalled();
    });
  });

  it('disables reward button if points only options is 0', async () => {
    const { getByText } = render(<Component {...{ ...mockProps, details: { ...mockDetails, pointsToValueList: [{ amount: 0, points: 0 }] } }} />);

    const redeemButton = getByText('Redeem');
    expect(redeemButton).toBeInTheDocument();
    expect(redeemButton).toBeDisabled();
  });

  it('should show rewards notification error banner when error code is 6566', () => {
    mockProps.notificationErrorMessage =
      'The new subtotal is less than the amount of rewards applied so they have been removed. Please reapply your rewards accordingly.';
    const { getByText } = render(<Component {...mockProps} hasError />);
    expect(
      getByText('The new subtotal is less than the amount of rewards applied so they have been removed. Please reapply your rewards accordingly.')
    ).toBeInTheDocument();
  });
});

describe('AuthenticatedUser - redeemed points flow', () => {
  const mockApplyRewards = jest.fn();
  const mockRemoveRewards = jest.fn();

  const mockDetailsRedeemed = {
    activePoints: 300,
    pointsToValueList: [
      { amount: 3, points: 300 },
      { amount: 2, points: 200 },
      { amount: 1, points: 100 },
    ],
    minPoints: 100,
  };

  const mockProps = {
    applyRewards: mockApplyRewards,
    removeRewards: mockRemoveRewards,
    details: mockDetailsRedeemed,
    isAuthenticated: true,
    isMTLMember: true,
    hasAppliedRewards: true,
    appliedPoints: 100,
    appliedAmount: 1,
    hasError: false,
    merchandiseSubTotal: 100,
  };

  it('should show error message when remove rewards fails', () => {
    const { getByText } = render(<Component {...mockProps} hasError />);
    expect(getByText('Unable to remove rewards. Please try again.')).toBeInTheDocument();
  });

  it('renders Remaining Balance header', () => {
    const { getByText } = render(<Component {...mockProps} />);
    expect(getByText('Remaining Balance: 200 points')).toBeInTheDocument();
  });

  it('renders Rewards Redeemed', () => {
    const { getByText } = render(<Component {...mockProps} />);
    expect(getByText('Rewards Redeemed')).toBeInTheDocument();
  });

  it('renders Amount Redeemed', () => {
    const { getByText } = render(<Component {...mockProps} />);
    expect(getByText('$1.00')).toBeInTheDocument();
  });

  it('calls removeRewards when button is clicked', async () => {
    const { container } = render(<Component {...mockProps} />);

    const removeButton = container.querySelector('svg') as Element;
    expect(removeButton).toBeInTheDocument();

    fireEvent.click(removeButton);

    await waitFor(() => {
      expect(mockProps.removeRewards).toHaveBeenCalled();
    });
  });

  it('renders Popover Details', () => {
    const { queryByText, getByTestId } = render(<Component {...mockProps} />);
    const button = getByTestId('popover-trigger');
    expect(button).toBeInTheDocument();

    expect(
      queryByText(
        "Points can only be applied to merchandise (not gift cards, taxes, or shipping or handling). Rewards redeemed can't exceed the subtotal of your order (before tax and shipping). Rewards are redeemed in 100-point increments."
      )
    ).not.toBeInTheDocument();
    fireEvent.click(button);

    expect(
      queryByText(
        "Points can only be applied to merchandise (not gift cards, taxes, or shipping or handling). Rewards redeemed can't exceed the subtotal of your order (before tax and shipping). Rewards are redeemed in 100-point increments."
      )
    ).toBeInTheDocument();
  });
});
