import { useLocalize } from '@sitewide/providers/localization';
import { PopoverWrapper } from './PopoverWrapper';
import { RewardsProps } from './Rewards';

export type RedeemInfoProps = Pick<RewardsProps['details'], 'activePoints' | 'minPoints'> & {
  merchandiseSubTotal: number
};
export const RedeemInfo = ({ activePoints, minPoints, merchandiseSubTotal }: RedeemInfoProps) => {
  const { localize } = useLocalize();

  return (
    <>
      <p className='mb-2'>
        <strong>{localize('loyalty.lessPoints', { points: minPoints - activePoints })}</strong>
      </p>
      <p>
        {localize('loyalty.rewardsBalance')}: <strong>$0</strong> {localize('loyalty.points', { points: activePoints })}
      </p>
      {merchandiseSubTotal ? <PopoverWrapper /> : <div className='cb-special-sm text-g2'>{localize('loyalty.nonMerchandiseText')}</div>}
    </>
  );
};
