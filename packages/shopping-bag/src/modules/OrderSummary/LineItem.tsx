'use client';

import React, { ReactNode } from 'react';
import { Price } from '../../utils/Price';

export type LineItemProps = {
  currencySymbol: string;
  customPrice?: string | number;
  id?: string;
  isNegative?: boolean;
  label: ReactNode;
  value?: string | number;
};

export const LineItem = (props: LineItemProps) => {
  const { currencySymbol, customPrice, label, id, isNegative, value } = props;

  return (
    <div className='pb-2 inline-flex justify-between'>
      <span id={`${id}-label`} data-testid={`${id}-label`}>
        {label}
      </span>
      <span className='float-right' id={`${id}-value`} data-testid={`${id}-value`}>
        {isNegative && '-'}
        <Price amount={Number(value)} currency={currencySymbol} customPrice={customPrice} />
      </span>
    </div>
  );
};
