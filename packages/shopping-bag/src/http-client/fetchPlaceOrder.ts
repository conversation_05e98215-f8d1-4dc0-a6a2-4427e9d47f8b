import { clientFetch } from '@ecom-next/utils/clientFetch';
import { PLACE_ORDER } from '../utils/constants';
import { GetHeadersResponse } from '../utils/getHeaders';
import type { CheckoutPayload } from '../types/payment-types';

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export const fetchPlaceOrder = async ({
  checkoutPayload,
  headers,
  timeout,
}: {
  checkoutPayload: DeepPartial<CheckoutPayload>;
  headers: GetHeadersResponse;
  timeout?: number;
}) => {
  const response = await clientFetch<{ link: string; orderNumber: string }>(PLACE_ORDER, {
    method: 'POST',
    body: JSON.stringify({
      ...checkoutPayload,
      orderDeviceId: document.getElementById('order_device_id')?.getAttribute('value') || '',
      originatingClientApplication: 'BAG-UI',
    }),
    headers,
    timeout,
  });
  return response;
};
