import React from 'react';
import { render } from '../../../utils/test-utils';
import { EmptySavedBag } from '../EmptySavedBag';

jest.mock('../../../contexts/ActiveBagProvider', () => ({
  useActiveBagContext: () => ({
    bagState: {
      data: {
        bagAttributes: {
          bagItemsCount: {
            pickUp: 0,
            ship: 1,
          },
        },
        promos: {
          appliedPromotions: [
            {
              code: 'BAGUIUS001',
              promoDescription: 'Get 50% off your order!',
            },
          ],
          promoCount: 1,
        },
        currencySymbol: '$',
        savedList: [],
        productList: [
          {
            size: 'S',
            id: 'mockId',
            quantity: 2,
            productColor: 'Black',
            productName: 'High Rise Cheeky Straight Jeans',
            imageUrl: 'https://www1.assets-gap.com/webcontent/0052/199/490/cn52199490.jpg',
            markdownPrice: 16.99,
            regularPrice: 24.95,
            totalItemSavings: 7.96,
            productFlags: [
              { message: 'RETURN_BY_MAIL', type: 'informational', date: '' },
              { message: 'NO_RETURNS', type: 'informational', date: '' },
            ],
            promotions: [
              {
                code: 'BAGUIUS001',
                description: 'BAGUIUS001',
                isAutoApply: false,
              },
            ],
          },
        ],
        shipping: {
          isQualifiedForFreeShipping: true,
        },
        summaryOfCharges: {
          baseTotal: 100,
          myTotal: 90,
          savingsSummary: {
            markdownAndPromoSavings: 10,
            rewardSavings: 0,
          },
        },
      },
    },
    asyncDispatch: jest.fn(),
  }),
}));

describe('<EmptySavedBag />', () => {
  it('Should render Empty Saved for later section for Authenticated user', () => {
    const { container, getByText } = render(<EmptySavedBag isAuthenticated />);
    expect(container.innerHTML).toBeTruthy();
    expect(getByText('Saved For Later')).toBeInTheDocument();
    expect(getByText('(0 Items)')).toBeInTheDocument();
    expect(getByText('Items you save for later will be kept here.')).toBeInTheDocument();
  });

  it('Should render Empty Saved for later section for Non-Authenticated user', () => {
    const { container, getByText } = render(<EmptySavedBag isAuthenticated={false} />);
    expect(container.innerHTML).toBeTruthy();
    expect(getByText('Saved For Later')).toBeInTheDocument();
    expect(getByText('(0 Items)')).toBeInTheDocument();
    expect(getByText('Sign in')).toBeInTheDocument();
    expect(getByText('to see what you may have saved before.')).toBeInTheDocument();
  });
});
