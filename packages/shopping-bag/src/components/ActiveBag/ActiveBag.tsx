'use client';

import { useEffect, useState } from 'react';
import classNames from 'classnames';
import { useBagContext } from '@ecom-next/shopping-bag/contexts/ShoppingBagProvider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useBreakpoint } from '../../hooks/useBreakPoint';
import { SnackbarData } from '../../types/bag-types';
import { useActiveBagContext } from '../../contexts/ActiveBagProvider';
import GlobalNotification from '../GlobalNotification';
import { useDatalayer } from '../../hooks/useDataLayer';
import { EmptyBag } from './EmptyBag';
import { BagHeader } from './BagHeader';
import { ProductList } from './ProductList';
import { SnackbarWrapper } from './SnackbarWrapper';

export const ActiveBag = ({ isMergedBag }: { isMergedBag?: boolean }) => {
  const { publishDataLayerEvent, getRecognitionStatus } = useDatalayer();
  // TODO: Move the logics to custom hooks
  const { moveItem, setMoveItem } = useBagContext();
  const [snackbarData, setSnackbarData] = useState<SnackbarData>({
    show: false,
    name: '',
    productUrl: '',
  });
  const { isMobile } = useBreakpoint();

  const {
    bagState: { data },
    asyncDispatch,
  } = useActiveBagContext();

  useEffect(() => {
    if (moveItem.action !== 'MOVE_TO_ACTIVE_BAG') return;

    (async function () {
      await asyncDispatch(moveItem);
      setMoveItem({ action: 'GET_SAVED_BAG' });
    })();
  }, [moveItem]);

  useEffect(() => {
    if (data?.actionPayload?.action === 'moveToSaveForLaterAction') {
      publishDataLayerEvent({
        name: 'save_for_later',
        products: data?.productList || [],
        extraAttrs: { recognition_status: getRecognitionStatus(data?.bagAttributes?.userStatus) },
      });
    } else if (data?.actionPayload?.action === 'deleteShoppingBagItemAction') {
      publishDataLayerEvent({ name: 'cart_remove', products: data?.productList || [] });
    }
  }, [data]);

  const { localize } = useLocalize();

  if (data === undefined) return null;

  const {
    currencySymbol,
    bagAttributes: { bagItemsCount, userStatus },
    productList = [],
    outOfStockItems = [],
  } = data;

  const isAuthenticated = userStatus === 'AUTHENTICATED';

  const { show } = snackbarData;

  return (
    <>
      {!productList.length && <EmptyBag isAuthenticated={isAuthenticated} />}
      {!!productList.length && (
        <section className={classNames('bg-cb-coreColor-white font-crossband p-4', { 'mb-2': isMobile }, { 'mb-4': !isMobile })} id='active-bag'>
          <h1 className='font-crossbrand pb-2 text-2xl font-bold'>{localize('product.myBagHeader')}</h1>
          <BagHeader shipItemsCount={bagItemsCount.ship} bopisItemsCount={bagItemsCount.pickUp} localize={localize} />
          <GlobalNotification />
          <ProductList
            asyncDispatch={asyncDispatch}
            setSnackbarData={setSnackbarData}
            currencySymbol={currencySymbol}
            productList={productList}
            outOfStockItems={outOfStockItems}
          />
        </section>
      )}
      {show && (
        <SnackbarWrapper
          snackbarData={snackbarData}
          setSnackbarData={setSnackbarData}
          dataTestId='moved-to-saved-bag'
          ariaLabel='moved to saved bag'
          localizeText={localize('product.moveToSavedBag')}
        />
      )}
      {isMergedBag && (
        <SnackbarWrapper
          snackbarData={snackbarData}
          setSnackbarData={setSnackbarData}
          dataTestId='merge-bag-snackbar'
          ariaLabel='merge bag snackbar'
          localizeText={localize('MERGE_BAG_SNACKBAR')}
          isDismissible
        />
      )}
    </>
  );
};
