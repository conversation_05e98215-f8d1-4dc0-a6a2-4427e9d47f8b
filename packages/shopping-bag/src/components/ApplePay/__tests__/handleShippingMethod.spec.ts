import { fetchPaymentShipping } from '../../../http-client/fetchPaymentShipping';
import { GetHeadersResponse } from '../../../utils/getHeaders';
import { handleShippingMethod } from '../handleShippingMethod';
import { createShippingInfo } from '../utils/createShippingInfo';
import { createPaymentShipping } from '../utils/createPaymentShipping';
import { createLineItems, getTotal } from '../utils/createLineItems';
import * as logNewRelicErrorModule from '../../../utils/newrelic-logger';

global.fetch = jest.fn();

jest.mock('../../../http-client/fetchPaymentShipping');
jest.mock('../utils/createShippingInfo');
jest.mock('../utils/createPaymentShipping');
jest.mock('../utils/createLineItems');
jest.mock('../../../utils/newrelic-logger', () => ({
  __esModule: true,
  ...jest.requireActual('../../../utils/newrelic-logger'),
  logNewRelicError: jest.fn(),
}));

describe('handleShippingMethod', () => {
  const mockFetchPaymentShipping = fetchPaymentShipping as jest.Mock;
  const mockCreateShippingInfo = createShippingInfo as jest.Mock;
  const mockCreatePaymentShipping = createPaymentShipping as jest.Mock;
  const mockCreateLineItems = createLineItems as jest.Mock;
  const mockGetTotal = getTotal as jest.Mock;
  const mockErrorToNewRelic = jest.spyOn(logNewRelicErrorModule, 'logNewRelicError').mockImplementation(() => null);
  // @ts-ignore
  window.ApplePaySession = { STATUS_SUCCESS: 0 };

  const mockEvent = {
    shippingMethod: {
      identifier: 'shippingOptionId-1',
    },
  } as unknown as ApplePayJS.ApplePayShippingMethodSelectedEvent;
  const mockLocalize = jest.fn();
  const mockApplePaySession = {
    completeShippingMethodSelection: jest.fn(),
    abort: jest.fn(),
  } as unknown as ApplePaySession;
  const mockHeaders = {
    get: jest.fn(() => 'en_US'),
  } as unknown as GetHeadersResponse;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    window.ApplePaySession = undefined;
    sessionStorage.clear();
  });

  it('should handle shipping method selection successfully', async () => {
    const mockResponse = {
      /* mock response data */
    };
    const mockParsedPaymentOrderInfo = {
      /* mock parsed payment order info */
    };
    const mockLineItems = {
      /* mock line items */
    };
    const mockNewTotal = {
      /* mock new total */
    };

    const mockGetShippingContact = jest.fn(() => ({
      locality: 'locality',
      administrativeArea: 'administrativeArea',
      countryCode: 'countryCode',
      postalCode: 'postalCode',
    }));

    mockFetchPaymentShipping.mockResolvedValue(mockResponse);
    mockCreateShippingInfo.mockReturnValue({
      /* mock shipping info */
    });
    mockCreatePaymentShipping.mockReturnValue(mockParsedPaymentOrderInfo);
    mockCreateLineItems.mockReturnValue(mockLineItems);
    mockGetTotal.mockReturnValue(mockNewTotal);

    await handleShippingMethod({
      evt: mockEvent,
      localize: mockLocalize,
      applePaySession: mockApplePaySession,
      headers: mockHeaders,
      getShippingContact: mockGetShippingContact,
    });

    expect(mockFetchPaymentShipping).toHaveBeenCalledWith({
      shippingAddress: expect.any(Object),
      shippingOptionId: 'shippingOptionId',
      headers: mockHeaders,
    });
    expect(sessionStorage.getItem('shippingOptionId')).toBe('shippingOptionId');
    expect(mockCreatePaymentShipping).toHaveBeenCalledWith(mockResponse);
    expect(mockCreateLineItems).toHaveBeenCalledWith(mockParsedPaymentOrderInfo, mockLocalize);
    expect(mockGetTotal).toHaveBeenCalledWith(mockParsedPaymentOrderInfo);
    expect(mockApplePaySession.completeShippingMethodSelection).toHaveBeenCalledWith(expect.any(Number), mockNewTotal, mockLineItems);
    expect(mockApplePaySession.abort).not.toHaveBeenCalled();
    expect(mockErrorToNewRelic).not.toHaveBeenCalled();
  });

  it('should convert full state name to abbreviation', async () => {
    const mockResponse = {
      /* mock response data */
    };
    const mockParsedPaymentOrderInfo = {
      /* mock parsed payment order info */
    };
    const mockLineItems = {
      /* mock line items */
    };
    const mockNewTotal = {
      /* mock new total */
    };

    const mockGetShippingContact = jest.fn();

    mockFetchPaymentShipping.mockResolvedValue(mockResponse);
    mockCreateShippingInfo.mockReturnValue({
      locality: 'locality',
      administrativeArea: 'California',
      countryCode: 'countryCode',
      postalCode: 'postalCode',
    });
    mockCreatePaymentShipping.mockReturnValue(mockParsedPaymentOrderInfo);
    mockCreateLineItems.mockReturnValue(mockLineItems);
    mockGetTotal.mockReturnValue(mockNewTotal);

    await handleShippingMethod({
      evt: mockEvent,
      localize: mockLocalize,
      applePaySession: mockApplePaySession,
      headers: mockHeaders,
      getShippingContact: mockGetShippingContact,
    });

    expect(mockFetchPaymentShipping).toHaveBeenCalledWith({
      shippingAddress: {
        state: 'CA',
        zipCode: 'postalCode',
        country: 'countryCode',
        city: 'locality',
      },
      shippingOptionId: 'shippingOptionId',
      headers: mockHeaders,
    });
    expect(sessionStorage.getItem('shippingOptionId')).toBe('shippingOptionId');
    expect(mockCreatePaymentShipping).toHaveBeenCalledWith(mockResponse);
    expect(mockCreateLineItems).toHaveBeenCalledWith(mockParsedPaymentOrderInfo, mockLocalize);
    expect(mockGetTotal).toHaveBeenCalledWith(mockParsedPaymentOrderInfo);
    expect(mockApplePaySession.completeShippingMethodSelection).toHaveBeenCalledWith(expect.any(Number), mockNewTotal, mockLineItems);
    expect(mockApplePaySession.abort).not.toHaveBeenCalled();
    expect(mockErrorToNewRelic).not.toHaveBeenCalled();
  });

  it('should handle shipping method selection with error', async () => {
    const mockError = new Error('Some error');

    mockFetchPaymentShipping.mockRejectedValue(mockError);

    // Mock for getShippingContact
    const mockGetShippingContact = jest.fn(() => ({
      locality: 'locality',
      administrativeArea: 'administrativeArea',
      countryCode: 'countryCode',
      postalCode: 'postalCode',
    }));

    await handleShippingMethod({
      evt: mockEvent,
      localize: mockLocalize,
      applePaySession: mockApplePaySession,
      headers: mockHeaders,
      getShippingContact: mockGetShippingContact,
    });

    expect(mockFetchPaymentShipping).toHaveBeenCalledWith({
      shippingAddress: expect.any(Object),
      shippingOptionId: 'shippingOptionId',
      headers: mockHeaders,
    });
    expect(sessionStorage.getItem('shippingOptionId')).toBe('shippingOptionId');
    expect(mockCreatePaymentShipping).not.toHaveBeenCalled();
    expect(mockCreateLineItems).not.toHaveBeenCalled();
    expect(mockGetTotal).not.toHaveBeenCalled();
    expect(mockApplePaySession.completeShippingMethodSelection).not.toHaveBeenCalled();
    expect(mockApplePaySession.abort).toHaveBeenCalled();
    expect(mockErrorToNewRelic).toHaveBeenCalledWith(mockError, {
      caller: 'handleShippingMethod()',
      feature: 'APPLE_PAY',
      message: 'Error fetching payment shipping',
    });
  });
});
