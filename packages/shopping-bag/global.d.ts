type DataLayerOriginal = import('@core-ui/app-state-provider').DataLayer;

type GapGlobalProps = {
  // eslint-disable-next-line @typescript-eslint/ban-types
  [key: string]: string | {} | GapGlobalProps | GapGlobalProps[];
  datalayer: DataLayer;
};

interface Window {
  ApplePaySession: typeof ApplePaySession;
  NREUM: {
    noticeError: (error: Error | string, customAttributes?: { [key: string]: string | number } | number) => void;
  };
  /* eslint-disable @typescript-eslint/no-explicit-any */
  afterPayPresenter: any;
  braintree?: {
    client: any;
    dataCollector: any;
    paypalCheckout: any;
  };
  gap: GapGlobalProps;
  newrelic: {
    addPageAction: (name: string, attributes?: Record<string, unknown>) => void;
    addToTrace: (traceData: Record<string, unknown>) => void;
    interaction: () => {
      save: () => void;
      setAttribute: (...args: unknown[]) => {
        setAttribute: Window['newrelic']['interaction']['setAttribute'];
      };
    };
    noticeError: (error: Error | string, customAttributes?: { [key: string]: string | number } | number) => void;
    setCustomAttribute: (name: string, value: string | number) => void;
  };
  paypal?: any;
  paypal2?: any;
  presentAfterpay: any;
}
