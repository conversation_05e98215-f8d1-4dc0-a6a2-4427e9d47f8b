import { getPageContext } from '@ecom-next/utils/server';
import { cache, ReactNode } from 'react';
import { getBrandInformation } from '@mfe/brand-info';
import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { getPageDataPromisesWithParams, DISABLED_FEATURE_FLAGS_PARAM, ENABLED_FEATURE_FLAGS_PARAM } from '@ecom-next/sitewide/state-builder';
import { CID, isValidCid, fetchMarketing, MarketingContent } from '@ecom-next/marketing-ui/src/fetchMarketing';
import { cookies } from 'next/headers';
import { getSitewideMeta } from '@ecom-next/sitewide/meta';
import { getReviewRatings, ReviewRatings } from '../services/reviews';
import generateFacebookMetaData from '../getFacebookMetadata';
import { GeneralNoResultsError } from '../Errors';
import getSmartCrosslinkData from './seo-smart-crosslinks';
import { seo } from './seo';
import getAppConfig from './appConfig';
import shippingAndReturnsData from './ship-and-return-data';
import getProductData from './get-product-data';

export type Params = PageParams['searchParams'];

export function toParam(val: Params[string]) {
  return (Array.isArray(val) ? val[0] : val) || '';
}

export interface ProductPageParams {
  cid?: CID;
  modelSize?: string;
  pcid?: CID;
  pid: string;
  storeId?: string;
  vid?: string;
}

const isValidPid = isValidCid;

export function checkValidParams(params: unknown): params is ProductPageParams {
  const maybeProductParams = params as ProductPageParams;

  const pid = maybeProductParams['pid'];
  return pid !== undefined && isValidPid(pid);
}

type PageDataPromises = ReturnType<typeof getPageDataPromisesWithParams>;
export type StaticDataPromise = PageDataPromises['staticDataPromise'];
export type FeatureFlagPromise = PageDataPromises['enabledFeaturesPromise'];

async function getProductDataPromise(queryParamString: string, staticDataPromise: StaticDataPromise, featureFlagsPromise: FeatureFlagPromise) {
  const { brand, market, locale } = getPageContext();

  const searchParams = new URLSearchParams(queryParamString);
  const vid = searchParams.get('vid') || '';
  const maybeCid = searchParams.get('cid') || '';
  const pid = searchParams.get('pid') || '';
  const storeId = searchParams.get('storeId') || '';
  const modelSizeQuery = searchParams.get('modelSize') || '';

  const cid = isValidCid(maybeCid) ? maybeCid : '';
  const sanitizedQuery = Object.fromEntries(Array.from(searchParams.entries()).map(entry => [entry[0], toParam(entry[1])]));

  const country = locale === 'en_US' ? 'us' : 'ca';

  const { abSeg, previewDate = '', contentType = 'ecom', appConfig } = await staticDataPromise;

  const { apiConfig } = appConfig;
  const cookieStore = cookies();
  const modelSizeSelected = cookieStore.get('modelSizeSelected');

  return getProductData({
    abSeg,
    apiConfig,
    brand,
    cid,
    country,
    date: previewDate,
    locale,
    market,
    modelSizeCookieValue: modelSizeSelected?.value,
    modelSizeQuery,
    pid,
    requestType: contentType,
    sanitizedQuery,
    storeId,
    vid,
    featureFlagsPromise,
  });
}

function getUrlParamsString(params: PageParams['searchParams']) {
  const disabledFlags = params[DISABLED_FEATURE_FLAGS_PARAM]
    ? `&disabledFeatureFlags=${params[DISABLED_FEATURE_FLAGS_PARAM]}&${DISABLED_FEATURE_FLAGS_PARAM}=${params[DISABLED_FEATURE_FLAGS_PARAM]}`
    : '';

  // build consistent searchParams for cache
  const urlParams = new URLSearchParams(
    `fffs=${params['fffs'] || ''}&enabledFeatureFlags=${params[ENABLED_FEATURE_FLAGS_PARAM] || ''}${disabledFlags}&vid=${params['vid'] || ''}&cid=${params['cid'] || ''}&pcid=${params['pcid'] || ''}&pid=${params['pid'] || ''}&tid=${params['tid'] || ''}&storeId=${params['storeId'] || ''}&modelSize=${params['modelSize'] || ''}&autosuggest=${params['autosuggest'] || ''}&searchText=${params['searchText'] || ''}&position=${params['position'] || ''}&results=${params['results'] || ''}`
  );

  return urlParams.toString();
}

function getDataPromises(requestParamsUrl: string) {
  const searchParamsObj = new URLSearchParams(requestParamsUrl);
  const sanitizedQuery = Object.fromEntries(Array.from(searchParamsObj.entries()).map(entry => [entry[0], toParam(entry[1])]));
  if (!checkValidParams(sanitizedQuery)) {
    throw new GeneralNoResultsError('Invalid parameters provided for product data retrieval');
  }

  const { cid: maybeCid, pcid } = sanitizedQuery;

  const cid = isValidCid(maybeCid) ? maybeCid : '';
  const { enabledFeaturesPromise, staticDataPromise, navDataPromise } = getPageDataPromisesWithParams(cid, 'product', sanitizedQuery);

  const queryParamsString = searchParamsObj.toString();
  const capiAggregationPromise = getProductDataPromise(queryParamsString, staticDataPromise, enabledFeaturesPromise);

  const { brand, market, locale } = getPageContext();
  const country = locale === 'en_US' ? 'us' : 'ca';

  const { powerReviewsConfig } = getAppConfig();

  const marketingDataPromise = Promise.all([staticDataPromise, capiAggregationPromise, enabledFeaturesPromise])
    .then(data => {
      const [staticData, productPageData, featureFlags] = data;
      const { productData } = productPageData;
      const { enabledFeatures } = featureFlags;
      const { contentApi, previewDate } = staticData;
      const { primarySellingStyleId, primaryCategoryId } = productData;

      const pcidOrPrimarycid = primaryCategoryId || pcid;

      if (!isValidCid(pcidOrPrimarycid) || !enabledFeatures[`pdp-pmcs-${market}-${brand}`]) return Promise.resolve({} as MarketingContent<'product'>);

      const overrideLocale = enabledFeatures?.['default-canada-to-en-us'] || false;
      const passBadgeIdToPMCS = enabledFeatures?.['pass-badge-id-to-pmcs'] || false;

      return fetchMarketing(pcidOrPrimarycid, 'product', brand, contentApi, previewDate, primarySellingStyleId, overrideLocale, passBadgeIdToPMCS);
    })
    .catch(() => {
      return Promise.resolve({} as MarketingContent<'product'>);
    });

  // do not use `await` to start all promises in parallel instead of serial fetch
  const reviewRatingsPromise = enabledFeaturesPromise
    .then(data => {
      const { enabledFeatures } = data;
      const useCapiReviews = enabledFeatures?.['pdp-use-capi-reviews'] || false;

      return getReviewRatings({
        locale,
        ...powerReviewsConfig,
        featureFlags: enabledFeatures,
        useCapiReviews,
        capiAggregationPromise,
      });
    })
    .catch(() => {
      const fallback = {
        reviews: [],
        reviewHistogram: {
          average_rating: 0,
          rating_count: 0,
          review_count: 0,
        },
      } as unknown as ReviewRatings;
      return Promise.resolve(fallback);
    });

  const productAppConfigsPromise = staticDataPromise.then(({ appConfig, previewDate }) => ({
    appConfig: {
      apiConfig: {
        ...appConfig.apiConfig,
        pasConfig: {
          url: appConfig.apiConfig.internalOidcUrl,
        },
        pssConfig: {
          url: appConfig.apiConfig.internalOidcUrl,
        },
      },
      brandCodeUrls: appConfig.brandCodeUrls,
      powerReviewsConfig,
    },

    previewDate,
  }));

  const navPromise = navDataPromise.then(data => {
    return data.desktopNav;
  });

  const seoPromise = productAppConfigsPromise
    .then(({ previewDate, appConfig }) =>
      seo({
        appConfig,
        brand,
        country,
        featureFlagsPromise: enabledFeaturesPromise,
        date: previewDate || '',
        locale,
        market,
        navPromise,
        pcid: pcid || '',
        pssPromise: capiAggregationPromise,
        reviewRatingsPromise,
      })
    )
    .catch(() => {
      return {} as SeoResponse;
    });

  const crossLinkDataPromise = getSmartCrosslinkData({
    brand,
    featureFlagsPromise: enabledFeaturesPromise,
    market,
    navPromise,
    seoPromise,
  }).catch(() => {
    return {
      smartCrosslinks: {
        fallback: [],
      },
    } as unknown as SmartCrosslinkResponse;
  });

  return {
    marketingDataPromise,
    productDataPromise: capiAggregationPromise,
    sanitizedQuery,
    seoPromise,
    productAppConfigsPromise,
    navPromise,
    featureFlagsPromise: enabledFeaturesPromise,
    reviewRatingsPromise,
    staticDataPromise,
    crossLinkDataPromise,
  };
}

const cacheableDataPromises = cache(getDataPromises);

export function getCacheableDataPromises(params: PageParams['searchParams']) {
  const requestParamString = getUrlParamsString(params);
  return cacheableDataPromises(requestParamString);
}

export async function getMetaData(props: PageParams) {
  const { searchParams } = props;
  const requestParamString = getUrlParamsString(searchParams);
  const { seoPromise, sanitizedQuery, featureFlagsPromise, staticDataPromise } = cacheableDataPromises(requestParamString);

  const { brand, market, targetEnv } = getPageContext();
  const [seoData, featureFlags, staticData, sitewideMeta] = await Promise.all([
    seoPromise,
    featureFlagsPromise,
    staticDataPromise,
    getSitewideMeta({ brand, market, pageType: 'product', env: targetEnv }),
  ]);
  const { displayName } = getBrandInformation(brand, market);
  const { enabledFeatures } = featureFlags;
  const isFacebookMetadataEnabled = enabledFeatures['pdp-facebook-metadata'];
  const { canonicalUrl, title, description } = seoData;
  const { pid } = sanitizedQuery;
  const {
    appConfig: { brandCodeUrls },
  } = staticData;
  const { unsecureUrl } = brandCodeUrls;
  const fallbackLink = `https://${unsecureUrl}/browse/product.do?${requestParamString}`;
  const isFacebookExcluded = brand === 'brfs' || brand === 'gapfs';
  const facebookMetadata = isFacebookExcluded
    ? {}
    : generateFacebookMetaData({
        fallbackLink,
        brandName: brand,
        market,
        pid,
        isFacebookMetadataEnabled,
        brandDisplayName: displayName,
      });

  return {
    title,
    description,
    ...(canonicalUrl ? { alternates: { canonical: canonicalUrl } } : {}),
    ...facebookMetadata,
    ...sitewideMeta,
  };
}

export default async function getData(params: PageParams['searchParams']) {
  const requestParamString = getUrlParamsString(params);
  const {
    productDataPromise: capiAggregationPromise,
    sanitizedQuery,
    seoPromise,
    productAppConfigsPromise,
    reviewRatingsPromise,
    staticDataPromise,
  } = cacheableDataPromises(requestParamString);

  const { brand, locale } = getPageContext();
  const { socialGalleryConfig } = getAppConfig();

  const [pageData, productAppConfigData, capiAggregation, reviewsData, seoData] = await Promise.all([
    staticDataPromise,
    productAppConfigsPromise,
    capiAggregationPromise,
    reviewRatingsPromise,
    seoPromise,
  ]);

  const {
    appConfig: { brandCodeUrls },
  } = pageData;
  const { appConfig: productAppConfigs } = productAppConfigData;

  const returnsData = shippingAndReturnsData[locale][brand];

  return {
    shippingAndReturnsData: returnsData,
    ...capiAggregation,
    ...reviewsData,
    ...seoData,
    params: sanitizedQuery,
    brandCodeUrls,
    productAppConfigs,
    socialGalleryConfig,
  };
}

export type ProductImageRaw = {
  large: string;
  medium: string;
  small: string;
  thumbnail: string;
  video?: string;
  xlarge: string;
};

export type ProductData =
  ReturnType<typeof getData> extends Promise<infer U>
    ? U & {
        brandCode: BrandInformation['marketAwareBrandCode'];
        brandInformation: BrandInformation;
        breadcrumbs: ReactNode;
        breadcrumbsData: BreadCrumbs;
        crosslinks: ReactNode;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        fitInformation: { [key: string]: any };
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        infoTabs: { [key: string]: any };
        marketing: ReactNode;
        productImages: Record<string, ProductImageRaw>;
      }
    : never;
