const checkValidDate = (expirationDate: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return expirationDate >= today;
};

export const transformPidBatches = (pidBatches: Record<string, string> = {}) => {
  const isPidList = (key: string) => key.startsWith('pidBatch');

  const result = Object.entries(pidBatches)
    .filter(entry => isPidList(entry[0]))
    .map(entry => {
      const [key, val] = entry;
      const batchNumber = Number(key.replace(/[^0-9]+/g, ''));
      const expirationDate = new Date(pidBatches[`batch${batchNumber}ExpirationDate`] || 0);
      const pidList = val.replace(/\s/g, '').split(',');

      return {
        expirationDate,
        pidList,
      };
    });

  return result;
};

type PidBatches = ReturnType<typeof transformPidBatches>;

export const isValidPid = (pidBatches: PidBatches, styleId: string | undefined) => {
  if (!styleId || styleId === '') {
    return false;
  }

  const validPid = pidBatches.find(pidBatch => {
    const isNotExpired = checkValidDate(pidBatch.expirationDate);
    return isNotExpired && pidBatch.pidList.includes(styleId);
  });

  return !!validPid;
};
