import type { ProductColorGroup, ProductVariant } from '@pdp/types/product-data/style-level'; // NOSONAR
import type { ProductColorRaw } from '@pdp/types/product-data/style-level-raw'; // NOSONAR

import { considerOutOfStockColors, filterOnlyInStockColorGroup, filterOnlyInStockProductStyleColors, isOutOfStockAndOOSTemplate } from './color-stock-filters';

const adaptColorGroup = (colorGroup: ProductColorGroup, inStock: boolean, oosLPOTemplate: boolean): ProductColorGroup => {
  return {
    ...colorGroup,
    colors: isOutOfStockAndOOSTemplate(inStock, oosLPOTemplate) ? considerOutOfStockColors(colorGroup) : filterOnlyInStockColorGroup(colorGroup),
  };
};

export const filterOOSVariants = (variants: ProductVariant[], oosLPOTemplate: boolean): ProductVariant[] => {
  if (!oosLPOTemplate) {
    return variants.filter(
      variant =>
        (Object.keys(variant).includes('productStyleColors') && variant.productStyleColors.flat().some(color => color.inStock || color.bopisInStock)) ||
        variant.link
    );
  }

  return variants;
};

export const oosColorFilter = (variants: ProductVariant[] = [], inStock: boolean, oosLPOTemplate: boolean): ProductVariant[] => {
  const filteredVariants = [] as ProductVariant[];

  variants.forEach(variant => {
    const updatedVariant = { ...variant };
    if (variant.colorGroups) {
      const updateColorGroups = { ...variant.colorGroups };

      if (updateColorGroups && updateColorGroups.markdown) {
        updateColorGroups.markdown = updateColorGroups.markdown
          .map(colorGroup => adaptColorGroup(colorGroup, inStock, oosLPOTemplate))
          .filter(colorGroup => colorGroup.colors.length > 0);
      }

      if (updateColorGroups && updateColorGroups.fullprice) {
        updateColorGroups.fullprice = updateColorGroups.fullprice
          .map(colorGroup => adaptColorGroup(colorGroup, inStock, oosLPOTemplate))
          .filter(colorGroup => colorGroup.colors.length > 0);
      }
      updatedVariant.colorGroups = updateColorGroups;
    }

    const updatedProductStyleColors = [] as ProductColorRaw[][];

    variant.productStyleColors?.forEach(styleColors => {
      const updatedColors = isOutOfStockAndOOSTemplate(inStock, oosLPOTemplate) ? styleColors : filterOnlyInStockProductStyleColors(styleColors);
      if (updatedColors.length > 0) {
        updatedProductStyleColors.push(updatedColors);
      }
    });

    updatedVariant.productStyleColors = updatedProductStyleColors;

    filteredVariants.push(updatedVariant);
  });

  return filteredVariants;
};
