import { getPageContext } from '@ecom-next/utils/server';
import { getSitewideMeta } from '@ecom-next/sitewide/meta';
import { getBrandInformation } from '@mfe/brand-info';
import logger from '@ecom-next/app/logger';
import { cache } from 'react';
import generateFacebookMetaData from '../../pages/getFacebookMetadata';
import { cacheableAppConfigDataV2Promise, cacheableFeaturesConfigMVG, cacheableMvgSeoDataPromise } from '../getReWrittenData';
import { getSanitizedQuery } from '../helpers';

async function getMetaDataMvg(requestParamString: string) {
  const pdpLogger = logger.child({ module: 'pdp:getMetaData' });
  const searchParamsObj = new URLSearchParams(requestParamString);
  const sanitizedQuery = getSanitizedQuery(searchParamsObj);
  const { brand, market, targetEnv } = getPageContext();
  const [
    {
      appConfig: { brandCodeUrls },
    },
    { isFacebookMetadataEnabled },
    sitewideMeta,
    { title, description, canonicalUrl },
  ] = await Promise.all([
    cacheableAppConfigDataV2Promise(requestParamString),
    cacheableFeaturesConfigMVG(requestParamString, pdpLogger),
    getSitewideMeta({ brand, market, pageType: 'product', env: targetEnv }),
    cacheableMvgSeoDataPromise(requestParamString, pdpLogger),
  ]);
  const canonicalUrlData = canonicalUrl ? { alternates: { canonical: canonicalUrl } } : {};
  const { unsecureUrl } = brandCodeUrls;
  const fallbackLink = `https://${unsecureUrl}/browse/product.do?${requestParamString}`;
  const { pid } = sanitizedQuery;
  const isFacebookExcluded = brand === 'brfs' || brand === 'gapfs';
  const brandDisplayName = getBrandInformation(brand, market).displayName;
  const facebookMetadata = isFacebookExcluded
    ? {}
    : generateFacebookMetaData({
        fallbackLink,
        brandName: brand,
        market,
        pid,
        isFacebookMetadataEnabled,
        brandDisplayName,
      });
  return {
    title,
    description,
    ...canonicalUrlData,
    ...facebookMetadata,
    ...sitewideMeta,
  };
}
export const cachedMvgMetaData = cache(getMetaDataMvg);
