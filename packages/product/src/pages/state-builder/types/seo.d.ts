declare type BreadCrumbs = {
  category?: {
    name: string;
    url: string;
  };
  division?: {
    name: string;
    url: string;
  };
};

declare type SeoResponse = {
  breadcrumbs: BreadCrumbs;
  canonicalUrl: string;
  department: string;
  description: string;
  htmlLangAttribute: string;
  selectedNodes: SelectedNodes;
  seoBreadcrumbSchema: BreadcrumbSchema;
  seoPageSchema: SeoPageSchema;
  title: string;
};

declare type Offer = {
  '@type': 'Offer';
  availability: IsInStockSchema;
  image?: string;
  itemCondition: 'https://schema.org/NewCondition';
  itemOffered?: {
    '@type': 'IndividualProduct';
    brand: BrandDisplayName;
    color: string;
    description: string;
    image?: string;
    name: string;
    sku: string;
  };
  price: number | string;
  priceCurrency: string;
  seller: {
    '@type': 'Organization';
    name: string;
  };
  sku?: string;
  url: string;
};

declare type ProductPageSchema =
  | {
      '@context': 'https://schema.org';
      '@type': 'Product';
      brand: {
        '@type': 'Thing';
        name: BrandDisplayName;
      };
      description: string;
      name: string;
      offers: Offer[];
      productID: string;
    }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | any;

declare type BreadcrumbListElement = {
  '@type': 'ListItem';
  item: {
    '@id': string;
    name: string;
  };
  position: number;
};

declare type BreadcrumbSchema =
  | {
      '@context': 'https://schema.org';
      '@type': 'BreadcrumbList';
      itemListElement: BreadcrumbListElement[];
    }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | any;

declare type SelectedNode = {
  id: string;
  name: string;
  type: string;
};

declare type SelectedNodes = SelectedNode[];

declare type NavigationNode = {
  children?: NavigationNode[];
  customUrl?: string;
  department?: string;
  hasSubDivision?: boolean;
  hidden?: boolean;
  id: string;
  link?: string;
  name: string;
  parents?: string[];
  selected?: boolean;
  type: string;
};
