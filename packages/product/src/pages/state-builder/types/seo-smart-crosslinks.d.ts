declare module '@ecom-next/seo-helpers/bloomreach-seo-solution';

declare type SmartCrosslinkRequestParams = {
  brand: AbbrBrand;
  featureFlagsPromise: Promise<FeatureFlags>;
  market: Market;
  navPromise: Promise<NavigationNode>;
  seoPromise: Promise<SeoResponse>;
};

declare type SmartCrosslink = {
  ariaLabel: string;
  link: string;
  name: string;
};

declare type SmartCrosslinkError = {
  errorMessage?: string;
  errorName?: string;
  log?: string;
  type?: string;
};

declare type SmartCrosslinkData = {
  crosslinks?: SmartCrosslink[];
  error?: SmartCrosslinkError | Record<string, string>;
};

declare type SmartCrosslinkResponse = SmartCrosslinkData & {
  errorLog?: string;
  fallback: NavigationNode[];
};
