import type { FeatureSchema } from '@ecom-next/sitewide/feature-flags';

const colorSwatchLayout = ['us-at', 'ca-at', 'us-gap', 'ca-gap', 'us-gapfs', 'ca-gapfs', 'us-br', 'ca-br', 'us-brfs', 'ca-brfs', 'us-on', 'ca-on'].map(
  marketBrandKey => {
    return {
      flag: `pdp-color-swatch-layout-${marketBrandKey}`,
      variables: [
        {
          type: 'Boolean',
          name: 'default', // wrapped layout
        },
        {
          type: 'Boolean',
          name: 'stacked',
        },
      ],
    };
  }
);

export const featureVariableSchema = [
  ...colorSwatchLayout,
  {
    flag: 'pdp-bricks-us-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-us-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-us-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-us-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'pdp-bricks-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minRequiredImages',
      },
      {
        type: 'Integer',
        name: 'maxAllowedImages',
      },
      {
        type: 'JSON',
        name: 'imageReorder',
      },
      {
        type: 'Boolean',
        name: 'displayAllAvailableImages',
      },
      {
        type: 'String',
        name: 'displayMainFromOtherCC',
      },
      {
        type: 'String',
        name: 'displayOrderOnlySelectedCC',
      },
    ],
  },
  {
    flag: 'bopis-us-on',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-us-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-us-at',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-us-br',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-us-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-us-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-on',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-br',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-at',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'bopis-ca-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'fulfillmentDisplay',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-at',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-br',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-gap',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-on',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-at',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-br',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-brfs',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-gap',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-gapfs',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-us-on',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-brfs',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-carousel-ca-gapfs',
    variables: [
      {
        type: 'JSON',
        name: 'imageReorder',
      },
    ],
  },
  {
    flag: 'pdp-afterpay',
    variables: [
      {
        type: 'String',
        name: 'threshold-us-on',
      },
      {
        type: 'String',
        name: 'categories-us-on',
      },
      {
        type: 'String',
        name: 'threshold-us-br',
      },
      {
        type: 'String',
        name: 'categories-us-br',
      },
      {
        type: 'String',
        name: 'threshold-us-gap',
      },
      {
        type: 'String',
        name: 'categories-us-gap',
      },
      {
        type: 'String',
        name: 'threshold-us-at',
      },
      {
        type: 'String',
        name: 'categories-us-at',
      },
      {
        type: 'String',
        name: 'threshold-us-gapfs',
      },
      {
        type: 'String',
        name: 'categories-us-gapfs',
      },
      {
        type: 'String',
        name: 'threshold-us-brfs',
      },
      {
        type: 'String',
        name: 'categories-us-brfs',
      },
      {
        type: 'String',
        name: 'threshold-ca-on',
      },
      {
        type: 'String',
        name: 'categories-ca-on',
      },
      {
        type: 'String',
        name: 'threshold-ca-gap',
      },
      {
        type: 'String',
        name: 'categories-ca-gap',
      },
      {
        type: 'String',
        name: 'threshold-ca-br',
      },
      {
        type: 'String',
        name: 'categories-ca-br',
      },
      {
        type: 'String',
        name: 'threshold-ca-at',
      },
      {
        type: 'String',
        name: 'categories-ca-at',
      },
      {
        type: 'Boolean',
        name: 'hide-us-on',
      },
      {
        type: 'Boolean',
        name: 'hide-us-gap',
      },
      {
        type: 'Boolean',
        name: 'hide-us-br',
      },
      {
        type: 'Boolean',
        name: 'hide-us-at',
      },
      {
        type: 'Boolean',
        name: 'hide-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'hide-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-on',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-br',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-at',
      },
      {
        type: 'String',
        name: 'threshold-ca-brfs',
      },
      {
        type: 'String',
        name: 'categories-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-brfs',
      },
      {
        type: 'String',
        name: 'threshold-ca-gapfs',
      },
      {
        type: 'String',
        name: 'categories-ca-gapfs',
      },
      {
        type: 'Boolean',
        name: 'hide-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-gap',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-on',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-br',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-at',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-gapfs',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-us-brfs',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-gap',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-br',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-on',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-at',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-brfs',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-free-shipping-bar-ca-gapfs',
    variables: [
      {
        type: 'Double',
        name: 'priceLimit',
      },
    ],
  },
  {
    flag: 'pdp-power-reviews',
    variables: [
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-lpo-power-reviews',
    variables: [
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-lpo-hybrid-power-reviews',
    variables: [
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-on',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-br',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-at',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'hidden-photogallery-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-sizeinclusivity',
    variables: [
      {
        type: 'JSON',
        name: 'imagesMapping',
      },
      {
        type: 'JSON',
        name: 'primaryCategoryIds',
      },
      {
        type: 'JSON',
        name: 'defaultSize',
      },
      {
        type: 'JSON',
        name: 'certonaModelSizes',
      },
    ],
  },
  {
    flag: 'pdp-anchor-marketingcontainer',
    variables: [
      {
        type: 'String',
        name: 'tid',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-us-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-size-sampling-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Integer',
        name: 'minReviewThreshold',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'sliderFeature',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-on',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-br',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-at',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-drapr-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'primaryCategoryId',
      },
      {
        type: 'Boolean',
        name: 'isAutoSelectEnabled',
      },
      {
        type: 'Boolean',
        name: 'isOOSErrorEnabled',
      },
      {
        type: 'Boolean',
        name: 'isSizeLabelEnabled',
      },
    ],
  },
  {
    flag: 'pdp-photos-stacked-layout-us-br',
    variables: [
      {
        type: 'Integer',
        name: 'maxQuantityPhotos',
      },
      {
        type: 'Boolean',
        name: 'topImagesLazy',
      },
      {
        type: 'Boolean',
        name: 'bottomImagesLazy',
      },
    ],
  },
  {
    flag: 'pdp-photos-stacked-layout-us-brfs',
    variables: [
      {
        type: 'Integer',
        name: 'maxQuantityPhotos',
      },
      {
        type: 'Boolean',
        name: 'topImagesLazy',
      },
      {
        type: 'Boolean',
        name: 'bottomImagesLazy',
      },
    ],
  },
  {
    flag: 'pdp-photos-stacked-layout-ca-br',
    variables: [
      {
        type: 'Integer',
        name: 'maxQuantityPhotos',
      },
      {
        type: 'Boolean',
        name: 'topImagesLazy',
      },
      {
        type: 'Boolean',
        name: 'bottomImagesLazy',
      },
    ],
  },
  {
    flag: 'pdp-photos-stacked-layout-ca-brfs',
    variables: [
      {
        type: 'Integer',
        name: 'maxQuantityPhotos',
      },
      {
        type: 'Boolean',
        name: 'topImagesLazy',
      },
      {
        type: 'Boolean',
        name: 'bottomImagesLazy',
      },
    ],
  },
  {
    flag: 'pdp-photos-stacked-layout-ca-gapfs',
    variables: [
      {
        type: 'Integer',
        name: 'maxQuantityPhotos',
      },
      {
        type: 'Boolean',
        name: 'topImagesLazy',
      },
      {
        type: 'Boolean',
        name: 'bottomImagesLazy',
      },
    ],
  },
  {
    flag: 'pdp-paypal',
    variables: [
      {
        type: 'Boolean',
        name: 'paypal-us-on',
      },
      {
        type: 'Boolean',
        name: 'paypal-us-at',
      },
      {
        type: 'Boolean',
        name: 'paypal-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'paypal-us-gap',
      },
      {
        type: 'Boolean',
        name: 'paypal-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'paypal-us-br',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-on',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-at',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-br',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'paypal-ca-gapfs',
      },
      {
        type: 'String',
        name: 'threshold-us-on',
      },
      {
        type: 'String',
        name: 'threshold-us-br',
      },
      {
        type: 'String',
        name: 'threshold-us-gap',
      },
      {
        type: 'String',
        name: 'threshold-us-at',
      },
      {
        type: 'String',
        name: 'threshold-us-gapfs',
      },
      {
        type: 'String',
        name: 'threshold-us-brfs',
      },
      {
        type: 'String',
        name: 'threshold-ca-on',
      },
      {
        type: 'String',
        name: 'threshold-ca-gap',
      },
      {
        type: 'String',
        name: 'threshold-ca-br',
      },
      {
        type: 'String',
        name: 'threshold-ca-at',
      },
      {
        type: 'String',
        name: 'threshold-ca-brfs',
      },
      {
        type: 'String',
        name: 'threshold-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script',
    variables: [
      {
        type: 'String',
        name: 'current-version',
      },
      {
        type: 'String',
        name: 'test-version',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-br',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-br',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-gap',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-at',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-at',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-us-on',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-on',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-drapr-script-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'url_3D',
      },
      {
        type: 'String',
        name: 'url_test',
      },
      {
        type: 'String',
        name: 'url_fit',
      },
    ],
  },
  {
    flag: 'pdp-mfe-load-certona',
    variables: [
      {
        type: 'String',
        name: 'badges_at_girls',
      },
      {
        type: 'String',
        name: 'badges_standard',
      },
      {
        type: 'Boolean',
        name: 'pollCertonaSegment',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-at',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-br',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-on',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-at',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-br',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-us-on',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-dropship-ca-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'showAgreeToAtbModal',
      },
      {
        type: 'Boolean',
        name: 'showQuickAddModal',
      },
      {
        type: 'Boolean',
        name: 'showSizeGuide',
      },
      {
        type: 'Boolean',
        name: 'showFindMine',
      },
      {
        type: 'Boolean',
        name: 'showDrapr',
      },
      {
        type: 'Boolean',
        name: 'showTrueFit',
      },
      {
        type: 'Boolean',
        name: 'showOtherPaymentMethods',
      },
      {
        type: 'Boolean',
        name: 'showBOPIS',
      },
    ],
  },
  {
    flag: 'pdp-fulfillment-selection-v2',
    variables: [
      {
        type: 'Boolean',
        name: 'gap-ca',
      },
      {
        type: 'Boolean',
        name: 'on-us',
      },
      {
        type: 'Boolean',
        name: 'brfs-us',
      },
      {
        type: 'Boolean',
        name: 'at-us',
      },
      {
        type: 'Boolean',
        name: 'br-ca',
      },
      {
        type: 'Boolean',
        name: 'gap-us',
      },
      {
        type: 'Boolean',
        name: 'at-ca',
      },
      {
        type: 'Boolean',
        name: 'on-ca',
      },
      {
        type: 'Boolean',
        name: 'gapfs-us',
      },
      {
        type: 'Boolean',
        name: 'br-us',
      },
      {
        type: 'Boolean',
        name: 'brfs-ca',
      },
      {
        type: 'Boolean',
        name: 'gapfs-ca',
      },
    ],
  },
  {
    flag: 'findmine',
    variables: [
      {
        type: 'Boolean',
        name: 'mainImage-ca-at',
      },
      {
        type: 'Boolean',
        name: 'mainImage-ca-br',
      },
      {
        type: 'Boolean',
        name: 'mainImage-ca-gap',
      },
      {
        type: 'Boolean',
        name: 'mainImage-ca-on',
      },
      {
        type: 'Boolean',
        name: 'mainImage-ca-brfs',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-at',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-br',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-gap',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-on',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-brfs',
      },
      {
        type: 'Boolean',
        name: 'mainImage-us-gapfs',
      },
      {
        type: 'Boolean',
        name: 'mainImage-ca-gapfs',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-gap',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-on',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-br',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-at',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-ca-on',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-ca-br',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-pmcs-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'container',
      },
    ],
  },
  {
    flag: 'pdp-chas',
    variables: [
      {
        type: 'Boolean',
        name: 'showReviews',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-gap',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-br',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-br',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-at',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-at',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-us-on',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-on',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-drapr-analytics-script-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'url_analytics',
      },
      {
        type: 'String',
        name: 'url_test',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-at',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-at',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-br',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-br',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-gap',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-us-on',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-on',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-certona-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-at',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-at',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-br',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-br',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-gap',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-us-on',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-on',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'pdp-lpo-plp-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'TID',
      },
    ],
  },
  {
    flag: 'true-fit-ca-at',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-at',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-ca-on',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-on',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-ca-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-gap',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-ca-br',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-us-br',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-ca-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'true-fit-ca-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'nextgenWidget',
      },
    ],
  },
  {
    flag: 'pdp-pixleeegc-widget',
    variables: [
      {
        type: 'Boolean',
        name: 'showPixleeCategoryWidget',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-at',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-br',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-on',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-at',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-br',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-gap',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-us-on',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-special-oos-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-at',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-br',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-on',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-at',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-br',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-gap',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-us-on',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'special-collection-oos-pdp-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'pidBatch1',
      },
      {
        type: 'String',
        name: 'pidBatch2',
      },
      {
        type: 'String',
        name: 'pidBatch3',
      },
      {
        type: 'String',
        name: 'pidBatch4',
      },
      {
        type: 'String',
        name: 'pidBatch5',
      },
      {
        type: 'String',
        name: 'batch1ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch2ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch3ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch4ExpirationDate',
      },
      {
        type: 'String',
        name: 'batch5ExpirationDate',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-us-on',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-ca-on',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-us-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-ca-brfs',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-us-br',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-ca-br',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-us-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-percentage-off-ca-gapfs',
    variables: [
      {
        type: 'Boolean',
        name: 'useFromCapi',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-gap',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-on',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-br',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-at',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-gapfs',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-us-brfs',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-gap',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-on',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-br',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-at',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-brfs',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-reviews-ca-gapfs',
    variables: [
      {
        type: 'String',
        name: 'default',
      },
      {
        type: 'String',
        name: 'test1',
      },
      {
        type: 'String',
        name: 'test2',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-gap',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-on',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-br',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-at',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-brfs',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-us-gapfs',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-gap',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-on',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-br',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-at',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-brfs',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
  {
    flag: 'pdp-ai-recommendations-ca-gapfs',
    variables: [
      {
        type: 'Integer',
        name: 'recommendedItemSize',
      },
    ],
  },
] as FeatureSchema[];
