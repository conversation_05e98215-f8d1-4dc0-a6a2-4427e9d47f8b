import type { ProductColor, ProductColorGroup } from '@pdp/types/product-data/style-level';
import type { ProductColorRaw } from '@pdp/types/product-data/style-level-raw';

export const filterOnlyInStockColorGroup = (colorGroup: ProductColorGroup): ProductColor[] => {
  return Array.isArray(colorGroup.colors) ? colorGroup.colors.filter(color => color.inStock || color.bopisInStock) : [];
};

export const filterOnlyInStockProductStyleColors = (styleColors: ProductColorRaw[]): ProductColorRaw[] => {
  return Array.isArray(styleColors) ? styleColors.filter(color => color.inStock || color.bopisInStock) : [];
};

export const considerOutOfStockColors = (colorGroup: ProductColorGroup): ProductColor[] => {
  return Array.isArray(colorGroup.colors) ? colorGroup.colors : [];
};

export const isOutOfStockAndOOSTemplate = (inStock: boolean, oosLPOTemplate: boolean): boolean => {
  return oosLPOTemplate && !inStock;
};
