import { getPageContext } from '@ecom-next/utils/server';

const powerReviewsConfig = JSON.parse(process.env.POWER_REVIEWS_CONFIG || '{}');
const socialGalleryConfig = JSON.parse(process.env.SOCIAL_GALLERY_CONFIG || '{}');

export default () => {
  const { brand, locale } = getPageContext();
  return {
    powerReviewsConfig: powerReviewsConfig[locale]?.[brand] || {},
    socialGalleryConfig: socialGalleryConfig || {},
  };
};
