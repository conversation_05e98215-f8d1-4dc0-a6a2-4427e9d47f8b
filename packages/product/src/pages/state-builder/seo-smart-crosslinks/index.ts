import { requestCrosslinkData } from '@ecom-next/seo-helpers/crosslinks';

import { getRequestType } from '@ecom-next/utils/server';
import getNavChildCategories from './get-nav-child-categories';

const getSmartCrosslinkData = async ({
  featureFlagsPromise,
  seoPromise,
  navPromise,
  brand,
  market,
}: SmartCrosslinkRequestParams): Promise<{ smartCrosslinks: SmartCrosslinkResponse }> => {
  const [featureFlags, metaData, navData] = await Promise.all([featureFlagsPromise, seoPromise, navPromise]);
  const navigationFallback = getNavChildCategories({ metaData, navData });
  const url = metaData?.canonicalUrl;
  const breadcrumbs = metaData?.breadcrumbs;
  const pageType = 'product';
  let smartCrosslinks: SmartCrosslinkData | null = null;
  const requestType = getRequestType();

  if (featureFlags.enabledFeatures['adeptmind-crosslinks-pdp']) {
    const similarProductsApiEnabled = !!featureFlags.enabledFeatures['seo-am-use-similar-products-api'];

    smartCrosslinks = await requestCrosslinkData({
      brand,
      divisionName: breadcrumbs?.division?.name || '',
      market,
      pageName: breadcrumbs?.category?.name || '',
      pageType,
      similarProductsApiEnabled,
      url,
      requestType,
    });
  }

  if (smartCrosslinks?.error) {
    (smartCrosslinks as SmartCrosslinkResponse).errorLog = `[${smartCrosslinks.error?.type}] ${smartCrosslinks.error?.log}. ${
      smartCrosslinks.error?.errorName || ''
    }: ${smartCrosslinks.error?.errorMessage || ''}`;
  }

  if (smartCrosslinks) {
    return {
      smartCrosslinks: {
        ...smartCrosslinks,
        fallback: navigationFallback,
      },
    };
  }
  return {
    smartCrosslinks: {
      fallback: navigationFallback,
    },
  };
};

export default getSmartCrosslinkData;
