const formatCategory = (category: NavigationNode): NavigationNode & { ariaLabel: string } => {
  const { link, customUrl, name } = category;
  const preferredUrl = customUrl || link || '';
  const [urlWithoutHash, hashValue] = preferredUrl.split('#');
  const hash = hashValue ? `#${hashValue}` : '';
  const parameterDivider = preferredUrl.indexOf('?') > -1 ? '&' : '?';
  const trackingParam = 'cl=true';

  return {
    ...category,
    ariaLabel: name,
    link: `${urlWithoutHash}${parameterDivider}${trackingParam}${hash}`,
  };
};

export default formatCategory;
