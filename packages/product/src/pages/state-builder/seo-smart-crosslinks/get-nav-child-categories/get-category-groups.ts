const getCategoryGroups = (webHierarchy: NavigationNode[] = [], currentDivisionId = ''): NavigationNode[] =>
  webHierarchy
    .map(division => {
      if (division?.hasSubDivision && division?.children) {
        return division.children
          .map(subdivision => (subdivision.id === currentDivisionId ? subdivision.children : false))
          .filter((group): group is NavigationNode[] => Boolean(group))
          .reduce((allGroups, group) => allGroups.concat(group), []);
      }

      return division.id === currentDivisionId ? division.children : false;
    })
    .filter((group): group is NavigationNode[] => Boolean(group))
    .reduce((allGroups, group) => allGroups.concat(group), []);

export default getCategoryGroups;
