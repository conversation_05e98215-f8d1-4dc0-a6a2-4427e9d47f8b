import removeInvalidCategory from '../remove-invalid-category';

describe('Remove Invalid Category', () => {
  const currentCategoryId = '12345';
  const testCategory: NavigationNode = {
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1152367',
    link: '/browse/category.do?cid=1152367',
    name: 'Jumpsuits & Rompers',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  };

  test('should return false when test category is hidden', () => {
    const hiddenCategory = {
      ...testCategory,
      hidden: true,
    };

    expect(removeInvalidCategory({ category: hiddenCategory, currentCategoryId })).toBeFalsy();
  });

  test('should return false when test category id equals the current category id', () => {
    expect(removeInvalidCategory({ category: testCategory, currentCategoryId: testCategory.id })).toBeFalsy();
  });

  test('should return false when test category type is spacer', () => {
    const spacerCategory = {
      ...testCategory,
      type: 'spacer',
    };

    expect(removeInvalidCategory({ category: spacerCategory, currentCategoryId })).toBeFalsy();
  });

  test('should return false when test category has factory in its name', () => {
    const factoryCategory = {
      ...testCategory,
      name: `${testCategory.name} Factory`,
    };

    expect(removeInvalidCategory({ category: factoryCategory, currentCategoryId })).toBeFalsy();
  });

  test('should return true otherwise (i.e. when all above conditions are not met)', () => {
    expect(removeInvalidCategory({ category: testCategory, currentCategoryId })).toBeTruthy();
  });
});
