import formatCategory from '../format-category';

describe('Format Category Link', () => {
  const testCategory: NavigationNode = {
    children: [],
    customUrl: '/browse/category.do?cid=12345#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '29504',
    link: '/browse/category.do?cid=29504',
    name: 'Pajamas & Loungewear',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  };

  test('Should use category link when custom URL does not exist', () => {
    const categoryWithoutCustomURL = { ...testCategory };
    delete categoryWithoutCustomURL.customUrl;

    const formattedCategory = formatCategory(categoryWithoutCustomURL);

    expect(formattedCategory.link).toContain(categoryWithoutCustomURL.link);
  });

  test('Should give preference to category custom URL when it exists', () => {
    const formattedCategory = formatCategory(testCategory);

    expect(formattedCategory.link).toContain('/browse/category.do?cid=12345');
  });

  test('Should append cl=true param to category link', () => {
    const formattedCategory = formatCategory(testCategory);

    expect(formattedCategory.link).toContain('cl=true');
  });

  test('Should preserve existing URL hash values', () => {
    const formattedCategory = formatCategory(testCategory);

    expect(formattedCategory.link).toContain('#pageId=0&department=136');
  });

  test('Should use category name as aria label and add it to the category object', () => {
    const formattedCategory = formatCategory(testCategory);

    expect(formattedCategory.ariaLabel).toBeDefined();
    expect(formattedCategory.ariaLabel).toStrictEqual(formattedCategory.name);
  });
});
