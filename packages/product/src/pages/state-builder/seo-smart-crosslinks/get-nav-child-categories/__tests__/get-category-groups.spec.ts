import { mockDivisionWithNoSubdivision, mockDivisionWithSubdivision } from '../../fixtures/mock-navigation-response';
import getCategoryGroups from '../get-category-groups';

describe('Get Category Groups', () => {
  test('should filter through web hierarchy and return categories groups', () => {
    const division: NavigationNode = { ...mockDivisionWithNoSubdivision };
    const currentDivisionId = division.id;
    const webHierarchy: NavigationNode[] = [division];

    expect(getCategoryGroups(webHierarchy, currentDivisionId)).toStrictEqual(division.children);
  });

  test('should filter out divisions that do not match current division id', () => {
    const division1: NavigationNode = { ...mockDivisionWithNoSubdivision };
    const division2: NavigationNode = {
      ...mockDivisionWithNoSubdivision,
      id: '1234',
    };
    const currentDivisionId = division1.id;
    const webHierarchy: NavigationNode[] = [division1, division2];

    expect(getCategoryGroups(webHierarchy, currentDivisionId)).toStrictEqual(division1.children);
    expect(getCategoryGroups(webHierarchy, currentDivisionId)).not.toContain(division2.children);
  });

  test('should return category groups of subdivisions', () => {
    const division: NavigationNode = { ...mockDivisionWithSubdivision };
    const webHierarchy: NavigationNode[] = [division];
    const subdivision = division.children && division.children[0];
    const currentDivisionId = subdivision?.id as string;

    expect(getCategoryGroups(webHierarchy, currentDivisionId)).toStrictEqual(subdivision?.children);
  });

  test('should return empty array when current division id is not defined', () => {
    const division: NavigationNode = { ...mockDivisionWithNoSubdivision };
    const webHierarchy: NavigationNode[] = [division];
    const currentDivisionId = '';

    expect(getCategoryGroups(webHierarchy, currentDivisionId)).toStrictEqual([]);
  });

  test('should return empty array when web hierarchy data is not defined', () => {
    const division: NavigationNode = { ...mockDivisionWithNoSubdivision };
    const currentDivisionId = division.id;
    const webHierarchy: NavigationNode[] = [];

    expect(getCategoryGroups(webHierarchy, currentDivisionId)).toStrictEqual([]);
  });
});
