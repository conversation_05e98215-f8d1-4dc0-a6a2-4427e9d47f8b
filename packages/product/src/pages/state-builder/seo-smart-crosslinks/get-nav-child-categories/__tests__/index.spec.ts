import { mockNavResponse } from '../../fixtures/mock-navigation-response';
import mockSeoResponse from '../../fixtures/mock-seo-response';
import getNavChildCategories from '..';

describe('Get Navigation Child Categories', () => {
  const navData = mockNavResponse;
  const metaData = mockSeoResponse;

  test('should return an array of valid categories', () => {
    const navCategories = getNavChildCategories({ metaData, navData });

    expect(navCategories).toStrictEqual(
      expect.arrayContaining([
        expect.objectContaining({
          ariaLabel: expect.any(String),
          hidden: false,
          link: expect.any(String),
          name: expect.any(String),
          type: 'category',
        }),
      ])
    );
  });

  test('should append cl=true param to all category links', () => {
    const navCategories = getNavChildCategories({ metaData, navData });

    navCategories.forEach(category => {
      expect(category.link).toContain('cl=true');
    });
  });

  test('should use the category name for the aria label', () => {
    const navCategories = getNavChildCategories({ metaData, navData });

    navCategories.forEach(category => {
      expect(category.ariaLabel).toStrictEqual(category.name);
    });
  });

  test('should return an empty array when the navigation call fails', () => {
    const defaultNavigationResponse = { children: [] } as unknown as NavigationNode;
    const navCategories = getNavChildCategories({ metaData, navData: defaultNavigationResponse });

    expect(navCategories).toStrictEqual([]);
  });

  test('should return an empty array when selected nodes are not defined in the seo meta data', () => {
    const metaDataWithoutSelectedNodes = { ...metaData, selectedNodes: [] };
    const navCategories = getNavChildCategories({ metaData: metaDataWithoutSelectedNodes, navData });

    expect(navCategories).toStrictEqual([]);
  });
});
