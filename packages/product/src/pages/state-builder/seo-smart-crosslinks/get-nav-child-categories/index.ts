import formatCategory from './format-category';
import getCategoryGroups from './get-category-groups';
import removeInvalidCategory from './remove-invalid-category';

const getNavChildCategories = ({ metaData, navData }: { metaData: SeoResponse; navData: NavigationNode }): (NavigationNode & { ariaLabel: string })[] => {
  const selectedNodes: SelectedNodes = metaData.selectedNodes || [];
  const webHierarchy: NavigationNode[] = navData.children || [];

  if (!selectedNodes.length || !webHierarchy.length) {
    return [];
  }

  const currentCategory: Partial<SelectedNode> = selectedNodes.find(({ type }) => type === 'category' || type === 'sale') || {};
  const currentDivision: Partial<SelectedNode> = selectedNodes.find(({ type }) => type === 'division' || type === 'sub-division') || {};

  const currentCategoryId: string = currentCategory.id || '';
  const currentDivisionId: string = currentDivision.id || '';

  return getCategoryGroups(webHierarchy, currentDivisionId)
    .reduce((allCategories: NavigationNode[], group: NavigationNode) => allCategories.concat(group.children as NavigationNode[]), [])
    .filter(category => removeInvalidCategory({ category, currentCategoryId }))
    .map(category => formatCategory(category));
};

export default getNavChildCategories;
