const mockSeoResponse: SeoResponse = {
  breadcrumbs: {
    category: { name: 'category-name', url: 'category-url' },
    division: { name: 'division-name', url: 'division-url' },
  },
  canonicalUrl: 'product-url',
  department: 'some-department',
  description: 'some-description',
  htmlLangAttribute: 'en_US',
  selectedNodes: [
    {
      id: '5646',
      name: 'Womens',
      type: 'sub-division',
    },
    {
      id: '5664',
      name: '<PERSON><PERSON>',
      type: 'category',
    },
  ],
  seoBreadcrumbSchema: { '@': 'breadcrumbList' },
  seoPageSchema: { '@': 'product' },
  title: 'some-title',
};

export default mockSeoResponse;
