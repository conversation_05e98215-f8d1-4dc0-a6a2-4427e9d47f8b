const mockInterlinkData = {
  crosslinks: {
    'https://oldnavy.gap.com/shop/example-page-1': 'DLP Page Title 1',
    'https://oldnavy.gap.com/shop/example-page-2': 'DLP Page Title 2',
    'https://oldnavy.gap.com/shop/example-page-3': 'DLP Page Title 3',
    'https://oldnavy.gap.com/shop/example-page-4': 'DLP Page Title 4',
    'https://oldnavy.gap.com/shop/example-page-5': 'DLP Page Title 5',
  },
};

const mockInterlinkError = {
  error: {
    brand: 'gap',
    market: 'us',
    divisionName: 'division-name',
    errorMessage: 'errorMessage',
    errorName: 'errorName',
    log: 'log',
    pageName: 'category-name',
    pageType: 'product',
    type: 'type',
    url: 'product-url',
  },
};

const mockInterlinkDataWithError = {
  ...mockInterlinkData,
  ...mockInterlinkError,
};

export { mockInterlinkData, mockInterlinkDataWithError, mockInterlinkError };
