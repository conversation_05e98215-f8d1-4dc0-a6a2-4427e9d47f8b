const nameJustArrived = 'Just Arrived';
const nameNewArrivals = 'New Arrivals';
const nameShopAllStyles = 'Shop All Styles';
const cidLink8792 = '/browse/category.do?cid=8792';
const cidLink5643 = '/browse/division.do?cid=5643';
const customUrl = '/browse/category.do?cid=8792#pageId=0&department=136';

const expectedFallback = [
  {
    ariaLabel: 'New Arrivals',
    children: [],
    customUrl: '/browse/category.do?cid=8792#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '8792',
    link: '/browse/category.do?cid=8792&cl=true#pageId=0&department=136',
    name: 'New Arrivals',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Fall Campaign',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1183206',
    link: '/browse/category.do?cid=1183206&cl=true',
    name: 'The Fall Campaign',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Gap Collective',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1164042',
    link: '/browse/category.do?cid=1164042&cl=true',
    name: 'The Gap Collective',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Matching Soft Sets Shop',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1165327',
    link: '/browse/category.do?cid=1165327&cl=true',
    name: 'The Matching Soft Sets Shop',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Everyone’s Matching!',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1157520',
    link: '/browse/category.do?cid=1157520&cl=true',
    name: 'Everyone’s Matching!',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Responsibly-Made Shop ',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1147561',
    link: '/browse/category.do?cid=1147561&cl=true',
    name: 'Responsibly-Made Shop ',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shop All Styles',
    children: [],
    customUrl: '/browse/category.do?cid=1127938#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1127938',
    link: '/browse/category.do?cid=1127938&cl=true#pageId=0&department=136',
    name: 'Shop All Styles',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Dresses',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '13658',
    link: '/browse/category.do?cid=13658&cl=true',
    name: 'Dresses',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Jumpsuits & Rompers',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1152367',
    link: '/browse/category.do?cid=1152367&cl=true',
    name: 'Jumpsuits & Rompers',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'T-Shirts',
    children: [],
    customUrl: '/browse/category.do?cid=17076#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '17076',
    link: '/browse/category.do?cid=17076&cl=true#pageId=0&department=136',
    name: 'T-Shirts',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shirts & Tops',
    children: [],
    customUrl: '/browse/category.do?cid=34608#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '34608',
    link: '/browse/category.do?cid=34608&cl=true#pageId=0&department=136',
    name: 'Shirts & Tops',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Hoodies',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1167927',
    link: '/browse/category.do?cid=1167927&cl=true',
    name: 'Hoodies',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Sweatshirts & Sweatpants',
    children: [],
    customUrl: '/browse/category.do?cid=1041168#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1041168',
    link: '/browse/category.do?cid=1041168&cl=true#pageId=0&department=136',
    name: 'Sweatshirts & Sweatpants',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Sweaters ',
    children: [],
    customUrl: '/browse/category.do?cid=5745#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '5745',
    link: '/browse/category.do?cid=5745&cl=true#pageId=0&department=136',
    name: 'Sweaters ',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Outerwear & Jackets',
    children: [],
    customUrl: '/browse/category.do?cid=5736#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '5736',
    link: '/browse/category.do?cid=5736&cl=true#pageId=0&department=136',
    name: 'Outerwear & Jackets',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Pants',
    children: [],
    customUrl: '/browse/category.do?cid=1011761#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1011761',
    link: '/browse/category.do?cid=1011761&cl=true#pageId=0&department=136',
    name: 'Pants',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Joggers',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1169932',
    link: '/browse/category.do?cid=1169932&cl=true',
    name: 'Joggers',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shorts',
    children: [],
    customUrl: '/browse/category.do?cid=1041308#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1041308',
    link: '/browse/category.do?cid=1041308&cl=true#pageId=0&department=136',
    name: 'Shorts',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Skirts',
    children: [],
    customUrl: '/browse/category.do?cid=1082574#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1082574',
    link: '/browse/category.do?cid=1082574&cl=true#pageId=0&department=136',
    name: 'Skirts',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Swim ',
    children: [],
    customUrl: '/browse/category.do?cid=1027291#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1027291',
    link: '/browse/category.do?cid=1027291&cl=true#pageId=0&department=136',
    name: 'Swim ',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shoes & Accessories ',
    children: [],
    customUrl: '/browse/category.do?cid=35300#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '35300',
    link: '/browse/category.do?cid=35300&cl=true#pageId=0&department=136',
    name: 'Shoes & Accessories ',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Cold Weather Accessories',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1184924',
    link: '/browse/category.do?cid=1184924&cl=true',
    name: 'Cold Weather Accessories',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Face Masks',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1157839',
    link: '/browse/category.do?cid=1157839&cl=true',
    name: 'Face Masks',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shop All Styles',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1117374',
    link: '/browse/category.do?cid=1117374&cl=true',
    name: 'Shop All Styles',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Sports Bras ',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '83221',
    link: '/browse/category.do?cid=83221&cl=true',
    name: 'Sports Bras ',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Tops',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1006976',
    link: '/browse/category.do?cid=1006976&cl=true',
    name: 'Tops',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Sweatshirts & Jackets',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '83072',
    link: '/browse/category.do?cid=83072&cl=true',
    name: 'Sweatshirts & Jackets',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Leggings & Joggers',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1005439',
    link: '/browse/category.do?cid=1005439&cl=true',
    name: 'Leggings & Joggers',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shorts',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1056307',
    link: '/browse/category.do?cid=1056307&cl=true',
    name: 'Shorts',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Shop All Styles',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1140272',
    link: '/browse/category.do?cid=1140272&cl=true',
    name: 'Shop All Styles',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Bras',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '34524',
    link: '/browse/category.do?cid=34524&cl=true',
    name: 'Bras',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Undies',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1015387',
    link: '/browse/category.do?cid=1015387&cl=true',
    name: 'Undies',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Pajamas & Loungewear',
    children: [],
    customUrl: '/browse/category.do?cid=29504#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '29504',
    link: '/browse/category.do?cid=29504&cl=true#pageId=0&department=136',
    name: 'Pajamas & Loungewear',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Petite Shop',
    children: [],
    customUrl: '/browse/category.do?cid=1026609#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1026609',
    link: '/browse/category.do?cid=1026609&cl=true#pageId=0&department=136',
    name: 'The Petite Shop',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Tall Shop',
    children: [],
    customUrl: '/browse/category.do?cid=1026616#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '1026616',
    link: '/browse/category.do?cid=1026616&cl=true#pageId=0&department=136',
    name: 'The Tall Shop',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Maternity',
    children: [],
    customUrl: '/browse/category.do?cid=1127956',
    hasSubDivision: false,
    hidden: false,
    id: '1131699',
    link: '/browse/category.do?cid=1127956&cl=true',
    name: 'Maternity',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Sale',
    children: [],
    customUrl: '/browse/category.do?cid=65179#pageId=0&department=136',
    hasSubDivision: false,
    hidden: false,
    id: '65179',
    link: '/browse/category.do?cid=65179&cl=true#pageId=0&department=136',
    name: 'Sale',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'sale',
  },
  {
    ariaLabel: 'Final Sale',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1015684',
    link: '/browse/category.do?cid=1015684&cl=true',
    name: 'Final Sale',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'sale',
  },
  {
    ariaLabel: 'GapFit Shop by Activity',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1131377',
    link: '/browse/category.do?cid=1131377&cl=true',
    name: 'GapFit Shop by Activity',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'The Logo Shop',
    children: [],
    hasSubDivision: false,
    hidden: false,
    id: '1052025',
    link: '/browse/category.do?cid=1052025&cl=true',
    name: 'The Logo Shop',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Teen Collection',
    children: [],
    customUrl: '/browse/category.do?cid=1127146',
    hasSubDivision: false,
    hidden: false,
    id: '1158115',
    link: '/browse/category.do?cid=1127146&cl=true',
    name: 'Teen Collection',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
  {
    ariaLabel: 'Gift Cards',
    children: [],
    customUrl: '/customerService/info.do?cid=2116',
    hasSubDivision: false,
    hidden: false,
    id: '1185067',
    link: '/customerService/info.do?cid=2116&cl=true',
    name: 'Gift Cards',
    parents: ['5646', '5643', '5058'],
    selected: false,
    type: 'category',
  },
];

const mockNavResponse: NavigationNode = {
  children: [
    {
      children: [
        {
          children: [
            {
              children: [
                {
                  children: [],
                  customUrl,
                  hasSubDivision: false,
                  hidden: false,
                  id: '8792',
                  link: cidLink8792,
                  name: nameNewArrivals,
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1183206',
                  link: '/browse/category.do?cid=1183206',
                  name: 'The Fall Campaign',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1164042',
                  link: '/browse/category.do?cid=1164042',
                  name: 'The Gap Collective',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1165327',
                  link: '/browse/category.do?cid=1165327',
                  name: 'The Matching Soft Sets Shop',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1163597',
                  link: '/browse/category.do?cid=1163597',
                  name: 'Workforce Collection',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1157520',
                  link: '/browse/category.do?cid=1157520',
                  name: 'Everyone’s Matching!',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1147561',
                  link: '/browse/category.do?cid=1147561',
                  name: 'Responsibly-Made Shop ',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1164545',
              name: nameJustArrived,
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [],
              hasSubDivision: false,
              hidden: false,
              id: '1185253',
              name: 'Gifts',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'header',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1127938#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1127938',
                  link: '/browse/category.do?cid=1127938',
                  name: nameShopAllStyles,
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=5664#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '5664',
                  link: '/browse/category.do?cid=5664',
                  name: 'Jeans',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '13658',
                  link: '/browse/category.do?cid=13658',
                  name: 'Dresses',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1152367',
                  link: '/browse/category.do?cid=1152367',
                  name: 'Jumpsuits & Rompers',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=17076#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '17076',
                  link: '/browse/category.do?cid=17076',
                  name: 'T-Shirts',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=34608#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '34608',
                  link: '/browse/category.do?cid=34608',
                  name: 'Shirts & Tops',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1167927',
                  link: '/browse/category.do?cid=1167927',
                  name: 'Hoodies',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1041168#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1041168',
                  link: '/browse/category.do?cid=1041168',
                  name: 'Sweatshirts & Sweatpants',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=5745#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '5745',
                  link: '/browse/category.do?cid=5745',
                  name: 'Sweaters ',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=5736#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '5736',
                  link: '/browse/category.do?cid=5736',
                  name: 'Outerwear & Jackets',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1011761#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1011761',
                  link: '/browse/category.do?cid=1011761',
                  name: 'Pants',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1169932',
                  link: '/browse/category.do?cid=1169932',
                  name: 'Joggers',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1041308#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1041308',
                  link: '/browse/category.do?cid=1041308',
                  name: 'Shorts',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1082574#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1082574',
                  link: '/browse/category.do?cid=1082574',
                  name: 'Skirts',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1027291#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1027291',
                  link: '/browse/category.do?cid=1027291',
                  name: 'Swim ',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=35300#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '35300',
                  link: '/browse/category.do?cid=35300',
                  name: 'Shoes & Accessories ',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1184924',
                  link: '/browse/category.do?cid=1184924',
                  name: 'Cold Weather Accessories',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1157839',
                  link: '/browse/category.do?cid=1157839',
                  name: 'Face Masks',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              customUrl: cidLink5643,
              hasSubDivision: false,
              hidden: false,
              id: '1042481',
              name: 'Categories',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/info.do?cid=1171233',
                  hasSubDivision: false,
                  hidden: true,
                  id: '1172898',
                  link: '/browse/category.do?cid=1172898',
                  name: 'GapFit for Life',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1117374',
                  link: '/browse/category.do?cid=1117374',
                  name: nameShopAllStyles,
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '83221',
                  link: '/browse/category.do?cid=83221',
                  name: 'Sports Bras ',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1006976',
                  link: '/browse/category.do?cid=1006976',
                  name: 'Tops',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '83072',
                  link: '/browse/category.do?cid=83072',
                  name: 'Sweatshirts & Jackets',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1005439',
                  link: '/browse/category.do?cid=1005439',
                  name: 'Leggings & Joggers',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1056307',
                  link: '/browse/category.do?cid=1056307',
                  name: 'Shorts',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1131696',
              name: 'Activewear',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'header',
            },
            {
              children: [
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1140272',
                  link: '/browse/category.do?cid=1140272',
                  name: nameShopAllStyles,
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '34524',
                  link: '/browse/category.do?cid=34524',
                  name: 'Bras',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1015387',
                  link: '/browse/category.do?cid=1015387',
                  name: 'Undies',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=29504#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '29504',
                  link: '/browse/category.do?cid=29504',
                  name: 'Pajamas & Loungewear',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '5903',
              name: 'GapBody',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'header',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1026609#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1026609',
                  link: '/browse/category.do?cid=1026609',
                  name: 'The Petite Shop',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1026616#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1026616',
                  link: '/browse/category.do?cid=1026616',
                  name: 'The Tall Shop',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1127956',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1131699',
                  link: '/browse/category.do?cid=1131699',
                  name: 'Maternity',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1131698',
              name: 'More Sizes',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=65179#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: false,
                  id: '65179',
                  link: '/browse/category.do?cid=65179',
                  name: 'Sale',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'sale',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1015684',
                  link: '/browse/category.do?cid=1015684',
                  name: 'Final Sale',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'sale',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1122595',
              name: 'Deals',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1131377',
                  link: '/browse/category.do?cid=1131377',
                  name: 'GapFit Shop by Activity',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1153129',
                  link: '/browse/category.do?cid=1153129',
                  name: 'Relearn the Khaki',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1125585',
                  link: '/browse/category.do?cid=1125585',
                  name: 'Body First Layer Essentials',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: false,
                  id: '1052025',
                  link: '/browse/category.do?cid=1052025',
                  name: 'The Logo Shop',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1127146',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1158115',
                  link: '/browse/category.do?cid=1158115',
                  name: 'Teen Collection',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1072170',
                  link: '/browse/category.do?cid=1072170',
                  name: 'Washwell™ Denim',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1131702',
              name: 'Featured Shops',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=85615#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: true,
                  id: '85615',
                  link: '/browse/category.do?cid=85615',
                  name: 'Shoes',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: true,
              id: '1047701',
              name: 'Featured Shops',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'header',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/browse/category.do?cid=1088544#pageId=0&department=136',
                  hasSubDivision: false,
                  hidden: true,
                  id: '1088544',
                  link: '/browse/category.do?cid=1088544',
                  name: 'Bags',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: true,
              id: '1131695',
              name: 'Activewear',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1069608',
                  link: '/browse/category.do?cid=1069608',
                  name: 'DK TEST CATEGORY',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '72319',
                  link: '/browse/category.do?cid=72319',
                  name: 'Facet Test - Girls Uniform Gym',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: true,
              id: '1131697',
              name: 'Love by Gap',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
            {
              children: [
                {
                  children: [],
                  hasSubDivision: false,
                  hidden: true,
                  id: '1173387',
                  link: '/browse/category.do?cid=1173387',
                  name: 'Spring Flow',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: true,
              id: '1131700',
              name: 'Sale: 40% Off All Styles',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'header',
            },
            {
              children: [
                {
                  children: [],
                  customUrl: '/customerService/info.do?cid=2116',
                  hasSubDivision: false,
                  hidden: false,
                  id: '1185067',
                  link: '/browse/category.do?cid=1185067',
                  name: 'Gift Cards',
                  parents: ['5646', '5643', '5058'],
                  selected: false,
                  type: 'category',
                },
              ],
              hasSubDivision: false,
              hidden: false,
              id: '1185066',
              name: 'Gift Cards',
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'trimheader',
            },
          ],
          hasSubDivision: false,
          hidden: false,
          id: '5646',
          link: '/browse/subDivision.do?cid=5646',
          name: 'Womens',
          parents: ['5643', '5058'],
          selected: false,
          type: 'sub-division',
        },
      ],
      hasSubDivision: true,
      hidden: false,
      id: '5643',
      link: cidLink5643,
      name: 'Women',
      parents: ['5058'],
      selected: false,
      type: 'division',
    },
  ],
  hasSubDivision: false,
  id: '5058',
  name: 'Home',
  parents: [],
  type: 'home',
};

const mockDivisionWithSubdivision: NavigationNode = {
  children: [
    {
      children: [
        {
          children: [
            {
              children: [],
              customUrl,
              hasSubDivision: false,
              hidden: false,
              id: '8792',
              link: cidLink8792,
              name: nameNewArrivals,
              parents: ['5646', '5643', '5058'],
              selected: false,
              type: 'category',
            },
          ],
          hasSubDivision: false,
          hidden: false,
          id: '1164545',
          name: nameJustArrived,
          parents: ['5646', '5643', '5058'],
          selected: false,
          type: 'trimheader',
        },
      ],
      hasSubDivision: false,
      hidden: false,
      id: '5646',
      link: '/browse/subDivision.do?cid=5646',
      name: 'Womens',
      parents: ['5643', '5058'],
      selected: false,
      type: 'sub-division',
    },
  ],
  hasSubDivision: true,
  hidden: false,
  id: '5643',
  link: cidLink5643,
  name: 'Women',
  parents: ['5058'],
  selected: false,
  type: 'division',
};

const mockDivisionWithNoSubdivision: NavigationNode = {
  children: [
    {
      children: [
        {
          children: [],
          customUrl,
          hasSubDivision: false,
          hidden: false,
          id: '8792',
          link: cidLink8792,
          name: nameNewArrivals,
          parents: ['5646', '5643', '5058'],
          selected: false,
          type: 'category',
        },
      ],
      hasSubDivision: false,
      hidden: false,
      id: '1164545',
      name: nameJustArrived,
      parents: ['5646', '5643', '5058'],
      selected: false,
      type: 'trimheader',
    },
  ],
  hasSubDivision: false,
  hidden: false,
  id: '5643',
  link: cidLink5643,
  name: 'Women',
  parents: ['5058'],
  selected: false,
  type: 'division',
};

export { expectedFallback, mockDivisionWithNoSubdivision, mockDivisionWithSubdivision, mockNavResponse };
