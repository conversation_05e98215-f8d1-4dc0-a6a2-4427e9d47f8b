import type { PageAppState } from '@ecom-next/sitewide/app-state-provider';
import type { ProductAvailability } from '@pdp/types/product-data/product-availability';
import { fetchEnableFeatures } from '@ecom-next/sitewide/state-builder';
import type { CAPIAggregationService, ServicesArgs } from '../services/capi-aggregation-service';
import capiAggregationService from '../services/capi-aggregation-service';
import { getInitialSelection } from '../services/capi-aggregation-service/adapters/initial-selections';
import productAvailabilityRequest from '../services/productavailability-service';
import { ProductData } from '../../capi-utils';
import { isPDPErrors } from '../Errors';
import { filterOOSVariants, oosColorFilter } from './oos-color-filter';
import { getModelSize, handleModelToggle } from './model-size';

import getLPOFeaturesStatus from './get-lpo-features-status';
import injectPasBopisData from './pas-availability';

export type UrlParamsArgs = ServicesArgs & {
  abSeg: PageAppState['abSeg'];
  apiConfig: {
    catalogStyleLookupUrl: string;
    internalOidcUrl: string;
  };
  cid: string;
  featureFlagsPromise: ReturnType<typeof fetchEnableFeatures>;
  modelSizeCookieValue: string | undefined;
  modelSizeQuery: string | undefined;
  pid: string;
  requestType: string;
  sanitizedQuery: Record<string, string>;
  storeId: string;
  vid: string;
};

export type ModelSize = {
  modelSize?: string;
};

type GetProductDataReturns = CAPIAggregationService & { rawResponse: ProductData } & ModelSize & { pasData: ProductAvailability | undefined };

const getProductData = async ({
  abSeg,
  brand,
  market,
  country,
  locale,
  cid,
  pid,
  vid,
  modelSizeCookieValue,
  modelSizeQuery,
  requestType,
  date,
  sanitizedQuery,
  storeId,
  apiConfig,
  featureFlagsPromise,
}: UrlParamsArgs): Promise<GetProductDataReturns> => {
  const { enabledFeatures, featureVariables } = await featureFlagsPromise;
  let modelSize = getModelSize({
    brand,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    featureVariables: featureVariables as any,
    modelSizeCookieValue,
    modelSizeQuery,
  });
  const oosFlag = enabledFeatures['pdp-oos-considered-bopis'];
  const oosLPOTemplate = enabledFeatures['pdp-optimized-oos'];
  const storeExistsAndIncludeOOS = storeId && oosFlag;

  const capiData = capiAggregationService({
    brand,
    catalogStyleLookupUrl: apiConfig.catalogStyleLookupUrl,
    cid,
    country,
    date,
    featureFlagsPromise,
    internalOidcUrl: apiConfig.internalOidcUrl,
    locale,
    market,
    pid,
    requestType,
    vid,
  });

  let pasPromise;
  if (storeExistsAndIncludeOOS) {
    pasPromise = productAvailabilityRequest({
      brand,
      country,
      market,
      internalOidcUrl: apiConfig.internalOidcUrl,
      pid,
      storeId,
    });
  } else {
    pasPromise = Promise.resolve();
  }
  const [pssResult, pasResult, featureFlags] = await Promise.all([capiData, pasPromise, featureFlagsPromise]);
  // inject pas data into pss data
  // Check if capiData is an Error
  if (isPDPErrors(pssResult)) {
    throw pssResult;
  }
  const rawResponse = pssResult.rawResponse;
  const { productData, priceData } = pssResult.adaptedResponse;
  let parseProductData = productData;
  const { variants } = productData;
  if (storeExistsAndIncludeOOS) {
    injectPasBopisData({
      apiData: pasResult as ProductAvailability,
      selectedColor: null,
      selectedSize: null,
      selectedVariant: null,
      updateVariant: oosFlag,
      variants,
    });
  }

  modelSize = handleModelToggle({
    currentModelSize: modelSize,
    modelSizes: productData.infoTabs?.overview?.modelSizes?.[0],
  });

  if (variants && Array.isArray(variants)) {
    productData.variants = oosColorFilter(productData.variants, productData.inStock, oosLPOTemplate);
    productData.variants = filterOOSVariants(productData.variants, oosLPOTemplate);

    if (productData.variants.length > 0 && !productData.variants.every(variant => variant.link)) {
      // delete redirect key
      delete productData.redirect;
      delete productData.redirectUrl;

      parseProductData = {
        ...productData,
        ...getInitialSelection({
          defaultVariantId: productData.defaultVariantId,
          pid,
          priceData,
          variants: productData.variants,
          vid,
        }),
      };
    }
  }

  return {
    features: {
      ...getLPOFeaturesStatus({
        abSeg,
        brand,
        featureFlags: featureFlags.enabledFeatures,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        featureVariables: featureFlags.featureVariables as any,
        inStock: parseProductData.inStock,
        market,
        styleId: productData.styleId,
        tid: sanitizedQuery?.tid,
      }),
    },
    modelSize,
    pasData: pasResult,
    productData: parseProductData,
    rawResponse,
  } as CAPIAggregationService & { rawResponse: ProductData } & ModelSize & { pasData: ProductAvailability | undefined };
};

export default getProductData;
