export const getTIDFromUrl = (paramTid = ''): string => {
  if (typeof window !== 'undefined') {
    const { search } = window.location;

    const urlParams = new URLSearchParams(search);

    return urlParams.get('tid') || '';
  }

  return paramTid;
};

export const isSupportedTid = (tids = '', tidFromUrl = ''): boolean => {
  if (!tids || !tidFromUrl) {
    return false;
  }

  const listOfTids = tids.toString().split?.(',');

  return listOfTids.map(tid => tid?.trim?.()).includes(tidFromUrl.toString().trim?.());
};
