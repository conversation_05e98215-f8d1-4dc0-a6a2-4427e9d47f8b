type Node = NavigationNode & {
  selected?: boolean;
};
type MatchingNodes = Node[];

const isDivisionType = (node: Node): boolean => node.type === 'sub-division' || (node.type === 'division' && !node.hasSubDivision);

const markAndReturnSelected = (pcid: string, rootNode: Node): MatchingNodes => {
  const found: MatchingNodes = [];
  const stack: { checkedChildren: boolean; node: Node }[] = [{ checkedChildren: false, node: rootNode }];

  while (stack.length > 0) {
    const { node, checkedChildren } = stack.pop()!;

    if (!checkedChildren) {
      if (pcid && pcid === node.id) {
        found.push({
          ...node,
          selected: true,
        });
      }

      if (node.children && node.children.length) {
        stack.push({ checkedChildren: true, node });
        /* eslint-disable no-plusplus */
        for (let i = node.children.length - 1; i >= 0; i--) {
          stack.push({ checkedChildren: false, node: node.children[i] });
        }
      }
    } else {
      const hasMatchingChildNode = found.length && isDivisionType(node);

      if (hasMatchingChildNode) {
        found.push({
          ...node,
          selected: true,
        });
        // breaks the loop after the first matching child node is found
        break;
      }
    }
  }

  return found;
};

type BuildSelectedNodesArgs = {
  nav: NavigationNode | void;
  pcid: string;
};

function buildSelectedNodes({ pcid, nav }: BuildSelectedNodesArgs): SelectedNode[] {
  if (!nav) {
    return [];
  }
  const selectedNodes = markAndReturnSelected(pcid, nav).reverse() || [];
  return selectedNodes.map(({ id, type, name }) => ({ id, name, type }));
}

export { buildSelectedNodes };
