import { buildSelectedNodes } from '../selected-nodes';
import { navServiceResponse1, navServiceResponse2, navServiceResponse3, pcid, result1, result2 } from './selected-nodes-test-helper';

describe('Selected Nodes', () => {
  describe('when pcid and nav are not null', () => {
    describe('when the pcid matches ids within the nodes array', () => {
      test('returns an array of matching nodes when the parent division id matches the pcid and/or cid', () => {
        const result = buildSelectedNodes({ nav: navServiceResponse1, pcid });
        expect(result).toStrictEqual(result1);
      });
      test('returns an array with a child node and a parent node when the parent id does not match the pcid/cid but the child node id does', () => {
        const result = buildSelectedNodes({ nav: navServiceResponse2, pcid });
        expect(result).toStrictEqual(result2);
      });
    });
    test('returns an empty array when the pcid and cid do not match any nodes', () => {
      const result = buildSelectedNodes({ nav: navServiceResponse3, pcid });
      expect(result).toHaveLength(0);
    });
  });
  test('returns an empty array when a pcid and a cid are not provided', () => {
    const result = buildSelectedNodes({ nav: navServiceResponse1, pcid: '' });
    expect(result).toHaveLength(0);
  });
});
