export const pcid = '1038092';
const divisionType = 'division';
const subDivisionType = 'sub-division';

export const navServiceResponse1: NavigationNode = {
  children: [
    {
      hasSubDivision: false,
      id: pcid,
      name: 'Boys',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: '83063',
      name: 'Girls',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: '83064',
      name: '<PERSON><PERSON>',
      type: subDivisionType,
    },
  ],
  hasSubDivision: false,
  id: pcid,
  name: 'GapFit',
  type: divisionType,
};

export const navServiceResponse2: NavigationNode = {
  children: [
    {
      hasSubDivision: false,
      id: pcid,
      name: 'Boys',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: '83063',
      name: 'Girls',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: pcid,
      name: '<PERSON><PERSON>',
      type: subDivisionType,
    },
  ],
  hasSubDivision: false,
  id: '83063',
  name: '<PERSON>F<PERSON>',
  type: divisionType,
};

export const navServiceResponse3: NavigationNode = {
  children: [
    {
      hasSubDivision: false,
      id: '83063',
      name: 'Boys',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: '83063',
      name: 'Girls',
      type: divisionType,
    },
    {
      hasSubDivision: false,
      id: '83063',
      name: 'Jeans',
      type: subDivisionType,
    },
  ],
  hasSubDivision: false,
  id: '83063',
  name: 'GapFit',
  type: divisionType,
};

export const result1 = [
  { id: '1038092', name: 'GapFit', type: divisionType },
  {
    id: '1038092',
    name: 'Boys',
    type: 'division',
  },
  {
    id: '1038092',
    name: 'GapFit',
    type: 'division',
  },
];

export const result2 = [
  { id: '83063', name: 'GapFit', type: divisionType },
  { id: '1038092', name: 'Jeans', type: 'sub-division' },
  {
    id: '1038092',
    name: 'Boys',
    type: divisionType,
  },
];
