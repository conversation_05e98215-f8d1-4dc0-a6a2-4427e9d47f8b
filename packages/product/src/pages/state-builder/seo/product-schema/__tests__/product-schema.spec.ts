import type { ProductInfoTabs, ProductVariant } from '@pdp/types/product-data/style-level';

import logger from '@ecom-next/seo-helpers/logger';
import type { ProductSchemaArgs, ProductSchemaMvgArgs } from '../product-schema';
import { buildProductSchema, buildProductSchemaMvg } from '../product-schema';
import { CAPIV3TransformedData } from '../../../../../pages/services/capi-aggregation-service';
import {
  infoTabs,
  infoTabsMvg,
  productImages,
  productImagesMvg,
  reviewRatings,
  reviews,
  selectedColor,
  selectedColorMvg,
  variants,
  variantsMvg,
} from './product-schema-fixture';
import { GetEnabledFeaturesAndVariablesType } from '@/src/pages/getPageState';
import { InitialSelections } from '@/src/pages/services/capi-aggregation-service/v3/product-data-transformer/getInitialSelection';

jest.mock('@ecom-next/seo-helpers/logger');

describe('Product Schema', () => {
  const args = (overrides?: Partial<ProductSchemaArgs>, enabledFeatures?: Record<string, boolean>): ProductSchemaArgs => ({
    currencyCode: 'USD',
    displayName: 'Gap',
    featureFlags: {
      enabledFeatures: {
        'pdp-power-reviews': false,
        ...enabledFeatures,
      },
      featureVariables: {},
    },
    hostname: 'test.host',
    infoTabs: infoTabs(),
    productImagesData: productImages(),
    productName: 'Climbing Shirt',
    reviewRatings: reviewRatings(),
    reviews: reviews(),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedColor: selectedColor() as any,
    styleId: '260030',
    variants: variants(),
    ...overrides,
  });

  describe('buildProductSchema', () => {
    test('should return schema that contains @context', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema['@context']).toBe('https://schema.org');
    });

    test('should return schema that contains @type', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema['@type']).toBe('Product');
    });

    test('should return schema that contains the name of the product', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.name).toBe('Climbing Shirt');
    });

    test('should return schema that contains the specific 6 digit product id', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.productID).toBe('260030');
    });

    test('should return schema that contains the 9 digit product id when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const productSchema = buildProductSchema(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
      expect(productSchema.productID).toBe('123456789');
    });

    test('should return schema that contains the brand and its type', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.brand['@type']).toBe('Brand');
    });

    test('should return schema that contains brand attr and its brand name', () => {
      const productSchema = buildProductSchema(
        args({
          displayName: 'Banana Republic',
        })
      );
      expect(productSchema.brand.name).toBe('Banana Republic');
    });

    test('should return schema that contains color when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const productSchema = buildProductSchema(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
      expect(productSchema.color).toBe('red');
    });
  });

  describe('AggregateRating', () => {
    test("shouldn't return aggregateRating when power-reviews feature flag is disabled", () => {
      const productSchema = buildProductSchema(args());
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test("shouldn't return aggregateRating when there's not any rating", () => {
      const argsWithEmptyRatings = args();
      argsWithEmptyRatings.reviewRatings = {};
      const productSchema = buildProductSchema(argsWithEmptyRatings);
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test("shouldn't return aggregateRating when the rating count is 0", () => {
      const zeroRatingCount = {
        average_rating: 0,
        ratingHistogram: [],
        rating_count: 0,
        reviewHistogram: [],
        review_count: 0,
      };
      const argsWithZeroRatingCount = args();
      argsWithZeroRatingCount.reviewRatings = zeroRatingCount;
      const productSchema = buildProductSchema(argsWithZeroRatingCount);
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test('should return aggregateRating in a correct format when there is a power-reviews feature flag is enabled', () => {
      const productSchema = buildProductSchema(
        args(
          {},
          {
            'pdp-power-reviews': true,
          }
        )
      );
      expect(Object.prototype.hasOwnProperty.call(productSchema, 'aggregateRating')).toBe(true);
      expect(productSchema.aggregateRating['@type']).toBe('AggregateRating');
      expect(productSchema.aggregateRating.ratingValue).toBe(args().reviewRatings.average_rating);
      expect(productSchema.aggregateRating.ratingCount).toBe(args().reviewRatings.rating_count);
    });
  });

  describe('createDescription', () => {
    test('should not throw error when infotabs is empty', () => {
      const newArgs = args({
        infoTabs: {} as ProductInfoTabs,
      });

      expect(() => buildProductSchema(newArgs)).not.toThrow();

      const result = buildProductSchema(newArgs);
      expect(result.description).toBe('');
    });

    test('should return schema that contains the description of the product', () => {
      const result = buildProductSchema(args());
      expect(result.description).toStrictEqual(
        "Travis Lombardo psyches up for the committing 'brain-mantel' finish. " + 'Travis Lombardo tries not to get closed out on The Wave!'
      );
    });
  });

  describe('Offers', () => {
    test('should return schema that contains offers', () => {
      const productSchema = buildProductSchema(args());
      expect(Array.isArray(productSchema.offers)).toBe(true);
    });

    test('should return an offer with the type being Offer', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0]['@type']).toBe('Offer');
    });

    test('should return an offer with the current price from rawCurrentPrice if this is not empty', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].price).toBe('19.50');
      expect(typeof productSchema.offers[0].price).toBe('string');
    });

    test('should return an offer with price from rawRegularPrice if rawCurrentPrice is null', () => {
      const { variants } = args();
      (variants[0] as ProductVariant).productStyleColors[0][0].rawCurrentPrice = 0;
      (variants[0] as ProductVariant).productStyleColors[0][0].rawRegularPrice = 34.567;

      const productSchema = buildProductSchema(
        args({
          variants,
        })
      );

      expect(productSchema.offers[0].price).toBe('34.57');
    });

    test('should return price as empty string if both rawRegularPrice and rawRegularPrice not present', () => {
      const { variants } = args();
      (variants[0] as ProductVariant).productStyleColors[0][0].rawRegularPrice = 0;
      (variants[0] as ProductVariant).productStyleColors[0][0].rawCurrentPrice = 0;

      const productSchema = buildProductSchema(
        args({
          variants,
        })
      );

      expect(productSchema.offers[0].price).toBe('');
    });

    test('should return the price currency for each offer', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].priceCurrency).toBe('USD');
    });

    test('should return the item condition as new for each offer', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].itemCondition).toBe('https://schema.org/NewCondition');
    });

    test('should return the item offered with type being IndividualProduct', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].itemOffered).toBeInstanceOf(Object);
      expect(productSchema.offers[0].itemOffered['@type']).toBe('IndividualProduct');
    });

    test('should return an offer with a seller of type Organization', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].seller['@type']).toBe('Organization');
    });

    test('should return an offer with a name that equals the brand of the product', () => {
      const productSchema = buildProductSchema(
        args({
          displayName: 'Athleta',
        })
      );
      expect(productSchema.offers[0].seller.name).toBe('Athleta');
    });

    test('should return an offer with the product url', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].url).toBe('https://test.host/browse/product.do?pid=123456789123');
    });

    describe('Offers when seo-product-schema-refresh-q1-2023 is disabled', () => {
      test('should return an offer with the color of the individual product', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.color).toBe('Cinnamon Pudding');
      });

      test('should return an offer with the unique sku of the individual product', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.sku).toBe('123456789123');
      });

      test('should return an offer with the path of the large hero image', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.image).toBe('https://test.host/webcontent/Cinnamon_Pudding_mainImage.png');
      });

      test('should not have an image attribute if there is not a large hero image', () => {
        const { variants } = args();
        (variants[0] as ProductVariant).productStyleColors[0][0].largeImagePath = '';
        const productSchema = buildProductSchema(args({ variants }));
        expect(productSchema.offers[0].itemOffered.image).toBeUndefined();
      });

      test('should return an offer with the product name of the individual product', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.name).toBe('Climbing Shirt');
      });

      test('should return an offer with the brand of the project inside the itemOffered object', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.brand).toBe('Gap');
      });

      test('should return an offer with the productDescription inside the itemOffered object', () => {
        const productSchema = buildProductSchema(args());
        expect(productSchema.offers[0].itemOffered.description).toBe(
          "Travis Lombardo psyches up for the committing 'brain-mantel' finish. Travis Lombardo tries not to get closed out on The Wave!"
        );
      });
    });

    describe('Offers when seo-product-schema-refresh-q1-2023 is enabled', () => {
      test('should return sku in the offers level', () => {
        const productSchema = buildProductSchema(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers[0].sku).toBe('skuId1');
        expect(productSchema.offers[0].itemOffered).toBeUndefined();
      });

      test('should return image in the offers level', () => {
        const productSchema = buildProductSchema(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers[0].image).toBe('https://test.host/webcontent/image.jpg');
        expect(productSchema.offers[0].itemOffered).toBeUndefined();
      });

      test('should not have property "itemOffered"', () => {
        const productSchema = buildProductSchema(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers).not.toHaveProperty('itemOffered');
      });
    });
  });

  describe('createAvailabilityForSize', () => {
    test('should return the availability as in stock when the unique offer is in stock', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[0].availability).toBe('https://schema.org/InStock');
    });

    test('should return the availability as out of stock when the unique offer is not in stock', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[1].availability).toBe('https://schema.org/OutOfStock');
    });

    test('should return the availability as backorder when the unique offer is marked as in backorder by inventoryStatusId', () => {
      const productSchema = buildProductSchema(args());
      expect(productSchema.offers[2].availability).toBe('https://schema.org/BackOrder');
    });
  });

  describe('createImages', () => {
    test('should return all productStyleColorImages in selectedColor when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const expectedRes = [
        'https://test.hostimage_main_large.jpg',
        'https://test.hostimage_AV2_large.jpg',
        'https://test.hostimage_AV3_large.jpg',
        'https://test.hostimage_AV4_medium.jpg',
        'https://test.hostimage_AV5_small.jpg',
        'https://test.hostimage_AV6_thumbnail.jpg',
      ];
      const productSchema = buildProductSchema(
        args(
          {},
          {
            'seo-product-schema-refresh-q1-2023': true,
          }
        )
      );
      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });

    test('should return unique image(s) from the itemOffered in offers when seo-product-schema-refresh-q1-2023 is disabled', () => {
      const expectedRes = ['https://test.host/webcontent/Cinnamon_Pudding_mainImage.png', 'https://test.host/webcontent/Blue_mainImage.png'];
      const productSchema = buildProductSchema(
        args(
          {},
          {
            'seo-product-schema-refresh-q1-2023': false,
          }
        )
      );

      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });

    test('should not return undefined image paths for schema when seo-product-schema-refresh-q1-2023 is disabled', () => {
      const expectedRes = ['https://test.host/webcontent/Blue_mainImage.png'];
      const { variants } = args();
      (variants[0] as ProductVariant).productStyleColors[0][0].largeImagePath = '';

      const productSchema = buildProductSchema(args({ variants }));
      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });
  });

  describe('Reviews', () => {
    test('should return schema that contains reviews', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(Array.isArray(productSchema.reviews)).toBe(true);
      expect(productSchema.reviews).toHaveLength(3);
    });

    test('should return an review with the type being Review', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(productSchema.reviews[0]['@type']).toBe('Review');
    });

    test('should return a review with author property', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(productSchema.reviews[0].author['@type']).toBe('Person');
      expect(productSchema.reviews[0].author.name).toBe('test android');
    });

    test('should return a review with datePublished property', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(productSchema.reviews[0].datePublished).toBe('2023-02-09');
    });

    test('should return a review with reviewBody property', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(productSchema.reviews[0].reviewBody).toBe('this is android');
    });

    test('should return a review with reviewRating property', () => {
      const productSchema = buildProductSchema(args({}, {}));
      expect(productSchema.reviews[0].reviewRating['@type']).toBe('Rating');
      expect(productSchema.reviews[0].reviewRating.ratingValue).toBe(5);
    });
  });
});
describe('Product Schema MVG', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  const args = (overrides?: Partial<ProductSchemaMvgArgs>, enabledFeatures?: Record<string, boolean>): ProductSchemaMvgArgs => ({
    currencyCode: 'USD',
    displayName: 'Gap',
    featureFlags: {
      enabledFeatures: {
        'pdp-power-reviews': false,
        ...enabledFeatures,
      },
      featureVariables: {},
    } as unknown as Awaited<GetEnabledFeaturesAndVariablesType>,
    hostname: 'test.host',
    infoTabs: infoTabsMvg(),
    legacyProductImages: productImagesMvg() as unknown as CAPIV3TransformedData['legacyProductImages'],
    productName: 'Climbing Shirt',
    reviewRatings: reviewRatings(),
    reviews: reviews(),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedCustomerChoice: selectedColorMvg() as unknown as InitialSelections['selectedCustomerChoice'],
    styleId: '260030',
    variants: variantsMvg() as unknown as CAPIV3TransformedData['variants'],
    ...overrides,
  });

  describe('buildProductSchema', () => {
    test('should return schema that contains @context', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema['@context']).toBe('https://schema.org');
    });

    test('should return schema that contains @type', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema['@type']).toBe('Product');
    });

    test('should return schema that contains the name of the product', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.name).toBe('Climbing Shirt');
    });

    test('should return schema that contains the specific 6 digit product id', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.productID).toBe('260030');
    });

    test('should return schema that contains the 9 digit product id when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const error = jest.fn();
      (logger.error as jest.Mock).mockImplementation(error);
      const productSchema = buildProductSchemaMvg(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
      expect(error).not.toHaveBeenCalled();
      expect(productSchema.productID).toBe('575881072');
    });

    test('should return schema that contains the brand and its type', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.brand['@type']).toBe('Brand');
    });

    test('should return schema that contains brand attr and its brand name', () => {
      const productSchema = buildProductSchemaMvg(
        args({
          displayName: 'Banana Republic',
        })
      );
      expect(productSchema.brand.name).toBe('Banana Republic');
    });

    test('should return schema that contains color when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const productSchema = buildProductSchemaMvg(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
      expect(productSchema.color).toBe('Glow');
    });
  });

  describe('AggregateRating', () => {
    test("shouldn't return aggregateRating when power-reviews feature flag is disabled", () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test("shouldn't return aggregateRating when there's not any rating", () => {
      const argsWithEmptyRatings = args();
      argsWithEmptyRatings.reviewRatings = {};
      const productSchema = buildProductSchemaMvg(argsWithEmptyRatings);
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test("shouldn't return aggregateRating when the rating count is 0", () => {
      const zeroRatingCount = {
        average_rating: 0,
        ratingHistogram: [],
        rating_count: 0,
        reviewHistogram: [],
        review_count: 0,
      };
      const argsWithZeroRatingCount = args();
      argsWithZeroRatingCount.reviewRatings = zeroRatingCount;
      const productSchema = buildProductSchemaMvg(argsWithZeroRatingCount);
      expect(typeof productSchema.aggregateRating).toBe('undefined');
    });

    test('should return aggregateRating in a correct format when there is a power-reviews feature flag is enabled', () => {
      const productSchema = buildProductSchemaMvg(
        args(
          {},
          {
            'pdp-power-reviews': true,
          }
        )
      );
      expect(Object.prototype.hasOwnProperty.call(productSchema, 'aggregateRating')).toBe(true);
      expect(productSchema.aggregateRating['@type']).toBe('AggregateRating');
      expect(productSchema.aggregateRating.ratingValue).toBe(args().reviewRatings.average_rating);
      expect(productSchema.aggregateRating.ratingCount).toBe(args().reviewRatings.rating_count);
    });
  });

  describe('createDescription', () => {
    test('should not throw error when infotabs is empty', () => {
      const newArgs = args({
        infoTabs: [],
      });

      expect(() => buildProductSchemaMvg(newArgs)).not.toThrow();

      const result = buildProductSchemaMvg(newArgs);
      expect(result.description).toBe('');
    });

    test('should return schema that contains the description of the product', () => {
      const result = buildProductSchemaMvg(args());
      expect(result.description).toStrictEqual(
        'BEST FOR STUDIO: yoga + barre + pilates IMPACT: Low&#45impact workouts, best for A&#45C cups FEEL: Powervita&#153 fabric is buttery soft with support that feels like a gentle hug FAVE: Bonded chest band lies flat and never pinches #531254'
      );
    });
  });

  describe('Offers', () => {
    test('should return schema that contains offers', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(Array.isArray(productSchema.offers)).toBe(true);
    });

    test('should return an offer with the type being Offer', () => {
      const error = jest.fn();
      (logger.error as jest.Mock).mockImplementation(error);
      const productSchema = buildProductSchemaMvg(args());
      expect(error).not.toHaveBeenCalled();
      expect(productSchema.offers[0]['@type']).toBe('Offer');
    });

    test('should return an offer with the current price from rawCurrentPrice if this is not empty', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].price).toBe('87.20');
      expect(typeof productSchema.offers[0].price).toBe('string');
    });

    test('should return an offer with price from rawRegularPrice if rawCurrentPrice is null', () => {
      const { variants } = args();
      variants['REGULAR|mid|mid|oversized|restore'].price.max_effective_price = 0;
      variants['REGULAR|mid|mid|oversized|restore'].price.max_regular_price = 34.567;

      const productSchema = buildProductSchemaMvg(
        args({
          variants,
        })
      );

      expect(productSchema.offers[0].price).toBe('87.20');
    });

    test('should return price as empty string if both rawRegularPrice and rawRegularPrice not present', () => {
      const newColor = {
        ...selectedColorMvg(),
        price: {
          ...selectedColorMvg().price,
          max_effective_price: 0,
          max_regular_price: 0,
        },
      };
      const newVariant = {
        ...variantsMvg()['REGULAR|mid|mid|oversized|restore'],
        price: {
          max_effective_price: 0,
          max_regular_price: 0,
        },
        customer_choices: {
          [575881002]: {
            ...variantsMvg()['REGULAR|mid|mid|oversized|restore'].customer_choices[575881002],
            price: {
              max_effective_price: 0,
              max_regular_price: 0,
            },
          },
        },
      };

      const newProps = {
        ...args(),
        selectedCustomerChoice: newColor,
        variants: { 'REGULAR|mid|mid|oversized|restore': newVariant },
      } as unknown as ProductSchemaMvgArgs;

      const productSchema = buildProductSchemaMvg(newProps);

      expect(productSchema.offers[0].price).toBe('');
    });

    test('should return the price currency for each offer', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].priceCurrency).toBe('USD');
    });

    test('should return the item condition as new for each offer', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].itemCondition).toBe('https://schema.org/NewCondition');
    });

    test('should return the item offered with type being IndividualProduct', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].itemOffered).toBeInstanceOf(Object);
      expect(productSchema.offers[0].itemOffered['@type']).toBe('IndividualProduct');
    });

    test('should return an offer with a seller of type Organization', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].seller['@type']).toBe('Organization');
    });

    test('should return an offer with a name that equals the brand of the product', () => {
      const productSchema = buildProductSchemaMvg(
        args({
          displayName: 'Athleta',
        })
      );
      expect(productSchema.offers[0].seller.name).toBe('Athleta');
    });

    test('should return an offer with the product url', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].url).toBe('https://test.host/browse/product.do?pid=5758810020001');
    });

    describe('Offers when seo-product-schema-refresh-q1-2023 is disabled', () => {
      test('should return an offer with the color of the individual product', () => {
        const productSchema = buildProductSchemaMvg(args());
        expect(productSchema.offers[0].itemOffered.color).toBe('Black');
      });

      test('should return an offer with the unique sku of the individual product', () => {
        const productSchema = buildProductSchemaMvg(args());
        expect(productSchema.offers[0].itemOffered.sku).toBe('5758810020001');
      });

      test('should return an offer with the path of the large hero image', () => {
        const error = jest.fn();
        (logger.error as jest.Mock).mockImplementation(error);
        const productSchema = buildProductSchemaMvg(args());
        expect(error).not.toHaveBeenCalled();
        expect(productSchema.offers[0].itemOffered.image).toBe('https://test.host/webcontent/Cinnamon_Pudding_mainImage.png');
      });

      test('should not have an image attribute if there is not a large hero image', () => {
        const { variants } = args();
        variants['REGULAR|mid|mid|oversized|restore'].customer_choices['575881003'].largeImagePath = '';
        const productSchema = buildProductSchemaMvg(args({ variants }));
        expect(productSchema.offers[1].itemOffered.image).toBeUndefined();
      });

      test('should return an offer with the product name of the individual product', () => {
        const productSchema = buildProductSchemaMvg(args());
        expect(productSchema.offers[0].itemOffered.name).toBe('Climbing Shirt');
      });

      test('should return an offer with the brand of the project inside the itemOffered object', () => {
        const productSchema = buildProductSchemaMvg(args());
        expect(productSchema.offers[0].itemOffered.brand).toBe('Gap');
      });

      test('should return an offer with the productDescription inside the itemOffered object', () => {
        const productSchema = buildProductSchemaMvg(args());
        expect(productSchema.offers[0].itemOffered.description).toBe(
          'BEST FOR STUDIO: yoga + barre + pilates IMPACT: Low&#45impact workouts, best for A&#45C cups FEEL: Powervita&#153 fabric is buttery soft with support that feels like a gentle hug FAVE: Bonded chest band lies flat and never pinches #531254'
        );
      });
    });

    describe('Offers when seo-product-schema-refresh-q1-2023 is enabled', () => {
      test('should return sku in the offers level', () => {
        const productSchema = buildProductSchemaMvg(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers[0].sku).toBe('5758810720000');
        expect(productSchema.offers[0].itemOffered).toBeUndefined();
      });

      test('should return image in the offers level', () => {
        const productSchema = buildProductSchemaMvg(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers[0].image).toBe('https://test.host/webcontent/0057/250/945/cn57250945.jpg');
        expect(productSchema.offers[0].itemOffered).toBeUndefined();
      });

      test('should not have property "itemOffered"', () => {
        const productSchema = buildProductSchemaMvg(args({}, { 'seo-product-schema-refresh-q1-2023': true }));
        expect(productSchema.offers).not.toHaveProperty('itemOffered');
      });
    });
  });

  describe('createAvailabilityForSize', () => {
    test('should return the availability as in stock when the unique offer is in stock', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[0].availability).toBe('https://schema.org/InStock');
    });

    test('should return the availability as out of stock when the unique offer is not in stock', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[1].availability).toBe('https://schema.org/OutOfStock');
    });

    test('should return the availability as backorder when the unique offer is marked as in backorder by inventoryStatusId', () => {
      const productSchema = buildProductSchemaMvg(args());
      expect(productSchema.offers[2].availability).toBe('https://schema.org/BackOrder');
    });
  });

  describe('createImages', () => {
    test('should return all productStyleColorImages in selectedColor when seo-product-schema-refresh-q1-2023 is enabled', () => {
      const error = jest.fn();
      (logger.error as jest.Mock).mockImplementation(error);
      const expectedRes = [
        'https://test.hostimage_main_large.jpg',
        'https://test.hostimage_AV2_large.jpg',
        'https://test.hostimage_AV5_small.jpg',
        'https://test.hostimage_AV6_thumbnail.jpg',
      ];
      const productSchema = buildProductSchemaMvg(
        args(
          {},
          {
            'seo-product-schema-refresh-q1-2023': true,
          }
        )
      );
      expect(error).not.toHaveBeenCalled();
      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });

    test('should return unique image(s) from the itemOffered in offers when seo-product-schema-refresh-q1-2023 is disabled', () => {
      const expectedRes = ['https://test.host/webcontent/Cinnamon_Pudding_mainImage.png', 'https://test.host/webcontent/Blue_Pudding_mainImage.png'];
      const productSchema = buildProductSchemaMvg(
        args(
          {},
          {
            'seo-product-schema-refresh-q1-2023': false,
          }
        )
      );

      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });

    test('should not return undefined image paths for schema when seo-product-schema-refresh-q1-2023 is disabled', () => {
      const expectedRes = ['https://test.host/webcontent/Cinnamon_Pudding_mainImage.png', 'https://test.host/webcontent/Blue_Pudding_mainImage.png'];
      const { variants } = args();
      variants['REGULAR|mid|mid|oversized|restore'].customer_choices['575881002'].largeImagePath = '';

      const productSchema = buildProductSchemaMvg(args({ variants }));
      expect(productSchema).toHaveProperty('image');
      expect(productSchema.image).toEqual(expectedRes);
    });
  });

  describe('Reviews', () => {
    test('should return schema that contains reviews', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(Array.isArray(productSchema.reviews)).toBe(true);
      expect(productSchema.reviews).toHaveLength(3);
    });

    test('should return an review with the type being Review', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(productSchema.reviews[0]['@type']).toBe('Review');
    });

    test('should return a review with author property', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(productSchema.reviews[0].author['@type']).toBe('Person');
      expect(productSchema.reviews[0].author.name).toBe('test android');
    });

    test('should return a review with datePublished property', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(productSchema.reviews[0].datePublished).toBe('2023-02-09');
    });

    test('should return a review with reviewBody property', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(productSchema.reviews[0].reviewBody).toBe('this is android');
    });

    test('should return a review with reviewRating property', () => {
      const productSchema = buildProductSchemaMvg(args({}, {}));
      expect(productSchema.reviews[0].reviewRating['@type']).toBe('Rating');
      expect(productSchema.reviews[0].reviewRating.ratingValue).toBe(5);
    });
  });
});
