import type { ProductImagesData, ProductInfoTabs, ProductMergedVariant, ProductVariant, SelectedColor } from '@pdp/types/product-data/style-level'; // NOSONAR
import type { ProductColorRaw, ProductVariantRaw } from '@pdp/types/product-data/style-level-raw'; // NOSONAR

import logger from '@ecom-next/seo-helpers/logger';
import { ProductColor } from '@pdp/types/product-data/style-level';
import { is6To9DigitEnabled } from '../utils';
import type { PowerReview, ReviewHistogram } from '../../../services/reviews';
import { GetEnabledFeaturesAndVariablesType } from '../../../../pages/getPageState';
import { CAPIV3TransformedData, InventoryStatusType, StylesRaw } from '../../../../pages/services/capi-aggregation-service';
import { InitialSelections } from '../../../../pages/services/capi-aggregation-service/v3/product-data-transformer/getInitialSelection';

const CURRENCY_CODE = 'USD';
const SCHEMA_URL = 'https://schema.org';

type AvailabilitySchema = 'https://schema.org/InStock' | 'https://schema.org/OutOfStock' | 'https://schema.org/BackOrder';

const createDescription = (infoTabs: ProductInfoTabs): string => {
  const description = infoTabs?.overview;
  return description?.bulletAttributes?.join(' ') || '';
};
const createMvgDescription = (infoTabs?: StylesRaw['copy_headers']): string => {
  if (!infoTabs) {
    return '';
  }
  const description = infoTabs.find(item => item.id === 'overview') ?? infoTabs[0];
  return description?.bullets?.join(' ') || '';
};

const createAvailabilityForSize = (isInStock: boolean, inventoryStatusId?: number): AvailabilitySchema => {
  if (inventoryStatusId === 4) {
    return 'https://schema.org/BackOrder';
  }

  return isInStock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock';
};
const createAvailabilityForSizeMvg = (status: InventoryStatusType): AvailabilitySchema => {
  if (status === 'ON_ORDER') {
    return 'https://schema.org/BackOrder';
  }

  return ['IN_STOCK', 'HIGH_STOCK', 'LOW_STOCK'].includes(status) ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock';
};
const onlyVariantsWithProductStyleColors = (variant: ProductVariant): boolean => variant.productStyleColors !== undefined;
const onlyProductStyleColorsWithSizes = (styleColor: ProductColorRaw): boolean => styleColor.sizes !== undefined;
const getStyleColorPrice = (styleColor: ProductColorRaw | SelectedColor | ProductColor): number | string => {
  const price = styleColor.rawCurrentPrice || styleColor.rawRegularPrice;
  return price ? price.toFixed(2) : '';
};
const getStyleColorPriceMvg = (selectedCustomerChoice: InitialSelections['selectedCustomerChoice']): number | string => {
  const price = selectedCustomerChoice.price.max_effective_price || selectedCustomerChoice.price.max_regular_price;
  return price ? price.toFixed(2) : '';
};

type CreateProductOffersArgs = {
  currencyCode: string;
  displayName: BrandDisplayName;
  hostname: string;
  productDescription: string;
  productName: string;
  productVariants: ProductVariant[];
};
type CreateProductOffersMvgArgs = {
  currencyCode: string;
  displayName: BrandDisplayName;
  hostname: string;
  productDescription: string;
  productName: string;
  variants: CAPIV3TransformedData['variants'];
};

type Offers = {
  offers: Offer[];
};

type CreateImagesArgs = {
  hostname: string;
  isProductSchemaRefresh: boolean;
  offers: Offers;
  productImagesData: ProductImagesData;
  selectedColor: SelectedColor | ProductColor;
};
type CreateImagesMvgArgs = {
  hostname: string;
  isProductSchemaRefresh: boolean;
  legacyProductImages: CAPIV3TransformedData['legacyProductImages'];
  offers: Offers;
  selectedCustomerChoice: InitialSelections['selectedCustomerChoice'];
};

export type ProductSchemaArgs = {
  currencyCode: string;
  displayName: BrandDisplayName;
  featureFlags: FeatureFlags;
  hostname: string;
  infoTabs: ProductInfoTabs;
  productImagesData: ProductImagesData;
  productName: string;
  reviewRatings: ReviewHistogram;
  reviews: Array<PowerReview>;
  selectedColor: SelectedColor | ProductColor;
  styleId: string;
  variants: (ProductVariant | ProductMergedVariant | ProductVariantRaw)[];
};

export type ProductSchemaMvgArgs = {
  currencyCode: string;
  displayName: BrandDisplayName;
  featureFlags: Awaited<GetEnabledFeaturesAndVariablesType>;
  hostname: string;
  infoTabs?: StylesRaw['copy_headers'];
  legacyProductImages: CAPIV3TransformedData['legacyProductImages'];
  productName: string;
  reviewRatings: ReviewHistogram;
  reviews: Array<PowerReview>;
  selectedCustomerChoice: InitialSelections['selectedCustomerChoice'];
  styleId?: string;
  variants: CAPIV3TransformedData['variants'];
};

function createProductOffers({
  productName,
  productDescription,
  productVariants,
  displayName,
  hostname,
  currencyCode = CURRENCY_CODE,
}: CreateProductOffersArgs): Offers {
  const offers: Offer[] = [];

  Array.isArray(productVariants) &&
    productVariants.filter(onlyVariantsWithProductStyleColors).forEach(variant => {
      variant.productStyleColors?.forEach(productStyleColorsByPrice => {
        Array.isArray(productStyleColorsByPrice) &&
          productStyleColorsByPrice.filter(onlyProductStyleColorsWithSizes).forEach(styleColor => {
            styleColor.sizes.forEach(size => {
              const offer: Offer = {
                '@type': 'Offer',
                availability: createAvailabilityForSize(size.inStock, size.inventoryStatusId),
                itemCondition: 'https://schema.org/NewCondition',
                itemOffered: {
                  '@type': 'IndividualProduct',
                  brand: displayName,
                  color: styleColor.colorName,
                  description: productDescription,
                  image: styleColor.largeImagePath ? `https://${hostname}${styleColor.largeImagePath}` : undefined,
                  name: productName,
                  sku: size.skuId,
                },
                price: getStyleColorPrice(styleColor),
                priceCurrency: currencyCode,
                seller: {
                  '@type': 'Organization',
                  name: displayName,
                },
                url: `https://${hostname}/browse/product.do?pid=${size.skuId}`,
              };
              offers.push(offer);
            });
          });
      });
    });

  return { offers };
}
function createProductOffersMvg({
  productName,
  productDescription,
  variants,
  displayName,
  hostname,
  currencyCode = CURRENCY_CODE,
}: CreateProductOffersMvgArgs): Offers {
  const offers: Offer[] = [];

  Object.entries(variants).forEach(([_, variant]) => {
    for (const cc in variant.customer_choices) {
      variant.customer_choices[cc].skus.forEach(size => {
        const offer: Offer = {
          '@type': 'Offer',
          availability: createAvailabilityForSizeMvg(size.inventory_status.status),
          itemCondition: 'https://schema.org/NewCondition',
          itemOffered: {
            '@type': 'IndividualProduct',
            brand: displayName,
            color: variant.customer_choices[cc].description,
            description: productDescription,
            image: variant.customer_choices[cc].largeImagePath ? `https://${hostname}${variant.customer_choices[cc].largeImagePath}` : undefined,
            name: productName,
            sku: size.sku_id,
          },
          price: getStyleColorPriceMvg(variant.customer_choices[cc]),
          priceCurrency: currencyCode,
          seller: {
            '@type': 'Organization',
            name: displayName,
          },
          url: `https://${hostname}/browse/product.do?pid=${size.sku_id}`,
        };
        offers.push(offer);
      });
    }
  });
  const uniqueOffers = new Map(offers.map(offer => [offer?.itemOffered?.sku, offer]));
  const uniqueOffersArray = Array.from(uniqueOffers.values());

  return { offers: uniqueOffersArray };
}

const createConsumerChoiceLevelProductOffers = ({
  hostname,
  displayName,
  currencyCode,
  selectedColor,
}: {
  currencyCode: string;
  displayName: string;
  hostname: string;
  selectedColor: SelectedColor | ProductColor;
}) => {
  const { largeImagePath, sizes } = selectedColor;
  const offers: Offer[] = sizes?.map(size => ({
    '@type': 'Offer',
    availability: createAvailabilityForSize(size.inStock, size.inventoryStatusId),
    image: largeImagePath ? `https://${hostname}${largeImagePath}` : undefined,
    itemCondition: 'https://schema.org/NewCondition',
    price: getStyleColorPrice(selectedColor),
    priceCurrency: currencyCode,
    seller: {
      '@type': 'Organization',
      name: displayName,
    },
    sku: size.skuId,
    url: `https://${hostname}/browse/product.do?pid=${size.skuId}`,
  }));

  return { offers };
};
const createConsumerChoiceLevelProductOffersMvg = ({
  hostname,
  displayName,
  currencyCode,
  selectedCustomerChoice,
}: {
  currencyCode: string;
  displayName: string;
  hostname: string;
  selectedCustomerChoice: InitialSelections['selectedCustomerChoice'];
}) => {
  const { largeImagePath, skus } = selectedCustomerChoice;
  const offers: Offer[] = skus?.map(size => ({
    '@type': 'Offer',
    availability: createAvailabilityForSizeMvg(size.inventory_status.status),
    image: largeImagePath ? `https://${hostname}${largeImagePath}` : undefined,
    itemCondition: 'https://schema.org/NewCondition',
    price: getStyleColorPriceMvg(selectedCustomerChoice),
    priceCurrency: currencyCode,
    seller: {
      '@type': 'Organization',
      name: displayName,
    },
    sku: size.sku_id,
    url: `https://${hostname}/browse/product.do?pid=${size.sku_id}`,
  }));

  return { offers };
};

type SEOAggregateRating =
  | {
      aggregateRating?: {
        '@type': 'AggregateRating';
        ratingCount: number;
        ratingValue: number;
      };
    }
  | Record<string, unknown>;

function createReviewHistogram(reviewHistogram: ReviewHistogram, featureFlags: FeatureFlags): SEOAggregateRating {
  const shouldIncludeHistogram = reviewHistogram?.rating_count > 0 && featureFlags.enabledFeatures[`pdp-power-reviews`];

  if (shouldIncludeHistogram) {
    return {
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingCount: reviewHistogram.rating_count,
        ratingValue: reviewHistogram.average_rating,
      },
    };
  }

  return {};
}

type SEOReview =
  | {
      '@type': 'Review';
      reviewBody: string;
      reviewRating: {
        '@type': 'Rating';
        ratingValue: number;
      };
      // eslint-disable-next-line typescript-sort-keys/interface
      author: {
        '@type': 'Person';
        name: string;
      };
      datePublished: string;
    }
  | Record<string, unknown>;

function createReviews(reviews: Array<PowerReview>): Array<SEOReview> {
  const shouldIncludeReviews = reviews?.length > 0;

  if (shouldIncludeReviews) {
    return reviews.map(review => ({
      '@type': 'Review',
      author: {
        '@type': 'Person',
        name: review.details?.nickname,
      },
      datePublished: new Date(review.details?.created_date).toISOString().substring(0, 10),
      reviewBody: review.details?.comments,
      reviewRating: {
        '@type': 'Rating',
        ratingValue: review.metrics?.rating,
      },
    }));
  }

  return [];
}

type ProductImageType = {
  image: (string | undefined)[];
};

const imageTypes = ['large', 'medium', 'small', 'thumbnail'] as const;
/**
 * Create **image** property based on feature flag *seo-product-schema-refresh-q1-2023*
 * @param {object} CreateImagesArgs
 * @return {object} ProductImageType
 */
const createImages = ({ offers, isProductSchemaRefresh, productImagesData, hostname, selectedColor }: CreateImagesArgs): ProductImageType => {
  if (!isProductSchemaRefresh) {
    const images = new Set(offers?.offers?.map((offer: Offer) => offer?.itemOffered?.image || ''));
    return { image: Array.from(images).filter(img => !!img) || [] };
  }

  const { productStyleColorImages } = selectedColor;
  const basePath = `https://${hostname}`;

  const images = productStyleColorImages?.map((imageMapId: string) => {
    let imagePath = '';

    imageTypes.forEach(size => {
      if (imagePath === '') {
        if (productImagesData[imageMapId]?.[size]) {
          imagePath = `${basePath}${productImagesData[imageMapId]?.[size]}`;
        }
      }
    });
    return imagePath;
  });

  return { image: [...images].filter(img => !!img) || [] };
};
const createMvgImages = ({ offers, isProductSchemaRefresh, legacyProductImages, hostname, selectedCustomerChoice }: CreateImagesMvgArgs): ProductImageType => {
  if (!isProductSchemaRefresh) {
    const images = new Set(offers?.offers?.map((offer: Offer) => offer?.itemOffered?.image ?? ''));
    return { image: Array.from(images).filter(img => !!img) || [] };
  }
  const basePath = `https://${hostname}`;
  const images = selectedCustomerChoice.legacyImages?.map((imageMapId: string) => {
    let imagePath = '';

    imageTypes.forEach(size => {
      if (imagePath === '') {
        if (legacyProductImages?.[imageMapId]?.[size]) {
          imagePath = `${basePath}${legacyProductImages[imageMapId]?.[size]}`;
        }
      }
    });
    return imagePath;
  });
  return { image: [...images].filter(img => !!img) || [] };
};

const createColor = (selectedColor: SelectedColor | ProductColor, isProductSchemaRefresh: boolean) => {
  if (!isProductSchemaRefresh) {
    return undefined;
  }

  return { color: selectedColor?.colorName };
};
const createMvgColor = (selectedCustomerChoice: InitialSelections['selectedCustomerChoice'], isProductSchemaRefresh: boolean) => {
  if (!isProductSchemaRefresh) {
    return undefined;
  }

  return { color: selectedCustomerChoice?.description };
};

export function buildProductSchema({
  displayName,
  hostname,
  productName,
  productImagesData,
  styleId,
  selectedColor,
  variants,
  currencyCode,
  infoTabs,
  reviewRatings,
  reviews,
  featureFlags,
}: ProductSchemaArgs): ProductPageSchema {
  let productPageSchema: ProductPageSchema;
  const validVariants = Array.isArray(variants) ? (variants.filter(variant => Object.keys(variant).includes('productStyleColors')) as ProductVariant[]) : [];
  try {
    const isProductSchemaRefresh = is6To9DigitEnabled(featureFlags.enabledFeatures, 'seo-product-schema-refresh-q1-2023');

    const productDescription = createDescription(infoTabs);
    const offers = isProductSchemaRefresh
      ? createConsumerChoiceLevelProductOffers({
          currencyCode,
          displayName,
          hostname,
          selectedColor,
        })
      : createProductOffers({
          currencyCode,
          displayName,
          hostname,
          productDescription,
          productName,
          productVariants: validVariants,
        });

    return {
      '@context': SCHEMA_URL,
      '@type': 'Product',
      brand: {
        '@type': 'Brand',
        name: displayName,
      },
      description: productDescription,
      name: productName,
      productID: isProductSchemaRefresh ? selectedColor?.businessCatalogItemId : styleId,
      reviews: createReviews(reviews),
      ...offers,
      ...createReviewHistogram(reviewRatings, featureFlags),
      ...createImages({ hostname, isProductSchemaRefresh, offers, productImagesData, selectedColor }),
      ...createColor(selectedColor, isProductSchemaRefresh),
    };
  } catch (error) {
    logger.error(`Unable to create Product Schema given input data. ${error}`);
    productPageSchema = {} as ProductPageSchema;
  }
  return productPageSchema;
}
export function buildProductSchemaMvg({
  displayName,
  hostname,
  productName,
  legacyProductImages,
  styleId,
  selectedCustomerChoice,
  variants,
  currencyCode,
  infoTabs,
  reviewRatings,
  reviews,
  featureFlags,
}: ProductSchemaMvgArgs): ProductPageSchema {
  let productPageSchema: ProductPageSchema;
  try {
    const isProductSchemaRefresh = is6To9DigitEnabled(featureFlags.enabledFeatures, 'seo-product-schema-refresh-q1-2023');

    const productDescription = createMvgDescription(infoTabs);
    const offers = isProductSchemaRefresh
      ? createConsumerChoiceLevelProductOffersMvg({
          currencyCode,
          displayName,
          hostname,
          selectedCustomerChoice,
        })
      : createProductOffersMvg({
          currencyCode,
          displayName,
          hostname,
          productDescription,
          productName,
          variants,
        });

    return {
      '@context': SCHEMA_URL,
      '@type': 'Product',
      brand: {
        '@type': 'Brand',
        name: displayName,
      },
      description: productDescription,
      name: productName,
      productID: isProductSchemaRefresh ? selectedCustomerChoice?.customer_choice_id : styleId,
      reviews: createReviews(reviews),
      ...offers,
      ...createReviewHistogram(reviewRatings, featureFlags),
      ...createMvgImages({ hostname, isProductSchemaRefresh, offers, legacyProductImages, selectedCustomerChoice }),
      ...createMvgColor(selectedCustomerChoice, isProductSchemaRefresh),
    };
  } catch (error) {
    logger.error(`Unable to create Product Schema given input data. ${error}`);
    productPageSchema = {} as ProductPageSchema;
  }
  return productPageSchema;
}
