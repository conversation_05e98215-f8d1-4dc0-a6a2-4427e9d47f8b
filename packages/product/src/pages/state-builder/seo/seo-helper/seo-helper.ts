import type { ProductData } from '../..';
/* eslint-disable no-restricted-syntax */

/**
 * @param {string} locale
 * @desc converts langCode_countryCode to langCode-countryCode (Eg:en_US to en-US)
 * @returns {string} if valid locale, returns lang="en-US" otherwise returns empty string
 */
export const getLangAttributes = (locale: Locale): string => {
  const attrValue = locale ? locale.replace('_', '-') : '';
  return attrValue !== '' ? `lang="${attrValue}"` : '';
};

export const buildCanonicalUrl = (hostname: string, styleId: string, locale: Locale): string => {
  if (!hostname) {
    return '';
  }
  const localeParam = locale === 'fr_CA' ? '&locale=fr_CA' : '';
  return `https://${hostname}/browse/product.do?pid=${styleId}${localeParam}`;
};

export const buildTitle = (productName: string, brandName: string): string => `${productName} | ${brandName}`;

export const getDepartmentIdFromCustomUrl = (customUrl = ''): string => {
  const hash = customUrl.slice(customUrl.indexOf('#')) || '';
  const hashAfterDepartment = hash?.split('department=')[1] || '';

  return hashAfterDepartment?.split('&')[0] || '';
};

export const findPath = (cid: string, nav: NavigationNode[], pathToCurrentNode: NavigationNode[] = []): NavigationNode[] => {
  if (!nav || nav.length === 0) {
    return [];
  }

  for (const node of nav) {
    if (node.id === cid) {
      return pathToCurrentNode.concat(node);
    }
    if (node.children && node.children.length > 0) {
      const pathToReturn = findPath(cid, node.children, pathToCurrentNode);
      if (pathToReturn.length > 0) {
        return pathToReturn;
      }
    }
  }
  return [];
};

export const get6DigitTo9DigitRedirectUrl = (requestUrl: string, state: ProductData): string => {
  const selectedColor = state.productData?.selectedColor?.businessCatalogItemId;
  const redirectUrl = requestUrl.replace(/pid=\d+/, `pid=${selectedColor}`);

  return selectedColor ? redirectUrl : '';
};

export const getPidFromUrl = (requestUrl: string): string => {
  const pidReg = /.*pid=(?<pid>\d+).*/;
  const matchRes = pidReg.exec(requestUrl);
  const pid = matchRes ? matchRes[1] : '';

  return pid;
};
