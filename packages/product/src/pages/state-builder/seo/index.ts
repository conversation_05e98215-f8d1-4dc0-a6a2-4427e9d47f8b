/**
 * SEO requirements are managed by the SEO team. Please see the following links for more information.
 * https://confluence.gapinc.com/pages/viewpage.action?spaceKey=SEO&title=SEO+Requirements+for+Every+Page#SEORequirementsforEveryPage-Title
 * https://confluence.gapinc.com/display/SEO/Breadcrumbs
 */

import type { Brand, Market } from '@ecom-next/utils/server';
import { buildBreadcrumbSchema, buildBreadcrumbs } from '@ecom-next/seo-helpers/breadcrumbs';
import { getBrandInformation } from '@mfe/brand-info';
import logger from '@ecom-next/seo-helpers/logger';
import type { ProductInfoTabs } from '@pdp/types/product-data/style-level'; // NOSONAR
import type { CAPIV3TransformedData, PSS } from '../../services/capi-aggregation-service';
import type { ReviewRatings } from '../../services/reviews';
import type { GetAppConfigType, GetEnabledFeaturesAndVariablesType } from '../../getPageState';
import { buildCanonicalUrl, buildTitle, findPath, getDepartmentIdFromCustomUrl, getLangAttributes } from './seo-helper/seo-helper';
import { buildDescription } from './meta-description/meta-description';
import { buildProductSchema, buildProductSchemaMvg } from './product-schema/product-schema';
import { buildSelectedNodes } from './selected-nodes/selected-nodes';
import { is6To9DigitEnabled } from './utils';

type SeoPageSchema = ProductPageSchema;

type ServicesArgs = {
  brand: Brand;
  country: string;
  date: string;
  locale: Locale;
  market: Market;
};

export type SeoRequestParams = ServicesArgs & {
  appConfig: AppConfig;
  featureFlagsPromise: Promise<FeatureFlags>;
  navPromise: Promise<NavigationNode>;
  pcid: string;
  pssPromise: Promise<PSS>;
  reviewRatingsPromise: Promise<ReviewRatings>;
};

export type SeoV2RequestParams = ServicesArgs & {
  appConfig: Awaited<GetAppConfigType>['appConfig'];
  capiPromise: Promise<CAPIV3TransformedData | null>;
  featureFlagsPromise: GetEnabledFeaturesAndVariablesType;
  navPromise: Promise<NavigationNode>;
  pcid: string;
  reviewRatingsPromise: Promise<ReviewRatings>;
};

type MaybeBreadCrumbs = Partial<ReturnType<typeof buildBreadcrumbs>>;

export async function seo({
  brand,
  pssPromise,
  reviewRatingsPromise,
  featureFlagsPromise,
  navPromise,
  appConfig,
  locale,
  pcid,
}: SeoRequestParams): Promise<SeoResponse> {
  const [{ productData }, nav, { reviewRatings, reviews }, featureFlags] = await Promise.all([
    pssPromise,
    navPromise,
    reviewRatingsPromise,
    featureFlagsPromise,
  ]);

  if (!productData || productData.redirectUrl) {
    return {} as SeoResponse;
  }

  const { displayName } = getBrandInformation(brand);
  const { commonHost } = appConfig.brandCodeUrls;
  const isProductSchemaRefresh = is6To9DigitEnabled(featureFlags.enabledFeatures, 'seo-product-schema-refresh-q1-2023');
  const pid = !isProductSchemaRefresh ? productData.styleId : productData?.selectedColor?.businessCatalogItemId;
  const productName = (productData?.selectedColor?.productTitle || productData?.selectedVariant?.productTitle) as string;

  const canonicalUrl = buildCanonicalUrl(commonHost, pid, locale);

  const productSchema = buildProductSchema({
    currencyCode: productData.currencyCode,
    displayName,
    featureFlags,
    hostname: commonHost,
    infoTabs: productData.infoTabs,
    productImagesData: productData.productImages,
    productName,
    reviewRatings,
    reviews: reviews || [],
    selectedColor: productData.selectedColor,
    styleId: productData.styleId,
    variants: productData.variants,
  });

  const htmlLangAttribute = getLangAttributes(locale);

  let seoPageSchema: SeoPageSchema = productSchema;
  let breadcrumbs: MaybeBreadCrumbs = {};
  let seoBreadcrumbSchema: BreadcrumbSchema;
  let selectedNodes: SelectedNodes | void = [];

  try {
    selectedNodes = buildSelectedNodes({ nav, pcid });

    if (nav.children?.length && productData.primaryCategoryId) {
      const cid = productData.primaryCategoryId;
      seoPageSchema = productSchema;
      breadcrumbs = buildBreadcrumbs({
        brand,
        cid,
        nav,
        locale,
        subcategoryInfo: null,
        facetInfo: null,
      });
      seoBreadcrumbSchema = buildBreadcrumbSchema({ hostname: commonHost, locale, ...breadcrumbs });
    }
  } catch (e) {
    logger.info('Error building breadcrumbs', e);
  }

  const currentCategory = findPath(productData.primaryCategoryId, nav?.children as NavigationNode[])[0];
  const department = getDepartmentIdFromCustomUrl(currentCategory?.customUrl);

  return {
    breadcrumbs,
    canonicalUrl,
    department,
    description: buildDescription({
      brandName: displayName,
      infoTabs: productData.infoTabs,
      locale,
      productName,
    }),
    htmlLangAttribute,
    selectedNodes: selectedNodes as SelectedNodes,
    seoBreadcrumbSchema: JSON.stringify(seoBreadcrumbSchema),
    seoPageSchema: JSON.stringify(seoPageSchema),
    title: buildTitle(productName, displayName),
  };
}

export async function seoMvg({
  brand,
  capiPromise,
  reviewRatingsPromise,
  featureFlagsPromise,
  navPromise,
  appConfig,
  locale,
  pcid,
}: SeoV2RequestParams): Promise<SeoResponse> {
  const [capiData, nav, featureFlags, { reviewRatings, reviews }] = await Promise.all([capiPromise, navPromise, featureFlagsPromise, reviewRatingsPromise]);
  if (!capiData) {
    return {} as SeoResponse;
  }

  if (capiData?.redirectUrl) {
    return {} as SeoResponse;
  }

  const { displayName } = getBrandInformation(brand);
  const { commonHost } = appConfig.brandCodeUrls;
  const isProductSchemaRefresh = is6To9DigitEnabled(featureFlags.enabledFeatures, 'seo-product-schema-refresh-q1-2023');
  const pid = !isProductSchemaRefresh ? capiData.selectedCustomerChoice?.style_id : capiData?.selectedCustomerChoice?.customer_choice_id;
  const productName = capiData?.selectedStyle?.description ?? '';

  const canonicalUrl = buildCanonicalUrl(commonHost, pid || '', locale);

  const productSchema = buildProductSchemaMvg({
    currencyCode: capiData.selectedCustomerChoice?.price.currency ?? '',
    displayName,
    featureFlags,
    hostname: commonHost,
    infoTabs: capiData.selectedStyle?.copy_headers,
    legacyProductImages: capiData.legacyProductImages,
    productName,
    reviewRatings,
    reviews: reviews || [],
    selectedCustomerChoice: capiData.selectedCustomerChoice,
    styleId: capiData.selectedStyle?.style_id,
    variants: capiData.variants,
  });

  const htmlLangAttribute = getLangAttributes(locale);

  let seoPageSchema: SeoPageSchema = {};
  let breadcrumbs: MaybeBreadCrumbs = {};
  let seoBreadcrumbSchema: BreadcrumbSchema;
  let selectedNodes: SelectedNodes | void = [];

  try {
    selectedNodes = buildSelectedNodes({ nav, pcid });

    if (nav.children?.length && capiData.selectedStyle?.primary_category_id) {
      const cid = capiData.selectedStyle?.primary_category_id;
      seoPageSchema = productSchema;
      breadcrumbs = buildBreadcrumbs({
        brand,
        cid,
        nav,
        locale,
        subcategoryInfo: null,
        facetInfo: null,
      });
      seoBreadcrumbSchema = buildBreadcrumbSchema({ hostname: commonHost, locale, ...breadcrumbs });
    }
  } catch (e) {
    logger.info('Error building breadcrumbs', e);
  }

  const currentCategory = findPath(capiData.selectedStyle?.primary_category_id ?? '', nav?.children as NavigationNode[])[0];
  const department = getDepartmentIdFromCustomUrl(currentCategory?.customUrl);

  return {
    breadcrumbs,
    canonicalUrl,
    department,
    description: buildDescription({
      brandName: displayName,
      infoTabs: capiData.selectedStyle?.copy_headers as ProductInfoTabs,
      locale,
      productName,
    }),
    htmlLangAttribute,
    selectedNodes: selectedNodes as SelectedNodes,
    seoBreadcrumbSchema: JSON.stringify(seoBreadcrumbSchema),
    seoPageSchema: JSON.stringify(seoPageSchema),
    title: buildTitle(productName, displayName),
  };
}
