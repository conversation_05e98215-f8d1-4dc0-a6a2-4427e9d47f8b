import type { ProductInfoTabs } from '@pdp/types/product-data/style-level'; // NOSONAR
import { encode } from 'html-entities';

const removeMarkups = (str: string): string => str.replace(/<[^>]*>?/gm, '');
const enLocalesRegEx = /^en_/;

export type DescriptionArgs = {
  brandName: BrandDisplayName;
  infoTabs: ProductInfoTabs;
  locale: Locale;
  productName: string;
};

const buildDescription = ({ infoTabs, productName, brandName, locale = 'en_US' }: DescriptionArgs): string => {
  let metaDescription = '';
  const suffixChar = "'s";
  const shopText = enLocalesRegEx.test(locale) ? 'Shop ' : '';

  const productInfo = productName ? `${encode(productName)}` : '';

  metaDescription += locale === 'fr_CA' ? `${productInfo} de ${brandName}: ` : `${shopText}${brandName}${suffixChar} ${productInfo}: `;

  if (infoTabs && infoTabs.overview) {
    if (infoTabs.overview.copyAttributes.length && infoTabs.overview.copyAttributes[0] !== undefined) {
      metaDescription += infoTabs.overview.copyAttributes.join(', ');
    }
    metaDescription += infoTabs.overview.bulletAttributes !== undefined ? infoTabs.overview.bulletAttributes.join(', ') : '';
  }

  return removeMarkups(metaDescription);
};

export { buildDescription };
