import type { DescriptionArgs } from '../meta-description';

const buildMetaDescriptionArgs = (
  copyAttributes: string[],
  bulletAttributes: string[],
  productName: string,
  brandName: BrandDisplayName,
  locale: Locale
): DescriptionArgs => {
  return {
    brandName,
    infoTabs: {
      fabric: {
        bulletAttributes: [],
        infoTabName: '',
      },
      overview: {
        bulletAttributes,
        copyAttributes,
        infoTabName: '',
        notes: [],
      },
    },
    locale,
    productName,
  };
};

const defaultProductName = 'cool-product';
const simpleMetaDescription = 'This is a sample meta description';

export const metaDescriptionArgs = buildMetaDescriptionArgs(
  ['copyAttributes1', 'copyAttributes2'],
  ['infoTab.overview.bulletAttributes'],
  defaultProductName,
  'Athleta',
  'en_US'
);

export const metaDescriptionArgsWithNoCopyAttributes = buildMetaDescriptionArgs(
  [],
  ['infoTab.overview.bulletAttributes'],
  defaultProductName,
  'Athleta',
  'en_US'
);

export const metaDescriptionArgsWithNoBulletAttributes = buildMetaDescriptionArgs(
  ['copyAttributes1', 'copyAttributes2'],
  [],
  defaultProductName,
  'Athleta',
  'en_US'
);

export const metaDescriptionArgsWithoutAnyAttributes = buildMetaDescriptionArgs([], [], defaultProductName, 'Athleta', 'en_US');

export const metaDescriptionArgsWithAnyHtmlTagsPresent = buildMetaDescriptionArgs(
  ['This is a sample meta description with <i> i tags </i>, <br/> and some <b> bold </b> tags. <br/>'],
  [],
  defaultProductName,
  'Athleta',
  'en_US'
);

export const metaDescriptionWithStartingWithShop = buildMetaDescriptionArgs([simpleMetaDescription], [], defaultProductName, 'Athleta', 'en_CA');

export const metaDescriptionWithOutShop = buildMetaDescriptionArgs([simpleMetaDescription], [], defaultProductName, 'Athleta', 'fr_CA');

export const metaDescriptionArgsWithBrandTextFrench = buildMetaDescriptionArgs([simpleMetaDescription], [], 'Sky High True Skinny Ankle Jeans', 'Gap', 'fr_CA');

export const metaDescriptionWithShop = buildMetaDescriptionArgs([simpleMetaDescription], [], defaultProductName, 'Athleta', 'en_US');

export const metaDescriptionArgsWithSpecialCharacters = buildMetaDescriptionArgs([], [], '"', 'Gap', 'en_US');
