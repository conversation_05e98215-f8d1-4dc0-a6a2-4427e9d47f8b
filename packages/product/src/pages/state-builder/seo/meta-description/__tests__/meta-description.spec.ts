import { buildDescription } from '../meta-description';
import {
  metaDescriptionArgs,
  metaDescriptionArgsWithAnyHtmlTagsPresent,
  metaDescriptionArgsWithBrandTextFrench,
  metaDescriptionArgsWithNoCopyAttributes,
  metaDescriptionArgsWithNoBulletAttributes,
  metaDescriptionArgsWithSpecialCharacters,
  metaDescriptionArgsWithoutAnyAttributes,
  metaDescriptionWithOutShop,
  metaDescriptionWithShop,
  metaDescriptionWithStartingWithShop,
} from './meta-description-test-helper';

describe('meta Description', () => {
  test('should return the meta description with all possible attributes', () => {
    const productMetadata = buildDescription(metaDescriptionArgs);
    expect(productMetadata).toBe("Shop Athleta's cool-product: copyAttributes1, copyAttributes2infoTab.overview.bulletAttributes");
  });

  test('should return the meta description without copyAttributes', () => {
    const productMetadata = buildDescription(metaDescriptionArgsWithNoCopyAttributes);
    expect(productMetadata).toBe("Shop Athleta's cool-product: infoTab.overview.bulletAttributes");
  });

  test('should return the meta description without bulletAttributes', () => {
    const productMetadata = buildDescription(metaDescriptionArgsWithNoBulletAttributes);
    expect(productMetadata).toBe("Shop Athleta's cool-product: copyAttributes1, copyAttributes2");
  });

  test('should return the meta description without any attributes', () => {
    const productMetadata = buildDescription(metaDescriptionArgsWithoutAnyAttributes);
    expect(productMetadata).toBe("Shop Athleta's cool-product: ");
  });

  test('should remove any html tags present in meta description', () => {
    const expectedOutput = "Shop Athleta's cool-product: This is a sample meta description with  i tags ,  and some  bold  tags. ";
    expect(buildDescription(metaDescriptionArgsWithAnyHtmlTagsPresent)).toBe(expectedOutput);
  });

  test('should return meta description starting with "Shop" for English supporting locales', () => {
    const expectedOutput = "Shop Athleta's cool-product: This is a sample meta description";
    expect(buildDescription(metaDescriptionWithStartingWithShop)).toBe(expectedOutput);
  });

  test('should return meta description without "Shop" prefix for non-English locales', () => {
    const expectedOutput = 'cool-product de Athleta: This is a sample meta description';
    expect(buildDescription(metaDescriptionWithOutShop)).toBe(expectedOutput);
  });

  test('should return meta description with brand text in french for french locale', () => {
    const expectedOutput = 'Sky High True Skinny Ankle Jeans de Gap: This is a sample meta description';
    expect(buildDescription(metaDescriptionArgsWithBrandTextFrench)).toBe(expectedOutput);
  });

  test('should return default meta description with "Shop" prefix if locales param not present', () => {
    const expectedOutput = "Shop Athleta's cool-product: This is a sample meta description";
    expect(buildDescription(metaDescriptionWithShop)).toBe(expectedOutput);
  });

  test('should encode special characters', () => {
    expect(buildDescription(metaDescriptionArgsWithSpecialCharacters)).toBe("Shop Gap's &quot;: ");
  });
});
