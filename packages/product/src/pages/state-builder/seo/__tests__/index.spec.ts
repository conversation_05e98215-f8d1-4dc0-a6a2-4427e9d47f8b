import { buildBreadcrumbSchema, buildBreadcrumbs } from '@ecom-next/seo-helpers/breadcrumbs';
import { type SeoRequestParams, seo } from '..';

import { buildProductSchema } from '../product-schema/product-schema';
import { CAPIAggregationService } from '../../../../pages/services/capi-aggregation-service';

jest.mock('../product-schema/product-schema');
jest.mock('@ecom-next/seo-helpers/logger');
jest.mock('@ecom-next/seo-helpers/breadcrumbs');

const pcid = '1038092';
const navServiceResponse = {
  children: [
    {
      id: pcid,
      name: 'Boys',
      type: 'sub-division',
    },
    {
      customUrl: '/browse/category.do?cid=26190&tlink=meganav%3AWomen%3ADeals%3ASale#pageId=0&department=136',
      id: '123456',
      name: 'Girls',
      type: 'sub-division',
    },
    {
      id: '83064',
      name: '<PERSON><PERSON>',
      type: 'sub-division',
    },
  ],
  id: pcid,
  name: 'GapFit',
  type: 'division',
  selectedVariant: {
    productTitle: 'cool-product',
  },
};
let metadataArgs: SeoRequestParams;
const testLocale = 'en_US';
const breadcrumbStub = { category: 'some category', division: 'some division' };
const fakeBreadcrumbSchema = { '@': 'breadcrumb' };
const fakeProductSchema = { '@': 'product' };

describe('Product SEO Data', () => {
  beforeEach(() => {
    (buildProductSchema as jest.Mock).mockImplementation(() => fakeProductSchema);

    (buildBreadcrumbs as jest.Mock).mockImplementation(function buildBreadcrumbs() {
      return breadcrumbStub;
    });

    (buildBreadcrumbSchema as jest.Mock).mockImplementation(() => fakeBreadcrumbSchema);

    metadataArgs = {
      appConfig: {
        apiConfig: {
          internalOidcUrl: '',
          oidcUrl: '',
          pasConfig: {
            url: '',
          },
          pmcsConfig: {
            url: '',
          },
          pssConfig: {
            url: '',
          },
        },
        brandCodeUrls: {
          atbOidcUrl: '',
          commonHost: 'some-domain',
          internalOidcUrl: '',
          oidcUrl: '',
          secureUrl: '',
          unsecureUrl: '',
        },
        powerReviewsConfig: {
          apiKey: '',
          groupId: 0,
          merchantId: 0,
        },
      },
      brand: 'at',
      country: 'us',
      date: '',
      featureFlagsPromise: Promise.resolve({
        enabledFeatures: {},
      }) as Promise<FeatureFlags>,
      locale: testLocale,
      market: 'us',
      navPromise: Promise.resolve(navServiceResponse) as Promise<NavigationNode>,
      pcid,
      pssPromise: Promise.resolve({
        productData: {
          name: 'cool-product',
          primaryCategoryId: '123456',
          selectedColor: { businessCatalogItemId: '123456789' },
          styleId: '123456',
          selectedVariant: {
            productTitle: 'cool-product',
          },
        },
      }) as unknown as Promise<CAPIAggregationService>,
      reviewRatingsPromise: Promise.resolve({ reviewRatings: {}, reviews: [] }),
    };
  });

  describe('Canonical URL', () => {
    test('should return a canonical url when getting product metadata', async () => {
      const productMetadata = await seo(metadataArgs);
      expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456');
    });

    test('should return a canonical url with 9 digits pid when FF seo-product-schema-refresh-q1-2023 is on', async () => {
      metadataArgs.featureFlagsPromise = Promise.resolve({
        enabledFeatures: { 'seo-product-schema-refresh-q1-2023': true },
        featureVariables: {},
      });
      const productMetadata = await seo(metadataArgs);
      expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456789');
    });

    test('should contain the locale parameter if locale is fr_CA', async () => {
      const locale = 'fr_CA';
      const productMetadata = await seo({ ...metadataArgs, locale });
      expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456&locale=fr_CA');
    });

    test('should not contain locale parameter if locale is en_CA', async () => {
      const locale = 'en_CA';
      const productMetadata = await seo({ ...metadataArgs, locale });
      expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456');
    });

    test('should not contain locale parameter if locale is en_US', async () => {
      const locale = 'en_US';
      const productMetadata = await seo({ ...metadataArgs, locale });
      expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456');
    });
  });

  test('should build seo product schema', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata.seoPageSchema).toBe(JSON.stringify(fakeProductSchema));
  });

  test('should build seo breadcrumb schema', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata.seoBreadcrumbSchema).toBe(JSON.stringify(fakeBreadcrumbSchema));
  });

  test('should return the title', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata.title).toBe('cool-product | Athleta');
  });

  test('should return a breadcrumb object when getting product metadata', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(buildBreadcrumbs).toHaveBeenCalledWith({
      brand: metadataArgs.brand,
      facetInfo: null,
      locale: 'en_US',
      cid: '123456',
      nav: navServiceResponse,
      subcategoryInfo: null,
    });
    expect(productMetadata.breadcrumbs).toStrictEqual({ category: 'some category', division: 'some division' });
  });

  test('should return empty breadcrumb if primaryCategoryId does not exist', async () => {
    metadataArgs.pssPromise = Promise.resolve({
      productData: {
        name: 'cool-product',
        styleId: '123456',
        selectedVariant: {
          productTitle: 'cool-product',
        },
      },
    }) as unknown as Promise<CAPIAggregationService>;

    const productMetadata = await seo(metadataArgs);

    expect(productMetadata.seoPageSchema).toStrictEqual(JSON.stringify(fakeProductSchema));
    expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456');
    expect(productMetadata.breadcrumbs).toStrictEqual({});
  });

  test('should return an empty object if productData does not exist', async () => {
    metadataArgs.pssPromise = Promise.resolve({}) as Promise<CAPIAggregationService>;
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata).toStrictEqual({});
  });

  test('should return an empty object if productData has redirectUrl present', async () => {
    const productData = {
      redirectUrl: '/browse/GeneralNoResults.do?',
    };
    metadataArgs.pssPromise = Promise.resolve(productData) as unknown as Promise<CAPIAggregationService>;
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata).toStrictEqual({});
  });

  test('should return canonical url, product schema as the seo schema, empty breadcrumb, and og metadata if nav call fails', async () => {
    const defaultNavigationResponse = { children: [] };
    const paramsWithFailingNav: SeoRequestParams = {
      appConfig: {
        brandCodeUrls: {
          commonHost: 'some-domain',
        },
      },
      brand: 'some-brand',
      featureFlagsPromise: Promise.resolve({
        enabledFeatures: {},
      }),
      navPromise: Promise.resolve(defaultNavigationResponse),
      pssPromise: Promise.resolve({
        productData: {
          name: 'cool-product',
          primaryCategoryId: '123456',
          styleId: '123456',
          selectedVariant: {
            productTitle: 'cool-product',
          },
        },
      }),
      reviewRatingsPromise: Promise.resolve({
        reviewHistogram: {},
      }),
    } as unknown as SeoRequestParams;

    const productMetadata = await seo(paramsWithFailingNav);

    expect(productMetadata.seoPageSchema).toStrictEqual(JSON.stringify(fakeProductSchema));
    expect(productMetadata.canonicalUrl).toBe('https://some-domain/browse/product.do?pid=123456');
    expect(productMetadata.breadcrumbs).toStrictEqual({});
  });

  test('should return selectedNodes with matching pcid', async () => {
    const productMetadata = await seo(metadataArgs);

    expect(productMetadata.selectedNodes).toHaveLength(3);
    expect(productMetadata.selectedNodes[0].id).toBe(pcid);
  });

  test('should return empty array if no matching nodes present', async () => {
    const navResponse = {
      children: [
        {
          id: '83064',
          name: 'Jeans',
          type: 'sub-division',
        },
      ],
      id: '123',
      name: 'GapFit',
      type: 'division',
    };

    const productMetadata = await seo({
      ...metadataArgs,
      navPromise: Promise.resolve(navResponse) as Promise<NavigationNode>,
    });

    expect(productMetadata.selectedNodes).toHaveLength(0);
  });

  test('should use locale for the html lang attribute', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata.htmlLangAttribute).toBe('lang="en-US"');
  });

  test('should not use the lang attribute when locale is absent', async () => {
    const metadataWithEmptyLocale: SeoRequestParams = {
      appConfig: {
        brandCodeUrls: {
          commonHost: 'some-domain',
          secureUrl: '',
          unSecureUrl: '',
        },
      },
      brand: 'at',
      brandName: 'Athleta',
      featureFlagsPromise: Promise.resolve({
        enabledFeatures: {},
      }),
      pcid,
      pssPromise: Promise.resolve({
        productData: {
          name: 'cool-product',
          primaryCategoryId: '123456',
          styleId: '123456',
          selectedVariant: {
            productTitle: 'cool-product',
          },
        },
      }),
      reviewRatingsPromise: Promise.resolve({
        reviewHistogram: {},
      }),
    } as unknown as SeoRequestParams;
    const productMetadata = await seo(metadataWithEmptyLocale);
    expect(productMetadata.htmlLangAttribute).toBe('');
  });

  test('should return department with matching id', async () => {
    const productMetadata = await seo(metadataArgs);
    expect(productMetadata.department).toBe('136');
  });
});
