import type { ProductAvailability, SkuAvailability, StyleAvailability, VariantAvailability } from '@pdp/types/product-data/product-availability';
import type { ProductColor, ProductColorGroup, ProductSku, ProductVariant } from '@pdp/types/product-data/style-level';
import type { ProductColorRaw } from '@pdp/types/product-data/style-level-raw';

/*
This module sets the `bopisInStock` flag for each variant/color/size.
It mutates the provided `variants` object. Because this triggers the eslint rule below,
the rule is disabled for this module
*/
/* eslint no-param-reassign: ["error", { "props": false }] */

type SetAvailabilityProp = {
  selectedColor: ProductColor | ProductColorRaw | null;
  selectedSize: ProductSku | Record<string, unknown> | null;
  selectedVariant: ProductVariant | null;
  updateVariant?: boolean;
  variants: ProductVariant[];
};

type InjectPasBopisDataProp = SetAvailabilityProp & {
  apiData: ProductAvailability;
};

export type InjectPasBopisData = {
  selectedColor: ProductColor | ProductColorRaw | null;
  selectedVariant: ProductVariant | null;
  variants: ProductVariant[];
};

const setBopisAvailability = (colorGroup: ProductColorGroup, availableSkus: SkuAvailability[]) => {
  colorGroup.colors.forEach((color: ProductColor) => {
    color.sizes.forEach((size: ProductSku) => {
      const skuFound = availableSkus.find((sku: SkuAvailability) => sku.skuId === size.skuId);
      size.bopisInStock = false;
      size.bopisInventoryStatusId = 99;

      if (skuFound) {
        size.bopisInStock = true;
        size.bopisInventoryStatusId = parseInt(skuFound.inventoryStatusId, 10);
      }
    });

    color.bopisInStock = color.sizes.some((size: ProductSku) => size.bopisInStock);
  });
};
// Used for variants, selectedVariant and selectedColor
const updateSizeAvailabilityInfo = (color: ProductColorRaw | ProductColor, inventory: VariantAvailability[]) => {
  color?.sizes?.forEach((size: ProductSku) => {
    inventory.forEach((variant: VariantAvailability) => {
      variant.styleColors.forEach(({ skus }: StyleAvailability) => {
        const skuFound = skus.find((sku: SkuAvailability) => sku.skuId === size.skuId);
        if (skuFound) {
          size.bopisInStock = skuFound.availability === 'IN_STOCK';
          size.bopisInventoryStatusId = parseInt(skuFound.inventoryStatusId, 10);
        }
      });
    });
  });

  if (color && color.sizes) {
    color.bopisInStock = color.sizes.some((size: ProductSku) => size.bopisInStock);
  }
};

const setSizeUnavailablilityInfo = (color: ProductColorRaw | ProductColor) => {
  color?.sizes?.forEach((size: ProductSku) => {
    size.bopisInStock = false;
    size.bopisInventoryStatusId = 99;
  });
  color.bopisInStock = false;
};

const setBopisUnavailability = (colorGroup: ProductColorGroup) => {
  colorGroup.colors.forEach((color: ProductColor) => {
    color.sizes.forEach((size: ProductSku) => {
      size.bopisInStock = false;
    });
    color.bopisInStock = false;
  });
};
/* eslint-disable consistent-return */
function injectPasBopisData({
  variants,
  selectedSize,
  selectedColor,
  selectedVariant,
  apiData,
  updateVariant = false,
}: InjectPasBopisDataProp): InjectPasBopisData | undefined {
  const inventory = apiData && apiData.variants;
  if (!Array.isArray(variants)) {
    return;
  }

  if (inventory) {
    // All of the skus for all variants returned from the Availability API
    const availableSkus = getPasAvailableSkus(inventory);

    variants.forEach((variant: ProductVariant) => {
      variant?.colorGroups?.fullprice?.forEach((colorGroup: ProductColorGroup) => {
        setBopisAvailability(colorGroup, availableSkus);
      });
      variant?.colorGroups?.markdown?.forEach((colorGroup: ProductColorGroup) => {
        setBopisAvailability(colorGroup, availableSkus);
      });
      variant?.productStyleColors?.forEach((productStyleColor: ProductColorRaw[]) => {
        productStyleColor.forEach((color: ProductColorRaw) => {
          updateSizeAvailabilityInfo(color, inventory);
        });
      });
      if (updateVariant) {
        variant.bopisInStock = variant.productStyleColors?.some(productStyleColor => productStyleColor.some(color => color.bopisInStock));
      }
    });

    if (selectedSize && selectedSize.skuId) {
      const skuFound = availableSkus.find(sku => sku.skuId === selectedSize.skuId);
      if (skuFound) {
        selectedSize.bopisInStock = skuFound.availability === 'IN_STOCK';
        selectedSize.bopisInventoryStatusId = parseInt(skuFound.inventoryStatusId, 10);
      } else {
        selectedSize.bopisInStock = false;
        selectedSize.bopisInventoryStatusId = 99;
      }
    }
    if (selectedColor) {
      updateSizeAvailabilityInfo(selectedColor, inventory);
    }
    if (selectedVariant && selectedVariant.colorGroups) {
      selectedVariant.colorGroups.fullprice.forEach((colorGroup: ProductColorGroup) => {
        setBopisAvailability(colorGroup, availableSkus);
      });

      selectedVariant.colorGroups.markdown.forEach((colorGroup: ProductColorGroup) => {
        setBopisAvailability(colorGroup, availableSkus);
      });

      selectedVariant.productStyleColors.forEach((productStyleColor: ProductColorRaw[]) => {
        productStyleColor.forEach((color: ProductColorRaw) => {
          updateSizeAvailabilityInfo(color, inventory);
        });
      });
      if (updateVariant) {
        selectedVariant.bopisInStock = selectedVariant.productStyleColors.some(productStyleColor => productStyleColor.some(color => color.bopisInStock));
      }
    }
  } else {
    setBopisNotAvailable({ selectedColor, selectedSize, selectedVariant, updateVariant, variants });
  }
  return {
    selectedColor,
    selectedVariant,
    variants,
  };
}

// loop through all SKUs and set `bopisInStock` flag to false
function setBopisNotAvailable({ variants, selectedSize, selectedColor, selectedVariant, updateVariant = false }: SetAvailabilityProp) {
  variants.forEach((variant: ProductVariant) => {
    variant?.colorGroups?.fullprice.forEach((colorGroup: ProductColorGroup) => {
      setBopisUnavailability(colorGroup);
    });
    variant?.colorGroups?.markdown?.forEach((colorGroup: ProductColorGroup) => {
      setBopisUnavailability(colorGroup);
    });
    variant?.productStyleColors?.forEach((productStyleColor: ProductColorRaw[]) => {
      productStyleColor.forEach((color: ProductColorRaw) => {
        setSizeUnavailablilityInfo(color);
      });
    });

    if (updateVariant) {
      variant.bopisInStock = false;
    }
  });

  if (selectedSize) {
    selectedSize.bopisInStock = false;
  }

  if (selectedColor) {
    selectedColor.bopisInStock = false;
  }

  selectedColor?.sizes?.forEach((size: ProductSku) => {
    size.bopisInStock = false;
  });

  selectedVariant?.colorGroups?.fullprice?.forEach((colorGroup: ProductColorGroup) => {
    setBopisUnavailability(colorGroup);
  });
  selectedVariant?.colorGroups?.markdown?.forEach((colorGroup: ProductColorGroup) => {
    setBopisUnavailability(colorGroup);
  });

  selectedVariant?.productStyleColors?.forEach((productStyleColor: ProductColorRaw[]) => {
    productStyleColor.forEach((color: ProductColorRaw) => {
      setSizeUnavailablilityInfo(color);
    });
  });

  if (updateVariant && selectedVariant) {
    selectedVariant.bopisInStock = false;
  }
}

/**
 * @param {Array} apiVariantInventory
 * @returns {Array} an array of the available skus returned from the service call
 */
function getPasAvailableSkus(apiVariantInventory: VariantAvailability[]): SkuAvailability[] {
  // guard against non-existent apiVariantInventory
  let result: SkuAvailability[] = [];
  if (apiVariantInventory.length === 0) {
    return result;
  }

  apiVariantInventory.forEach((variant: VariantAvailability) => {
    variant.styleColors.forEach(({ skus }: StyleAvailability) => {
      const availableSkus = Array.isArray(skus) ? skus.filter((sku: SkuAvailability) => sku.availability === 'IN_STOCK') : [];
      if (availableSkus.length) {
        result = result.concat(availableSkus);
      }
    });
  });

  // Return a set to remove any duplicates and for fast lookup
  return result;
}

export default injectPasBopisData;
