import { Brand } from '@ecom-next/utils/server';
import type { FeatureVariablesType } from '@ecom-next/core/legacy/feature-flags';

type GetModelSize = {
  brand: Brand;
  featureVariables: FeatureVariablesType;
  modelSizeCookieValue: string | undefined;
  modelSizeQuery: string | undefined;
};

type HandleModelToggleProps = {
  currentModelSize: string;
  modelSizes:
    | {
        height: string;
        size: string;
      }[]
    | undefined;
};

export const getModelSize = ({ brand, featureVariables, modelSizeQuery, modelSizeCookieValue }: GetModelSize): string => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sizeInclusivityConfig = featureVariables?.['pdp-sizeinclusivity'] as any;
  const configuredVariable = sizeInclusivityConfig?.defaultSize?.[brand];
  const defaultModelSize = configuredVariable || 'M';

  return modelSizeQuery || modelSizeCookieValue || defaultModelSize;
};

export const handleModelToggle = ({ modelSizes, currentModelSize }: HandleModelToggleProps): string => {
  if (Array.isArray(modelSizes)) {
    const modelToggleCount = modelSizes.length || 3;
    if (modelToggleCount !== 3 && currentModelSize === 'L') {
      return 'M';
    }
  }

  return currentModelSize;
};
