import { Brand } from '@ecom-next/utils/server';
import { getModelSize, handleModelToggle } from '../../index';
import { FeatureFlag } from '../../../../../hooks/use-feature-flag/use-feature-flag';

const sizeInclusivityKey = 'pdp-sizeinclusivity';
const brand: Brand = 'gap';
const featureVariables: FeatureFlag['featureVariables'] = {
  [sizeInclusivityKey]: {
    defaultSize: {
      gap: 'S',
    },
  },
};

describe('getModelSize', () => {
  test('should get the default value when no cookie nor query param is present', () => {
    const modelSizeQuery = undefined;
    const modelSizeCookieValue = undefined;
    const modelSize = getModelSize({ brand, featureVariables: {}, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual('M');
  });

  test('should get the correct value from query param when no cookie is present', () => {
    const modelSizeQuery = 'L';
    const modelSizeCookieValue = undefined;
    const modelSize = getModelSize({ brand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual('L');
  });

  test('should get the correct value from cookie if no query param is present', () => {
    const modelSizeQuery = undefined;
    const modelSizeCookieValue = 'S';
    const modelSize = getModelSize({ brand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual(modelSizeCookieValue);
  });

  test('should get the correct value from query param if query and cookie are present', () => {
    const modelSizeQuery = 'L';
    const modelSizeCookieValue = 'S';
    const modelSize = getModelSize({ brand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual(modelSizeQuery);
  });

  test('should return the size from query even if default size is set in feature variables', () => {
    const modelSizeQuery = 'L';
    const modelSizeCookieValue = 'S';
    const modelSize = getModelSize({ brand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual(modelSizeQuery);
  });

  test('should return the default size from feature variable if no model size query or cookie present', () => {
    const modelSizeQuery = undefined;
    const modelSizeCookieValue = undefined;
    const modelSize = getModelSize({ brand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual(featureVariables[sizeInclusivityKey]?.defaultSize[brand]);
  });

  test('should return M as default size if no size received from cookie, query or default size in feature variables', () => {
    const modelSizeQuery = undefined;
    const modelSizeCookieValue = undefined;
    const ATBrand = 'at';

    const modelSize = getModelSize({ brand: ATBrand, featureVariables, modelSizeCookieValue, modelSizeQuery });
    expect(modelSize).toEqual('M');
  });
});

describe('handleModelToggle', () => {
  test('should return currentModelSize value when modelSizes is not an array', () => {
    const modelSize = handleModelToggle({ currentModelSize: 'L', modelSizes: undefined });

    expect(modelSize).toEqual('L');
  });

  test('should return currentModelSize when modelSizes length is 0', () => {
    const modelSize = handleModelToggle({ currentModelSize: 'L', modelSizes: [] });

    expect(modelSize).toEqual('L');
  });

  test('should default to M when modelSizes length is not 3 and greater than 0, and currentModelSize is L', () => {
    const modelSizes = [{ height: '7"10', size: 'L' }];
    const modelSize = handleModelToggle({ currentModelSize: 'L', modelSizes });

    expect(modelSize).toEqual('M');
  });
});
