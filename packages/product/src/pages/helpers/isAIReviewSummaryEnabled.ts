import { FeatureFlags } from '../../hooks/use-feature-flag';
import { Segments } from '../../providers/abseg-provider/utils';
import { CAPIAggregationService } from '../services/capi-aggregation-service';
import type { SetupFeaturesReturns } from '../../providers/features-provider/use-features';

type isAIReviewSummaryEnabledProps = {
  capiData: CAPIAggregationService;
} & Pick<SetupFeaturesReturns, 'getAbSeg' | 'getFeatureFlag' | 'getFeatureVariables'>;

export const isAIReviewSummaryEnabled = ({ capiData, getAbSeg, getFeatureFlag, getFeatureVariables }: isAIReviewSummaryEnabledProps) => {
  const {
    productData: { reviewSummaries = null, primaryCategoryId },
  } = capiData;

  const isFeatureFlagEnabled = getFeatureFlag(FeatureFlags.aiReviews);

  const placementSegment = getAbSeg(Segments.reviewSummary) || '';
  const modelSegment = getAbSeg(Segments.reviewSummaryModel);
  const { test1, test2, default: defaultKey, supportedCategories = '' as string } = getFeatureVariables(FeatureFlags.aiReviews);
  const isEnabledPrimaryCategory =
    !supportedCategories || supportedCategories?.split(',').includes(primaryCategoryId) || supportedCategories?.toLowerCase() === 'all';
  const enabled = isFeatureFlagEnabled && isEnabledPrimaryCategory && (placementSegment === 'a' || placementSegment === 'b');

  let model;
  switch (modelSegment) {
    case 'a':
      model = test1;
      break;
    case 'b':
      model = test2;
      break;
    default:
      model = defaultKey || 'default';
  }

  return {
    display: enabled && !!reviewSummaries && !!reviewSummaries[model],
    enabled,
    model,
    modelNameForTracking: reviewSummaries?.[model]?.modelId,
    placement: placementSegment === 'a' ? 'default' : 'alternate',
  };
};
