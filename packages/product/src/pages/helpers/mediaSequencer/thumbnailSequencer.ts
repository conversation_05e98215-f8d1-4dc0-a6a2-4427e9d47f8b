import { applyMaxImagesLimit } from './mediaHelper';
import { AdaptedImage, AdaptedVideo, LayoutSequencer } from './types';

/**
 *
 * @description
 * Carousel layout reads videoPlacementDefaultIndex from feature config
 * Video is always displayed at the 4th position
 */

export function thumbnailSequencer({ images, videos, imageGalleryConfig }: LayoutSequencer): (AdaptedVideo | AdaptedImage)[] {
  const { videoPlacementDefaultIndex } = imageGalleryConfig;

  const imagesToShowInOrder = [...images.slice(0, videoPlacementDefaultIndex), ...videos.slice(0, 1), ...images.slice(videoPlacementDefaultIndex)];
  return applyMaxImagesLimit(imagesToShowInOrder, imageGalleryConfig);
}
