import { AdaptedVariant } from '../../v3';
import { getAllColorsMVG } from '../mvg-get-all-colors';

describe('getAllColorsMVG', () => {
  it('should return all colors from regular and markdown groups', () => {
    const mockColorsRegular = [{ customer_choice_id: '1' }, { customer_choice_id: '2' }];

    const mockColorsMarkdown = [{ customer_choice_id: '3' }, { customer_choice_id: '4' }];

    const selectedMultiVariantData = {
      color_groups: {
        regular: [mockColorsRegular],
        markdown: [mockColorsMarkdown],
      },
    } as unknown as AdaptedVariant;

    const result = getAllColorsMVG(selectedMultiVariantData);
    expect(result).toEqual([...mockColorsRegular, ...mockColorsMarkdown]);
  });

  it('should handle cases where when markdown is missing', () => {
    const mockColorsRegular = [{ customer_choice_id: '1' }, { customer_choice_id: '2' }];

    const selectedMultiVariantData = {
      color_groups: {
        regular: [mockColorsRegular],
      },
    } as unknown as AdaptedVariant;

    const result = getAllColorsMVG(selectedMultiVariantData);
    expect(result).toEqual([...mockColorsRegular]);
  });

  it('should handle cases where when regular is missing', () => {
    const mockColorsMarkdown = [{ customer_choice_id: '1' }, { customer_choice_id: '2' }];

    const selectedMultiVariantData = {
      color_groups: {
        markdown: [mockColorsMarkdown],
      },
    } as unknown as AdaptedVariant;

    const result = getAllColorsMVG(selectedMultiVariantData);
    expect(result).toEqual([...mockColorsMarkdown]);
  });
});
