/* eslint-disable no-restricted-syntax */
import type { ProductImageRaw } from '@pdp/types/product-data/style-level-raw'; // NOSONAR

type ProductImages = Record<string, ProductImageRaw>;

export function validImagePaths(images: ProductImages): ProductImages {
  const newImages: ProductImages = {};

  for (const [key, image] of Object.entries(images)) {
    newImages[key] = removeEmptyImages(image);
  }
  return newImages;
}

function removeEmptyImages(image: ProductImageRaw): ProductImageRaw {
  const newImage = {} as ProductImageRaw;
  for (const [key, path] of Object.entries(image)) {
    if (path) {
      newImage[key as keyof ProductImageRaw] = path;
    }
  }
  return newImage;
}
