import { AdaptedCustomerChoice, CAPIV3TransformedData, CustomerChoiceImage, CustomerChoiceVideo, SitePlacement, ImageCropLegacyType, ImageObj } from '../types';
/**
 * This is needed to support the legacy image components until we can consume redesigned image components
 */

const baseObj: ImageObj = {
  large: 'VIEW LARGE IMAGE',
  medium: 'PRIMARY',
  small: 'QUICK LOOK IMAGE',
  thumbnail: 'THUMBNAIL',
  video: 'VIDEO',
  xlarge: 'ZOOM',
};

const createImageObj = (image: CustomerChoiceImage): ImageObj => {
  const imageObj = {} as ImageObj;
  const possibleKeys = Object.keys(baseObj) as Array<keyof ImageObj>;

  possibleKeys.forEach(key => {
    image.crops.forEach(crop => {
      if (crop.type === baseObj[key]) {
        imageObj[key] = `/${crop.url}`;
      }
    });
  });

  return imageObj;
};

const createVideoObj = ({ image, url }: CustomerChoiceVideo): ImageObj => {
  const imageObj = {} as ImageObj;
  const possibleKeys = Object.keys(baseObj) as Array<keyof ImageObj>;
  if (Array.isArray(image?.crops)) {
    possibleKeys.forEach(key => {
      image.crops.forEach(crop => {
        if (crop.type === baseObj[key]) {
          imageObj[key] = `/${crop.url}`;
        }
      });
    });
  }
  return {
    video: `/${url}`,
    ...imageObj,
  };
};

const createImageKeyName = (CCNumber: string, legacyPlacement: ImageCropLegacyType | SitePlacement): string => {
  return `${CCNumber}_${legacyPlacement.includes('AV') ? legacyPlacement : 'main'}`;
};

const createVideoKeyName = (CCNumber: string): string => {
  return `${CCNumber}_AV9`;
};

export const getLegacyProductImages = (productData: CAPIV3TransformedData): Record<string, ImageObj> => {
  const productImages = {} as Record<string, ImageObj>;
  (Object.values(productData.customer_choices) as AdaptedCustomerChoice[]).forEach((ccData: AdaptedCustomerChoice) => {
    const { customer_choice_id, images, videos } = ccData;
    images.forEach(image => {
      const imageKeyName = createImageKeyName(customer_choice_id, image.legacy_placement as ImageCropLegacyType);
      productImages[imageKeyName] = createImageObj(image);
    });

    (videos as AdaptedCustomerChoice['videos']).forEach(video => {
      const videoKeyName = createVideoKeyName(customer_choice_id);
      productImages[videoKeyName] = createVideoObj(video);
    });
  });

  return productImages;
};
