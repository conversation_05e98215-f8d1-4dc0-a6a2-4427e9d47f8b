export const appConfig = {
  appConfig: {
    apiConfig: {
      catalogStyleLookupUrl: 'https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com',
      internalOidcUrl: 'https://internal-azeus-ecom-api.live.prod.gap.com',
      oidcUrl: 'https://api.gap.com',
      pmcsConfig: { url: 'https://browse-api-nginx-cache.aks.prod.azeus.gaptech.com/pmcs' },
      pasConfig: { url: 'https://internal-azeus-ecom-api.live.prod.gap.com' },
      pssConfig: { url: 'https://internal-azeus-ecom-api.live.prod.gap.com' },
    },
    brandCodeUrls: {
      us: {
        at: {
          secureUrl: 'secure-athleta.gap.com',
          unsecureUrl: 'athleta.gap.com',
          commonHost: 'athleta.gap.com',
          oidcUrl: 'https://api.gap.com',
          catalogStyleLookupUrl: 'https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com',
          internalOidcUrl: 'https://internal-azeus-ecom-api.live.prod.gap.com',
          apiHost: 'https://api.gap.com',
          atbOidcUrl: 'https://api.gap.com',
        },
      },
      secureUrl: 'secure-athleta.gap.com',
      unsecureUrl: 'athleta.gap.com',
      commonHost: 'athleta.gap.com',
      oidcUrl: 'https://api.gap.com',
      catalogStyleLookupUrl: 'https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com',
      internalOidcUrl: 'https://internal-azeus-ecom-api.live.prod.gap.com',
      apiHost: 'https://api.gap.com',
      atbOidcUrl: 'https://api.gap.com',
    },
    powerReviewsConfig: { groupId: 78519, merchantId: 123756, apiKey: 'powerreviews123456789' },
    socialGalleryConfig: {
      en_US: {
        gap: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        on: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        br: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        at: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        gapfs: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        brfs: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
      },
      en_CA: {
        gap: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        on: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        br: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        at: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        brfs: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
      },
      fr_CA: {
        gap: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        on: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        br: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        at: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
        brfs: { accountId: 1883, widgetId: ********, apiKey: 'socialgallery123456789' },
      },
    },
  },
};
