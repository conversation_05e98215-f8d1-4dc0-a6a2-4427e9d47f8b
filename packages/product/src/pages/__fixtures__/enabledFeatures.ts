export const features = {
  enabledFeatures: {
    'productstyle-service-us-hc': true,
    'productstyle-service-us-on': true,
    'productstyle-service-us-gap': true,
    'productstyle-service-us-at': true,
    'productstyle-service-us-br': true,
    'bopis-us-on': true,
    'breadcrumbs-hc': true,
    'bopis-category': true,
    'react-personalization-polling': true,
    'department-facet-hide-us-hc': true,
    'insitu-us-gap': true,
    'insitu-us-on': true,
    'insitu-us-br': true,
    'insitu-us-at': true,
    'bopis-us-br': true,
    'bopis-us-gap': true,
    'bopis-us-at': true,
    'bopis-enhancement-us-on': true,
    'bopis-enhancement-us-gap': true,
    'bopis-enhancement-us-at': true,
    'pdp-redesign-us-on': true,
    'pdp-recommendations-us-at': true,
    'pdp-recommendations-us-br': true,
    'pdp-redesign-us-hc': true,
    'pdp-recommendations-us-hc': true,
    'pdp-redesign-us-at': true,
    'pdp-recommendations-us-on': true,
    'pdp-recommendations-us-gap': true,
    'pdp-redesign-us-br': true,
    'pdp-redesign-us-gap': true,
    'pdp-bricks-us-on': true,
    'left-rail-facet-at-us': true,
    'left-rail-facet-on-us': true,
    'left-rail-facet-br-us': true,
    'left-rail-facet-hc-us': true,
    'scroll-to-top-category-us-hc': true,
    'scroll-to-top-search-us-hc': true,
    'productdiscovery-service': true,
    'pdp-sizeinclusivity-us-br': true,
    'pdp-sizeinclusivity-us-on': true,
    'pdp-sizeinclusivity-us-gap': true,
    'redesign-model-button-us-at': true,
    'redesign-model-button-us-br': true,
    'redesign-model-button-us-gap': true,
    'redesign-model-button-us-hc': true,
    'left-rail-mobile-facet-on-us': true,
    'left-rail-mobile-facet-at-us': true,
    'left-rail-mobile-facet-br-us': true,
    'left-rail-mobile-facet-hc-us': true,
    'left-rail-mobile-facet-gap-us': true,
    'sort-by-gap-us': true,
    'sort-by-on-us': true,
    'pdp-redesign-mobile-carousel-us-br': true,
    'sort-by-at-us': true,
    'pdp-redesign-desktop-drawer-us-on': true,
    'sort-by-br-us': true,
    'pdp-bricks-us-gap': true,
    'pdp-redesign-mobile-carousel-us-gap': true,
    'pdp-redesign-desktop-drawer-us-br': true,
    'pdp-bricks-us-hc': true,
    'pdp-redesign-mobile-carousel-us-at': true,
    'pdp-redesign-desktop-drawer-us-at': true,
    'sort-by-hc-us': true,
    'pdp-bricks-us-br': true,
    'pdp-redesign-desktop-drawer-us-gap': true,
    'pdp-redesign-mobile-carousel-us-on': true,
    'pdp-bricks-us-at': true,
    'division-pages-on': true,
    'pdp-redesign-us-brfs': true,
    'pdp-sizeinclusivity-ca-on': true,
    'pdp-redesign-ca-br': true,
    'pdp-redesign-ca-gap': true,
    'pdp-recommendations-ca-gap': true,
    'pdp-recommendations-us-gapfs': true,
    'pdp-bricks-ca-on': true,
    'pdp-bricks-us-brfs': true,
    'pdp-redesign-desktop-drawer-ca-gap': true,
    'pdp-redesign-desktop-drawer-us-brfs': true,
    'pdp-redesign-us-gapfs': true,
    'pdp-recommendations-ca-on': true,
    'pdp-redesign-ca-on': true,
    'pdp-redesign-desktop-drawer-ca-on': true,
    'pdp-redesign-mobile-carousel-us-brfs': true,
    'pdp-recommendations-us-brfs': true,
    'pdp-bricks-us-gapfs': true,
    'pdp-redesign-mobile-carousel-ca-br': true,
    'pdp-redesign-mobile-carousel-ca-on': true,
    'pdp-redesign-desktop-drawer-ca-br': true,
    'pdp-bricks-ca-gap': true,
    'pdp-recommendations-ca-br': true,
    'pdp-redesign-desktop-drawer-us-gapfs': true,
    'pdp-redesign-mobile-carousel-us-gapfs': true,
    'pdp-redesign-mobile-carousel-ca-gap': true,
    'pdp-bricks-ca-br': true,
    'pas-us-gap': true,
    'pas-us-at': true,
    'pas-us-br': true,
    'pas-us-on': true,
    'autosuggest-bloomreach': true,
    'shopping-bag-card-savings-calc': true,
    'search-bloomreach': true,
    'pdp-photo-carousel-slick': true,
    'pdp-hide-shipping-us-brfs': true,
    'cat-breadcrumb-br': true,
    'cat-breadcrumb-at': true,
    'cat-breadcrumb-hc': true,
    'cat-breadcrumb-gapfs': true,
    'cat-breadcrumb-gap': true,
    'cat-breadcrumb-on': true,
    'left-rail-facet-gapfs-us': true,
    'left-rail-mobile-facet-brfs-us': true,
    'left-rail-facet-brfs-us': true,
    'left-rail-mobile-facet-gapfs-us': true,
    'cat-alt-view-us-at': true,
    'bag-ui-leapfrog': true,
    'cat-quick-add-gap-ca': true,
    'cat-quick-add-gapfs-us': true,
    'bag-ui-paypal': true,
    'cat-quick-add-gap-us': true,
    'bopis-ca-br': true,
    'bag-ui-applepay': true,
    'bopis-ca-on': true,
    'bopis-ca-gap': true,
    'productstyle-service-ca-br': true,
    'productstyle-service-ca-on': true,
    'productstyle-service-us-brfs': true,
    'productstyle-service-us-gapfs': true,
    'productstyle-service-ca-gap': true,
    'pdp-power-reviews': true,
    'pdp-afterpay': true,
    'cat-always-sticky-mega-nav-on-us': true,
    'cat-always-sticky-mega-nav-br-us': true,
    'cat-always-sticky-mega-nav-gap-us': true,
    'cat-always-sticky-mega-nav-at-us': true,
    'left-rail-mobile-facet-br-ca': true,
    'left-rail-mobile-facet-gap-ca': true,
    'left-rail-facet-brfs-ca': true,
    'left-rail-facet-on-ca': true,
    'bag-ui-afterpay': true,
    'left-rail-mobile-facet-on-ca': true,
    'left-rail-mobile-facet-at-ca': true,
    'left-rail-facet-br-ca': true,
    'left-rail-mobile-facet-gapfs-ca': true,
    'left-rail-facet-at-ca': true,
    'pdp-recommendations-pricing': true,
    'cat-always-sticky-mega-nav-on-ca': true,
    'left-rail-mobile-facet-brfs-ca': true,
    'cat-always-sticky-mega-nav-gap-ca': true,
    'left-rail-facet-gap-ca': true,
    'left-rail-facet-gapfs-ca': true,
    'at-condensed-header': true,
    'br-condensed-header': true,
    'on-condensed-header': true,
    'gapfs-condensed-header': true,
    'brfs-condensed-header': true,
    'gap-condensed-header': true,
    'pdp-mfe-load-power-reviews': true,
    'cat-flex-facets': true,
    'new-shopping-bag-url': true,
    'bag-ui-paypal-bopis': true,
    on32: true,
    'pdp-quick-add-category-recommendations': true,
    'bloomreach-rank-by-customer-segment': true,
    'cat-model-toggle-ca': true,
    'pdp-scarcity': true,
    'cat-cache-navigation-data': true,
    'cat-product-dashboard-at-us': true,
    'cat-product-dashboard-gapfs-us': true,
    'cat-product-dashboard-brfs-ca': true,
    'cat-product-dashboard-br-us': true,
    'cat-product-dashboard-brfs-us': true,
    'cat-product-dashboard-gapfs-ca': true,
    'cat-product-dashboard-at-ca': true,
    'cat-product-dashboard-gap-us': true,
    'cat-product-dashboard-br-ca': true,
    'cat-product-dashboard-on-us': true,
    'cat-product-dashboard-gap-ca': true,
    'pdp-video-sound': true,
    'pdp-free-shipping-bar-ca-br': true,
    'pdp-free-shipping-bar-us-brfs': true,
    'pdp-free-shipping-bar-ca-on': true,
    'pdp-free-shipping-bar-us-on': true,
    'pdp-free-shipping-bar-us-br': true,
    'pdp-free-shipping-bar-us-gap': true,
    'pdp-free-shipping-bar-us-gapfs': true,
    'pdp-free-shipping-bar-us-at': true,
    'pdp-free-shipping-bar-ca-gap': true,
    'pdp-sizeinclusivity': true,
    'new-preview-button': true,
    'bag-ui-payments-disclaimer': true,
    'bag-ui-paypal-log': true,
    'cat-ism': true,
    'bopis-us-gapfs': true,
    'pas-ca-gap': true,
    'bopis-us-brfs': true,
    'pas-us-brfs': true,
    'pas-ca-br': true,
    'bopis-ca-at': true,
    'pas-ca-on': true,
    'pas-us-gapfs': true,
    'cat-pmcs': true,
    'pdp-hide-shipping-us-br': true,
    'bag-ui-loyalty': true,
    'pdp-hide-shipping-ca-br': true,
    'new-profile-urls': true,
    'pdp-carousel-us-at': true,
    'bag-ui-personalized-fulfillment': true,
    'pdp-recommendations-ca-at': true,
    'pdp-redesign-mobile-carousel-ca-at': true,
    'pdp-redesign-ca-at': true,
    'pdp-redesign-desktop-drawer-ca-at': true,
    'pas-ca-at': true,
    'pdp-bricks-ca-at': true,
    'bag-ui-apple-pay-logging': true,
    'pdp-loyalty-enroll-link': true,
    'yzy-product-page-leapfrog': true,
    'athleta-canada-sister-logo': true,
    'pdp-recommendations-ratings': true,
    'pdp-size-guide-modal': true,
    'pdp-stylist-videos': true,
    'cat-product-card': true,
    'pdp-wairfit': true,
    'cat-star-ratings': true,
    'pdp-marketing-container': true,
    'pdp-anchor-marketingcontainer': true,
    'cms-session': true,
    'pdp-insitu-marketing-container': true,
    'yzy-hybrid-page-data-capture': true,
    'pdp-pixleeegc-widget': true,
    'gap-exposed-mobile-search': true,
    'enable-new-br-fonts': true,
    'search-flex-facets': true,
    'bag-ui-bopis-enhacement': true,
    'pdp-hotfix-error': true,
    'bag-ui-paypal-xapi': true,
    'pdp-size-sampling-us-on': true,
    'search-keyword-animation': true,
    'pdp-size-sampling-us-gap': true,
    'pdp-size-sampling-ca-on': true,
    'pdp-size-sampling-ca-br': true,
    'pdp-size-sampling-us-gapfs': true,
    'pdp-size-sampling-us-brfs': true,
    'pdp-size-sampling-ca-at': true,
    'pdp-size-sampling-us-at': true,
    'pdp-size-sampling-us-br': true,
    'pdp-size-sampling-ca-gap': true,
    'yzy-hybrid-page-pristines': true,
    'search-page-null-recommendations': true,
    'enable-sister-brands-logs': true,
    'pdp-replace-third-image-with-video-us-on': true,
    'pdp-replace-third-image-with-video-ca-on': true,
    'gapfs-exposed-mobile-search': true,
    'at-exposed-mobile-search': true,
    useMCS: true,
    'on-exposed-mobile-search': true,
    'pdp-drapr-us-at': true,
    'pdp-drapr-us-on': true,
    'search-page-leapfrog-atb': true,
    'pdp-drapr-us-gapfs': true,
    'pdp-drapr-us-gap': true,
    'search-page-null-quick-add-recommendations': true,
    'search-page-null-new-arrivals': true,
    'pdp-alternate-customer-id': true,
    indexable_color_facets: true,
    'pdp-enable-storage-effect': true,
    'bag-ui-card-offer': true,
    'cat-style-v-category': true,
    'cat-pill-navigation': true,
    'find-gap-us-font-color-update': true,
    'find-gap-ca-font-color-update': true,
    'cat-switch-contract': true,
    'cat-color-swatches': true,
    'top-search-terms-desktop': true,
    'top-search-terms-mobile': true,
    category_color_page: true,
    'bag-ui-xapi-logs': true,
    'find-wcd-hamnav-manipulation': true,
    'pdp-redesign-2022': true,
    'acquisition-snackbar': true,
    'br-expanded-compressed-header': true,
    'pdp-photos-stacked-layout-ca-brfs': true,
    'gap-colors-2022': true,
    'pdp-photos-stacked-layout-us-brfs': true,
    'pdp-photos-stacked-layout-ca-br': true,
    'brfs-expanded-compressed-header': true,
    'pdp-drapr-v3-test-us-on': true,
    'pdp-photos-stacked-layout-us-br': true,
    'mock-content-service-on-sitewide': true,
    'search-quick-filter': true,
    'pdp-paypal': true,
    'pdp-review-snapshot-drawer-ca-brfs': true,
    'pdp-review-snapshot-drawer-us-brfs': true,
    'pdp-product-info-widgets-us-on': true,
    'bag-pmcs': true,
    'pdp-product-info-widgets-us-at': true,
    'pdp-product-info-widgets-us-gapfs': true,
    'pdp-oidc-url': true,
    'pdp-review-snapshot-drawer-ca-br': true,
    'pdp-product-info-widgets-ca-at': true,
    'pdp-product-info-widgets-ca-gap': true,
    'pdp-review-snapshot-drawer-us-br': true,
    'pdp-sticky-add-to-bag-ca-on': true,
    'pdp-product-info-widgets-us-gap': true,
    'pdp-pi4': true,
    'pdp-product-info-widgets-ca-on': true,
    'pdp-sticky-add-to-bag-us-on': true,
    'pdp-quickadd-redesign-2022': true,
    'sitewide-header-scroll-on-pdp': true,
    'pdp-fulfillment-selection-v2': true,
    'pdp-dropship-us-br': true,
    'footer-link-updates-620': true,
    'search-product-search': true,
    'pdp-dropship-us-gap': true,
    'cat-dropship': true,
    'pdp-mfe-load-drapr-script-us-at': true,
    'email-payload-update': true,
    'sw-mini-bag': true,
    'pdp-mfe-load-drapr-script-us-br': true,
    'pdp-mfe-load-drapr-script-us-brfs': true,
    'pdp-mfe-load-drapr-script-us-on': true,
    'pdp-mfe-load-drapr-script-us-gapfs': true,
    'pdp-mfe-load-drapr-script-us-gap': true,
    useMCSLib: true,
    'findmine-athleta-layout-update': true,
    'bag-ui-empty-bag-state': true,
    'sw-use-new-cache': true,
    'long-lived-sign-in-sitewide': true,
    'sw-disable-pmcs-cache': true,
    'pdp-redesign-video-ca-br': true,
    'use-chas-api': true,
    'pdp-redesign-video-us-br': true,
    'pdp-redesign-video-us-brfs': true,
    'shopping-api-update': true,
    'pdp-review-voting-us-at': true,
    'bag-ui-enable-sfl-url': true,
    'pdp-review-search-ca-br': true,
    'pdp-review-search-ca-at': true,
    'pdp-review-search-us-at': true,
    'pdp-review-search-us-on': true,
    'pdp-review-voting-ca-at': true,
    'pdp-review-voting-ca-br': true,
    'pdp-review-voting-us-brfs': true,
    'pdp-review-search-us-brfs': true,
    'pdp-review-voting-us-on': true,
    'pdp-review-search-us-br': true,
    'pdp-review-voting-ca-on': true,
    'pdp-review-search-us-gapfs': true,
    'pdp-review-search-us-gap': true,
    'pdp-review-search-on-ca': true,
    'pdp-review-voting-us-br': true,
    'pdp-review-search-ca-gap': true,
    'bag-ui-mini-bag': true,
    'tealium-priority-load-core': true,
    'bag-ui-barclays-calculator': true,
    'pdp-chas-bopis': true,
    'pdp-chas-quick-add': true,
    'swf-promodrawer-tta-fallback': true,
    'swf-use-pmcs-query-params-from-cookies': true,
    'pdp-chas': true,
    'swf-new-promodrawer': true,
    'cat-badging': true,
    'swf-use-cookie-package': true,
    'pdp-pmcs-us-on': true,
    'pdp-pmcs-us-at': true,
    'pdp-pmcs-ca-at': true,
    'swf-autofirescroll-disable-colapsecroll': true,
    'search-badging': true,
    'react-17': true,
    'sw-sitecode-order-change': true,
    'swf-promo-drawer-events': true,
    'swf-remote-version': true,
    'seo-product-schema-refresh-q1-2023': true,
    'pdp-drapr-analytics-script-us-on': true,
    'pdp-drapr-analytics-script-us-gap': true,
    'pdp-drapr-analytics-script-us-gapfs': true,
    'pdp-drapr-analytics-script-ca-at': true,
    'pdp-drapr-analytics-script-us-br': true,
    'pdp-drapr-analytics-script-ca-on': true,
    'pdp-drapr-analytics-script-ca-br': true,
    'pdp-drapr-analytics-script-us-at': true,
    'pdp-lpo-certona-us-at': true,
    'pdp-drapr-analytics-script-us-brfs': true,
    'pdp-drapr-analytics-script-ca-gap': true,
    'pdp-lpo-plp-us-at': true,
    'swf-use-coreui-double-providers': true,
    'pdp-optimized-oos': true,
    'swf-use-points-and-rewards-absolute-paths': true,
    'swf-preview-inventory-aware': true,
    'bag-ui-oos': true,
    'plp-filters-facets': true,
    'swf-new-relic-2023': true,
    'swf-disable-nr-features-list': true,
    'at-redesign-2023': true,
    'swf-preview-date-iso-string': true,
    'br-redesign-2024': true,
    'adeptmind-crosslinks-plp': true,
    'adeptmind-crosslinks-pdp': true,
    'swf-new-registration-endpoints': true,
    'br-redesign-2024-ph2': true,
    'bag-ui-order-summary': true,
    'swf-flip-header-config-precedence': true,
    'pdp-recommendations-ca-brfs': true,
    'at-redesign-2024': true,
    'pdp-mfe-load-certona': true,
    'enable-font-display-swap': true,
    'bag-ui-paypal-v5': true,
    'pdp-clothing-label-us-on': true,
    'search-color-swatches': true,
    'pdp-hide-shipping-ca-brfs': true,
    'pdp-bricks-ca-brfs': true,
    'pdp-bricks-brfs-ca': true,
    'pdp-video-repeated-us-at': true,
    'brfs-redesign-2024': true,
    'swf-hide-terms-and-conditions': true,
    'pdp-super-ph2': true,
    'mui-marketing-component-placeholder-wrapper-enabled': true,
    'swf-at-redesign-2024': true,
    'pdp-percentage-off-ca-on': true,
    'pdp-percentage-off-us-on': true,
    'plp-grid-toggle': true,
    'plp-at-redesign-2024': true,
    'gapfs-newlogo-2024': true,
    'pdp-percentage-off-us-brfs': true,
    'pdp-percentage-off-ca-brfs': true,
    'pdp-percentage-off-us-gapfs': true,
    'swf-preview-date-from-cookie': true,
    'redesign-2024-ph2-us-brfs': true,
    'redesign-2024-ph2-ca-brfs': true,
    'mui-br-redesign-2024': true,
    'variable-experiment': true,
    'wfo-server-side-facets-poc': true,
    'pdp-ai-reviews-us-on': true,
    'pdp-ai-reviews-us-at': true,
    'nav-friendly-url': true,
    'chk-demo-test': true,
    'br-redesign-slice-2': true,
    'seo-indexable-flex-facets': true,
    'checkout-ui-vault-service': true,
    'checkout-ui-rokt': true,
    'checkout-ui-donation': true,
    'checkout-ui-google-autocomplete': true,
    'checkout-ui-bopis': true,
    'checkout-ui-bopis-giftcard': true,
    'checkout-ui-bopis-sms': true,
    'checkout-ui-afterpay': true,
    'checkout-ui-paypal-button': true,
    'checkout-ui-hubbox': true,
    'checkout-ui-barclays-us': true,
    'checkout-ui-barclays-ocp': true,
    'checkout-ui-bopis-sms-canada': true,
    'checkout-ui-bopis-gift-card-canada': true,
    'buy-ui-express-paypal': true,
    'checkout-ui-apple-pay': true,
    'at-buybox-2024': true,
    'plp-seo-poc': true,
    'use-tst-ps-api': true,
    'profile-ui-is-test-flag-enabled': true,
    'gap-buybox-2024': true,
    'profile-ui-aug-6': true,
    'gap-button-redesign-2024': true,
    'checkout-ui-guest-free-shipping-banner': true,
    'swf-2024-header-redesign-at': true,
    'swf-2024-header-redesign-br': true,
    'swf-2024-header-redesign-brfs': true,
    'swf-2024-header-redesign-gap': true,
    'swf-2024-header-redesign-on': true,
    'pdp-catalog-internal-api': true,
    'pdp-certona-internal-url': true,
    'swf-certona-gap-host': true,
    'certona-gap-endpoint': true,
    'mui-certona-recommendations': true,
    'pdp-review-voting-ca-brfs': true,
    'pdp-review-search-ca-brfs': true,
    'swf-ios-safari-appsflyer-attribution': true,
    'profile-ui-logs-enabled': true,
    'swf-text-sister-brands-bar': true,
    'plp-dynamic-badge': true,
    'plp-level-fields': true,
    'pdp-use-lookup-id-us-at': true,
    'pdp-use-lookup-id-ca-at': true,
    'pdp-atb-conf-us-at': true,
    'pdp-atb-conf-ca-at': true,
    'bag-ui-oos-v2': true,
    'bag-ui-multi-oos': true,
    'swf-beacon-on': true,
    'swf-beacon-brfs': true,
    'bag-ui-edit-bag': true,
    'pdp-color-swatch-layout-ca-br': true,
    'pdp-color-swatch-layout-us-at': true,
    'pdp-color-swatch-layout-us-gap': true,
    'division-capi-seo-meta-data': true,
    'profile-ui-is-targeted-offers-enabled': true,
    'profile-ui-is-vc-future-offers-enabled': true,
    'profile-ui-is-show-date-range-on-birthday-bonus-promo-enabled': true,
    'checkout-ui-3party-token-url': true,
    'autosuggest-product-search': true,
  },
  featureVariables: {
    'pdp-color-swatch-layout-us-at': { default: true, stacked: false },
    'pdp-color-swatch-layout-us-gap': { default: false, stacked: true },
    'pdp-color-swatch-layout-ca-br': { default: true, stacked: false },
    'pdp-bricks-us-gap': {
      primaryCategoryId:
        '5664,1011761,1041308,13658,1082574,1041168,1169932,1117374,17076,34608,5745,5736,5168,29504,34524,1015387,1027291,85615,35300,3023114,6998,80799,5156,8655,5270,5225,1197004,15043,83056,1066503,5168,5180,1120779,11523,5300,1127946,6276,13148,14403,6300,1141739,3024180,14417,1122942,1153699,1056270,1161843,6303,1051487,1075777,6323,1082288,56233,1108132,3028099,1107336,6189,6187,1085428,6191,1070923,1122119,6197,3024160,1117991,1175613,6205,1050851,1075793,9470,1082280,96875,3011655,1107335,6436,6427,12378,1121815,6444,1132758,17846,1175977,8770,1072981,76918,1188469,6465,1084375,1065722,1033898,6359,1016106,1121839,3024164,1016107,1175972,1016108,1072982,1016109,1188470,1016110,1084376,1101803,1034157,3010841,1098333,1027203,6437,7189,3024155,1028587,1175810,1108608,7191,1102200,76748,1120931,1146519,1086661,1098335,1027202,95598,3024156,1028588,1175813,1114529,95684,1102201,95697,1120932,1146528,1086662,6013,1014425,1025764,1154239,1008565,3026780,6049,1014424,1019160,3023389,1016449,1050384,1021874,1023948,1103148,1020009,1088548,3023112,1167042,1084375,1016169,1084376,1034157,1033898,1086661,7191,1086194,1027203,1016096,3024164',
      minRequiredImages: 2,
      maxAllowedImages: 12,
      displayAllAvailableImages: true,
      displayMainFromOtherCC: '',
      displayOrderOnlySelectedCC:
        '5664,1011761,1041308,13658,1082574,1041168,1169932,1117374,17076,34608,5745,5736,29504,34524,1015387,1027291,6998,80799,5156,8655,5270,5225,1197004,15043,83056,1066503,5168,5180,1120779,6276,13148,14403,6300,1141739,3024180,14417,1122942,1153699,1056270,1161843,6303,1051487,1075777,6323,6191,6197,35300,3023112,85615,3023113,3023114,5300,11523,1082288,56233,1108132,3028099,1107336,6189,6187,1085428,1070923,1122119,3024160,1117991,1175613,6205,1050851,1075793,9470,1082280,96875,3011655,1107335,1123992,1098333,1027203,6437,95598,3024156,1175810,1108608,7191,1102201,95697,1120931,1146519,1086661,1059829,1188469,6436,6427,12378,1121815,6444,1132758,17846,1175977,8770,1072981,76918,76748,6465,1084375,1033898,1059828,1188470,6359,1016106,1121839,1016096,1016169,3024164,1016107,1175972,1016108,1072982,1016109,1016110,1084376,1034157,6013,1014425,1025764,1154239,1008565,3026780,6049,1014424,1019160,3023389,1016449,1050384,1021874,1023948,1103148,1020009,1088548',
    },
    'pdp-bricks-us-on': {
      primaryCategoryId:
        '1126947,1031103,1126951,72305,5199,1010005,5226,38529,63315,1016048,66124,1123385,5249,1126985,1053133,1053127,5211,5256,1017042,14675,5508,1084336,55147,1124176,15292,55474,85729,1066122,1176448,5475,1176451,72087,1035712,35158,79586,50058,20408,72808,68066,1095675,72091,1168140,1030828,96964,1000106,1016287,1042659,1126786,7322,46550,29916,5668,5681,1041222,96016,1099712,65080,7240,92969,1053229,1126887,48687,50200,5848,41578,5874,5816,65081,1090999,1182448,1182449,1034235,3035609',
      minRequiredImages: 4,
      maxAllowedImages: 8,
    },
    'pdp-bricks-us-at': {
      primaryCategoryId:
        '1059471,1059479,86354,1177200,1011678,1005761,1011680,1011679,1032080,1009710,1177199,89745,1017102,1170247,1090333,1164127,1054836,1111614,1067955,1055327,1054835,1054837,46814,1108864,1108862,1071601,46881,1032096,1111336,1038916,1058485,1108900,1031353,46883,,46884,1013846,1001223,1046322,1059481,84136,1015838,1032089,84117,1026473,1026482,97463,1076564,1099237,60494,1054837,1054836,1054835,1111614,1067955,1145147,1055327,1006482,89745,46887,1025878,1017102,1059471,1059479,86354,1177200,1170247,1177199,1163347,1032080,97464,1011680,1011679,1009710,1005761,1011678,1107874',
      minRequiredImages: 4,
      maxAllowedImages: 8,
    },
    'pdp-bricks-us-br': {
      primaryCategoryId:
        '69883,67595,1107488,5030,87056,5032,99915,5013,28660,1016720,44873,75310,32643,35878,5389,1072457,70016,5170,24906,5176,15227,40926,1107608,1055063,5037,5191,80117,1020051,28660,44866,10894,1189711,1184521,5022,1192385',
      minRequiredImages: 4,
    },
    'pdp-bricks-ca-gap': {
      primaryCategoryId:
        '5664,1011761,1041308,13658,1082574,1041168,1169932,1117374,17076,34608,5745,5736,5168,29504,34524,1015387,1027291,85615,35300,3023114,6998,80799,5156,8655,5270,5225,1197004,15043,83056,1066503,5168,5180,1120779,11523,5300,1127946,6276,13148,14403,6300,1141739,3024180,14417,1122942,1153699,1056270,1161843,6303,1051487,1075777,6323,1082288,56233,1108132,3028099,1107336,6189,6187,1085428,6191,1070923,1122119,6197,3024160,1117991,1175613,6205,1050851,1075793,9470,1082280,96875,3011655,1107335,6436,6427,12378,1121815,6444,1132758,17846,1175977,8770,1072981,76918,1188469,6465,1084375,1065722,1033898,6359,1016106,1121839,3024164,1016107,1175972,1016108,1072982,1016109,1188470,1016110,1084376,1101803,1034157,3010841,1098333,1027203,6437,7189,3024155,1028587,1175810,1108608,7191,1102200,76748,1120931,1146519,1086661,1098335,1027202,95598,3024156,1028588,1175813,1114529,95684,1102201,95697,1120932,1146528,1086662,6013,1014425,1025764,1154239,1008565,3026780,6049,1014424,1019160,3023389,1016449,1050384,1021874,1023948,1103148,1020009,1088548,3023112,1167042,1084375,1016169,1084376,1034157,1033898,1086661,7191,1086194,1027203,1016096,3024164',
      minRequiredImages: 2,
      maxAllowedImages: 10,
      displayAllAvailableImages: true,
      displayMainFromOtherCC: '',
      displayOrderOnlySelectedCC:
        '5664,1011761,1041308,13658,1082574,1041168,1169932,1117374,17076,34608,5745,5736,29504,34524,1015387,1027291,6998,80799,5156,8655,5270,5225,1197004,15043,83056,1066503,5168,5180,1120779,6276,13148,14403,6300,1141739,3024180,14417,1122942,1153699,1056270,1161843,6303,1051487,1075777,6323,6191,6197,35300,3023112,85615,3023113,3023114,5300,11523,1082288,56233,1108132,3028099,1107336,6189,6187,1085428,1070923,1122119,3024160,1117991,1175613,6205,1050851,1075793,9470,1082280,96875,3011655,1107335,1123992,1098333,1027203,6437,95598,3024156,1175810,1108608,7191,1102201,95697,1120931,1146519,1086661,1059829,1188469,6436,6427,12378,1121815,6444,1132758,17846,1175977,8770,1072981,76918,76748,6465,1084375,1033898,1059828,1188470,6359,1016106,1121839,1016096,1016169,3024164,1016107,1175972,1016108,1072982,1016109,1016110,1084376,1034157,6013,1014425,1025764,1154239,1008565,3026780,6049,1014424,1019160,3023389,1016449,1050384,1021874,1023948,1103148,1020009,1088548',
    },
    'pdp-bricks-ca-on': {
      primaryCategoryId:
        '1126947,1031103,1126951,72305,5199,1010005,5226,38529,63315,1016048,66124,1123385,5249,1126985,1053133,1053127,5211,5256,1017042,14675,5508,1084336,55147,1124176,15292,55474,85729,1066122,1176448,5475,1176451,72087,1035712,35158,79586,50058,20408,72808,68066,1095675,72091,1168140,1030828,96964,1000106,1016287,1042659,1126786,7322,46550,29916,5668,5681,1041222,96016,1099712,65080,7240,92969,1053229,1126887,48687,50200,5848,41578,5874,5816,65081,1090999,1182448,1182449,1034235,3035609',
      minRequiredImages: 4,
      maxAllowedImages: 8,
    },
    'pdp-bricks-ca-br': {
      primaryCategoryId:
        '69883,67595,1107488,5030,87056,5032,99915,5013,28660,1016720,44873,75310,32643,35878,5389,1072457,70016,5170,24906,5176,15227,40926,1107608,1055063,5037,5191,80117,1020051,28660,44866,10894,1184521,5022,1192385',
      minRequiredImages: 4,
    },
    'pdp-bricks-ca-brfs': { primaryCategoryId: 'ALL', minRequiredImages: 2 },
    'pdp-bricks-ca-at': {
      primaryCategoryId:
        '1059471,1059479,86354,1177200,1011678,1005761,1011680,1011679,1032080,1009710,1177199,89745,1017102,1170247,1090333,1164127,1054836,1111614,1067955,1055327,1054835,1054837,46814,1108864,1108862,1071601,46881,1032096,1111336,1038916,1058485,1108900,1031353,46883,,46884,1013846,1001223,1046322,1059481,84136,1015838,1032089,84117,1026473,1026482,97463,1076564,1099237,60494,1054837,1054836,1054835,1111614,1067955,1145147,1055327,1006482,89745,46887,1025878,1017102,1059471,1059479,86354,1177200,1170247,1177199,1163347,1032080,97464,1011680,1011679,1009710,1005761,1011678,1107874',
      minRequiredImages: 4,
      maxAllowedImages: 8,
    },
    'pdp-bricks-us-gapfs': {
      primaryCategoryId:
        '1127842,1127845,1041618,1078016,1041614,1050905,1041624,1041631,1079858,1041644,1087749,1078016,1127846,1167896,1041765,1041793,1077052,1041801,1102167,1160218,1112404,1041780,1041786,1153734,1109191,1045955,1049821,1041731,1041723,1155954,1041712,1041713,1108148,1064873,1041732,1041733,1041735,1041808,1041809,1049816,1041824,1109945,1154639,1041815,1041816,1041823,1041825,1041826,1041827,1041874,1163780,1041913,1041922,1049477,1192444,1184742,1175527,1041929,1041930,1041941,1163785,1041952,1041949,1184744,1041955,1192445,1041961,1041962,1041878,1064172,1064160,1064671,1064652,1041945,1064168,1064665,1041963,1161315,1101251,1101254,1195907,1101246,1064649,1064664,1045907,1187299,1187321,1041699,1134698',
      minRequiredImages: 2,
      maxAllowedImages: 8,
      displayAllAvailableImages: true,
      displayMainFromOtherCC: '',
      displayOrderOnlySelectedCC:
        '1127842,1127845,1041618,1078016,1041614,1050905,1041624,1041631,1079858,1134698,1041644,1087749,1078016,1127846,1167896,1187299,1045907,1041765,1041793,1077052,1041801,1102167,1160218,1112404,1041780,1041786,1153734,1109191,1187321,1045955,1041699,1049821,1041731,1041723,1155954,1041712,1041713,1108148,1064873,1041732,1041733,1041735,1041808,1041809,1049816,1041824,1109945,1154639,1041815,1041816,1041823,1041825,1041826,1041827,1041874,1163780,1041913,1041922,1049477,1192444,1184742,1175527,1041929,1041930,1041941,1163785,1041952,1041949,1184744,1041955,1192445,1041961,1041962,1041878,1064172,1064160,1064671,1064652,1041945,1064168,1064665,1041963,1101257,1161315,1064672,1101251,1101254,1195907,1101246,1064649,1064664',
    },
    'pdp-bricks-us-brfs': {
      primaryCategoryId:
        '1105807,1045445,1091207,1045430,1091204,1045450,1045437,1068364,1088690,1115892,1045390,1045321,1045411,1045385,1045402,1045334,1105538,1045346,1045229,1091197,1045210,1091198,1045335,1045225',
      minRequiredImages: 4,
    },
    'bopis-us-on': { fulfillmentDisplay: true },
    'bopis-us-gap': { fulfillmentDisplay: true },
    'bopis-us-at': { fulfillmentDisplay: true },
    'bopis-us-br': { fulfillmentDisplay: true },
    'bopis-us-gapfs': { fulfillmentDisplay: true },
    'bopis-us-brfs': { fulfillmentDisplay: true },
    'bopis-ca-on': { fulfillmentDisplay: true },
    'bopis-ca-gap': { fulfillmentDisplay: true },
    'bopis-ca-br': { fulfillmentDisplay: true },
    'bopis-ca-at': { fulfillmentDisplay: true },
    'pdp-carousel-us-at': { imageReorder: { '8000': { main: 1, AV9: 3, AV5: 5 }, default: { main: 1, AV9: 3 } } },
    'pdp-afterpay': {
      'threshold-us-on': '35',
      'categories-us-on': 'ALL',
      'threshold-us-br': '35',
      'categories-us-br': 'ALL',
      'threshold-us-gap': '35',
      'categories-us-gap': 'ALL',
      'threshold-us-at': '35',
      'categories-us-at': 'ALL',
      'threshold-us-gapfs': '35',
      'categories-us-gapfs': 'ALL',
      'threshold-us-brfs': '35',
      'categories-us-brfs': 'ALL',
      'threshold-ca-on': '35',
      'categories-ca-on': 'ALL',
      'threshold-ca-gap': '35',
      'categories-ca-gap': 'ALL',
      'threshold-ca-br': '35',
      'categories-ca-br': 'ALL',
      'threshold-ca-at': '35',
      'categories-ca-at': 'ALL',
      'hide-us-on': false,
      'hide-us-gap': false,
      'hide-us-br': true,
      'hide-us-at': false,
      'hide-us-gapfs': false,
      'hide-us-brfs': true,
      'hide-ca-on': true,
      'hide-ca-gap': true,
      'hide-ca-br': true,
      'hide-ca-at': true,
      'threshold-ca-brfs': '35',
      'categories-ca-brfs': 'ALL',
      'hide-ca-brfs': true,
    },
    'pdp-free-shipping-bar-us-gap': { priceLimit: 50 },
    'pdp-free-shipping-bar-us-on': { priceLimit: 50 },
    'pdp-free-shipping-bar-us-br': { priceLimit: 50 },
    'pdp-free-shipping-bar-us-at': { priceLimit: 50 },
    'pdp-free-shipping-bar-us-gapfs': { priceLimit: 50 },
    'pdp-free-shipping-bar-us-brfs': { priceLimit: 50 },
    'pdp-free-shipping-bar-ca-gap': { priceLimit: 50 },
    'pdp-free-shipping-bar-ca-br': { priceLimit: 50 },
    'pdp-free-shipping-bar-ca-on': { priceLimit: 50 },
    'pdp-power-reviews': {
      'hidden-photogallery-us-on': false,
      'hidden-photogallery-us-gap': true,
      'hidden-photogallery-us-br': true,
      'hidden-photogallery-us-at': true,
      'hidden-photogallery-us-gapfs': true,
      'hidden-photogallery-us-brfs': true,
      'hidden-photogallery-ca-on': false,
      'hidden-photogallery-ca-gap': true,
      'hidden-photogallery-ca-br': true,
      'hidden-photogallery-ca-at': true,
      'hidden-photogallery-ca-brfs': true,
    },
    'pdp-sizeinclusivity': {
      imagesMapping: {
        on: {
          '8000': { smallest: ['AV4', 'AV5', 'AV2', 'AV3'], medium: ['main', 'AV1', 'AV2', 'AV3'], largest: ['AV6', 'AV7', 'AV2', 'AV3'] },
          default: { smallest: ['AV4', 'AV5', 'AV2', 'AV3'], medium: ['main', 'AV1', 'AV2', 'AV3'], largest: ['AV6', 'AV7', 'AV2', 'AV3'] },
        },
        gap: {
          '8000': { smallest: ['AV1', 'AV3', 'AV2', 'main'], medium: ['AV6', 'AV3', 'AV7', 'AV5'] },
          default: { smallest: ['main', 'AV1', 'AV2', 'AV3', 'AV4', 'AV5'], medium: ['AV3', 'AV4', 'main', 'AV1', 'AV2', 'AV5'] },
        },
        at: {
          '8000': { smallest: ['AV1', 'AV3', 'AV2', 'main'], medium: ['AV6', 'AV3', 'AV7', 'AV5'] },
          default: { smallest: ['main', 'AV1', 'AV2', 'AV5', 'AV6', 'AV7', 'AV8', 'AV9'], medium: ['AV3', 'AV4', 'AV5', 'AV6', 'AV7', 'AV8', 'AV9'] },
        },
        br: {
          '5030': { smallest: ['main', 'AV1', 'AV4', 'AV5'], medium: ['AV2', 'AV3', 'AV4', 'AV5'] },
          '5389': { smallest: ['main', 'AV1', 'AV4', 'AV5'], medium: ['AV2', 'AV3', 'AV4', 'AV5'] },
          '35878': { smallest: ['main', 'AV1', 'AV4', 'AV5'], medium: ['AV2', 'AV3', 'AV4', 'AV5'] },
          '67595': { smallest: ['main', 'AV1', 'AV4', 'AV5'], medium: ['AV2', 'AV3', 'AV4', 'AV5'] },
          '1072457': { smallest: ['main', 'AV1', 'AV4', 'AV5'], medium: ['AV2', 'AV3', 'AV4', 'AV5'] },
        },
      },
      primaryCategoryIds: { gap: '', on: '85729,5508,72808,50058,1182448,72091,68066,14675,20408,55474', br: '', at: '', brfs: '', gapfs: '' },
      defaultSize: { gap: 'S', on: 'M', br: 'M', at: 'S', gapfs: 'M', brfs: 'M' },
      certonaModelSizes: { gap: false, on: false, br: false, at: false, gapfs: false, brfs: false },
    },
    'pdp-anchor-marketingcontainer': {
      tid: 'onem,onps,onpl,onpn,ongons,onmsns,onysns,onaff,onpm,onsm,gpem,gpps,gppl,gppn,gogons,gomsns,goysns,goaff,gppm,gpsm,brem,brps,brpl,brpn,brgons,brmsns,braff,brpm,brsm,atem,atps,atpl,atpn,atgons,ataff,atpm,atsm,gfem,gfps,gfpl,gfpn,gfaf,gfpm,gfsm,bfem,bfps,bfpl,bfpn,bfaf,bfpm,bfsm,ocem,ocps,ocpl,ocpn,onafca,ocpm,ocsm,bcem,bcps,bcpl,bcpn,brafca,bcpm,gcem,gcps,gcpl,gcpn,goafca,gcpm,gcsm',
    },
    'pdp-size-sampling-ca-at': {
      primaryCategoryId:
        '1025878,1059481,1059471,1046322,1059479,86354,1177200,1032080,1005761,1011680,1163347,1011678,1011679,1066282,1070197,1009710,1177199,1117779,1038916,89745,1017102,1031353,1026473,1076564,1026482,97463,93635,1099237,1170247,1177199,1177200,1177201',
      minReviewThreshold: 0,
    },
    'pdp-size-sampling-ca-br': {
      primaryCategoryId:
        '5013,5022,5030,5032,5037,5389,9437,10894,28660,29818,32643,35878,44866,44873,67595,69883,75310,80117,99915,1016720,1020051,1032801,1051540,1055063,1071875,1072088,1072457,1093753,1117541,1158049,70016,5191,5176,1125239,24906,5170,1148867,40926,15227,70016,70020,1072088,5141',
      minReviewThreshold: 0,
    },
    'pdp-size-sampling-ca-gap': {
      primaryCategoryId:
        '5156,5168,5180,5225,5270,5664,5736,5745,6013,6049,6998,8655,11523,13658,15043,17076,29504,34524,34608,35300,72485,80799,83056,83072,83221,1005439,1006976,1008565,1011761,1014424,1014425,1015387,1016449,1019160,1020009,1021874,1023948,1025764,1027291,1041168,1041308,1050384,1056307,1066503,1082574,1103148,1106658,1120779,1137350,1137363,1152367,1154239,1167927,1167929,1169932',
      minReviewThreshold: 0,
    },
    'pdp-size-sampling-ca-on': { primaryCategoryId: '5199,5211,5475,15292,85729,1124176,1126951', minReviewThreshold: 0 },
    'pdp-size-sampling-us-at': {
      primaryCategoryId:
        '1025878,1059481,1059471,1046322,1059479,86354,1177200,1032080,1005761,1011680,1163347,1011678,1011679,1066282,1070197,1009710,1177199,1117779,1038916,89745,1017102,1031353,1026473,1076564,1026482,97463,93635,1099237,1170247,1177199,1177200,1177201',
      minReviewThreshold: 10,
    },
    'pdp-size-sampling-us-br': {
      primaryCategoryId:
        '5013,5022,5030,5032,5037,5389,9437,10894,28660,29818,32643,35878,44866,44873,67595,69883,75310,80117,99915,1016720,1020051,1032801,1051540,1055063,1071875,1072088,1072457,1093753,1117541,1158049,70016,5191,5176,1125239,24906,5170,1148867,40926,15227,70016,70020,1072088,5141',
      minReviewThreshold: 15,
    },
    'pdp-size-sampling-us-brfs': {
      primaryCategoryId:
        '1045225,1091674,1045210,1044980,1045335,1045229,1045227,1105538,1091197,1118443,1091198,1120277,1164874,1045346,1173318,1180093,1045437,1092353,1045430,1045450,1045445,1091204,1091207,1105807,1045441,1045462,1045328,1045326,1068364,1045321,1120181,1045334,1045402,1045411,1045323,1155546,1045390,1115892,1045385,1045416,1118436,1166239,1088690',
      minReviewThreshold: 10,
    },
    'pdp-size-sampling-us-gap': {
      primaryCategoryId:
        '5156,5168,5180,5225,5270,5664,5736,5745,6013,6049,6998,8655,11523,13658,15043,17076,29504,34524,34608,35300,72485,80799,83056,83072,83221,1005439,1006976,1008565,1011761,1014424,1014425,1015387,1016449,1019160,1020009,1021874,1023948,1025764,1027291,1041168,1041308,1050384,1056307,1066503,1082574,1103148,1106658,1120779,1137350,1137363,1152367,1154239,1167927,1167929,1169932',
      minReviewThreshold: 10,
    },
    'pdp-size-sampling-us-gapfs': {
      primaryCategoryId:
        '1127842,1127845,1041618,1041614,1127846,1167896,1041624,1041631,1078016,1041644,1079858,1050905,1087749,1045907,1041647,1041765,1162684,1160218,1102167,1041780,1041786,1041793,1077052,1041801,1112404,1167791,1181701,1109191,1041648,1153734,1045955',
      minReviewThreshold: 10,
    },
    'pdp-size-sampling-us-on': { primaryCategoryId: '1124176,5475,85729,15292,1126951,5211,5199', minReviewThreshold: 10 },
    'pdp-drapr-us-gap': {
      primaryCategoryId:
        '5664,1011761,1005439,1041308,1041168,1082574,34608,1006976,17076,5745,1117374,5736,29504,1152367,13658,1027291,6998,80799,15043,5225,5168,5180,1066503,5156,1120779,5270',
      isAutoSelectEnabled: true,
      isOOSErrorEnabled: true,
      isSizeLabelEnabled: true,
    },
    'pdp-drapr-us-on': {
      primaryCategoryId:
        '5199,1126951,1031103,38529,1010005,5249,1126985,1016048,63315,72305,1124176,35158,5475,5508,72808,50058,14675,85729,72091,1030828,5226,1053133,1035712,1174474,20408,5211,5271,55474,79586',
      sliderFeature: true,
    },
    'pdp-drapr-us-at': { primaryCategoryId: '1032080', isAutoSelectEnabled: false, isOOSErrorEnabled: false, isSizeLabelEnabled: false },
    'pdp-drapr-us-gapfs': {
      primaryCategoryId:
        '1127842,1127845,1041618,1127846,1041614,1050905,1041624,1167896,1041631,1079858,1078016,1041765,1102167,1160218,1112404,1041780,1041801,1041786,1167791,1041793,1077052,1109191,1153734,1181701',
      isAutoSelectEnabled: true,
      isOOSErrorEnabled: true,
      isSizeLabelEnabled: true,
    },
    'pdp-photos-stacked-layout-us-br': { maxQuantityPhotos: 8, topImagesLazy: false, bottomImagesLazy: false },
    'pdp-photos-stacked-layout-us-brfs': { maxQuantityPhotos: 8, topImagesLazy: false, bottomImagesLazy: false },
    'pdp-photos-stacked-layout-ca-br': { maxQuantityPhotos: 8, topImagesLazy: false, bottomImagesLazy: false },
    'pdp-photos-stacked-layout-ca-brfs': { maxQuantityPhotos: 8, topImagesLazy: false, bottomImagesLazy: false },
    'pdp-paypal': {
      'paypal-us-on': true,
      'paypal-us-at': true,
      'paypal-us-gapfs': true,
      'paypal-us-gap': true,
      'paypal-us-brfs': false,
      'paypal-us-br': false,
      'paypal-ca-on': false,
      'paypal-ca-at': false,
      'paypal-ca-gap': false,
      'paypal-ca-br': false,
      'paypal-ca-brfs': false,
      'threshold-us-on': '35',
      'threshold-us-br': '35',
      'threshold-us-gap': '35',
      'threshold-us-at': '35',
      'threshold-us-gapfs': '35',
      'threshold-us-brfs': '35',
      'threshold-ca-on': '35',
      'threshold-ca-gap': '35',
      'threshold-ca-br': '35',
      'threshold-ca-at': '35',
      'threshold-ca-brfs': '35',
    },
    'pdp-mfe-load-drapr-script-us-br': {
      url_3D: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
      url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
    },
    'pdp-mfe-load-drapr-script-us-brfs': {
      url_3D: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-outlet-lite-v2.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-outlet-lite-v2.prod.js',
      url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-outlet-lite-v2.prod.js',
    },
    'pdp-mfe-load-drapr-script-us-gap': {
      url_3D: 'https://draprpubsubtest.firebaseapp.com/integration/gap-lite-v9.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/gap-lite-v4.prod.js',
      url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/gap-lite-v9.prod.js',
    },
    'pdp-mfe-load-drapr-script-us-gapfs': {
      url_3D: 'https://draprpubsubtest.firebaseapp.com/integration/gap-outlet-lite-v9.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/gap-outlet-lite-v6.prod.js',
      url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/gap-outlet-lite-v9.prod.js',
    },
    'pdp-mfe-load-drapr-script-us-at': { url_3D: '', url_test: '', url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/gap-lite.dev.js' },
    'pdp-mfe-load-drapr-script-us-on': {
      url_3D: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-prod-v45.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-dev.js',
      url_fit: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-prod-v45.js',
    },
    'pdp-mfe-load-certona': {
      badges_at_girls: '228157,228767,41028,50780,138201',
      badges_standard:
        '56891,73473,134305,31876,114583,75004,41022,41024,39133,138201,41034,41036,222963,222965,222967,222969,222971,223367,223369,51450,138203,50444,48444,137825,50442,613131,613133,343243,644965,671444,671446,671448,671450,671452,671454,671456,671458,671460,671462,671464,671466,671468,671470,671472,671474,671476,671478,719399,719401,719403,850213,850215,850217,850219,850221,414211,414229,414231,414233,414213,414215,414217,414219,414221,414223,414225,414227,414235,1263970,1263972,1263974,41028,41026,245488,245484,245480,245092,245486,243942',
      pollCertonaSegment: true,
    },
    'pdp-dropship-us-br': {
      showAgreeToAtbModal: true,
      showQuickAddModal: false,
      showSizeGuide: false,
      showFindMine: false,
      showDrapr: false,
      showTrueFit: false,
      showOtherPaymentMethods: false,
      showBOPIS: false,
    },
    'pdp-dropship-us-gap': {
      showAgreeToAtbModal: true,
      showQuickAddModal: true,
      showSizeGuide: false,
      showFindMine: false,
      showDrapr: false,
      showTrueFit: false,
      showOtherPaymentMethods: false,
      showBOPIS: false,
    },
    'pdp-fulfillment-selection-v2': {
      'gap-ca': true,
      'on-us': true,
      'brfs-us': true,
      'at-us': true,
      'br-ca': true,
      'gap-us': true,
      'at-ca': true,
      'on-ca': true,
      'gapfs-us': true,
      'br-us': true,
      'brfs-ca': true,
    },
    'pdp-pmcs-us-on': { container: 'pdp/banner1' },
    'pdp-pmcs-us-at': { container: 'pdp/banner1' },
    'pdp-chas': { showReviews: false },
    'pdp-drapr-analytics-script-us-gap': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/gap-ao-dev.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/gap-ao-dev.js',
    },
    'pdp-drapr-analytics-script-us-gapfs': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/gap-outlet-ao-dev.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/gap-outlet-ao-dev.js',
    },
    'pdp-drapr-analytics-script-ca-gap': {
      url_analytics: ' https://draprpubsubtest.firebaseapp.com/integration/gap-lite-v4.prod.js',
      url_test: ' https://draprpubsubtest.firebaseapp.com/integration/gap-lite-v4.prod.js',
    },
    'pdp-drapr-analytics-script-us-br': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
    },
    'pdp-drapr-analytics-script-us-brfs': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-outlet-lite-v2.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-outlet-lite-v2.prod.js',
    },
    'pdp-drapr-analytics-script-ca-br': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/banana-republic-lite-v2.prod.js',
    },
    'pdp-drapr-analytics-script-us-at': { url_analytics: '', url_test: '' },
    'pdp-drapr-analytics-script-ca-at': { url_analytics: '', url_test: '' },
    'pdp-drapr-analytics-script-us-on': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-ao-dev.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-prod-v47.js',
    },
    'pdp-drapr-analytics-script-ca-on': {
      url_analytics: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-prod-v36.js',
      url_test: 'https://draprpubsubtest.firebaseapp.com/integration/oldnavy-prod-v36.js',
    },
    'pdp-lpo-certona-us-at': { TID: 'lpotest, lpotest2, atpl, atsm' },
    'pdp-lpo-plp-us-at': { TID: 'lpohybridtest,lpohybridtest1' },
    'pdp-pixleeegc-widget': { showPixleeCategoryWidget: false },
    'pdp-percentage-off-us-on': { useFromCapi: true },
    'pdp-percentage-off-ca-on': { useFromCapi: true },
    'pdp-percentage-off-us-brfs': { useFromCapi: true },
    'pdp-percentage-off-ca-brfs': { useFromCapi: true },
    'pdp-percentage-off-us-gapfs': { useFromCapi: true },
    'pdp-ai-reviews-us-on': { default: 'default', test1: 'T1', test2: 'T2' },
    'pdp-ai-reviews-us-at': { default: 'default', test1: 'treatment1', test2: 'treatment2' },
    'swf-2024-header-redesign-gap': {
      us: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
      ca: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
    },
    'swf-2024-header-redesign-on': {
      us: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
      ca: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
    },
    'swf-2024-header-redesign-br': {
      us: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
      ca: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
    },
    'swf-2024-header-redesign-at': {
      us: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
      ca: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
    },
    'swf-2024-header-redesign-brfs': {
      us: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
      ca: { useNewRewardsLanguage: true, useNewMembershipStatus: true, useNewAccountHeaderIcon: true, useNewEdfsSignIn: true },
    },
    'checkout-ui-afterpay': { maxThreshold: 1000, minThreshold: 35 },
    'bag-ui-afterpay': { maxThreshold: 1000, minThreshold: 35 },
    'checkout-ui-guest-free-shipping-banner': { maxThreshold: 50 },
    'swf-beacon-on': { animationDuration: '1.6s', animationIterationCount: 'Infinite', minimumNumberOfItems: 1 },
    'swf-beacon-brfs': { animationDuration: '1.6s', animationIterationCount: 'Infinite', minimumNumberOfItems: 1 },
  },
  abSeg: {},
};
