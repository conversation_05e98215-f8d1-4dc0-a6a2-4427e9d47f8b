// @ts-nocheck
// This check is due to legacy ZoomMediator components usage, we can remove it as we get rid of legacy dependency
import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { ScriptLoaderProvider } from '@pdp/packages/script-loader-provider';
import { ABSegProvider } from '@pdp/src/app/components/abseg-provider';
import classNames from 'classnames';
import { Breakpoint, XLARGE } from '@ecom-next/core/components/breakpoint-provider';
import type { AppWrapperApps } from '../Product';
import { BuyBoxServer } from '../../components/buy-box/buy-box-wrapper/buy-box-server';
import { getUrlParamsString } from '../helpers';
import { PhotoBricksLegacy, PhotoCarouselLegacy, SingleColumnLayoutLegacy } from '../../components/product-images';
import { getBricksFeatures } from '../../components/product-images/utils';
import { getPageContextData } from '../getPageState';
import { FindMineLegacy } from '../../components/find-mine/FindMineClient';
import { ProductRecommendationsLegacy } from '../../components/product-recommendations/ProductRecommendationsLegacy';
import { Reviews } from '../../components/reviews/Reviews';
import { cacheableCapiDataPromise, cacheableEnabledFeaturesPromise, cacheableAppConfigDataPromise, cacheableFeaturesConfig } from '../getReWrittenData';
import SocialGalleryLegacy from '../../components/social-gallery/SocialGallery';
import { ProductTitle } from '../../components/product-title/ProductTitle';
import { ImageGallery } from '../../components/image-gallery/ImageGallery';
import { PDPRecsCarouselWrapper } from '../../components/pdp-recs-carousel-wrapper';
import ZoomMediator from '../../legacy/src/app/components/photo-carousel/components/zoom-mediator';
import { MarketingFlagClient } from '../../components/buy-box/components/marketing-flag/MarketingFlagClient';

export const PDPTemplate = async ({
  searchParams,
  crosslinks,
  params,
  brandCode,
  brandInformation,
  productMarketing,
  breadcrumbs,
}: PageParams & AppWrapperApps) => {
  const requestParamString = getUrlParamsString(searchParams);
  const { brand, market, isDesktop, locale } = getPageContextData();
  const [capiData, { enabledFeatures, featureVariables, abSeg }, { appConfig }] = await Promise.all([
    cacheableCapiDataPromise(requestParamString),
    cacheableEnabledFeaturesPromise(searchParams),
    cacheableAppConfigDataPromise(searchParams),
  ]);
  const featureConfig = await cacheableFeaturesConfig(searchParams);

  const {
    findmine: { isFindmineEnabled },
    reviews: { showReviews },
    bricks: { isBricksEnabled },
    imageGalleryConfig: { pdpVideoRepeated, redesignedImageGalleryEnabled },
    recommendationsConfig,
    isPercentageEnabled,
    marketingContainerPlacement,
    marketingContainerEnabled,
    bottomBreadcrumbsEnabled,
  } = featureConfig;
  const showBricks = isDesktop && isBricksEnabled;
  const { maxQuantityPhotos } = featureVariables[`pdp-photos-stacked-layout-${market}-${brand}`] || {};
  const { productData } = capiData;
  const isBrBrands = ['br', 'brfs'].includes(brand);

  const LegacyImageComponents = () =>
    isBrBrands && isDesktop ? (
      <SingleColumnLayoutLegacy maxQuantityPhotos={maxQuantityPhotos} />
    ) : showBricks ? (
      <PhotoBricksLegacy
        data={capiData.productData}
        brandName={brand}
        features={getBricksFeatures({ enabledFeatures, featureVariables, market, brand })}
        pdpVideoRepeated={pdpVideoRepeated}
      />
    ) : (
      <PhotoCarouselLegacy brandName={brand} />
    );

  const breadcrumbClasses = `${isDesktop ? 'desktop-breadcrumbs' : `bg-white ${!bottomBreadcrumbsEnabled && 'pr-4 pt-4'}`} ${brand === 'brfs' ? 'brfs-breadcrumbs' : ''}`;

  const gridClasses = classNames('grid', {
    'bg-white grid-cols-1': !isDesktop,
    'single-column-layout-grid': isDesktop && ['br', 'brfs'].includes(brand),
    'bricks-layout-grid': isDesktop && isBricksEnabled,
    'thumbnail-layout-grid': isDesktop && !isBricksEnabled && !['br', 'brfs'].includes(brand),
  });

  const containerPositions = { higher: 'a', lower: 'b' };

  const renderDesktopMarketingContainer = (position: string) => {
    if (!marketingContainerEnabled || marketingContainerPlacement.desktop !== position) return null;

    return (
      <Breakpoint is='greaterOrEqualTo' size={XLARGE}>
        {productMarketing}
      </Breakpoint>
    );
  };

  return (
    <>
      <ScriptLoaderProvider>
        <ZoomMediator>
          {!bottomBreadcrumbsEnabled && <div className={breadcrumbClasses}>{breadcrumbs}</div>}
          <div className={gridClasses} id='gallery-wrapper'>
            <div className='m-4 block sm:hidden'>
              <ProductTitle enabledFeatures={enabledFeatures} />
              <MarketingFlagClient isStyleLevelFlag />
            </div>
            {!redesignedImageGalleryEnabled ? <LegacyImageComponents /> : <ImageGallery searchParams={searchParams} />}

            <BuyBoxServer
              crosslinks={crosslinks}
              searchParams={searchParams}
              params={params}
              brandCode={brandCode}
              brandInformation={brandInformation}
              productMarketing={productMarketing}
              recommendationsConfig={recommendationsConfig}
            />
          </div>

          <ABSegProvider abSeg={abSeg} brandName={brand}>
            <SocialGalleryLegacy searchParams={searchParams} />

            {/* Above Findmine */}
            {renderDesktopMarketingContainer(containerPositions.higher)}

            {isFindmineEnabled && <FindMineLegacy brand={brand} />}

            {/* Below Findmine */}
            {renderDesktopMarketingContainer(containerPositions.lower)}

            {searchParams?.carousel === 'legacy' ? (
              <ProductRecommendationsLegacy
                brand={brand}
                locale={locale}
                market={market}
                abSeg={abSeg}
                brandCodeUrls={appConfig.brandCodeUrls}
                actionName='certonaRec'
                clientId='PDPQuickAddCertona'
                pagetype='PRODUCT'
              />
            ) : (
              <PDPRecsCarouselWrapper
                isPercentageEnabled={isPercentageEnabled}
                recommendationsConfig={recommendationsConfig}
                reportCertonaLoad={true}
                selectedCustomerChoiceId={productData?.selectedColor?.businessCatalogItemId}
              />
            )}
          </ABSegProvider>
          {showReviews && <Reviews searchParams={searchParams} />}
          {crosslinks}
          {bottomBreadcrumbsEnabled && <div className={breadcrumbClasses}>{breadcrumbs}</div>}
        </ZoomMediator>
      </ScriptLoaderProvider>
    </>
  );
};
