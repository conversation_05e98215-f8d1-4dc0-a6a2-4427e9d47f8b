import { Converter } from 'showdown';

import type { CopyHeader, Locale } from '../../../types';
import type { ModelSizes } from './model-sizes';
import { createModelSizes, removeModelSizes } from './model-sizes';

type InfoTab = {
  bulletAttributes: string[];
  copyAttributes?: string[];
  infoTabName: string;
  modelSizes?: ModelSizes[][];
  notes?: string[];
};

const splitCopyAttributes = (copyAttributes: string): string[] => {
  let copyAttributesArray = [copyAttributes];

  if (copyAttributes.match(/##/) && copyAttributes.match(/\w/)) {
    const indexOfHash = copyAttributes.indexOf('##');
    const firstString = copyAttributes.slice(0, indexOfHash);
    const secondString = copyAttributes.slice(indexOfHash);
    copyAttributesArray = [firstString, secondString];
  }
  return copyAttributesArray;
};

const createBulletAttributes = (bullets: string[]): string[] => {
  return bullets.map(removeModelSizes).filter(bullet => bullet.length > 0);
};

const createCopyAttributes = (description: string | null): string[] => {
  return description ? splitCopyAttributes(description) : [];
};

const createNotes = (description: string | null, styleId: string): string[] => {
  if (description) {
    const converter = new Converter();
    converter.setOption('noHeaderId', 'false');

    const splitString = splitCopyAttributes(description);
    const notesHtml = splitString[1] ? converter.makeHtml(`${splitString[1]}\n- #${styleId}`) : '';
    if (notesHtml.length > 0) {
      return [removeModelSizes(notesHtml)];
    }
  }
  return [];
};

export const createInfoTab = ({ id, label, description, bullets }: CopyHeader, styleId: string, locale: Locale): InfoTab => {
  const infoTab = {
    bulletAttributes: createBulletAttributes(bullets),
    infoTabName: label,
  } as InfoTab;

  if (id !== 'fabric') {
    infoTab.copyAttributes = createCopyAttributes(description);
    infoTab.notes = createNotes(description, styleId);
  }

  if (id === 'overview') {
    infoTab.modelSizes = createModelSizes(bullets, locale);
  }

  return infoTab;
};
