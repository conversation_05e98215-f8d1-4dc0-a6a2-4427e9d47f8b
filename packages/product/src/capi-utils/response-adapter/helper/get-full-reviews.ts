import { ReviewHistogram } from '../../../pages/services/reviews';
import type { VariantRaw } from '../../types';

export const getFullReviews = ({ rating }: VariantRaw): ReviewHistogram | null => {
  if (!rating) {
    return null;
  }
  const { totalReviews, averageRating, fiveStarRatings, fourStarRatings, threeStarRatings, twoStarRatings, oneStarRatings } = rating;
  return {
    average_rating: averageRating,
    ratingHistogram: [oneStarRatings, twoStarRatings, threeStarRatings, fourStarRatings, fiveStarRatings],
    rating_count: totalReviews,
    reviewHistogram: [oneStarRatings, twoStarRatings, threeStarRatings, fourStarRatings, fiveStarRatings],
    review_count: totalReviews,
  };
};
