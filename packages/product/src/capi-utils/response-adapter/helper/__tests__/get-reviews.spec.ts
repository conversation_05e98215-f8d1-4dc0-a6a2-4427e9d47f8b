import type { VariantRaw } from '../../../types';
import { getReviews } from '../get-reviews';

const variant = {
  rating: {
    averageRating: 3.67,
    fiveStarRatings: 5,
    fourStarRatings: 4,
    oneStarRatings: 1,
    threeStarRatings: 3,
    totalReviews: 15,
    twoStarRatings: 2,
  },
} as VariantRaw;

describe('GIVEN getReviews', () => {
  describe('WHEN receives a valid rating object', () => {
    test('THEN should return reviewCount with the same value as rating.totalReviews', () => {
      const reviews = getReviews(variant);
      expect(reviews.reviewCount).toBe(15);
    });

    test('THEN should return starRating with the same value as rating.averageRating', () => {
      const reviews = getReviews(variant);
      expect(reviews.starRating).toBe(3.67);
    });
  });

  describe('WHEN receives no values for totalReviews or averageRating', () => {
    test('THEN should return an empty object', () => {
      const noRatingVariant = {} as VariantRaw;
      const reviews = getReviews(noRatingVariant);
      expect(reviews).toEqual({});
    });
  });
});
