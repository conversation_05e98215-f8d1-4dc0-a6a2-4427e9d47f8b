import { render } from '@testing-library/react';
import fetchMock from 'fetch-mock';
import React from 'react';

import { stores } from '../data/stores.mock';
import { useSkuAvailabilityQuery } from '../use-sku-availability-query';

type SkuAvailabilityQuery = (storeIds: string[], pid: string) => Promise<Response>;

const responseOk = {
  status: 200,
  statusText: 'OK',
  text: 'return this string',
};
const storeIds = stores.map(store => store.storeId);
const pid = '5775390220003';
const oidcUrl = 'https://secure-internal-azeus-ecom-api.live.stage.factory-gaptechol.com';
const chasUrl = `${oidcUrl}/commerce/catalog/inventory/v1/sku/store/status?channel=ONL&market=US&brand=GAP&storeIds=${storeIds}&skuIds=${pid}`;

function setup(): SkuAvailabilityQuery | null {
  let returnVal = null;
  function TestComponent() {
    returnVal = useSkuAvailabilityQuery({
      brand: 'gap',
      clientId: 'product-page',
      market: 'us',
      oidcUrl,
    });
    return null;
  }

  render(<TestComponent />);

  return returnVal;
}

describe('useSkuAvailabilityQuery', () => {
  afterEach(() => fetchMock.reset());

  test('calls the correct URL', async () => {
    fetchMock.get('*', responseOk);
    const skuAvailabilityQuery = setup() as SkuAvailabilityQuery;
    await skuAvailabilityQuery(storeIds, pid);
    expect(fetchMock.calls()[0][0]).toBe(chasUrl);
    expect(fetchMock.called(chasUrl)).toBeTruthy();
  });

  test('returns the test response', async () => {
    fetchMock.get('*', responseOk);
    const skuAvailabilityQuery = setup();

    const response = skuAvailabilityQuery && (await skuAvailabilityQuery(storeIds, pid));
    expect(response?.text).toStrictEqual(responseOk.text);
  });

  test('returns empty response when there is no text in service response', async () => {
    fetchMock.get('*', 200);
    const skuAvailabilityQuery = setup() as SkuAvailabilityQuery;
    const response = await skuAvailabilityQuery(storeIds, pid);
    expect(response).toStrictEqual({});
  });

  test('response not ok should throw error message', async () => {
    jest.spyOn(console, 'error').mockImplementation();
    fetchMock.get('*', 400);
    const skuAvailabilityQuery = setup() as SkuAvailabilityQuery;
    await skuAvailabilityQuery(storeIds, pid);
    // eslint-disable-next-line no-console
    expect(console.error).toHaveBeenCalled();
  });
});
