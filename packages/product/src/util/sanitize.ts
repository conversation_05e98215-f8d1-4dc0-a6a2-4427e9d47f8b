import { encode } from 'html-entities';
import logger from '@ecom-next/app/logger';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Params = Record<any, any>;

export function sanitizeQuery(queryParams: Params = {}): Params {
  return Object.entries(queryParams).reduce((sanitized, [key, val]) => {
    try {
      if (key.toLowerCase() === 'di') {
        return {
          ...sanitized,
          di: encode(val),
        };
      }

      if (typeof val === 'string') {
        return {
          ...sanitized,
          [key]: encode(val),
        };
      }

      if (Array.isArray(val)) {
        return {
          ...sanitized,
          [key]: (val as string[]).map(v => encode(v)),
        };
      }

      return {
        ...sanitized,
        [key]: sanitizeQuery(val as Params),
      };
    } catch (error) {
      logger.error('error sanitizing query params', error);
      return {
        ...sanitized,
        [key]: val,
      };
    }
  }, {});
}
