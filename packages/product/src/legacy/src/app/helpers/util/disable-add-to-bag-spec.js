// @ts-nocheck
import shouldDisableAddToBag from './disable-add-to-bag';

describe('shouldDisableAddToBag', () => {
  const defaultDimension = {
    dimensionGroupId: 'sizeDimension1',
    dimensions: [
      {
        bopisInStock: false,
        inStock: true,
        name: 'S',
      },
    ],
    label: 'Size',
    selectedDimension: 'S',
  };

  describe('when shipping is available', () => {
    const isShippingAvailable = true;

    test('does not disable AddToBag when dimensions are valid', () => {
      // Arrange
      const colorsWithoutSize = {};
      const dimensions = [defaultDimension];
      const bopisAvailable = false; // Not relevant
      const bopisActive = false; // Not relevant
      const isValidSizeSelection = false;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeFalsy();
    });

    test('disables AddToBag when dimensions are invalid', () => {
      // Arrange
      const colorsWithoutSize = {};
      const dimensions = [];
      const bopisAvailable = false; // Not relevant
      const bopisActive = false; // Not relevant
      const isValidSizeSelection = false;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeTruthy();
    });
  });

  describe('when shipping is not available', () => {
    const isShippingAvailable = false;
    const bopisExclusionStyle = false;

    test('does not disable AddToBag when dimensions are valid', () => {
      // Arrange
      const colorsWithoutSize = {};
      const dimensions = [defaultDimension];
      const bopisAvailable = true;
      const bopisActive = true;
      const isValidSizeSelection = false;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisExclusionStyle,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeFalsy();
    });

    test('disables AddToBag when dimensions are invalid', () => {
      // Arrange
      const colorsWithoutSize = {};
      const dimensions = [];
      const bopisAvailable = true;
      const bopisActive = true;
      const isValidSizeSelection = false;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisExclusionStyle,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeTruthy();
    });

    test('disables AddToBag when bopis is not available', () => {
      // Arrange
      const colorsWithoutSize = {}; // Not relevant
      const dimensions = [defaultDimension]; // Not relevant
      const bopisAvailable = false;
      const bopisActive = false; // Not relevant
      const isValidSizeSelection = true;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisExclusionStyle,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeTruthy();
    });

    test('disables AddToBag when bopis is available AND not active', () => {
      // Arrange
      const colorsWithoutSize = {}; // Not relevant
      const dimensions = [defaultDimension]; // Not relevant
      const bopisAvailable = true;
      const bopisActive = false;
      const isValidSizeSelection = true;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisExclusionStyle,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeTruthy();
    });

    test('does not disable AddToBag when bopis is unavailable AND active AND product is excluded from bopis', () => {
      // Arrange
      const colorsWithoutSize = {}; // Not relevant
      const dimensions = [defaultDimension];
      const isShippingAvailable = true;
      const bopisAvailable = false;
      const bopisExclusionStyle = true;
      const bopisActive = true;
      const isValidSizeSelection = true;

      // Act
      const actual = shouldDisableAddToBag(
        colorsWithoutSize,
        dimensions,
        isShippingAvailable,
        bopisAvailable,
        bopisExclusionStyle,
        bopisActive,
        isValidSizeSelection
      );

      // Assert
      expect(actual).toBeFalsy();
    });
  });
});
