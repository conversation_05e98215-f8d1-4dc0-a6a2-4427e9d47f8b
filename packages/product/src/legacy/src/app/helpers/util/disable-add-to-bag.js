// @ts-nocheck
'use client'

import { filterSelectedDimensions, hasDimensions } from '@product-page/legacy/product-selection';

/**
 * Checks whether product is out of stock or back ordered
 * @param {Object} color // https://github.gapinc.com/pages/ecomfrontend/gap-future/patterns/pdp-api-data.html#variants
 * @param {Array} dimensions - Array of Objects defining the dimensions state
 * @returns {Boolean} - true when size is out of stock or back ordered, false otherwise
 */
export function isOutOfStockOrBackOrdered(color, dimensions) {
  const selectedDimensions = filterSelectedDimensions(dimensions);

  // Returns false when no dimension is selected
  if (selectedDimensions.length === 0) {
    return false;
  }

  // Returns false when all dimentions are in stock
  if (selectedDimensions.every(dim => dim.inStock || dim.bopisInStock)) {
    return false;
  }
  const { sizes = [] } = color;

  // Returns true when a product sku does not have selected dimensions, false otherwise
  const selectedCombination = sizes.find(colorSKU => hasDimensions(colorSKU, selectedDimensions));
  if (selectedCombination) {
    return !selectedCombination.inStock && !selectedCombination.bopisInStock;
  }

  // Falls back to out of stock or back ordered
  return true;
}

/**
 * Returns a boolean used to disable the AddToBag button when either
 * 1. Shipping and bopis are not available
 * 2. Shipping is not available AND bopis is available AND bopis is not active OR size selection is not valid
 * 3. Selected color and size combination is out of stock or backordered
 * 4. Dimensions array is empty or doesn't exist
 * @param {Object} color // https://github.gapinc.com/pages/ecomfrontend/gap-future/patterns/pdp-api-data.html#variants
 * @param {Array} dimensions Array of Objects defining the dimensions state
 * @param {Boolean} isShippingAvailable The shipping fulfillment method is available
 * @param {Boolean} bopisAvailable Bopis is available
 * @param {Boolean} bopisExclusionStyle Bopis is not on the page
 * @param {Boolean} bopisActive Bopis is active
 * @param {Boolean} isValidSizeSelection Size selection is valid
 */
function shouldDisableAddToBag(
  color,
  dimensions,
  isShippingAvailable,
  bopisAvailable,
  bopisExclusionStyle,
  bopisActive,
  isValidSizeSelection
) {
  return (
    (isValidSizeSelection && !isShippingAvailable && !bopisAvailable) ||
    (!isShippingAvailable && bopisAvailable && !bopisActive) ||
    (isShippingAvailable && !bopisAvailable && bopisActive && !bopisExclusionStyle) ||
    isOutOfStockOrBackOrdered(color, dimensions) ||
    !(dimensions && dimensions.length > 0)
  );
}

export default shouldDisableAddToBag;
