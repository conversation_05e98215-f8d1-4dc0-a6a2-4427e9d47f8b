// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";

import { badgesAtGirls, badgesStandard, tealiumBadges } from './badges-mock';
import { getCertonaConfiguration } from './get-certona-configuration';

describe('#getCertonaConfiguration', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const config = {
    brandName: Brands.Gap,
    locale: 'en_US' as Locale,
    pid: '123456',
  };

  test('should return base config', () => {
    expect(getCertonaConfiguration(config)).toStrictEqual({
      atGirlsBadges: false,
      badges: [],
      itemid: '123456',
      languagecode: 'en_US',
      purchased_sku: {},
      recommendations: true,
      site: 'GAP',
    });
  });

  test('should handle undefined parameters', () => {
    const errorMessage = 'brand, itemId and locale cannot be empty';

    expect(() => getCertonaConfiguration({ ...config, brandName: undefined })).toThrow(errorMessage);
    expect(() => getCertonaConfiguration({ ...config, pid: undefined })).toThrow(errorMessage);
    expect(() => getCertonaConfiguration({ ...config, locale: undefined })).toThrow(errorMessage);
  });

  describe('Tealium badges verification', () => {
    beforeEach(() => {
      Storage.prototype.getItem = () => tealiumBadges;
    });

    test('should return badges that are present in standard badges list', () => {
      const expectedBadges = ['56891', '134305', '223367', '1263970', '245488'];

      const configWithBadges = {
        ...config,
        badgesAtGirls,
        badgesStandard,
      };

      const { badges } = getCertonaConfiguration(configWithBadges);

      expect(badges).toEqual(expectedBadges);
    });

    test.skip('should return badges that are present in standard badges and AT girls badges lists when brand is AT', () => {
      const expectedBadges = ['56891', '134305', '223367', '1263970', '245488', '228157'];

      const configWithBadges = {
        ...config,
        badgesAtGirls,
        badgesStandard,
        brandName: Brands.Athleta,
      };

      const { badges } = getCertonaConfiguration(configWithBadges);

      expect(badges).toEqual(expectedBadges);
    });
  });
});
