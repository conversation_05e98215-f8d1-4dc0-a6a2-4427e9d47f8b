// @ts-nocheck
import { Brands } from '@ecom-next/core/legacy/utility';
import TestApp, { defaultBrandMarketData } from '@pdp/spec/test-app';
import { render } from '@testing-library/react';
import React from 'react';

import type { FeatureFlag } from '../constants/feature-flags';
import { RenderFor } from './render-for';

const testApp = (
  Component: JSX.Element,
  { enabledFeatures = {}, featureVariables = {}, market = 'us', brand = Brands.Gap, abSegValue }: any
) => {
  const defaultProps = {
    abSegValue,
    brandMarketCtxValue: {
      ...defaultBrandMarketData,
      brandName: brand ?? Brands.Gap,
      market,
    },
    brandName: brand ?? Brands.Gap,
    featureFlagsCtxValue: {
      enabledFeatures,
      featureVariables,
    },
  };

  return render(<TestApp {...defaultProps}>{Component}</TestApp>);
};

const renderedText = 'Some Component';
const ChildComponent = () => <div>{renderedText}</div>;

describe('<RenderFor/>', () => {
  test('renders child component if no props are passed', () => {
    const { getByText } = testApp(
      <RenderFor>
        <ChildComponent />
      </RenderFor>,
      {}
    );
    expect(getByText(renderedText)).toBeInTheDocument();
  });

  describe('Rendering for Brands', () => {
    test('renders child if brand matches current brand', () => {
      const { getByText } = testApp(
        <RenderFor brands={[Brands.Gap]}>
          <ChildComponent />
        </RenderFor>,
        { brand: Brands.Gap }
      );
      expect(getByText(renderedText)).toBeInTheDocument();
    });

    test("doesn't render child if brand doesn't match current brand", () => {
      const { queryByText } = testApp(
        <RenderFor brands={[Brands.Gap]}>
          <ChildComponent />
        </RenderFor>,
        { brand: Brands.OldNavy }
      );
      expect(queryByText(renderedText)).not.toBeInTheDocument();
    });
  });

  describe('when property', () => {
    test('renders child if when is true', () => {
      const { queryByText } = testApp(
        <RenderFor when>
          <ChildComponent />
        </RenderFor>,
        {}
      );
      expect(queryByText(renderedText)).toBeInTheDocument();
    });

    test("doesn't render child if when is false", () => {
      const { queryByText } = testApp(
        <RenderFor when={false}>
          <ChildComponent />
        </RenderFor>,
        {}
      );
      expect(queryByText(renderedText)).not.toBeInTheDocument();
    });
  });

  describe('Rendering for Feature Flags', () => {
    const feature: FeatureFlag = {
      isBrandAgnostic: true,
      key: 'pdp-test-feature',
    };

    test('renders child if feature flag is enabled', () => {
      const { queryByText } = testApp(
        <RenderFor feature={feature}>
          <ChildComponent />
        </RenderFor>,
        { enabledFeatures: { 'pdp-test-feature': true } }
      );
      expect(queryByText(renderedText)).toBeInTheDocument();
    });

    test("doesn't render child if feature flag is disabled", () => {
      const { queryByText } = testApp(
        <RenderFor feature={feature}>
          <ChildComponent />
        </RenderFor>,
        { enabledFeatures: { 'pdp-test-feature': false } }
      );
      expect(queryByText(renderedText)).not.toBeInTheDocument();
    });

    describe('Rendering for Feature Variables', () => {
      afterEach(() => {
        jest.clearAllMocks();
      });

      test('renders child if feature flag and variable are enabled', () => {
        feature.featureVariables = {
          showFeature: { name: 'showFeature' },
        };

        const { queryByText } = testApp(
          <RenderFor feature={feature} featureVariable={feature.featureVariables!.showFeature}>
            <ChildComponent />
          </RenderFor>,
          {
            enabledFeatures: { 'pdp-test-feature': true },
            featureVariables: {
              'pdp-test-feature': { showFeature: true },
            },
          }
        );
        expect(queryByText(renderedText)).toBeInTheDocument();
      });

      test("doesn't render child if feature flag is on and feature variable is off", () => {
        feature.featureVariables = {
          showFeature: { name: 'showFeature' },
        };

        const { queryByText } = testApp(
          <RenderFor feature={feature} featureVariable={feature.featureVariables!.showFeature}>
            <ChildComponent />
          </RenderFor>,
          {
            enabledFeatures: { 'pdp-test-feature': true },
            featureVariables: {
              'pdp-test-feature': { showFeature: false },
            },
          }
        );
        expect(queryByText(renderedText)).not.toBeInTheDocument();
      });
    });
  });
});
