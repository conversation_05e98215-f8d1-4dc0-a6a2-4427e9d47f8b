// @ts-nocheck
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { storageHelper } from '@ecom-next/core/legacy/utility';
import { isTouchDevice } from '@pdp/packages/helpers/util/is-touch-device';
import { useContext } from 'react';

import type { PageState, PageStateProductPage } from '../../../types/page-state';
import { useSegment } from '../components/abseg-provider';
import { useDrapr } from '../components/drapr/hooks/use-drapr';
import { useDraprWidget } from '../components/drapr/hooks/use-drapr-widget';
import { useFindmineFeature } from '../components/find-mine/colaborators/use-findmine-feature';
import showBrickLayout from '../components/product-page/collaborators/show-brick-layout';
import { useDraprAnalyticsScriptLoader } from './use-drapr-analytics-script-loader';
import useDropShipData from './use-drop-ship-data';
import {
  useBrandAgnosticFeatureFlag,
  useBrandAgnosticFeatureVariables,
  useFeatureFlag,
  useFeatureVariables,
  useMultiBrandFeatureVariables,
} from './use-feature-flag';
import { useImageScaling } from './use-image-scaling-feature';
import { useReviewSummary } from './use-review-summary';
import { useReviewsDrawer } from './use-reviews-drawer';

function useDatalayerEngagement(pid: string) {
  if (typeof window !== 'undefined' && localStorage) {
    const { getItem, removeItem } = storageHelper(localStorage);
    const dataLayerEngagementObject = `plpDatalayerReportObject-${pid}`;
    const value = JSON.parse(getItem(dataLayerEngagementObject) as string);
    removeItem(dataLayerEngagementObject);
    return value;
  }

  return null;
}

export function useProductPageWithFlags(props: PageState): PageStateProductPage {
  const bricksFlag = useFeatureFlag('pdp-bricks');
  const { primaryCategoryId: bricksIds = '', minRequiredImages } = useFeatureVariables('pdp-bricks');
  const { productData, brandName, shippingAndReturnsJSON, params } = props;
  const { primaryCategoryId: productPrimaryCategoryId } = productData;
  const { size: breakPointSize = 'large' } = useContext(BreakpointContext);
  const bricksCondition = bricksFlag && bricksIds !== '' && (bricksIds.split(',').includes(productPrimaryCategoryId) || bricksIds === 'ALL');

  const bricksEnabled = showBrickLayout(bricksCondition, minRequiredImages, productData);

  const { draprEnabled } = useDrapr();
  const { isFitOnly, is3D: is3DDraprVersion } = useDraprWidget();
  const hideShippingEnabled = useFeatureFlag('pdp-hide-shipping');
  const pdpInterlinksEnabled = useBrandAgnosticFeatureFlag('adeptmind-crosslinks-pdp');
  const tertiaryFontEnabled = useBrandAgnosticFeatureFlag(`${brandName}-tertiary-font`);
  const bottomBreadcrumbsEnabled = useBrandAgnosticFeatureFlag('bottom-product-breadcrumbs');
  const ratingsSegment = useSegment('recommendationsRatings');
  const recommendationsRatingsEnabled = useBrandAgnosticFeatureFlag('pdp-recommendations-ratings') && (ratingsSegment === 'a' || ratingsSegment === 'b');
  const marketingContainerEnabled = useBrandAgnosticFeatureFlag('pdp-marketing-container');
  const marketingContainerPosition = useSegment('marketingContainers');
  const pdpAnchorMarketingContainerFlag = useBrandAgnosticFeatureFlag('pdp-anchor-marketingcontainer');
  const pdpAnchorMarketingContainerSegment = useSegment('pdpAnchorMarketingContainer');
  const { tid } = useBrandAgnosticFeatureVariables('pdp-anchor-marketingcontainer');
  const isReviewsDrawerEnabled = useReviewsDrawer();
  const shouldLoadReviewsScript = useBrandAgnosticFeatureFlag('pdp-mfe-load-power-reviews');
  const powerReviewsEnabled = useBrandAgnosticFeatureFlag('pdp-power-reviews');
  const powerReviewsFeatureVariables = useMultiBrandFeatureVariables(`pdp-power-reviews`);
  const shouldHidePhotoGallery = powerReviewsFeatureVariables['hidden-photogallery'];
  const { isDropShip, vendorId, vendorName } = useDropShipData();
  const findMineEnabled = useFindmineFeature();
  // Sometimes the tid param is set twice in the URL, which results in an array
  // instead of a single value. The fact that it is set twice is a bug
  // originating elsewhere, but we should handle it gracefully regardless.
  // So just use the first value as the canonical one.
  const tidQueryParam = Array.isArray(params?.tid) ? params?.tid[0] : params?.tid;

  const isImageScalingEnabled = useImageScaling();

  const shouldScaleImage = typeof window !== 'undefined' && isImageScalingEnabled && window.innerWidth <= 450 && isTouchDevice();

  let pdpAnchorMarketingContainerEnabled = false;
  if (tid && typeof tid === 'string' && tidQueryParam) {
    const tidList = tid.split(',');

    pdpAnchorMarketingContainerEnabled =
      pdpAnchorMarketingContainerFlag && pdpAnchorMarketingContainerSegment === 'a' && !!tidList.find(tidElement => tidQueryParam.startsWith(tidElement));
  }
  const dataLayerEngagement = useDatalayerEngagement(params.pid);
  const { display: isAIReviewSummaryPresent, modelNameForTracking: aiReviewSummaryModel, enabled: isAIReviewSummaryEnabled } = useReviewSummary();

  const enabledFeatures = {
    aiReviewSummaryModel,
    bottomBreadcrumbsEnabled,
    bricksEnabled,
    draprEnabled,
    findMineEnabled,
    hideShippingEnabled,
    isAIReviewSummaryEnabled,
    isAIReviewSummaryPresent,
    isReviewsDrawerEnabled,
    marketingContainerEnabled,
    pdpAnchorMarketingContainerEnabled,
    pdpInterlinksEnabled,
    powerReviewsEnabled,
    recommendationsRatingsEnabled,
    shouldHidePhotoGallery,
    shouldLoadReviewsScript,
    shouldScaleImage,
    tertiaryFontEnabled,
  };

  const { localize } = useLocalize();
  const shippingAndReturnsData = JSON.parse(shippingAndReturnsJSON, (key, value) => (typeof value === 'string' ? localize(value) : value));

  useDraprAnalyticsScriptLoader();

  const productPageWithFlagsParams = {
    ...props,
    ...enabledFeatures,
    breakPointSize,
    dataLayerEngagement,
    is3DDraprVersion,
    isDropShip,
    isFitOnly,
    localize,
    marketingContainerPosition,
    shippingAndReturnsData,
    vendorId,
    vendorName,
  };

  return productPageWithFlagsParams;
}
