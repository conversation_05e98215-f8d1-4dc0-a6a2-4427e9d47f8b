// @ts-nocheck
import { cleanup, renderHook } from '@testing-library/react-hooks';

import { useAppState } from './use-app-state';
import { useGetStarRatingsProps } from './use-get-star-ratings-props';

jest.mock('./use-app-state');

const useAppStateMock = (productData: any, reviewRatings: any) => {
  (useAppState as unknown as jest.Mock).mockReturnValue({
    productData,
    reviewRatings,
  });
};

const reportStarRatings = jest.fn();
describe('use-get-star-ratings-props hook', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });
  test('should return the handleOnClick as a function, isGiftCard as undefined and reviewRatings as empty object', () => {
    useAppStateMock({}, {});
    const { result } = renderHook(() => useGetStarRatingsProps(reportStarRatings));
    expect(result.current.handleOnClick).toBeInstanceOf(Function);
    expect(result.current.isGiftCard).toBeUndefined();
    expect(result.current.reviewRatings).toBeInstanceOf(Object);
    expect(Object.keys(result.current.reviewRatings)).toHaveLength(0);
  });
  test('should return the handleOnClick as a function, isGiftCard as true and reviewRatings as empty object', () => {
    useAppStateMock({ isGiftCard: true }, {});
    const { result } = renderHook(() => useGetStarRatingsProps(reportStarRatings));
    expect(result.current.handleOnClick).toBeInstanceOf(Function);
    expect(result.current.isGiftCard).toBeTruthy();
    expect(result.current.reviewRatings).toBeInstanceOf(Object);
    expect(Object.keys(result.current.reviewRatings)).toHaveLength(0);
  });
  test('should return the handleOnClick as a function, isGiftCard as false and reviewRatings as empty object', () => {
    useAppStateMock({ isGiftCard: false }, {});
    const { result } = renderHook(() => useGetStarRatingsProps(reportStarRatings));
    expect(result.current.handleOnClick).toBeInstanceOf(Function);
    expect(result.current.isGiftCard).toBeFalsy();
    expect(result.current.reviewRatings).toBeInstanceOf(Object);
    expect(Object.keys(result.current.reviewRatings)).toHaveLength(0);
  });
  test('should return the handleOnClick as a function, isGiftCard as false and reviewRatings has review_count property', () => {
    useAppStateMock({ isGiftCard: false }, { review_count: 10 });
    const { result } = renderHook(() => useGetStarRatingsProps(reportStarRatings));
    expect(result.current.handleOnClick).toBeInstanceOf(Function);
    expect(result.current.isGiftCard).toBeFalsy();
    expect(result.current.reviewRatings).toBeInstanceOf(Object);
    expect(Object.keys(result.current.reviewRatings)).toHaveLength(1);
    expect(result.current.reviewRatings?.review_count).toBe(10);
  });
});
