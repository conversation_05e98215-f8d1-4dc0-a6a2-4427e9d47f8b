// @ts-nocheck
import { renderHook } from '@testing-library/react-hooks';

import { useSegment } from '../components/abseg-provider';
import { useAppState } from './use-app-state';
import { useBrandAgnosticFeatureFlag, useBrandAgnosticFeatureVariables } from './use-feature-flag';
import { useSizeInclusivityFlag } from './use-size-inclusivity-flag';

jest.mock('./use-feature-flag');
jest.mock('../components/abseg-provider');
jest.mock('@ecom-next/sitewide/app-state-provider');

describe('useSizeInclusivity Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '1111',
      },
    });

    (useSegment as jest.Mock).mockReturnValue('a');
    (useBrandAgnosticFeatureFlag as jest.Mock).mockReturnValue(true);
    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      primaryCategoryIds: {
        gap: 'ALL',
      },
    });
  });

  test('returns true when feature flag, segment and categories are enabled', () => {
    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(true);
  });

  test('returns true when categories config consists of product primary category ID', () => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '1111',
      },
    });

    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      primaryCategoryIds: {
        gap: '2222,1111',
      },
    });

    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(true);
  });

  test('returns false when categories config is empty string but segment and flags are enabled', () => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '1111',
      },
    });

    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      primaryCategoryIds: {
        gap: '',
      },
    });

    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(false);
  });

  test('returns true when categories config does not exits and segment and flags are enabled', () => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '1111',
      },
    });

    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      imagesMapping: {},
    });

    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(true);
  });

  test('returns false when categories config does not contain of product primary category ID', () => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '1111',
      },
    });
    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      primaryCategoryIds: {
        gap: '3333,2222,4444',
      },
    });

    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(false);
  });

  test('returns false when categories config contain product primary category ID, but experiment is turned off', () => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: 'gap',
      productData: {
        primaryCategoryId: '3333',
      },
    });
    (useBrandAgnosticFeatureVariables as jest.Mock).mockReturnValue({
      primaryCategoryIds: {
        gap: '3333,2222,4444',
      },
    });

    (useSegment as jest.Mock).mockReturnValue('x');

    const { result } = renderHook(() => useSizeInclusivityFlag());
    expect(result.current.sizeInclusivityEnabled).toBe(false);
  });
});
