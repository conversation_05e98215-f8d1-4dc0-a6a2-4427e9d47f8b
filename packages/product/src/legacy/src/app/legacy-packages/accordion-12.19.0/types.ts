// @ts-nocheck
'use client'

// @ts-nocheck
"use client";
import {SerializedStyles, StyleFn} from "@ecom-next/core/react-stitch";
import {BreakpointProviderState} from "@ecom-next/core/breakpoint-provider";
import React from "react";

export type ChangeHandler = (
  event: React.ChangeEvent,
  expandedId?: string
) => void;

export type CommonProps = {
  /**
   * Uses crossbrand styling, including fonts and colors
   */
  crossBrand?: boolean;
  /**
   * Changes the default toggle to a custom one
   */
  showMoreToggle?: string;
  showLessToggle?: string;
  /**
   * removes most styling from the accordion
   */
  unstyled?: boolean;
  /**
   * Changes the icon type for the panels to use chevron icons
   */
  withChevron?: boolean;
  /**
   * Changes the icon type for the panels to use plus/minus icons
   */
  withPlusMinus?: boolean;
  /**
   * Removes the default toggle icon to add the custom toggle
   */
  hideDefaultToggles?: boolean;
  /**
   * If true, headings will stick
   */
  sticky?: boolean;
  /**
   * If true, headings will snap to the top of the parent container on expanding click
   */
  snap?: boolean;
  /**
   * If true, on click to close a panel, headings will snap to the top and then close
   */
  snapToTopOnClose?: boolean;
  /**
   * Uses inverse styling, including fonts and colors
   */
  inverse?: boolean;
};

export type AccordionProps = {
  /**
   * Controls whether a specific panel is expanded by default
   */
  defaultExpanded?: string[];
  /**
   * any child components
   */
  children: React.ReactNode;
  /**
   * The id of the currently expanded item
   */
  expanded?: string[];
  /**
   * Callback fired when an item is expanded or collapsed.
   */
  onChange?: ChangeHandler;
  /**
   * If true, only one panel open at a time
   */
  singlePanelExpanded?: boolean;
} & CommonProps;

export type AccordionContextType = {
  onChange: ChangeHandler;
  minWidth: BreakpointProviderState["minWidth"];
} & CommonProps &
  Pick<AccordionProps, "expanded" | "unstyled">;

export type AccordionStyleFn<
  OtherTypes = Record<string, unknown>,
  Return = SerializedStyles
> = StyleFn<Partial<CommonProps> & OtherTypes, Return>;

export type DetailsProps = {
  children: React.ReactNode;
  className?: string;
};

export type PanelProps = {
  children: JSX.Element | JSX.Element[];
  className?: string;
  disabled?: boolean;
  onChange?: ChangeHandler;
  id: string;
  scrollableElement?: HTMLElement;
};
