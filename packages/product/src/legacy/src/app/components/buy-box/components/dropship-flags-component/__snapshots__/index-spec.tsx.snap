// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DropshipFlagsContainer /> renders correctly for - AT &  1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-phantom-sans),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;

exports[`<DropshipFlagsContainer /> renders correctly for - GAP &  1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;

exports[`<DropshipFlagsContainer /> renders correctly for - GAPFS &  1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;

exports[`<DropshipFlagsContainer /> renders correctly for - ON &  1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-gotham),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;

exports[`<DropshipFlagsContainer /> renders correctly for BR and BRFS for - BR & brColors = true 1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;

exports[`<DropshipFlagsContainer /> renders correctly for BR and BRFS for - BRFS &  1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <ul
    css={[Function]}
    data-testid="dropshipFlagsContainer"
  >
    <li>
      Return by Mail Only
    </li>
    <li>
      Final Sale - No Returns or Exchanges
    </li>
  </ul>
</div>
`;
