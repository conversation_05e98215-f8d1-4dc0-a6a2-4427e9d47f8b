// @ts-nocheck
'use client';

import type { SerializedStyles } from '@ecom-next/core/react-stitch';
import { css, getFontWeight } from '@ecom-next/core/react-stitch';

export const onExcludedFromPromotionsStyles = (): SerializedStyles => {
  return css`
    color: rgb(var(--color-black-and-white-black));
    font-style: normal;
    ${getFontWeight('medium')};
    font-size: 0.75rem;
    line-height: 1.125rem;
  `;
};
