// @ts-nocheck
'use client';

import type { SerializedStyles, Theme } from '@ecom-next/core/react-stitch';
import { css, forBrands, getFontWeight } from '@ecom-next/core/react-stitch';
import { setupRedesignStyles } from '@product-page/legacy/styles';
import type { InterpolationPrimitive } from '@emotion/serialize';
import { dropshipStylesAt } from './at.styles';
import { dropshipRedesignstyles2024Gap } from './gap.styles';
import { dropshipStylesOn } from './on.styles';

export const shippedByVendorStyles = (theme: Theme): InterpolationPrimitive => {
  const setGapBuyboxRedesign2024Styles = setupRedesignStyles(theme).isGapBuyBoxRedesign2024;
  const setAtBuyboxRedesign2024Styles = setupRedesignStyles(theme).isAtBuyBoxRedesign2024;

  const dropshipVendorStyles = forBrands(theme, {
    at: () => setAtBuyboxRedesign2024Styles(dropshipStylesAt(theme)),
    gap: () => setGapBuyboxRedesign2024Styles(dropshipRedesignstyles2024Gap(theme)),
    on: dropshipStylesOn,
  });
  return css`
    font-size: 1rem;
    line-height: 1.188rem;
    color: ${theme.color.bk};
    margin: 1.625rem 0 0.5rem;
    padding-left: 0;
    position: relative;
    text-align: center;

    .vendorName {
      ${getFontWeight('bold')};
    }
    .brandHelpIcon {
      padding-left: 0.313rem;
      cursor: pointer;
    }
    ${dropshipVendorStyles};
  `;
};
