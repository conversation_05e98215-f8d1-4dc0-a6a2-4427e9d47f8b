// @ts-nocheck
import { BopisContext } from '@ecom-next/core/legacy/bopis';
import TestApp from '@pdp/spec/test-app';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { fireEvent, render, screen } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import React from 'react';

import { useInsituModalContextActions } from '../../../../../../hooks/use-insitu-modal-context-actions';
import InsituModalBase from '../..';
import { shoppingBagItems, shoppingBagItemsNoSalePrice, shoppingBagMultipleItems } from '../../data/shopping-data';
import { contentStyles } from '../../styles/index.styles';
import InSituMarketingContainer from '../InSituMarketingContainer';

jest.mock('../InSituMarketingContainer');
(InSituMarketingContainer as jest.Mock).mockReturnValue(null);

jest.mock('../../../../../../hooks/use-insitu-modal-context-actions');
(useInsituModalContextActions as jest.Mock).mockReturnValue({
  closeInsituModal: jest.fn(),
});

const BopisMockProvider = ({ active = false, enabled = false, selectedStore = {}, children }) => {
  const providerValue = {
    active,
    enabled,
    selectedStore,
  };

  return <BopisContext.Provider value={providerValue}>{children}</BopisContext.Provider>;
};

jest.mock('@ecom-next/core/legacy/localization-provider', () => {
  const originalModule = jest.requireActual('@ecom-next/core/legacy/localization-provider');

  const useLocalize = () => {
    return {
      localize: (key: string) => {
        return {
          'pdp.closeModal.altText': 'close modal',
          'pdp.currencySymbol': '$',
          'pdp.insitu.button.checkout': 'checkout',
          'pdp.insitu.button.keepShopping': 'keep shopping',
          'pdp.insitu.label.color': 'Color',
          'pdp.insitu.label.pickup': 'Pickup In-Store',
          'pdp.insitu.label.qty': 'Qty',
          'pdp.insitu.label.size': 'Size',
          'pdp.insitu.label.subtotal': 'Subtotal',
          'pdp.insitu.multipleItemsInBag': 'items in bag',
          'pdp.insitu.noReturns': 'No returns – final sale item',
          'pdp.insitu.oneItemInBag': '1 item in bag',
          'pdp.insitu.returnsByMailOnly': 'Return by mail only',
          'pdp.insitu.title.addToBag': 'Added to your bag',
          'pdp.insitu.title.altAddedToBag': 'product added to bag',
          'pdp.product.sale': 'Sale',
        }[key];
      },
    };
  };

  return {
    __esModule: true,
    ...originalModule,
    useLocalize,
  };
});

const modalProps = {
  bopisActive: false,
  bopisEnabled: false,
  brand: 'gap',
  brandCodeUrls: {
    secureUrl: 'https://secure-bananarepublic.gap.com',
    unsecureUrl: 'https://bananarepublic.gap.com',
  },
  brandName: 'gp',
  errorLogger: () => {},
  itemCount: 17,
  locale: 'en_US',
  subTotal: '$1087.83',
};

const setup = (props = {}, featureFlagsCtxValue = {}) => {
  render(
    <TestApp featureFlagsCtxValue={featureFlagsCtxValue}>
      <BopisMockProvider {...props} {...modalProps}>
        <InsituModalBase {...props} {...modalProps} />
      </BopisMockProvider>
    </TestApp>
  );
};

const getInsituModalStyles = (brandName, enabledFeatures) => (
  <TestApp
    abSegValue={{ on197: 'a' }}
    brandName={brandName}
    featureFlagsCtxValue={{ enabledFeatures: { ...enabledFeatures, 'pdp-percentage-off-us-on': true } }}
  >
    <div css={contentStyles} />
  </TestApp>
);

describe('<InsituModal />', () => {
  const matchSnapshotsAllBrands = setupSnapshots();

  matchSnapshotsAllBrands(getInsituModalStyles, 'renders insitu styles correctly');

  test('opens the modal when insituModalIsOpen is set to true', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems });
    expect(screen.getByText('Dark Gray')).toBeInTheDocument();
  });

  test('closes the modal when insituModalIsOpen is set to false', () => {
    setup({ insituModalIsOpen: false, shoppingBagItems });
    expect(screen.queryByText('Dark Gray')).not.toBeInTheDocument();
  });

  test('closeInsituModal should be called on close modal', async () => {
    const { result } = renderHook(() => useInsituModalContextActions());
    const closeInsituModalSpy = result.current.closeInsituModal;
    setup({ insituModalIsOpen: true, shoppingBagItems });
    const closeButton = screen.getByLabelText('close modal', { selector: 'button' });
    await act(async () => { 

     fireEvent.click(closeButton); 

     })
    expect(closeInsituModalSpy).toHaveBeenCalled();
  });

  test('displays one product card if one item in inlineBagItems', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems });
    expect(screen.getAllByTestId('product-card')).toHaveLength(1);
  });

  test('displays multiple product cards if multiple items in inlineBagItems', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems: shoppingBagMultipleItems });
    expect(screen.getAllByTestId('product-card')).toHaveLength(2);
  });

  test('does not display sale price if one does not exist', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems: shoppingBagItemsNoSalePrice });
    expect(screen.getByTestId('list-price')).toBeInTheDocument();
    expect(screen.queryByTestId('sale-price')).not.toBeInTheDocument();
    expect(screen.queryByTestId('sale-list-price')).not.toBeInTheDocument();
  });

  test('displays sale price if one exists', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems });
    expect(screen.getByTestId('sale-list-price')).toBeInTheDocument();
    expect(screen.getByText('$128.00')).toBeInTheDocument();
    expect(screen.getByTestId('sale-price')).toBeInTheDocument();
    expect(screen.getByText('$63.99')).toBeInTheDocument();
  });

  test('checkout button should contain shopping bag url', () => {
    setup({ insituModalIsOpen: true, shoppingBagItems });
    expect(screen.getByRole('link', { name: 'checkout' })).toHaveAttribute(
      'href',
      'https://secure-bananarepublic.gap.com/shopping-bag'
    );
  });

  describe('getPickupInfo', () => {
    test('displays pick up info if information available', () => {
      setup({
        active: true,
        enabled: true,
        insituModalIsOpen: true,
        selectedStore: {
          storeAddress: { cityName: 'testCity' },
          storeName: 'testStore',
        },
        shoppingBagItems,
      });
      expect(screen.getByText('testStore - testCity')).toBeInTheDocument();
    });

    test('displays empty pick up info if data is not present', () => {
      setup({
        active: true,
        enabled: true,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems,
      });
      expect(screen.queryByText('testStore - testCity')).not.toBeInTheDocument();
    });

    test('does not display pick up info if shouldDisplayPickup is false', () => {
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        shoppingBagItems,
      });
      expect(screen.queryByTestId('pickup-info')).not.toBeInTheDocument();
    });
  });

  describe('keepFocusWhenTabbingBack', () => {
    test('checkout button should have focus when tabbing back', () => {
      setup({
        active: true,
        enabled: true,
        insituModalIsOpen: true,
        shoppingBagItems,
      });

      fireEvent.keyDown(screen.getByRole('presentation'), {
        key: 'Tab',
        shiftKey: true,
      });

      expect(screen.getByTestId('checkoutButton')).toHaveFocus();
    });

    test('checkout button should not have focus with a different key press', () => {
      setup({
        active: true,
        enabled: true,
        insituModalIsOpen: true,
        shoppingBagItems,
      });

      fireEvent.keyDown(screen.getByRole('presentation'), {});

      expect(screen.getByTestId('checkoutButton')).not.toHaveFocus();
    });

    test('checkout button should not have focus when id got changed', () => {
      setup({
        active: true,
        enabled: true,
        insituModalIsOpen: true,
        shoppingBagItems,
      });

      screen.getByTestId('checkoutButton').id = 'checkoutButton1';
      fireEvent.keyDown(screen.getByRole('presentation'), {
        key: 'Tab',
        shiftKey: true,
      });

      expect(screen.getByTestId('checkoutButton')).not.toHaveFocus();
    });
  });

  describe('partial success warning', () => {
    test('displays partial success message if warnings and message are present', () => {
      const warnings = {
        message: 'Only 2 items are in stock and have been added to the bag.',
      };
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems: [{ ...shoppingBagItems[0], warnings }],
      });
      expect(screen.getAllByTestId('success-notification')).toHaveLength(1);
      expect(screen.getByText(warnings.message)).toBeInTheDocument();
    });

    test('does not show any warnings if warnings comes empty', () => {
      const warnings = null;
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems: [{ ...shoppingBagItems[0], warnings }],
      });

      expect(screen.queryByTestId('success-notification')).not.toBeInTheDocument();
    });
  });

  describe('getReturnsMessage', () => {
    test('displays no returns message', () => {
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems: [
          {
            ...shoppingBagItems[0],
            returnType: 'N',
            shouldShowReturnsMessage: true,
          },
        ],
      });
      expect(screen.getByText('No returns – final sale item')).toBeInTheDocument();
    });

    test('displays returns by mail only message', () => {
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems: [
          {
            ...shoppingBagItems[0],
            returnType: 'M',
            shouldShowReturnsMessage: true,
          },
        ],
      });
      expect(screen.getByText('Return by mail only')).toBeInTheDocument();
    });

    test('empty returns message', () => {
      setup({
        active: true,
        enabled: false,
        insituModalIsOpen: true,
        selectedStore: {},
        shoppingBagItems: [
          {
            ...shoppingBagItems[0],
            returnType: '',
            shouldShowReturnsMessage: true,
          },
        ],
      });
      expect(screen.queryByText('No returns – final sale item')).not.toBeInTheDocument();
      expect(screen.queryByText('Return by mail only')).not.toBeInTheDocument();
    });
  });

  describe('Free shipping bar', () => {
    test('should display when feature flag pdp-free-shipping-bar is enabled', () => {
      setup(
        {
          bopisAvailable: false,
          insituModalIsOpen: true,
          shoppingBagItems: [
            {
              ...shoppingBagItems[0],
              returnType: '',
              shouldShowReturnsMessage: true,
            },
          ],
        },
        {
          enabledFeatures: {
            'pdp-free-shipping-bar-us-gap': true,
          },
          featureVariables: {
            'pdp-free-shipping-bar-us-gap': {
              priceLimit: 100,
            },
          },
        }
      );
      expect(screen.getByTestId('free-shipping-progress')).toBeInTheDocument();
    });

    test('should not display when feature flag pdp-free-shipping-bar is disabled', () => {
      setup(
        {
          bopisAvailable: false,
          insituModalIsOpen: true,
          shoppingBagItems,
        },
        {
          enabledFeatures: {
            'pdp-free-shipping-bar-us-gap': false,
          },
        }
      );
      expect(screen.queryByTestId('free-shipping-progress')).not.toBeInTheDocument();
    });
  });

  describe('focusFirstElement', () => {
    test('keepShoppingButton button should have focus when tabbing back', () => {
      setup({
        active: true,
        bopisAvailable: false,
        enabled: true,
        insituModalIsOpen: true,
        shoppingBagItems,
      });
      screen.getByTestId('checkoutButton').id = 'checkoutButton1';
      fireEvent.keyDown(screen.getByTestId('checkoutLink'), {
        key: 'Tab',
        shiftKey: true,
      });
      expect(screen.getByTestId('keepShoppingButton')).toHaveFocus();
    });

    test('should navigate to shoppingBagUrl when checkoutLink have focus and Enter button pressed', () => {
      setup({
        active: true,
        bopisAvailable: false,
        enabled: true,
        insituModalIsOpen: true,
        shoppingBagItems,
      });
      fireEvent.keyDown(screen.getByTestId('checkoutLink'), {
        key: 'Enter',
        shiftKey: true,
      });
      // eslint-disable-next-line no-restricted-globals
      expect(location.href).toContain('shopping-bag');
    });
  });
});
