// @ts-nocheck
import TestApp from '@pdp/spec/test-app';
import React from 'react';
import renderer from 'react-test-renderer';

import { FreeShippingBarWithLoyaltyLinks } from './index';

const testAppDefault = {
  featureFlagsCtxValue: {
    enabledFeatures: {
      'pdp-loyalty-enroll-link': true,
    },
    featureVariables: {
      'pdp-free-shipping-bar-us-gap': {
        priceLimit: 120,
      },
    },
  },
};

describe('<FreeShippingBarWithLoyaltyLinks />', () => {
  test('renders correctly', () => {
    const tree = renderer
      .create(
        <TestApp {...testAppDefault}>
          <FreeShippingBarWithLoyaltyLinks
            percentage={100}
            positionBasedOnThreshold="underThreshold"
            translationParameters={{}}
          />
        </TestApp>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });
});
