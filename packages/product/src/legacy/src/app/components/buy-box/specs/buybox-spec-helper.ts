// @ts-nocheck
import productData from '@pdp/spec/data/old-navy/mid-rise-jeans_en-US.json';
import { ColorPicker } from '@product-page/legacy/product-selection';

const { variants, selectedVariant, selectedColor } = productData;
const styleId = '123456';
const secureBrandUrl = '';
const unselectedLabels = ['Size', 'Length'];
const brand = 'on' as Brand;
const brandName = 'on' as AbbrBrand;
const abbrBrandForTrueFit = '';
const market = 'us' as Market;
const locale = 'en_US' as Locale;
const productTitle = selectedVariant.name || '';
const priceAdapter = (val: any) => val;
const abSeg = {};
const reporting = () => {};
const enabledFeatures = {};

const fitInformation = {
  attributes: ['Slightly fitted through body.', 'Cardigan hits below waist.'],
  label: 'Fit & Sizing',
  url: '/browse/sizeChart.do?cid=2093&mdts=true&dgmBlocker=true',
};

const selectedStore: ProductStore = {
  activeFeatureToggles: {
    inStorePickup: false,
  },
  latitude: 'string',
  longitude: 'string',
  storeAddress: {
    addressLine1: '150 NORTH STATE STREET',
    cityName: 'CHICAGO',
    countryCode: 'US',
    phoneNumber: '+5581991291290',
    postalCode: '60601',
    stateProvinceCode: 'IL',
  },
  storeDistance: '0.0',
  storeHours: [''],
  storeId: '3997',
  storeName: '150 N. STATE ST.',
  storeTimeZone: 'string',
};

const carouselActions = {
  hoverColor: () => {},
  updateColor: () => {},
  updateVariant: () => {},
};
const productInformationActions = {
  updateReturnType: () => {},
};

const price = {
  currentMaxPrice: '22.94',
  currentMinPrice: '14.94',
  localizedCurrencySymbol: '',
  maxPercentageOff: 0,
  minPercentageOff: 0,
  priceType: 0,
  regularMaxPrice: '24.94',
  regularMinPrice: '14.94',
};

const personalizationData = {
  isEmpty: true,
  isLoading: false,
  marketingMessageInfo: {},
};

const productImages = {
  '360442002_main': {
    large: '/webcontent/0015/888/704/cn15888704.jpg',
    medium: '/webcontent/0015/888/705/cn15888705.jpg',
    small: '/webcontent/0015/888/712/cn15888712.jpg',
    thumbnail: '/webcontent/0015/888/711/cn15888711.jpg',
    xlarge: '/webcontent/0015/888/709/cn15888709.jpg',
  },
};

const datalayer = {
  add: () => 1,
  build: () => Promise.resolve(),
  link: () => Promise.resolve(),
  reset: () => {},
  view: () => Promise.resolve(),
  viewWith: () => Promise.resolve(),
};

const bopisData = {
  active: false,
  bopisEnabled: false,
  bopisInitialed: true,
  enabled: false,
  fetchStores: 'complete',
  getStores: () => Promise.resolve([selectedStore]),
  postalCode: '94117',
  selectedStore,
  setActive: (e: boolean): void => {
    bopisData.active = e;
  },
  setEnabled: (e: boolean): void => {
    bopisData.enabled = e;
  },
  setPostalCode: () => {},
  setSelectedStore: () => {},
  setStores: () => {},
  stores: [selectedStore],
};

const reporter = {
  reportAddToBagClick: () => {},
  reportInventoryStatus: () => {},
  reportStarRatings: () => {},
};

export const BuyBoxProps = {
  ColorPicker,
  abSeg,
  abbrBrandForTrueFit,
  backOrderMessage: {
    ariaLabelText: 'BackOrder Message',
    mainText: 'BackOrder Message',
  },
  backOrderText: '',
  bopisAvailable: false,
  bopisData,
  bopisExclusion: false,
  bopisExclusionStyle: false,
  bopisExclusionStyleDate: { endDate: '', startDate: '' },
  brand,
  brandCodeUrls: {
    secureUrl: '',
    unsecureUrl: '',
  },
  brandName,
  carouselActions,
  customerId: '',
  datalayer,
  dimensions: [],
  enabledFeatures,
  env: '',
  errorFlag: false,
  errorLogger: () => {},
  fitInformation,
  gidMessage: 'Gid message',
  hideShippingEnabled: false,
  isGiftCard: false,
  isShippingAvailable: true,
  itemCount: 1,
  locale,
  localize: (): string => 'pdp.addToBag.selectionError',
  market,
  marketAwareBrandCode: '1' as BrandCode,
  marketingFlag: 'Marketing flag',
  pasConfig: {
    url: '',
  },
  personalizationData,
  price,
  priceAdapter,
  productImages,
  productInformationActions,
  productTitle,
  regularMaxPrice: '24.94',
  regularMinPrice: '24.94',
  reporter,
  reporting,
  secureBrandUrl,
  selectedColor,
  selectedItemBackOrderMessage: {
    ariaLabelText: 'Selected Item BackOrder Message',
    mainText: 'Selected Item BackOrder Message',
  },
  selectedStore,
  selectedVariant,
  selectedVariantId: 1,
  shouldUseInsituModalComponent: true,
  styleId,
  unselectedLabels,
  variants,
  visibleBackOrderTooltip: '',
};
