// @ts-nocheck
import { Brands } from '@ecom-next/core/legacy/utility';
import TestApp from '@pdp/spec/test-app';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { ColorPromoFlag, MarketingFlag } from '@product-page/legacy/marketing-flag';
import Price from '@product-page/legacy/product-price';
import React from 'react';
import type { ReactTestRenderer } from 'react-test-renderer';
import TestRenderer from 'react-test-renderer';

import { MarketingFlagBuyBox } from '../../marketing-flag';
import PowerReviews from '../../power-reviews';
import Title from '../../title/index';
import { BuyBoxHeader } from '..';
import { BuyBoxHeaderAT } from '../buyboxheader-at';
import { BuyBoxHeaderBR } from '../buyboxheader-br';
import { buyBoxHeaderProps } from './buyboxheader-spec-helper';

jest.mock('@product-page/legacy/marketing-flag');

const mockMarketingFlag = () => {
  (MarketingFlag as jest.Mock).mockImplementation(() => <div>MarketingFlag Mock</div>);
};

const mockColorPromoFlag = () => {
  (ColorPromoFlag as jest.Mock).mockImplementation(() => <div>ColorPromoFlag Mock</div>);
};

const buyBoxHeaderComponent = (
  brandName: Brands,
  enabledFeatures: Record<string, boolean> = {},
  abSeg: Record<string, string> = {}
) => (
  <TestApp abSegValue={abSeg} brandName={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
    <BuyBoxHeader
      {...buyBoxHeaderProps}
      priceInTitleProps={{
        ...buyBoxHeaderProps.priceInTitleProps,
      }}
    />
  </TestApp>
);

const renderBuyBoxHeaderWithFlags = (
  brandName: Brands,
  enabledFeatures: Record<string, boolean> = {},
  abSeg: Record<string, string> = {}
): ReactTestRenderer => TestRenderer.create(buyBoxHeaderComponent(brandName, enabledFeatures, abSeg));

describe('<BuyBoxHeader />', () => {
  beforeAll(() => {
    mockMarketingFlag();
    mockColorPromoFlag();
  });

  const matchSnapshotsAllBrands = setupSnapshots();
  matchSnapshotsAllBrands(buyBoxHeaderComponent, 'BuyboxHeader');

  describe('when brand is Athleta', () => {
    test('renders expected components', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.Athleta).root;

      expect(component.findByType(BuyBoxHeaderAT)).toBeDefined();
    });
  });

  describe('when brand is BR', () => {
    test('renders BuyBoxHeaderBR', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.BananaRepublic).root;

      expect(component.findByType(BuyBoxHeaderBR)).toBeDefined();
    });
  });

  describe('when brand is BRFS', () => {
    test('renders BuyBoxHeaderBR', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.BananaRepublicFactoryStore).root;

      expect(component.findByType(BuyBoxHeaderBR)).toBeDefined();
    });
  });

  describe('when brand is Gap', () => {
    test('renders expected components', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.Gap).root;

      expect(component.findByType(Title)).toBeDefined();
    });
  });

  describe('when brand is GapFactoryStore', () => {
    test('renders expected components', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.GapFactoryStore).root;

      expect(component.findByType(Title)).toBeDefined();
    });
  });

  describe('when brand is OldNavy', () => {
    test('renders expected components', () => {
      const component = renderBuyBoxHeaderWithFlags(Brands.OldNavy).root;

      expect(component.findByType(Title)).toBeDefined();
      expect(component.findByType(Price)).toBeDefined();
      expect(component.findByType(MarketingFlagBuyBox)).toBeDefined();
      expect(component.findByType(PowerReviews)).toBeDefined();
    });
  });
});
