// @ts-nocheck
'use client'

/***/
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { jsx, useTheme } from "@ecom-next/core/react-stitch";
import { Meter } from '@ecom-next/core/legacy/meter';
import { Brands } from '@ecom-next/core/legacy/utility';
import React, { useCallback, useMemo } from 'react';

import { isMarketCA } from '../../../../../../helpers/util/evaluate-market';
import { useAppState } from '../../../../../../hooks/use-app-state';
import { useFeatureFlag, useFeatureVariables } from '../../../../../../hooks/use-feature-flag';
import { useLoyaltyEnrollLinkFeature } from '../../../../../../hooks/use-loyalty-enroll-link-feature';
import { usePersonalization } from '../../../../../../hooks/use-personalization';
import Show from '../../../../../show';
import { FreeShippingBarWithLoyaltyLinks } from './free-shipping-with-loyalty-links';
import { freeShippingStyles } from './index.styles';

type FreeShippingProps = {
  subTotal: string;
};

export function FreeShipping(props: FreeShippingProps): JSX.Element {
  const { subTotal } = props;
  const theme = useTheme();
  const { localize } = useLocalize();
  const personalizationContextData = usePersonalization();
  const freeShippingBarEnabled = useFeatureFlag('pdp-free-shipping-bar');
  const { priceLimit } = useFeatureVariables('pdp-free-shipping-bar');
  const loyaltyEnrollLinkEnabled = useLoyaltyEnrollLinkFeature();
  const {
    productData: { isGiftCard = false },
    brandName,
    market,
    url: targetURL,
  } = useAppState();

  const targetUrlWithScapedCharacter = targetURL.replace('?', '\\?');
  const nonFactoryBrands = [Brands.Gap, Brands.BananaRepublic, Brands.Athleta, Brands.OldNavy].includes(
    brandName as Partial<Brands>
  );
  const notSignedIn =
    !personalizationContextData?.userContext?.isLoggedInUser ||
    personalizationContextData?.userContext?.isLoggedInUser === 'false';

  const currencySymbol = localize('pdp.currencySymbol');

  const subtotalNoSymbol = subTotal.replace(currencySymbol, '');
  const subtotalAsNumber = parseFloat(subtotalNoSymbol.replace(',', '.'));
  const priceLimitReached = subtotalAsNumber >= priceLimit;
  const percentageCompleted = (subtotalAsNumber * 100) / priceLimit;

  const positionBasedOnThreshold = subtotalAsNumber < priceLimit ? 'underThreshold' : 'overThreshold';
  let freeShippingText = localize('pdp.insitu.freeShippingCompleted');
  let remainingAmount = '';

  const createCanadaFreeShippingText = useCallback(() => {
    if (notSignedIn && loyaltyEnrollLinkEnabled) {
      return [
        <span key="0">{`${localize('pdp.insitu.notSigned.freeForRewardsMembers')} `}</span>,
        <a
          key="1"
          className="free-shipping-signin-join"
          href={`/my-account/sign-in?targetURL=${targetUrlWithScapedCharacter}`}
          rel="noopener noreferrer"
          target="_blank"
        >
          {`${localize('pdp.insitu.notSigned.freeForRewardsMembers.signIn')}`}
        </a>,
        <span key="2">{` ${localize('pdp.insitu.notSigned.freeForRewardsMembers.or')} `}</span>,
        <a
          key="3"
          className="free-shipping-signin-join"
          href={`/my-account/sign-in?targetURL=${targetUrlWithScapedCharacter}`}
          rel="noopener noreferrer"
          target="_blank"
        >
          {`${localize('pdp.insitu.notSigned.freeForRewardsMembers.join')}`}
        </a>,
      ];
    }
    return localize('pdp.insitu.signedIn.freeForRewardsMembers');
  }, [localize, notSignedIn, targetUrlWithScapedCharacter, loyaltyEnrollLinkEnabled]);

  if (!priceLimitReached) {
    remainingAmount = (priceLimit - subtotalAsNumber).toFixed(2);
    freeShippingText = localize('pdp.insitu.freeShippingNotCompleted', { currencySymbol, remainingAmount });
  }

  const showCanadaMessage = useMemo(() => nonFactoryBrands && isMarketCA(market), [nonFactoryBrands, market]);

  const translationParameters = {
    currencySymbol,
    priceLimit,
    remainingAmount,
    subtotalNoSymbol,
  };

  const CaMessage = () => (
    <div className="free-shipping-progress" css={freeShippingStyles}>
      <p className="free-shipping-text">{createCanadaFreeShippingText()}</p>
    </div>
  );

  const FreeShippingDefaultMessage = () => {
    if (!freeShippingBarEnabled) {
      return null;
    }
    return loyaltyEnrollLinkEnabled ? (
      <FreeShippingBarWithLoyaltyLinks
        percentage={percentageCompleted}
        positionBasedOnThreshold={positionBasedOnThreshold}
        translationParameters={translationParameters}
      />
    ) : (
      <div className="free-shipping-progress" css={freeShippingStyles} data-testid="free-shipping-progress">
        <p className="free-shipping-text" dangerouslySetInnerHTML={{ __html: freeShippingText }} />
        {!priceLimitReached && (
          <Meter
            animation
            backgroundColor={theme.color.g5}
            borderColor={theme.color.g3}
            percentage={percentageCompleted}
            valueColor={theme.crossBrand.color.b2}
          />
        )}
      </div>
    );
  };

  return <Show when={!isGiftCard}>{showCanadaMessage ? <CaMessage /> : <FreeShippingDefaultMessage />}</Show>;
}
