// @ts-nocheck
'use client'

import { waitFor } from '../../../../helpers/util';

type WaitForOptions = {
  interval?: number;
  maxWait?: number;
};

export async function pollWindow(options?: WaitForOptions): Promise<void> {
  try {
    await waitFor(() => window?.draprViewerBridge, options);
    return await Promise.resolve();
  } catch (error) {
    return Promise.reject(new Error(error as string));
  }
}
