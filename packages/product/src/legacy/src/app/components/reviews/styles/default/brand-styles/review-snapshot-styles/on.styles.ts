// @ts-nocheck
'use client';

import type { Theme } from '@ecom-next/core/react-stitch';
import { css } from '@ecom-next/core/react-stitch';
import type { InterpolationPrimitive } from '@emotion/serialize';
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

export const reviewSnapshotStylesForON =
  ({ reviewSummaryEnabled }) =>
  (theme: Theme): InterpolationPrimitive => {
    const snapshotContainerStyles = css`
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      @media (min-width: 781px) {
        flex-direction: row;
      }
    `;
    const partialStars = (starPercentage: number) => {
      switch (starPercentage) {
        case 25:
          return `
          &:before {
            width: 33%;
          }
          &:after {
            width: 67%;
            background-position-x: 100%;
          }
        `;
        case 50:
          return `
          &:before {
            width: 50%;
          }
          &:after {
            width: 50%;
            background-position-x: 100%;
          }
        `;
        case 75:
          return `
          &:before {
            width: 67%;
          }
          &:after {
            width: 33%;
            background-position-x: 100%;
          }
        `;
        default:
          return `
          &:before {
            width: 100%;
          }
          &:after {
            width: 0;
          }
        `;
      }
    };

    return css`
      max-width: 87.5rem;
      margin: auto;

      & > .p-w-r {
        margin: 0;
        padding: 0 0.75rem;

        @media (min-width: ${sdsBreakpoint.medium}) {
          padding: 0 0.938rem;
        }
      }

      .pr-rating-stars {
        white-space: nowrap;

        .pr-star-v4 {
          width: 32px !important;
          height: 32px !important;
        }
      }

      .pr-star-v4.pr-star-v4-25-filled {
        ${partialStars(25)}
      }

      .pr-star-v4.pr-star-v4-50-filled {
        ${partialStars(50)}
      }

      .pr-star-v4.pr-star-v4-75-filled {
        ${partialStars(75)}
      }

      .pr-review-snapshot-block.pr-review-snapshot-block-recommend,
      .pr-review-snapshot-content-block.pr-review-snapshot-header {
        display: none !important;
      }

      .pr-review-snapshot {
        ${reviewSummaryEnabled ? snapshotContainerStyles : ''};
        .pr-review-snapshot-snippets-headline,
        .pr-snippet-review-count,
        .pr-review-snapshot-recomend .pr-reco-to-friend-message {
          ${theme.font.primary};
          color: ${theme.color.b1};
          font-size: 1rem;
          line-height: 1.38;
        }

        .pr-ratings-histogram {
          .pr-ratings-histogram-barValue {
            background: ${theme.color.b1} !important;
          }

          .pr-histogram-label,
          .pr-histogram-count {
            ${theme.font.primary};
            color: ${theme.color.b1};
            font-size: 1rem;
            line-height: 1.38;
          }

          .pr-histogram-count {
            color: ${theme.color.g2};
          }
        }

        .pr-slider .pr-slider-node-group .pr-slider-node.pr-active {
          background: ${theme.color.b1} !important;
          width: 1rem;
          height: 1rem;
        }

        .pr-review-snapshot-block-container section.pr-review-snapshot-block,
        .pr-review-snapshot-histogram-msq-container,
        & .pr-review-snapshot-msq {
          border: none !important;
        }
        .pr-review-snapshot-histogram-msq-container {
          order: ${reviewSummaryEnabled ? '2' : 'unset'};
        }

        .pr-review-snapshot-msq-container {
          .pr-slider-root {
            .pr-min-text,
            .pr-max-text {
              ${theme.font.primary};
              font-size: 1rem;
              line-height: 1.38;
              color: rgb(var(--pdp-color-black-1300));
              font-size: 0.8rem;
              text-transform: capitalize;
              margin: 0;
            }

            .pr-slider-node {
              background-color: ${theme.color.gray20};
              height: 0.5rem;
              width: 0.5rem;
            }

            .pr-slider-line {
              border-top-color: ${theme.color.gray20};
              border-top-width: 4px;
              transform: translateY(-50%);
            }
          }

          .pr-review-snapshot-msq-overallquality {
            ${theme.font.primary};
            color: ${theme.color.b1};
            font-size: 1rem;
            line-height: 1.38;

            .pr-rating-stars {
              .pr-star-v4 {
                width: 1.25rem !important;
              }

              .pr-star-v4,
              .pr-star-v4::before,
              .pr-star-v4::after {
                height: 1.25rem !important;
                background-size: 1.25rem 1.25rem;
              }
            }
          }

          .pr-star-v4.pr-star-v4-25-filled {
            ${partialStars(25)};
          }

          .pr-star-v4.pr-star-v4-50-filled {
            ${partialStars(50)};
          }

          .pr-star-v4.pr-star-v4-75-filled {
            ${partialStars(75)};
          }
        }
      }

      .pr-review-snapshot-snippets-headline {
        display: none !important;
      }

      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple {
        display: inline-block;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        width: 100%;

        @media (min-width: 62.5rem) {
          width: 30%;
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-simple .pr-review-snapshot-block {
        width: 100%;
      }

      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple .pr-review-snapshot-block {
        min-height: 0;
      }

      .p-w-r .pr-review-snapshot .pr-snippet .pr-snippet-stars-png {
        overflow: visible;
      }

      .pr-review-snapshot-snippets .pr-snippet {
        display: block;
        text-align: center;
        flex-direction: row;

        @media (min-width: 62.875rem) {
          display: flex;
          justify-content: space-between;
          width: 100%;
        }
      }

      .p-w-r .pr-review-snapshot.pr-snapshot-mobile .pr-review-snapshot-simple .pr-snippet-read-and-write {
        text-align: center;

        @media (min-width: 62.5rem) {
          text-align: left;
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container .pr-review-snapshot-msq-histogram,
      .p-w-r .pr-review-snapshot .pr-review-snapshot-msq-histogram {
        width: 100%;
        height: auto;
        padding-top: 0;
        padding-bottom: 0;
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container .pr-review-snapshot-msq-histogram {
        padding: 0 0 1rem !important;
      }

      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile
        .pr-review-snapshot-histogram-msq-container.pr-review-snapshot-histogram-msq-simple
        .pr-review-snapshot-msq-histogram {
        @media (min-width: 62.5rem) {
          padding-left: 0.937rem;
        }
      }

      .p-w-r .pr-slider-root.pr-text-above-root {
        max-width: 100%;
      }

      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple .pr-review-snapshot-snippets {
        width: 100%;
        padding: 0;
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-simple {
        margin-top: 0;
        margin-bottom: 0;
        ${reviewSummaryEnabled ? 'position: relative' : ''};
      }

      ${'' /* Layout */}

      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile,
      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-desktop,
      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-tablet {
        display: flex;
        ${reviewSummaryEnabled ? 'padding-top: 1.5rem' : ''}
      }

      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile {
        flex-flow: column;
      }

      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-desktop,
      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-tablet {
        position: relative;
        margin-bottom: ${reviewSummaryEnabled ? '2rem' : '0'} !important;
        @media (min-width: 780px) and (max-width: 999px) {
          margin: 4.5rem auto 0px;
        }
      }

      .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile {
        position: relative;
        margin: 0;
        padding-top: 1rem;
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-block-container .pr-review-snapshot-block:first-of-type {
        padding-top: 0;
      }

      ${'' /* Stars */}
      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-simple {
        .pr-review-snapshot-block.pr-review-snapshot-block-snippet {
          margin-bottom: 1rem;
          @media (min-width: 62.5rem) {
            margin-bottom: ${reviewSummaryEnabled ? '0' : '2rem'};
            padding-left: ${reviewSummaryEnabled ? '0' : '1.25rem'} !important;
          }
        }
      }

      .p-w-r .pr-review-snapshot.pr-snapshot-mobile .pr-review-snapshot-simple .pr-snippet-rating-decimal {
        display: none;
      }

      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple .pr-review-snapshot-snippets .pr-snippet-rating-count,
      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple .pr-review-snapshot-snippets .pr-snippet-review-count {
        color: rgb(var(--pdp-color-black-1300)) !important;
        font-size: 1.125rem;
        ${theme.font.primary}
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-block .pr-ratings-histogram-bar,
      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-block .pr-ratings-histogram-barValue {
        border-radius: 0;
      }

      .p-w-r .pr-review-snapshot .pr-ratings-histogram .pr-ratings-histogram-barValue {
        border: none;
      }

      ${'' /* Bar Chart */}
      .p-w-r .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile .pr-review-snapshot-simple {
        .pr-review-snapshot-block.pr-review-snapshot-block-histogram {
          margin-bottom: 0;
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-block-container .pr-review-snapshot-block.pr-review-snapshot-block-histogram {
        padding-top: 0;
        padding-left: 0;

        .pr-review-snapshot-block-container {
          order: ${reviewSummaryEnabled ? '1' : 'unset'};
        }
      }

      .p-w-r .pr-review-snapshot .pr-ratings-histogram .pr-histogram-label {
        padding-left: 0;
      }

      .p-w-r .pr-review-snapshot .pr-ratings-histogram .pr-histogram-count {
        padding-right: 0;
        ${theme.font.primary}
      }

      .p-w-r .pr-histogram-cross {
        display: none;
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-block {
        .pr-histogram-label {
          text-decoration: ${theme.color.wh};
          ${theme.font.primary}
        }

        .pr-ratings-histogram-bar {
          background-color: #e5e5e5 !important;
        }

        .pr-ratings-histogram-barValue {
          background-color: ${theme.color.b1} !important;
        }
      }

      ${'' /* Slider */}
      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container {
        width: 100%;

        @media (min-width: 62.5rem) {
          width: 30%;
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container .pr-review-snapshot-msq-histogram .pr-histogram-msq-slider {
        padding-top: 0;
        display: flex;
        flex-direction: column-reverse;

        @media (min-width: 62.5rem) {
          padding: 0.625rem;
          padding-top: 0.5rem;
          padding-bottom: 0;
        }
      }

      .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container .pr-review-snapshot-msq-histogram .pr-histogram-msq-headline {
        font-size: 1rem;
        color: rgb(var(--pdp-color-black-1300));
        ${theme.font.primary}
        padding: 0;

        @media (min-width: 62.5rem) {
          padding: 0.625rem 0.625rem 0 0.625rem;
        }
      }

      .p-w-r
        .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile
        .pr-review-snapshot-histogram-msq-container
        .pr-review-snapshot-msq-histogram
        .pr-histogram-msq-slider {
        max-width: 100%;
        padding: 0 0 0.625rem 0;

        @media (min-width: 62.5rem) {
          margin: 0 0.937rem;
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container {
        display: inline-block;
        margin-top: 1.625rem;

        @media (min-width: 62.5rem) {
          margin-top: 0;
          margin-left: ${reviewSummaryEnabled ? '3.125rem' : '5%'};
        }
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced .pr-review-snapshot-histogram-msq-container .pr-review-snapshot-msq-histogram .pr-histogram-msq-headline {
        color: rgb(var(--pdp-color-black-1300));
        font-size: 16px;
        padding-top: 0;
        padding-bottom: 0;
      }

      .p-w-r .pr-review-snapshot.pr-review-enhanced.pr-snapshot-mobile .pr-review-snapshot-histogram-msq-container {
        margin-top: 2rem;
      }

      .p-w-r,
      .p-w-r .pr-review-snapshot .pr-review-snapshot-simple .pr-review-snapshot-snippets {
        a.pr-snippet-write-review-link {
          position: relative;
          display: inline-block;
          flex: 1;
          height: fit-content;
          background-color: ${theme.color.wh};
          color: ${theme.color.b1} !important;
          border-radius: 0 !important;
          border: 1px solid ${theme.color.b1};
          ${theme.font.secondary}
          text-transform: uppercase;
          font-size: 1rem;
          padding: 12px 15px;
          margin-bottom: 1.75rem;
          text-align: center;
          text-decoration: none;

          @media (min-width: 62rem) {
            ${reviewSummaryEnabled ? 'width: calc(100% - 1.25rem)' : 'margin-left: 4.25rem'}
          }

          &.focus-visible {
            outline: 0;
            box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
          }

          &:hover {
            background-color: ${theme.color.b1} !important;
            color: ${theme.color.wh} !important;
          }
        }
      }

      .p-w-r .pr-review-snapshot {
        .pr-review-snapshot-faceoff,
        .pr-review-snapshot-subratings {
          display: none;
        }
      }
    `;
  };

export const redesignReviewSnapshotStylesForON = (theme: Theme): InterpolationPrimitive => {
  return css`
    background-color: ${theme.color.wh};

    .p-w-r {
      .pr-review-snapshot,
      .pr-rating-stars,
      a.pr-snippet-write-review-link {
        background-color: ${theme.color.wh};
      }
    }
  `;
};
