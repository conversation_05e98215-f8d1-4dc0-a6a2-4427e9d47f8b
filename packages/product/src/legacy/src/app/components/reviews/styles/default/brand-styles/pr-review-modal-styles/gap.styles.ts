// @ts-nocheck
'use client'

import { css, getFontWeight } from "@ecom-next/core/react-stitch";

export const prReviewModalStylesForGAP = (snippetTitle, snippetFormTitle) => () => {
  return css`
    //Applies styles for modal overflow
    .pr-content-collection-form .pr-media-modal .modal__body {
      overflow-y: auto !important;
    }

    .modal__body .p-w-r {
      margin: 0;
      & header,
      & h1,
      & h2,
      & h3,
      & span,
      & div,
      & p,
      & button,
      & .pr-accordion-btn svg {
        -webkit-font-smoothing: subpixel-antialiased;
        line-height: 1.1;
      }
      & h2 {
        color: inherit;
      }

      padding: 0;

      .pr-rating-only_status {
        margin: 1rem 0;
        font-weight: ${getFontWeight('semiBold').fontWeight};
        height: 1rem;
      }

      //Applies styles to rating stars
      .pr-form-group.form-group.pr-rating-form-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow: hidden;
      }

      //Removes overflow scroll from form titles
      .pr-form-control-error-wrapper {
        overflow: hidden;
      }

      //Remove message before rating stars
      h2.pr-rating-only_heading:nth-of-type(1) {
        display: none;
        visibility: hidden;
      }

      //Changes text before form and text before product image in first modal
      h2.pr-rating-only_heading:nth-of-type(2),
      h3.pr-header-title:first-of-type {
        text-align: center;
        width: 100%;
        visibility: hidden;
      }

      h3.pr-header-title:first-of-type::after {
        visibility: visible;
        position: absolute;
        content: '${snippetTitle}';
        pointer-events: all;
        left: 50%;
        transform: translateX(-50%);
      }

      h2.pr-rating-only_heading:nth-of-type(2)::after {
        visibility: visible;
        position: absolute;
        content: '${snippetFormTitle}';
        padding-top: 1.5rem;
        pointer-events: all;
        left: 50%;
        transform: translateX(-50%);
      }

      .pr-header {
        border-bottom: none !important;
      }

      .pr-table-cell.pr-header-product-img {
        padding: 1rem 0;
      }

      .pr-table.pr-header-table {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .pr-header-product-name {
        text-align: center;
        padding-top: 0.5rem;
      }

      //Remove orange background in rating stars
      input:focus + label.pr-star-v4-100-filled,
      input:focus + label.pr-star-v4-0-filled {
        background-color: transparent !important;
        border: none !important;
      }
    }

    .pr-alert-container {
      padding-top: 2rem;

      & .pr-alert_list {
        line-height: normal;
        list-style: none;
        margin: 0;
      }
    }

    #pr-war-form {
      margin-top: 3rem;
    }
  `;
};
