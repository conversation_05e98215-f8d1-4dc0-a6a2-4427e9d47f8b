// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

export const reviewContainerStylesForAT = (theme: Theme): InterpolationPrimitive => {
  const redesignUploadIconStyles = color => {
    const colorHex = color?.replace('#', '');

    return `.pr-btn-fileinput::before {
      content: url("data:image/svg+xml,%3Csvg width='11' height='15' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23${colorHex}' fill-rule='evenodd'%3E%3Cpath d='M4.55 3h1.933l.067 7h-2zM0 12h11v2H0z'/%3E%3Cpath fill-rule='nonzero' d='M5.5.55l4.95 4.95-1.414 1.414L5.499 3.38 1.964 6.914.55 5.5z'/%3E%3C/g%3E%3C/svg%3E");
      margin-right: 0.5rem; 
      margin-top: 0.125rem;
    }`;
  };

  const reviewFormStyles = (theme: Theme, forceReviewForm: boolean) => {
    const modalLeft = forceReviewForm ? 'left: 50%;' : '';

    return css`
      ${theme.crossBrand.font.primary}

      .p-w-r .pr-content-collection-form .pr-media-modal .modal__body {
        ${modalLeft}
        visibility: visible;
        max-height: 90%;
        padding: 1rem 1.75rem;

        @media (min-width: 600px) {
          padding: 2rem;
        }
      }
      .p-w-r .pr-content-collection-form .pr-media-modal {
        .button__close .pr-cross-icon__line {
          stroke: ${theme.color.b1};
        }
        .button__close:hover .pr-cross-icon__line {
          stroke: ${theme.color.b1};
        }
      }
      .p-w-r :focus,
      .p-w-r a:focus {
        stroke: ${theme.crossBrand.color.b1};
      }

      .modal__body .p-w-r {
        .pr-alert-container {
          margin-top: 0.5rem;
        }

        .pr-rating-stars .pr-star-v4 {
          width: 2.5rem !important;
          height: 2.5rem !important;
        }

        .pr-header-required,
        .pr-rating-form-group .pr-control-label {
          position: absolute;
          height: 1px;
          width: 1px;
          overflow: hidden;
          clip: rect(0 0 0 0);
        }

        .pr-header {
          margin-top: 1.25rem;

          .pr-rating-stars {
            margin: 0;
          }

          .pr-header-table {
            position: relative;
            padding-top: 2rem;
          }

          .pr-table-cell {
            display: inline-block;
            max-width: 75%;

            @media (max-width: ${sdsBreakpoint.small}) {
              display: flex;
              padding-left: 0;
              padding-right: 0;
            }
          }

          .pr-header-product-img {
            width: auto;
          }

          .pr-header-title {
            ${theme.crossBrand.font.primary}
            font-size: 1.375rem;
            color: ${theme.color.gray80};
            position: absolute;
            top: 0;
            left: 0;
            margin: 0px;
          }

          .pr-header-product-name {
            font-size: 1.125rem;
          }

          .pr-header-product-name a {
            ${theme.crossBrand.font.secondary};
            color: #595959;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        .pr-header-product-img img {
          margin-left: -10px;
          max-width: 100px;
        }

        .pr-header-product-img img[alt*='Product Image Unavailable'] {
          margin-left: 0px;
        }

        #pr-war-form {
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: auto;
          grid-row-gap: 2rem;
          grid-column-gap: 1rem;

          @media (min-width: 600px) {
            grid-row-gap: 3rem;
          }
        }

        .pr-control-label span {
          font-size: 1.125rem;
          color: ${theme.color.gray80};
        }

        .form-group.pr-rating-form-group .pr-rating-stars {
          margin-bottom: 0;
        }

        .form-group {
          position: relative;
          margin-bottom: 0;
          grid-column-start: 1;
          grid-column-end: 3;
          order: 3;
        }

        .pr-rating-form-group,
        .pr-headline-form-group,
        .pr-comments-form-group,
        .pr-name-form-group {
          order: 1;
        }

        .pr-email_collection-form-group {
          order: 2;
        }

        label,
        legend.pr-control-label {
          ${theme.crossBrand.font.primary}
          margin: 0 0 0.75rem;
        }

        .pr-star-v4.pr-star-v4-0-filled,
        .pr-star-v4.pr-star-v4-100-filled {
          background-size: 2.5rem 2.5rem;
          background-color: white;
        }

        .pr-star-v4,
        .pr-star-v4-0-filled,
        .pr-star-v4-100-filled {
          width: 2.5rem;
          height: 2.5rem;
          margin-right: 0.5rem;
          margin-bottom: 0;
        }

        .pr-file-input-btn-group {
          display: block;
        }

        .pr-btn-fileinput {
          ${theme.crossBrand.font.secondary}
          background-color: ${theme.color.wh};
          color: ${theme.crossBrand.color.b1};
          border: 1px solid ${theme.crossBrand.color.b1};
          width: 100%;
          padding: 0.75rem 0;
          text-align: center;
          font-size: 1rem;
          margin-left: 0;
          display: flex;
          text-transform: uppercase;
          justify-content: center;
        }

        .pr-btn-fileinput:hover {
          background-color: ${theme.crossBrand.color.b1};
          color: ${theme.color.wh};
          border: 1px solid ${theme.crossBrand.color.b1};
        }

        .pr-btn-fileinput svg {
          display: none;
        }

        ${redesignUploadIconStyles(theme.crossBrand.color.b1)};

        .pr-btn-fileinput:hover::before {
          content: url("data:image/svg+xml,%3Csvg width='11' height='15' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23FFFFFF' fill-rule='evenodd'%3E%3Cpath d='M4.55 3h1.933l.067 7h-2zM0 12h11v2H0z'/%3E%3Cpath fill-rule='nonzero' d='M5.5.55l4.95 4.95-1.414 1.414L5.499 3.38 1.964 6.914.55 5.5z'/%3E%3C/g%3E%3C/svg%3E");
        }

        .pr-file-input-btn-group .pr-file-input-label {
          font-size: 1.125rem;
        }

        .pr-accessible-btn {
          background-color: ${theme.color.bk};
          border: none;
          width: 100%;
          padding: 1rem 0;
          margin: 0.5rem auto 2rem;
          text-transform: uppercase;
        }

        .pr-accessible-btn:hover {
          background-color: ${theme.color.g2};
          color: ${theme.color.wh};
        }

        .pr-accessible-btn:focus {
          color: ${theme.color.wh};
        }

        .form-group.pr-height-form-group {
          margin-top: 1rem;
          grid-column-start: 1;
          grid-column-end: 2;
        }

        .form-group.pr-weight-form-group {
          margin-top: 1rem;
          grid-column-start: 2;
          grid-column-end: 3;
        }

        #pr-height-input,
        #pr-weight-input {
          color: ${theme.crossBrand.color.g1};
        }

        #pr-lengthpurchased-input {
          max-width: 100%;
        }

        .pr-width-form-group {
          position: relative;
        }

        .pr-clear-all-radios {
          position: absolute;
          bottom: 50px;
          right: 0;
        }

        .btn-group-radio-horizontal-linked {
          position: relative;
          margin-top: 0;
          display: flex;

          &::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 3px;
            top: 50%;
            transform: translateY(-50%);
            background-color: ${theme.color.gray10};
          }

          .pr-btn {
            position: relative;
            margin: 0;
            padding: 38px 0 0;
            white-space: normal;
            text-align: center;
            text-transform: capitalize;
            height: 26px;
            background-color: transparent;
            border: 0;
            text-transform: capitalize;
          }

          .pr-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            height: 1.25rem;
            width: 1.25rem;
            background-color: ${theme.color.wh};
            border: 2px solid ${theme.color.gray54};
            border-radius: 50%;
          }

          .pr-btn:nth-of-type(1) {
            text-align: left;

            &::before {
              left: 0;
              transform: translate(0, -50%);
            }
          }

          .pr-btn:nth-of-type(2) {
            text-align: center;

            &::before {
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .pr-btn:nth-of-type(3) {
            text-align: right;
            &::before {
              right: 0;
              transform: translate(0, -50%);
            }
          }

          .pr-btn:hover {
            color: ${theme.color.g1};

            &::before {
              border-color: ${theme.color.gray54};
              background-color: ${theme.color.gray54};
            }
          }

          .pr-btn.active {
            color: ${theme.color.g1};
            background-color: inherit;
          }

          .pr-btn.active::before,
          .pr-btn.active:hover::before {
            border-color: ${theme.crossBrand.color.b1};
            background-color: ${theme.crossBrand.color.b1};
          }

          input.focus-visible + label {
            outline: none;
            box-shadow: 0 0 0 0 !important;
          }

          input[type='radio']:focus,
          input[type='radio']:focus + label {
            outline: none;
          }

          input[type='radio'].focus-visible + label::before {
            outline: 0;
            box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
          }
        }

        button.focus-visible,
        a.focus-visible,
        .form-control:focus {
          border-color: inherit;
          outline: 0;
          box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
        }

        .pr-facebook-btn,
        .pr-instagram-btn,
        .pr-media_videourl-form-group,
        .form-group.pr-overallquality-form-group,
        .pr-bottomline-form-group,
        .pr-pros-form-group,
        .pr-cons-form-group,
        .pr-describeyourself-form-group,
        .pr-bestuses-form-group,
        .pr-location-form-group {
          display: none;
        }
      }

      .pr-submit {
        margin-top: 3rem;

        .pr-logo-container {
          margin-top: 3rem;
        }
      }
    `;
  };

  const atStarIcon =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiMzMzMiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';

  const lightGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiNFREVDRUMiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';

  return css`
    background-color: ${theme.color.wh};
    margin-top: 0.25rem;

    .pdp-reviews-widget {
      margin-bottom: 0.25rem;
      padding-bottom: 2.5rem;
      margin-top: 0.25rem;

      @media (min-width: ${sdsBreakpoint.xLarge}) {
        margin-bottom: 0.625rem;
        margin-top: 0.625rem;
      }
    }

    .pr-star-v4.pr-star-v4-100-filled {
      background: url(data:image/svg+xml;base64,${atStarIcon}) no-repeat;
    }

    .pr-star-v4.pr-star-v4-0-filled {
      background: url(data:image/svg+xml;base64,${lightGrayStar}) no-repeat;
    }

    .pr-star-v4.pr-star-v4-25-filled,
    .pr-star-v4.pr-star-v4-50-filled,
    .pr-star-v4.pr-star-v4-75-filled {
      background: none !important;
      padding: 0 !important;

      &::before,
      &::after {
        content: '';
        display: inline-block;
        height: 30px;
      }

      &::before {
        background: url(data:image/svg+xml;base64,${atStarIcon}) no-repeat;
      }

      &::after {
        background: url(data:image/svg+xml;base64,${lightGrayStar}) no-repeat;
      }
    }

    @media (max-width: ${sdsBreakpoint.small}) {
      .pdp-reviews-widget {
        padding-bottom: 1.5rem;
      }

      .pdp-customer-photos-widget {
        margin-bottom: -1rem;
        border-bottom: 0.4rem solid ${theme.color.gray05};
      }
    }

    ${reviewFormStyles(theme, false)}
  `;
};
