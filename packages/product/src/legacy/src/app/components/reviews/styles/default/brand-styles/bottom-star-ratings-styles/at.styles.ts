// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import type { SerializedStyles } from '@emotion/serialize';

export const bottomStarRatingsStylesForAT = (theme: Theme): SerializedStyles => {
  return css`
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 12px;

    & > div > a {
      & > div:last-of-type {
        font-size: 14px;
        text-decoration: none;
        padding-top: 8px;
        border-bottom: 0px;
      }
    }

    .review-ratings {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 0px;

      & > div > div > figure > span,
      & > div > figure > span {
        width: 31.5px;
      }
    }

    .pdp-no-reviews-message {
      padding-top: 32px;
      font-size: 14px;
      font-family: ${theme.brandFont.fontFamily};
    }
  `;
};
