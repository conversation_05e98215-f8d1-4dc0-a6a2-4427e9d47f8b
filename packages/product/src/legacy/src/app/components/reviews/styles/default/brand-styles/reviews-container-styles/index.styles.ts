// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { reviewContainerStylesForAT } from './at.styles';
import { reviewContainerStylesForBR } from './br.styles';
import { reviewContainerStylesForGAP } from './gap.styles';
import { reviewContainerStylesForON } from './on.styles';

export const reviewContainerStyles = (theme: Theme): InterpolationPrimitive => {
  return forBrands(theme, {
    at: reviewContainerStylesForAT(theme),
    br: reviewContainerStylesForBR(theme),
    brfs: reviewContainerStylesForBR(theme),
    gap: reviewContainerStylesForGAP(theme),
    gapfs: reviewContainerStylesForGAP(theme),
    on: reviewContainerStylesForON(theme),
  });
};
