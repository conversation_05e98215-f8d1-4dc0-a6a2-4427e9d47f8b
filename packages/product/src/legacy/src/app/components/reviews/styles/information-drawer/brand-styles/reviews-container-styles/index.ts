// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { drawerReviewContainerStylesForBR } from './br.styles';

export const containerStyles = (theme: Theme, localize = null): InterpolationPrimitive =>
  forBrands(theme, {
    br: drawerReviewContainerStylesForBR(theme, localize),
    brfs: drawerReviewContainerStylesForBR(theme, localize),
  });
