// @ts-nocheck
'use client'

import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";

import { useReviewSummary } from '../../../hooks/use-review-summary';
import { useReviewsDrawer } from '../../../hooks/use-reviews-drawer';
import useTemplateFactory, { Templates } from '../../../hooks/use-template-factory';
import { bottomStarRatingsStyles } from '../styles/default/brand-styles/bottom-star-ratings-styles/index.styles';
import { imageStyles } from '../styles/default/brand-styles/image-styles/index.styles';
import { redesignLoaderPlaceholderStyles } from '../styles/default/brand-styles/loader-palceholder-styles/index.styles';
import { prReviewModalStyles } from '../styles/default/brand-styles/pr-review-modal-styles/index.styles';
import {
  redesignReviewSnapshotStyles,
  useReviewSnapshotStyles,
} from '../styles/default/brand-styles/review-snapshot-styles';
import { reviewContainerStyles } from '../styles/default/brand-styles/reviews-container-styles/index.styles';
import { redesignReviewListStyles, reviewListStyles } from '../styles/default/review-list-styles/review-list.styles';
import { drawerImageStyles } from '../styles/drawer/brand-styles/image-styles';
import { drawerLoaderPlaceholderStyles } from '../styles/drawer/brand-styles/loader-placeholder-styles';
import { drawerPRReviewModalStyles } from '../styles/drawer/brand-styles/pr-review-modal-styles/pr-review-modal.styles';
import { drawerReviewSnapshotStyles } from '../styles/drawer/brand-styles/review-snapshot-styles';
import { drawerReviewContainerStyles } from '../styles/drawer/brand-styles/reviews-container-styles';
import { drawerBottomStarRatingsStyles } from '../styles/drawer/brand-styles/use-bottom-star-ratings-styles';
import { drawerUseReviewSnapshotStyles } from '../styles/drawer/brand-styles/use-review-snapshot-styles';
import {
  drawerRedesignReviewListStyles,
  drawerReviewListStyles,
} from '../styles/drawer/review-list-styles/review-list.styles';

const nonRegularPDP = [
  Templates.HYBRID,
  Templates.LPO,
  Templates.OOS,
  Templates.SPECIALCOLLABOOS,
  Templates.SPECIALOOS,
];

const useStylesByDrawerFlag = (): SerializedStyles => {
  const isReviewsDrawerEnabled = useReviewsDrawer();
  const { locale, brandName, market, featureVariables, enabledFeatures, abSeg, productData } = useAppState();

  const template = useTemplateFactory({
    abSeg,
    enabledFeatures,
    featureVariables,
    locale,
    market,
    params: { brandName },
    productData,
  });

  const shouldShowDrawer = !nonRegularPDP.includes(template) && isReviewsDrawerEnabled;
  const { display: reviewSummaryEnabled, placement: reviewSummaryPlacement } = useReviewSummary();

  let imageStylesValue = imageStyles;
  let redesignLoaderPlaceholderStylesValue = redesignLoaderPlaceholderStyles;
  let redesignReviewListStylesValue = redesignReviewListStyles;
  let reviewListStylesValue = reviewListStyles;
  let reviewContainerStylesValue = reviewContainerStyles;
  let prReviewModalStylesValue = (theme: Theme, reviewsSnippetTitle: string, reviewsSnippetFormTitle: string) =>
    prReviewModalStyles(theme, reviewsSnippetTitle, reviewsSnippetFormTitle);
  let bottomStarRatingsStylesValue = bottomStarRatingsStyles;
  let reviewSnapshotStylesValue = redesignReviewSnapshotStyles;
  let snapshotReviewStylesValue = useReviewSnapshotStyles(reviewSummaryEnabled, reviewSummaryPlacement);

  if (shouldShowDrawer) {
    imageStylesValue = drawerImageStyles;
    redesignLoaderPlaceholderStylesValue = drawerLoaderPlaceholderStyles;
    redesignReviewListStylesValue = drawerRedesignReviewListStyles;
    reviewListStylesValue = drawerReviewListStyles;
    reviewContainerStylesValue = drawerReviewContainerStyles;
    prReviewModalStylesValue = (theme: Theme, reviewsSnippetTitle: string, reviewsSnippetFormTitle: string) =>
      drawerPRReviewModalStyles(reviewsSnippetTitle, reviewsSnippetFormTitle);
    bottomStarRatingsStylesValue = drawerBottomStarRatingsStyles;
    reviewSnapshotStylesValue = drawerReviewSnapshotStyles;
    snapshotReviewStylesValue = drawerUseReviewSnapshotStyles;
  }

  return {
    bottomStarRatingsStylesValue,
    imageStylesValue,
    prReviewModalStylesValue,
    redesignLoaderPlaceholderStylesValue,
    redesignReviewListStylesValue,
    reviewContainerStylesValue,
    reviewListStylesValue,
    reviewSnapshotStylesValue,
    snapshotReviewStylesValue,
  };
};

export default useStylesByDrawerFlag;
