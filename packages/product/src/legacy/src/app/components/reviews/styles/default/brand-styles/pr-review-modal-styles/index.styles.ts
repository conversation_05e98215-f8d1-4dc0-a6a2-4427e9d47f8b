// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { prReviewModalStylesForAT } from './at.styles';
import { prReviewModalStylesForBR } from './br.styles';
import { prReviewModalStylesForGAP } from './gap.styles';
import { prReviewModalStylesForON } from './on.styles';

export const prReviewModalStyles = (
  theme: Theme,
  snippetTitle: string,
  snippetFormTitle: string
): InterpolationPrimitive => {
  return forBrands(theme, {
    at: prReviewModalStylesForAT(snippetTitle, snippetFormTitle),
    br: prReviewModalStylesForBR(snippetTitle, snippetFormTitle),
    brfs: prReviewModalStylesForBR(snippetTitle, snippetFormTitle),
    gap: prReviewModalStylesForGAP(snippetTitle, snippetFormTitle),
    gapfs: prReviewModalStylesForGAP(snippetTitle, snippetFormTitle),
    on: prReviewModalStylesForON(snippetTitle, snippetFormTitle),
  });
};
