// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { drawerBottomStarRatingsStylesForBR } from './br.styles';
import { drawerBottomStarRatingsStylesForGAP } from './gap.styles';

export const drawerBottomStarRatingsStyles = (theme: Theme): InterpolationPrimitive =>
  forBrands(theme, {
    br: drawerBottomStarRatingsStylesForBR,
    brfs: drawerBottomStarRatingsStylesForBR,
    default: '',
    gap: drawerBottomStarRatingsStylesForGAP,
    gapfs: drawerBottomStarRatingsStylesForGAP,
  });
