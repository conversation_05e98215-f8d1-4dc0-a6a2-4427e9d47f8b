// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import {
  redesignReviewSnapshotStylesForAT,
  useReviewSnapshotStylesForAT as reviewSnapshotStylesForAT,
} from './at.styles';
import { redesignReviewSnapshotStylesForBR, useReviewSnapshotStylesForBR } from './br.styles';
import { redesignReviewSnapshotStylesForGAP, useReviewSnapshotStylesForGAP } from './gap.styles';
import { redesignReviewSnapshotStylesForON, reviewSnapshotStylesForON } from './on.styles';

export const useReviewSnapshotStyles =
  (reviewSummaryEnabled, reviewSummaryPlacement) =>
  (theme: Theme): InterpolationPrimitive =>
    forBrands(theme, {
      at: reviewSnapshotStylesForAT({ reviewSummaryEnabled, reviewSummaryPlacement }),
      br: useReviewSnapshotStylesForBR,
      brfs: useReviewSnapshotStylesForBR,
      gap: useReviewSnapshotStylesForGAP,
      gapfs: useReviewSnapshotStylesForGAP,
      on: reviewSnapshotStylesForON({ reviewSummaryEnabled }),
    });

export const redesignReviewSnapshotStyles = (theme: Theme): InterpolationPrimitive =>
  forBrands(theme, {
    at: redesignReviewSnapshotStylesForAT,
    br: redesignReviewSnapshotStylesForBR,
    brfs: redesignReviewSnapshotStylesForBR,
    gap: redesignReviewSnapshotStylesForGAP,
    gapfs: redesignReviewSnapshotStylesForGAP,
    on: redesignReviewSnapshotStylesForON,
  });
