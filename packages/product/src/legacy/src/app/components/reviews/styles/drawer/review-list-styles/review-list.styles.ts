// @ts-nocheck
'use client';

import type { Theme } from '@ecom-next/core/react-stitch';
import { css, forBrands, getFontWeight } from '@ecom-next/core/react-stitch';
import type { InterpolationPrimitive } from '@emotion/serialize';
import { setupRedesignStyles } from '@product-page/legacy/styles';
import { getBrBackgroundColor } from '@product-page/legacy/styles/brand-styles/utils/br.styles';
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

import { useFeatureFlag } from '../../../../../hooks/use-feature-flag';
import { gapBuyBox2024RedesignReviewStyles } from '../../default/review-list-styles/review-list.styles';

export const drawerRedesignReviewListStyles = (theme: Theme) => {
  const brStarStyles = forBrands(theme, {
    br: 'flex: 1;',
    brfs: 'flex: 1;',
  });

  const borderBottom = theme => {
    return forBrands(theme, {
      br: () => 'border-bottom: 1px solid rgba(44, 40, 36, 0.2)',
      brfs: () => 'border-bottom: 1px solid rgba(44, 40, 36, 0.2)',
      default: () => '',
    });
  };

  const brandTextDecorationUnderline = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `text-decoration: underline;`,
      brfs: `text-decoration: underline;`,
    });
  };

  const brandPositionUnderline = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `text-underline-offset: 4px;`,
      brfs: `text-underline-offset: 4px;`,
    });
  };

  const gapFirstReviewButtonStyle = (theme: Theme): InterpolationPrimitive => css`
    margin-top: 20px;
    margin-bottom: 40px;
    @media (max-width: ${sdsBreakpoint.large}) {
      margin-bottom: 20px;
    }

    a {
      background-color: ${theme.color.wh};
      color: ${theme.color.b1} !important;
      border-radius: 0 !important;
      border: 1px solid ${theme.color.b1};
      width: 100%;
      padding: 12px 73px;
      font-size: 16px;
      font-weight: ${theme.fontWeight.regular};
      text-transform: uppercase;

      &:hover {
        background-color: ${theme.color.b1};
        color: ${theme.color.wh} !important;
        text-decoration: none;
      }

      @media (max-width: 375px) {
        padding: 7px 50px;
      }
    }
  `;

  const firstReviewButtonStyle = (theme: Theme): InterpolationPrimitive => {
    const redesignFirstReviewStyle = forBrands(theme, {
      default: '',
      gap: gapFirstReviewButtonStyle,
      gapfs: gapFirstReviewButtonStyle,
    });

    return redesignFirstReviewStyle;
  };

  const snippetStarsBorder = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'border: none',
      brfs: 'border: none',
      default: '',
    });
  };

  return css`
    .p-w-r {
      .pr-review-display,
      .pr-snippet-stars,
      header.pr-rd-main-header > div.pr-rd-review-header-contents,
      .pr-multiselect-button,
      .pr-multiselect-item,
      select.pr-rd-sort {
        background-color: transparent !important;

        @media (max-width: ${sdsBreakpoint.small}) {
          ${borderBottom(theme)};
        }
      }
      .pr-review-display {
        a {
          color: ${forBrands(theme, {
            br: `rgb(var(--pdp-color-black-1400))`,
            brfs: `rgb(var(--pdp-color-black-1400))`,
            default: '',
          })};
          ${brandTextDecorationUnderline(theme)}
          ${brandPositionUnderline(theme)}
        }
        .pr-rd-no-reviews {
          ${firstReviewButtonStyle(theme)};
        }
      }
      .pr-review-display {
        .pr-review {
          ${borderBottom(theme)};
        }
      }
      .pr-snippet-stars {
        ${brStarStyles}
        @media (max-width: ${sdsBreakpoint.small}) {
          ${snippetStarsBorder(theme)};
        }
      }
    }
  `;
};

export const drawerReviewListStyles = (theme: Theme, localize) => {
  const setStylesForBuyBoxRedesign = setupRedesignStyles(theme).isGapBuyBoxRedesign2024;

  const newRedesignFullStarWidth = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 10,
      brfs: 10,
      default: 20,
    });
  };

  const newRedesignCaretIconTransform = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'transform: rotate(-90deg)',
      brfs: 'transform: rotate(-90deg)',
      default: '',
    });
  };

  const fullStarWidth = newRedesignFullStarWidth(theme);

  const caretIconTransform = newRedesignCaretIconTransform(theme);

  const showReviewSearchField = () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const pdpReviewSearch = useFeatureFlag('pdp-review-search');
    return css`
      .pr-rd-main-header-search {
        ${!pdpReviewSearch ? 'display: none' : 'overflow: visible'}
      }
    `;
  };

  const keepReviewsVotingVisible = css`
    &.pr-accordion-collapsed {
      .pr-accordion-content {
        max-height: 60px;
      }
    }
  `;

  const textColor = theme =>
    forBrands(theme, {
      brfs: () => theme.color.g1,
      default: () => theme.color.b1,
      gap: () => theme.color.g1,
      gapfs: () => theme.color.g2,
    });

  const textStyles = theme => {
    const fontStyles = forBrands(theme, {
      br: () => ({
        fontSize: '0.75rem',
        lineHeight: '1.38',
      }),
      brfs: () => ({
        fontSize: '0.75rem',
        lineHeight: '1.4',
      }),
      default: () => ({
        fontSize: '1rem',
        lineHeight: '1.38',
      }),
      gap: () => ({
        fontSize: '.875rem',
        lineHeight: '1.38',
      }),
      gapfs: () => ({
        fontSize: '.875rem',
        lineHeight: '1.25',
      }),
    });

    return css`
      ${theme.font.primary};
      color: ${textColor(theme)};
      ${fontStyles};
    `;
  };

  const redesignMainMargin = theme => {
    const brStyles = `
      margin: 0 1rem;
      @media (max-width: ${sdsBreakpoint.small}) {
        margin: 0 3px;
      }
    `;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '',
    });
  };

  const newRedesignContentBlockMarginBottom = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-bottom: 0 !important',
      brfs: 'margin-bottom: 0 !important',
      default: '',
    });
  };

  const newRedesignPaginationStyles = (theme: Theme): InterpolationPrimitive => {
    const gapPaginationStyles = `
      padding-top: 24px;
    `;
    return forBrands(theme, {
      default: '',
      gap: gapPaginationStyles,
      gapfs: gapPaginationStyles,
    });
  };

  const brandLightColorFactoryStylesReviews = (theme: Theme, fallbackColor?: string): InterpolationPrimitive => {
    return forBrands(theme, {
      br: () => `rgb(var(--pdp-color-black-1400))`,
      brfs: () => `rgb(var(--pdp-color-black-1400))`,
      default: () => fallbackColor,
      gap: () => theme.color.g1,
      gapfs: () => theme.color.g1,
      on: () => theme.color.b1,
    });
  };

  const newRedesignMainHeaderContainerPadding = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '0 15px 15px',
      gap: '0 32px 0px',
      gapfs: '0 32px 0px',
    });
  };

  const newRedesignReviewsContentsWidth = (theme: Theme): InterpolationPrimitive => {
    const widthOnAt = 'width: 44% !important';
    const defaultStyles = 'width: 100% !important';

    return forBrands(theme, {
      at: widthOnAt,
      default: defaultStyles,
      on: widthOnAt,
    });
  };

  const newRedesignMobileWidthColumn = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
    });
  };

  const newRedesignHeaderContentsPadding = (theme: Theme): InterpolationPrimitive => {
    const brStyles = css`
      padding-left: 14px !important;
      padding-right: 6px !important;
    `;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '',
    });
  };

  const newRedesignHeaderContentsJustifyContent = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'justify-content: space-between',
      brfs: 'justify-content: space-between',
      default: '',
      gap: 'justify-content: space-between',
      gapfs: 'justify-content: space-between',
    });
  };

  const newRedesignHeaderContentsOrder = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '3',
      brfs: '3',
      default: '2',
      gap: '3',
      gapfs: '3',
    });
  };

  const newRedesignHeaderContents = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '',
      brfs: '',
      default: '',
      gap: 'white',
      gapfs: 'white',
    });
  };

  const newRedesignHeaderContentsColumnGap = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
    });
  };

  const newRedesignHeaderPadding = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '10px 0',
      gap: '10px 0 32px',
      gapfs: '10px 0 32px',
    });
  };

  const newRedesignHeaderContent = (theme: Theme): InterpolationPrimitive => {
    const commonStyles = theme.color.bk;

    return forBrands(theme, {
      br: commonStyles,
      brfs: commonStyles,
      default: commonStyles,
      gap: commonStyles,
      gapfs: commonStyles,
    });
  };

  const newRedesignMainHeaderSearchSortFontSize = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'font-size: 0.875rem',
      brfs: 'font-size: 0.875rem',
      default: '',
    });
  };

  const newRedesignDrawerMultiSelectFlex = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'flex-grow: 1 !important',
      brfs: 'flex-grow: 1 !important',
      default: 'flex: 1 1 auto',
    });
  };

  const newRedesignDrawerMultiSelectMarginRight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-right: 6px !important',
      brfs: 'margin-right: 6px !important',
      default: '',
    });
  };

  const newRedesignDrawerMultiSelectMobileMarginRight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-right: 1px !important',
      brfs: 'margin-right: 1px !important',
      default: '',
    });
  };

  const newRedesignDrawerMultiSelectMobilePadding = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'padding: 0 3px',
      brfs: 'padding: 0 3px',
      default: '',
    });
  };

  const newRedesignMultiSelectButton = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `rgb(var(--pdp-color-black-1400))`,
      brfs: `rgb(var(--pdp-color-black-1400))`,
      default: brandLightColorFactoryStylesReviews(theme, theme.color.g1),
    });
  };

  const newRedesignContent = (theme: Theme): InterpolationPrimitive => {
    const blackColor = theme.color.bk;
    const blackColorWithOpacity = `rgb(var(--color-black-and-white-black)/66%)`;

    const colorStyles = forBrands(theme, {
      br: blackColor,
      brfs: blackColor,
      default: '',
      gap: blackColorWithOpacity,
      gapfs: blackColorWithOpacity,
    });

    return colorStyles;
  };

  const newRedesignMultiSelectButtonPadding = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'padding-right: 18px',
      brfs: 'padding-right: 18px',
      default: '',
    });
  };

  const newRedesignMultiselctButtonLabelFontStyles = (theme: Theme): InterpolationPrimitive => {
    const brStyles = css`
      font-size: 0.75rem;
      font-weight: 350;
    `;
    const gapStyles = css`
      font-size: 1rem;
    `;
    const labelFontStyles = forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });

    return labelFontStyles;
  };

  const newRedesignstroke = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = brandLightColorFactoryStylesReviews(theme, theme.color.g1);
    const brStyles = `rgb(var(--pdp-color-black-1400))`;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: defaultStyles,
    });
  };

  const newRedesignHeaderContentsMinWidth = (theme: Theme): InterpolationPrimitive => {
    const gapMultiSelectButtonWidth = 'min-width: 85px !important';
    return forBrands(theme, {
      default: '',
      gap: gapMultiSelectButtonWidth,
      gapfs: gapMultiSelectButtonWidth,
    });
  };

  const newRedesignMultiSelectWidth = (theme: Theme): InterpolationPrimitive => {
    const commonMultiSelectWidth = '';
    return forBrands(theme, {
      br: commonMultiSelectWidth,
      brfs: commonMultiSelectWidth,
      default: '70% !important',
      gap: commonMultiSelectWidth,
      gapfs: commonMultiSelectWidth,
    });
  };

  const newRedesignPadding = theme => {
    return forBrands(theme, {
      br: () => '15px 0 30px',
      brfs: () => '15px 0 30px',
      default: () => '15px 0',
    });
  };

  const newRedesignBorderBottom = theme => {
    return forBrands(theme, {
      br: () => 'border-bottom: 1px solid rgba(44, 40, 36, 0.2)',
      brfs: () => 'border-bottom: 1px solid rgba(44, 40, 36, 0.2)',
      default: () => '',
    });
  };

  const newRedesignMultiSelectFlex = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'flex-grow: 1 !important',
      brfs: 'flex-grow: 1 !important',
      default: 'flex: 0 0 auto',
    });
  };

  const newRedesignMultiSelectMarginRight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-right: 7px',
      brfs: 'margin-right: 7px',
      default: '',
    });
  };

  const newRedesignMultiSelectButtonWidth = (theme: Theme): InterpolationPrimitive => {
    const brMultiSelectButtonWidth = 'min-width: auto !important';
    const gapMultiSelectButtonWidth = 'min-width: 85px !important';
    return forBrands(theme, {
      br: brMultiSelectButtonWidth,
      brfs: brMultiSelectButtonWidth,
      default: 'width: auto',
      gap: gapMultiSelectButtonWidth,
      gapfs: gapMultiSelectButtonWidth,
    });
  };

  const newRedesignMainHeaderSearchSortMargin = (theme: Theme): InterpolationPrimitive => {
    const redesignMargin = '0';

    return forBrands(theme, {
      default: 'auto',
      gap: redesignMargin,
      gapfs: redesignMargin,
    });
  };

  const newRedesignMainHeaderSearchSortPadding = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '10px 0',
      brfs: '10px 0',
      default: '15px 5px',
    });
  };

  const newRedesignHeaderSearchSortOrder = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '2',
      brfs: '2',
      default: '1',
    });
  };

  const newRedesignMainHeaderWidth = (theme: Theme): InterpolationPrimitive => {
    const commonWidthContent = '';

    return forBrands(theme, {
      at: commonWidthContent,
      default: '100% !important',
      on: commonWidthContent,
    });
  };

  const newRedesignHeaderSearchFlexGrowth = (theme: Theme): InterpolationPrimitive => {
    const flexGrowthOnAT = '2';

    return forBrands(theme, {
      at: flexGrowthOnAT,
      default: '',
      on: flexGrowthOnAT,
    });
  };

  const newRedesignHeaderSearchSortMaxWidth = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '',
      brfs: '',
      default: '340px',
    });
  };

  const newRedesignHeaderSearchSortWidth = (theme: Theme): InterpolationPrimitive => {
    const commonWidth = '100%';
    const widthOnAt = '';

    return forBrands(theme, {
      br: commonWidth,
      brfs: commonWidth,
      default: widthOnAt,
      gap: commonWidth,
      gapfs: commonWidth,
    });
  };

  const newRedesignHeaderSearchSortDisplay = (theme: Theme): InterpolationPrimitive => {
    const displayFlexColumn = `
      display: flex;
      flex-direction: column;
    `;

    return forBrands(theme, {
      br: displayFlexColumn,
      brfs: displayFlexColumn,
      default: '',
      gap: displayFlexColumn,
      gapfs: displayFlexColumn,
    });
  };

  const newRedesignMainHeaderPadding = (theme: Theme): InterpolationPrimitive => {
    const defaultPadding = '15px 0';
    const brPadding = '10px 0';
    const gapPadding = '0';
    const mainHeaderPadding = forBrands(theme, {
      br: brPadding,
      brfs: brPadding,
      default: defaultPadding,
      gap: gapPadding,
      gapfs: gapPadding,
    });

    return mainHeaderPadding;
  };

  const newRedesignSnapshotHeaderPaddingMobile = (theme: Theme): InterpolationPrimitive => {
    const defaultPadding = '15px 5px';
    const commonPadding = '0px';
    return forBrands(theme, {
      br: commonPadding,
      brfs: commonPadding,
      default: defaultPadding,
      gap: commonPadding,
      gapfs: commonPadding,
    });
  };

  const reviewHeaderFieldWidth = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = 'auto';

    return forBrands(theme, {
      default: defaultStyles,
    });
  };

  const reviewHeaderPadding = (theme: Theme): InterpolationPrimitive => {
    const gapPadding = '0px 5px';
    const defaultPadding = '0px 0px 0px 4px';
    const brPadding = '0px 5px 0 0';

    return forBrands(theme, {
      br: brPadding,
      brfs: brPadding,
      default: defaultPadding,
      gap: gapPadding,
      gapfs: gapPadding,
    });
  };

  const reviewHeaderSearchField = (theme: Theme): InterpolationPrimitive => {
    const brStyle = css`
      .pr-rd-search-container {
        .pr-rd-search-reviews-input {
          @media (min-width: 840px) {
            max-width: 340px;
          }

          @media (max-width: 840px) {
            padding: 0px 5px 0 0 !important;
          }

          input {
            height: 34.55px !important;
            background-color: ${getBrBackgroundColor(theme)};
            border-color: rgb(var(--pdp-color-black-1400)) !important;
            border-radius: 0px;
            ::placeholder {
              color: ${theme.color.g2};
            }
            :focus {
              box-shadow: none !important;
            }
          }

          input + button:focus {
            box-shadow: none !important;
          }

          input:focus + button span svg path {
            fill: white !important;
          }

          input:focus + button {
            border: none !important;
          }

          .pr-rd-search-reviews-icon-button {
            background-color: rgb(var(--pdp-color-black-1400)) !important;
            height: 100% !important;
            width: 34.55px !important;
            svg {
              width: 13px;
              height: 13px;
            }
          }
        }
      }
    `;

    const gapStyle = css`
      .pr-rd-search-container .pr-rd-search-reviews-input {
        input {
          height: 40px !important;
          padding: 8px 40px 8px 8px !important;
          border-color: ${theme.color.bk} !important;
          border-radius: 0px;
          ::placeholder {
            color: ${theme.color.g2};
            font-size: 16px;
            font-weight: 400;
          }
          :focus {
            box-shadow: none !important;
          }
        }

        input + button:focus {
          box-shadow: none !important;
        }

        input:focus + button span svg path {
          fill: white !important;
        }

        input:focus + button {
          border: none !important;
        }

        .pr-rd-search-reviews-icon-button {
          background-color: ${theme.color.bk} !important;
          height: 100% !important;
          width: 40px !important;
          svg {
            width: 13px;
            height: 13px;
          }
        }
      }
    `;

    return forBrands(theme, {
      br: brStyle,
      brfs: brStyle,
      default: '',
      gap: gapStyle,
      gapfs: gapStyle,
    });
  };

  const newRedesignSortTextColor = (theme: Theme): InterpolationPrimitive => {
    const blackColor = theme.color.bk;

    const searchColor = forBrands(theme, {
      br: blackColor,
      brfs: blackColor,
      default: '',
      gap: blackColor,
      gapfs: blackColor,
    });

    return searchColor;
  };

  const newRedesignReviewHeaderSortsPadding = (theme: Theme): InterpolationPrimitive => {
    const brPadding = '0 5px 0 0';

    return forBrands(theme, {
      br: brPadding,
      brfs: brPadding,
      default: '',
    });
  };

  const newRedesignSortBorder = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `rgb(var(--pdp-color-black-1400))`,
      brfs: `rgb(var(--pdp-color-black-1400))`,
      default: brandLightColorFactoryStylesReviews(theme, theme.color.g1),
    });
  };

  const newRedesignSortSelectFontSize = (theme: Theme): InterpolationPrimitive => {
    const gapFontSize = 'font-size: 16px;';
    const fontSizeStyles = forBrands(theme, {
      default: '',
      gap: gapFontSize,
      gapfs: gapFontSize,
    });

    return fontSizeStyles;
  };

  const brandDropdownCaretPosition = theme =>
    forBrands(theme, {
      br: () => 'top: 35px; right: 8px;',
      brfs: () => 'top: 35px; right: 8px;',
      gap: () => 'top: 42px; right: 8px;',
      gapfs: () => 'top: 42px; right: 8px;',
    });

  const brandDropdownCaretPositionMobile = theme =>
    forBrands(theme, {
      br: () => 'top: 42px; right: 11px;',
      brfs: () => 'top: 42px; right: 11px;',
      gap: () => 'top: 48px; right: 12px;',
      gapfs: () => 'top: 48px; right: 12px;',
    });

  const brResetStyle = (theme: Theme, fallback?: string): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'none',
      brfs: 'none',
      default: fallback,
    });
  };

  const brandImageColor = (theme: Theme, newColor: string, fallbackColor: string): InterpolationPrimitive => {
    return forBrands(theme, {
      br: newColor,
      brfs: newColor,
      default: fallbackColor,
    });
  };

  const brDropdownCaretIcon =
    'PHN2ZyB3aWR0aD0iMTVweCIgaGVpZ2h0PSIxNXB4IiB2aWV3Qm94PSItOSAtMTAgNTIgNTIiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGc+PHBvbHlsaW5lIGNsYXNzPSJwci1jYXJldC1pY29uX19saW5lIiBmaWxsPSJub25lIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iNiIgcG9pbnRzPSIyMy43LDMxLjUgOC4zLDE2IDIzLjcsMC41ICI+PC9wb2x5bGluZT48L2c+PC9zdmc+';
  const brfsDropdownCaretIcon =
    'PHN2ZyB3aWR0aD0iMTVweCIgaGVpZ2h0PSIxNXB4IiB2aWV3Qm94PSItOSAtMTAgNTIgNTIiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGc+PHBvbHlsaW5lIGNsYXNzPSJwci1jYXJldC1pY29uX19saW5lIiBmaWxsPSJub25lIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iNiIgcG9pbnRzPSIyMy43LDMxLjUgOC4zLDE2IDIzLjcsMC41ICI+PC9wb2x5bGluZT48L2c+PC9zdmc+';

  const defaultDropdownCaretIcon =
    'PHN2ZyB3aWR0aD0iMTVweCIgaGVpZ2h0PSIxNXB4IiB2aWV3Qm94PSItOSAtMTAgNTIgNTIiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGc+PHBvbHlsaW5lIGNsYXNzPSJwci1jYXJldC1pY29uX19saW5lIiBmaWxsPSJub25lIiBzdHJva2U9IiMzMzMiIHN0cm9rZS13aWR0aD0iNiIgcG9pbnRzPSIyMy43LDMxLjUgOC4zLDE2IDIzLjcsMC41ICI+PC9wb2x5bGluZT48L2c+PC9zdmc+';

  const gapDropdownCaretIcon = defaultDropdownCaretIcon;
  const gapfsDropdownCaretIcon = defaultDropdownCaretIcon;

  const lightChevron =
    'PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEuNSA0TDYgOEwxMC41IDQiIHN0cm9rZT0iI0Y2RjRFQiIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=';
  const crossBrandCheckmark =
    'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+SWNvbjwvdGl0bGU+CiAgICA8ZyBpZD0iUG93ZXItUmV2aWV3cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ik1vYmlsZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTE3LjAwMDAwMCwgLTYxNjMuMDAwMDAwKSI+CiAgICAgICAgICAgIDxnIGlkPSJJY29uIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxNy4wMDAwMDAsIDYxNjMuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNNywxNCBDMTAuODY1OTkzMiwxNCAxNCwxMC44NjU5OTMyIDE0LDcgQzE0LDMuMTM0MDA2NzUgMTAuODY1OTkzMiwwIDcsMCBDMy4xMzQwMDY3NSwwIDAsMy4xMzQwMDY3NSAwLDcgQzAsMTAuODY1OTkzMiAzLjEzNDAwNjc1LDE0IDcsMTQgWiIgaWQ9IkNvbWJpbmVkLVNoYXBlIiBmaWxsPSIjMDAwMDAwIj48L3BhdGg+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iUmVjdGFuZ2xlIiBmaWxsPSIjRkZGRkZGIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg3LjIyMDUwMCwgNi41MjA1MDApIHJvdGF0ZSgtMzE1LjAwMDAwMCkgdHJhbnNsYXRlKC03LjIyMDUwMCwgLTYuNTIwNTAwKSAiIHBvaW50cz0iNy43IDIuOCA5LjU0MSAyLjggOS41NDEgMTAuMjQxIDQuOSAxMC4yNDEgNC45IDguNDU4ODc3NDcgNy43IDguNDU4ODc3NDciPjwvcG9seWdvbj4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';

  const grayCheckmark =
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcuMDAxMDYgMTIuNjQ0N0MxMC4xMTkgMTIuNjQ0NyAxMi42NDY3IDEwLjExNzEgMTIuNjQ2NyA2Ljk5OTExQzEyLjY0NjcgMy44ODExMyAxMC4xMTkgMS4zNTM1MiA3LjAwMTA2IDEuMzUzNTJDMy44ODMwOSAxLjM1MzUyIDEuMzU1NDcgMy44ODExMyAxLjM1NTQ3IDYuOTk5MTFDMS4zNTU0NyAxMC4xMTcxIDMuODgzMDkgMTIuNjQ0NyA3LjAwMTA2IDEyLjY0NDdaIiBmaWxsPSIjRjZGNEVCIiBzdHJva2U9IiNGNkY0RUIiLz4KPHBhdGggZD0iTTEwLjA3NTMgNS42NTkyMUw2LjE0NjI1IDkuNjA1NTJDNi4wNjE1NSA5LjY5MDE0IDUuOTI1MzggOS42NTk1OCA1Ljg0MDk4IDkuNTc0ODhMNS4zODI5MSA5LjEyODgyQzUuMzgxMzQgOS4xMjYyNiA1LjM4MDQgOS4xMzE0NyA1LjM3ODUyIDkuMTI5NTJMMy45MjY4MiA3LjY2NDUzQzMuODQyMSA3LjU3OTg5IDMuODQyMSA3LjQ0NDQgMy45MjY4MiA3LjM2MDA4TDQuMzg0ODggNi44OTk4N0M0LjQ2ODk3IDYuODE1NTUgNC42MDU3NiA2LjgxNjE5IDQuNjkwMTYgNi45MDExNUw1Ljk5NTk3IDguMjIzODhMOS4zMTIyOSA0Ljg5MTk3QzkuMzk2OTkgNC44MDY2OCA5LjUzMzE4IDQuODA3MDEgOS42MTc1NyA0Ljg5MTk3TDEwLjA3NTYgNS4zNTIxOEMxMC4xNjAzIDUuNDM1ODYgMTAuMTYwMyA1LjU3NDI1IDEwLjA3NTYgNS42NTg4OSIgZmlsbD0iIzJDMjgyNCIvPgo8L3N2Zz4K';

  const brandDropdownCaret = theme =>
    forBrands(theme, {
      br: () => brDropdownCaretIcon,
      brfs: () => brfsDropdownCaretIcon,
      gap: () => gapDropdownCaretIcon,
      gapfs: () => gapfsDropdownCaretIcon,
    });

  const newRedesignDrawerReviewStylesChevron = theme => {
    return forBrands(theme, {
      br: brandImageColor(theme, brDropdownCaretIcon, brandDropdownCaret(theme)),
      brfs: brandImageColor(theme, brDropdownCaretIcon, brandDropdownCaret(theme)),
      default: brandImageColor(theme, lightChevron, brandDropdownCaret(theme)),
    });
  };

  const newRedesignHeaderSortMaxWidth = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '',
      brfs: '',
      default: '400px',
      gap: '',
      gapfs: '',
    });
  };

  const newRedesignSnapshotReviewTotalTextAlign = (theme: Theme, fallBack: string): InterpolationPrimitive => {
    const textAlign = forBrands(theme, {
      br: 'left',
      brfs: 'left',
      default: fallBack,
      gap: 'center',
      gapfs: 'center',
    });
    return textAlign;
  };

  const newRedesignSnapshotReviewTotalOrderMobile = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '',
      brfs: '',
      default: '3',
    });
  };

  const redesignFontWeight = theme => {
    const defaultFontWeight = theme.fontWeight.light;

    return forBrands(theme, {
      br: theme.fontWeight.regular,
      brfs: theme.fontWeight.regular,
      default: defaultFontWeight,
      gap: theme.fontWeight.regular,
      gapfs: theme.fontWeight.regular,
    });
  };

  const fontColorStylesReviews = (theme: Theme, fallbackColor?: string): InterpolationPrimitive => {
    return forBrands(theme, {
      br: () => `rgb(var(--pdp-color-black-1400))`,
      brfs: () => `rgb(var(--pdp-color-black-1400))`,
      default: () => fallbackColor,
      gap: () => theme.color.g1,
      gapfs: () => theme.color.g1,
    });
  };

  const newRedesignReviewsTotal = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = fontColorStylesReviews(theme, theme.color.gray90);
    const reviewsTotalStyles = forBrands(theme, {
      br: theme.color.bk,
      brfs: theme.color.bk,
      default: defaultStyles,
    });

    return reviewsTotalStyles;
  };

  const newRedesignReviewsKeyline = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'rgba(44, 40, 36, 0.2)',
      brfs: 'rgba(44, 40, 36, 0.2)',
      default: '#ccc',
    });
  };

  const newRedesignReviewsTotalBorderTop = (theme: Theme): InterpolationPrimitive => {
    const gapBorderTop = `border-top: 1px solid ${newRedesignReviewsKeyline(theme)} !important;`;
    return forBrands(theme, {
      default: '',
      gap: gapBorderTop,
      gapfs: gapBorderTop,
    });
  };

  const newRedesignReviewsTotalMargin = (theme: Theme): InterpolationPrimitive => {
    const gapBorderMargin = '32px 0 10px 0 !important';
    return forBrands(theme, {
      default: '',
      gap: gapBorderMargin,
      gapfs: gapBorderMargin,
    });
  };

  const redesignLineHeight = theme =>
    forBrands(theme, {
      br: 'line-height: 17px;',
      brfs: 'line-height: 17px;',
      default: '',
    });

  const newRedesignSnapshotReviewTotalOrderDesktop = (theme: Theme): InterpolationPrimitive => {
    const gapOrder = '3';

    return forBrands(theme, {
      default: '1',
      gap: gapOrder,
      gapfs: gapOrder,
    });
  };

  const newRedesignReviewsTotalPadding = (theme: Theme): InterpolationPrimitive => {
    const gapBorderPadding = 'padding: 20px 0';
    return forBrands(theme, {
      default: '0 0 16px',
      gap: gapBorderPadding,
      gapfs: gapBorderPadding,
    });
  };

  const newRedesignReviewTotalFontSizeDesktop = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = '14px';
    return forBrands(theme, {
      br: '0.75rem',
      brfs: '0.75rem',
      default: defaultStyles,
    });
  };

  const newRedesignReviewPadding = (theme: Theme): InterpolationPrimitive => {
    const defaultPadding = '20px 0 20px';
    const brPadding = '6px 0 0';
    const gapPadding = '32px 0 13px';
    const reviewPadding = forBrands(theme, {
      br: brPadding,
      brfs: brPadding,
      default: defaultPadding,
      gap: gapPadding,
      gapfs: gapPadding,
    });

    return reviewPadding;
  };

  const newRedesignReviewMargin = (theme: Theme, fallBack: string): InterpolationPrimitive => {
    return forBrands(theme, {
      default: fallBack,
      gap: '0 32px',
      gapfs: '0 32px',
    });
  };

  const newRedesignReviewHeaderStyles = (theme: Theme): InterpolationPrimitive => {
    const brStyles = css`
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      margin-bottom: 0;
    `;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '',
    });
  };

  const reviewsHeadlineStyles = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = `
    color: ${theme.color.g1};
    letter-spacing: 1px;
    text-transform: uppercase;
  `;

    const brStyles = `
      color: rgb(var(--pdp-color-black-1400));
      flex: .99;
    `;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignDrawerFontSize = theme =>
    forBrands(theme, {
      br: () => '0.875rem',
      brfs: () => '0.875rem',
      default: () => `1.125rem`,
      gap: () => '1.5rem',
      gapfs: () => '1.5rem',
    });

  const headerStyles = theme => css`
    ${theme.font.primary};
    font-size: ${newRedesignDrawerFontSize(theme)};
    ${reviewsHeadlineStyles(theme)};
  `;

  const newRedesignStarRatingHeight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'height: 17px',
      brfs: 'height: 17px',
      default: '',
    });
  };

  const newRedesignStarsHeight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '',
      brfs: '',
      default: '20px !important',
    });
  };

  const newRedesignStarsBackgroundSize = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '10px',
      brfs: '10px',
      default: '20px 20px',
    });
  };

  const partialStars = starPercentage => {
    switch (starPercentage) {
      case 25:
        return `
          &:before {
            width: 33%;
          }
          &:after {
            width: 67%;
            background-position-x: 100%;
          }
        `;
      case 50:
        return `
          &:before {
            width: 50%;
          }
          &:after {
            width: 50%;
            background-position-x: 100%;
          }
        `;
      case 75:
        return `
          &:before {
            width: 67%;
          }
          &:after {
            width: 33%;
            background-position-x: 100%;
          }
        `;
      default:
        return `
          &:before {
            width: 100%;
          }
          &:after {
            width: 0;
          }
        `;
    }
  };

  const newRedesignPartialStar75MarginTop = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-top: 1px',
      brfs: 'margin-top: 1px',
      default: '',
    });
  };

  const newRedesignDescriptionDisplay = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: 'flex',
      gap: 'block',
      gapfs: 'block',
    });
  };

  const newRedesignDescriptionFlex = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'flex-wrap: wrap',
      brfs: 'flex-wrap: wrap',
      default: '',
    });
  };

  const newRedesignReviewDrawerDetailsWidth = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = `margin-bottom: 0;`;

    return forBrands(theme, {
      br: 'width: 100%',
      brfs: 'width: 100%',
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignTextStyles = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = fontColorStylesReviews(theme, theme.color.b1);
    const brStyles = `rgb(var(--pdp-color-black-1400))`;
    const gapStyles = theme.color.bk;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: defaultStyles,
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const reviewTextStyles = theme => {
    const fontStyles = forBrands(theme, {
      br: () => ({
        fontSize: '0.75rem !important',
        fontWeight: 'normal',
        lineHeight: '1.38',
      }),
      brfs: () => ({
        fontSize: '0.75rem !important',
        fontWeight: 'normal',
        lineHeight: '1.4',
      }),
      default: () => ({
        fontSize: '1rem',
        lineHeight: '1.38',
      }),

      gap: () => ({
        fontSize: '.875rem',
        lineHeight: '1.38',
      }),
      gapfs: () => ({
        fontSize: '.875rem',
        lineHeight: '1.25',
      }),
    });

    return css`
      ${theme.font.primary};
      color: ${newRedesignTextStyles(theme)} !important;
      ${fontStyles};
    `;
  };

  const newRedesignindividualReviewHeight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '25px',
      gap: 'auto',
      gapfs: 'auto',
    });
  };

  const newRedesignSubratingsDefListColor = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `rgb(var(--pdp-color-black-1400))`,
      brfs: `rgb(var(--pdp-color-black-1400))`,
      default: '',
    });
  };

  const newRedesignSubratingsDefListFontWeight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 350,
      brfs: 350,
      default: 400,
    });
  };

  const newRedesignSubratingsDefListDisplay = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
      gap: 'display: block',
      gapfs: 'display: block',
    });
  };

  const reviewSliderLabelStyles = theme => {
    const fontStyles = forBrands(theme, {
      br: () => ({
        fontSize: '0.75rem',
        lineHeight: '1.38',
      }),
      brfs: () => ({
        fontSize: '0.75rem',
        lineHeight: '1.4',
      }),
      default: () => ({
        fontSize: '.875rem',
        lineHeight: '1.38',
      }),
      gap: () => ({
        fontSize: '.625rem',
        lineHeight: '1.38',
      }),
      gapfs: () => ({
        fontSize: '.625rem',
        lineHeight: '1.25',
      }),
    });

    return css`
      ${theme.font.primary};
      color: ${fontColorStylesReviews(theme, textColor(theme))};
      ${fontStyles};
    `;
  };

  const newRedesignPaddingReviewer = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
      gap: '4px',
      gapfs: '4px',
    });
  };

  const newRedesignReviewerDetailsFontSize = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '14px',
      brfs: '14px',
      default: '14px !important',
    });
  };

  const newRedesignReviewerInfo = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
      gap: 'clear: left !important;',
      gapfs: 'clear: left !important;',
    });
  };

  const newRedesignAuthorSubmissionDateMarginLeft = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'margin-left: 3px',
      brfs: 'margin-left: 3px',
      default: '',
    });
  };

  const newRedesignAuthorSubmissionDateFloat = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = 'left';
    return forBrands(theme, {
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newReviewerTypeDiplay = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = 'block';
    return forBrands(theme, {
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignReviewerInfoAlignment = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
      gap: `display: initial;
      padding-right: 1rem;`,
      gapfs: `display: initial;
      padding-right: 1rem;`,
    });
  };

  const newRedesignReviewerTypeVerticalAlign = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'vertical-align: bottom',
      brfs: 'vertical-align: bottom',
      default: '',
    });
  };

  const newRedesignReviewerTypeFontSizeMobile = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '0.75rem',
      brfs: '0.75rem',
      default: '0.875rem',
    });
  };

  const newRedesignDisclosurePosition = (theme: Theme): InterpolationPrimitive => {
    const commonStyles = 'static';

    return forBrands(theme, {
      br: commonStyles,
      brfs: commonStyles,
      default: 'absolute',
      gap: commonStyles,
      gapfs: commonStyles,
    });
  };

  const newRedesignDisclosureDisplay = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = 'contents';
    return forBrands(theme, {
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignDisclosureWidth = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      default: '',
      gap: '100%',
      gapfs: '100%',
    });
  };

  const newRedesignReviewDisclosureLeft = (theme: Theme): InterpolationPrimitive => {
    const disclosureLeftStyles = forBrands(theme, {
      br: '0px',
      brfs: '0px',
      default: '-3px',
      gap: '-5px',
      gapfs: '-5px',
    });

    return disclosureLeftStyles;
  };

  const newRedesignReviewDisclosureMargin = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      at: '0 0 10px 0 !important',
      br: '0',
      brfs: '0',
      default: '0 0 10px 0',
      gap: '0 0 10px 0 !important',
      gapfs: '0 0 10px 0 !important',
      on: '0 0 10px 0 !important',
    });
  };

  const newRedesignReviewDisclosureBottom = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      at: '0.8em',
      default: '0.8em',
      gap: '0.8em',
      gapfs: '0.8em',
      on: '0em',
    });
  };

  const newRedesignVerifiedBuyerLeft = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: 'left: 4px',
      brfs: 'left: 4px',
      default: '',
    });
  };

  const newRedesignSnapshotDescriptionTextWidth = (theme: Theme): InterpolationPrimitive => {
    const gapWidth = '100%';
    return forBrands(theme, {
      default: '75%',
      gap: gapWidth,
      gapfs: gapWidth,
    });
  };

  const newRedesignSnapshotDescriptionTextMargin = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '0',
      brfs: '0',
      default: '15px 0 5px',
    });
  };

  const newRedesignAccordionMarginBottom = (theme: Theme): InterpolationPrimitive => {
    const defaultMargin = '30px';
    const accordionMargin = forBrands(theme, {
      br: '5px',
      brfs: '5px',
      default: defaultMargin,
      gap: '0px',
      gapfs: '0px',
    });

    return accordionMargin;
  };

  const newRedesignAccordionBtnFontSize = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '0.75rem',
      brfs: '0.75rem',
      default: '0.875rem',
      gap: '1rem',
      gapfs: '1rem',
    });
  };

  const newRedesignSizeFitSliderNodeActiveBackground = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: `rgb(var(--pdp-color-black-1400))`,
      brfs: `rgb(var(--pdp-color-black-1400))`,
      default: theme.color.bk,
    });
  };

  const newRedesignSliderWidth = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = 'width: 100%;';

    return forBrands(theme, {
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignSizeFitSliderNodeActiveDimension = (theme: Theme): InterpolationPrimitive => {
    const commonStyles = '14px';
    return forBrands(theme, {
      br: commonStyles,
      brfs: commonStyles,
      default: '16px',
      gap: commonStyles,
      gapfs: commonStyles,
    });
  };

  const newRedesignSizeFitSliderNodeColor = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: theme.brGreyColor2,
      brfs: theme.brGreyColor2,
      default: theme.color.gray20,
    });
  };

  const newRedesignSizeFitSliderNodeHeight = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '7px',
      brfs: '7px',
      default: '8px',
    });
  };

  const newRedesignSizeFitSliderNodeWidth = (theme: Theme): InterpolationPrimitive => {
    const brStyles = '7px';
    const gapStyles = '8px';

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: '7px',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignSizeFitSliderLineColor = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: theme.brGreyColor2,
      brfs: theme.brGreyColor2,
      default: theme.color.gray20,
    });
  };

  const newRedesignSizeFitSliderLineWidth = (theme: Theme): InterpolationPrimitive => {
    return forBrands(theme, {
      br: '2px',
      brfs: '2px',
      default: '3px',
    });
  };

  const reviewsDetailsIcon = (theme: Theme): InterpolationPrimitive => {
    const gapStyles = css`
      display: none;
    `;

    return forBrands(theme, {
      default: '',
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const redesignAccordionButton = (theme: Theme): InterpolationPrimitive => {
    const defaultColor = fontColorStylesReviews(theme, theme.color.g1);
    const brStyles = 'rgba(44, 40, 36, 1)';
    const gapStyles = theme.color.bk;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: defaultColor,
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const linkStyles = theme => {
    const linkOnHover = forBrands(theme, {
      at: () => 'none',
      br: () => 'none',
    });

    return css`
      color: ${fontColorStylesReviews(theme, theme.color.b1)} !important;
      ${theme.font.primary};
      text-decoration: underline;
      &:hover {
        text-decoration: ${linkOnHover};
      }
    `;
  };

  const reviewVotingTextStyle = theme => {
    return forBrands(theme, {
      at: () => `
            font-weight: ${getFontWeight('demiBold').fontWeight};
            font-size: 1rem;
      `,
      br: () => `
            font-weight: ${getFontWeight('regular').fontWeight};
            font-size: 0.75rem;
      `,
      brfs: () => `
            font-weight: ${getFontWeight('regular').fontWeight};
            font-size: 0.75rem;
      `,
      on: () => `
            font-weight: ${getFontWeight('medium').fontWeight};
            font-size: 1rem;
      `,
    });
  };

  const showReviewVoting = () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const pdpReviewVoting = useFeatureFlag('pdp-review-voting');
    return css`
      .pr-rd-helpful-action-legend,
      .pr-rd-helpful-action-btn-group {
        ${!pdpReviewVoting ? 'display: none' : 'overflow: visible'};
      }
      ${pdpReviewVoting &&
      ` .pr-helpful-voting,
        .pr-rd-helpful-action-btn-group {
          display: flex;
          margin-left: 5px;
          & .pr-helpful-btn[aria-disabled] {
            opacity: 60%;
          }
          & .pr-helpful-btn:hover
           {
            background: none;
            > span {
              > svg {
                > g {
                  > path {
                    fill: #767676;
                  }
                }
              }
            }
            & .pr-helpful-count{
              color: #767676;
            }
          }
          & .pr-helpful-btn:focus {
            box-shadow: none
          }
          & .pr-helpful-btn {
            padding-top: 2.2px;
            padding-bottom: 2.2px;
            padding-left: 3.2px;
            padding-right: 3.2px;
            width: 45px;
            height: 24px;
            line-height: 0px;
            position: relative;
            border: none;
            margin: 0px;
            margin-right: ${forBrands(theme, {
              at: '2px',
              default: '-6px',
              on: '2px',
            })};
            & .pr-helpful-count {
              font-size: 12px;
            }
            > span {
              width: 0px;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              > svg {
                width: 24px;
              }
            }
            & .pr-thumbs-icon-down {
              top: 50%;
            }
            & .pr-helpful-count {
              right: 15px;
              font-weight: ${getFontWeight('semiBold').fontWeight};
            }
          }
          & button.pr-helpful-btn.pr-helpful-yes.pr-helpful-active,
          button.pr-helpful-btn.pr-helpful-no.pr-helpful-active {
            background-color: transparent !important;
            > span {
              > svg {
                > g {
                  > path {
                    fill: #4B72A2 !important;
                  }
                }
              }
            }
            & .pr-helpful-count{
              color: #4B72A2 !important;
            }
          }
        }
        .pr-rd-helpful-action-group {
          display: flex;
          align-items: center;
          ${!theme.isRedesignReviewsVotingEnabled && `overflow-y: hidden;`}
        }
        .pr-rd-helpful-action-legend {
          & .pr-rd-helpful-text {
            color: ${theme.color.bk};
            white-space: nowrap;
            ${reviewVotingTextStyle(theme)}
          }
        }
        .pr-rd-helpful-text {
          vertical-align: top;
        }
        .pr-rd-flag-review-container {
          position: absolute;
          bottom: 2vh;
        }`}
    `;
  };

  const reviewListPaginationTextMargin = theme => {
    const marginStyleBP1 = forBrands(theme, {
      br: () => ({
        marginBottom: '-31px',
      }),
      default: () => ({
        marginBottom: '-33px',
      }),
      gap: () => ({
        marginBottom: '-31px',
      }),
    });
    const marginStyleBP2 = forBrands(theme, {
      br: () => ({
        marginBottom: '-19px !important',
      }),
      default: () => ({
        marginBottom: '-23px !important',
      }),
      gap: () => ({
        marginBottom: '-20px !important',
      }),
    });

    return css`
      margin-bottom: 0;
      @media (min-width: 540px) {
        ${marginStyleBP1};
      }
      @media (min-width: 790px) {
        ${marginStyleBP2};
      }
    `;
  };

  const footerTextColor = (theme: Theme): InterpolationPrimitive => {
    const defaultStyles = fontColorStylesReviews(theme, theme.color.b1);
    const brStyles = `rgb(var(--pdp-color-black-1400))`;
    const gapStyles = theme.color.gray60;

    return forBrands(theme, {
      br: brStyles,
      brfs: brStyles,
      default: defaultStyles,
      gap: gapStyles,
      gapfs: gapStyles,
    });
  };

  const newRedesignPaginationBtnTextStyles = (theme: Theme): InterpolationPrimitive => {
    const gapTextStyles = `
      color: ${theme.color.b1} !important;
      font-size: 16px;
    `;
    return forBrands(theme, {
      default: '',
      gap: gapTextStyles,
      gapfs: gapTextStyles,
    });
  };

  const gapPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzEyMjM0NCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';
  const onPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzAwMzc2NCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';
  const brPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzAwMDAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';
  const atPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzMzMzMzMyIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';
  const gapfsPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzEyMjM0NCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';
  const brfsPaginationArrowIcon =
    'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzAwMDAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+';

  const brandPaginationArrow = theme =>
    forBrands(theme, {
      at: () => atPaginationArrowIcon,
      br: () => brPaginationArrowIcon,
      brfs: () => brfsPaginationArrowIcon,
      gap: () => gapPaginationArrowIcon,
      gapfs: () => gapfsPaginationArrowIcon,
      on: () => onPaginationArrowIcon,
    });

  const reviewListPaginationCaretPositionLeft = theme =>
    forBrands(theme, {
      at: () => ({
        left: '0',
        top: '9px',
      }),
      br: () => ({
        left: '0',
        top: '6px',
      }),
      brfs: () => ({
        left: '0',
        top: '7px',
      }),
      default: () => ({
        left: '0',
        top: '7px',
      }),
      gap: () => ({
        left: '0',
        top: '6px',
      }),
      gapfs: () => ({
        left: '0',
        top: '7px',
      }),
      on: () => ({
        left: '0',
        top: '7px',
      }),
    });

  const brandCheckStyleByNewRedesign = (theme: Theme, newStyle: any, fallback?: any): InterpolationPrimitive => {
    return forBrands(theme, {
      br: newStyle,
      brfs: newStyle,
      default: fallback,
    });
  };

  const reviewListPaginationCaretPositionRight = theme =>
    forBrands(theme, {
      at: () => ({
        left: '0',
        top: '9px',
      }),
      br: () => ({
        left: '0',
        top: '6px',
      }),
      brfs: () => ({
        left: '0',
        top: '7px',
      }),
      default: () => ({
        left: '0',
        top: '7px',
      }),
      gap: () => ({
        left: '0',
        top: '6px',
      }),
      gapfs: () => ({
        left: '0',
        top: '7px',
      }),
      on: () => ({
        left: '0',
        top: '7px',
      }),
    });

  return css`
    header,
    h1,
    h2,
    h3,
    span,
    div,
    p,
    button,
    .pr-accordion-btn svg {
      ${textStyles(theme)};
      box-shadow: none;
    }

    .pr-review-display {
      ${redesignMainMargin(theme)}
      h1 {
        color: ${forBrands(theme, {
          br: `${theme.whiteCreame} !important`,
          brfs: `${theme.whiteCreame} !important`,
          default: '',
        })};
      }
    }

    .pr-review-filter-clear-all {
      color: ${forBrands(theme, {
        br: `${theme.whiteCreame} !important`,
        brfs: `${theme.whiteCreame} !important`,
        default: '',
      })};
    }
    .pr-rd-header .pr-rd-content-block {
      margin-bottom: 0 !important;
    }

    .pr-read-review .pr-rd-content-block,
    .p-w-r .pr-review-display .pr-rd-content-block {
      margin-bottom: 0 !important;
      overflow: visible;

      .pr-read-review .pr-rd-content-block,
      .p-w-r .pr-review-display .pr-rd-content-block {
        ${newRedesignContentBlockMarginBottom(theme)};
      }
    }
    .p-w-r .pr-review-display .pr-rd-pagination {
      ${newRedesignPaginationStyles(theme)}
    }

    .pr-alert div {
      color: unset;
    }

    .pr-rd-flag-review-btn {
      color: ${brandLightColorFactoryStylesReviews(theme)} !important;
    }

    .pr-rd-main-header {
      display: flex;
      flex-wrap: wrap;
      padding: ${newRedesignMainHeaderContainerPadding(theme)} !important;
      margin-bottom: 0 !important;

      > div:first-child {
        ${newRedesignReviewsContentsWidth(theme)};
        @media (max-width: 840px) {
          ${newRedesignMobileWidthColumn(theme)}
        }
      }

      span + div {
        order: 2;
        width: 100%;
      }

      .pr-rd-review-header-contents {
        display: none;
        flex-wrap: wrap;
        ${newRedesignHeaderContentsPadding(theme)};
        ${newRedesignHeaderContentsJustifyContent(theme)};
        order: ${newRedesignHeaderContentsOrder(theme)};
        flex-grow: 1;
        background-color: ${newRedesignHeaderContents(theme)} !important;
        column-gap: ${newRedesignHeaderContentsColumnGap(theme)};
        padding: ${newRedesignHeaderPadding(theme)} !important;

        &:before {
          content: '${localize('pdp.Reviews.list.filterBy')}' !important;
          color: ${newRedesignHeaderContent(theme)};
          text-align: left !important;
          padding-left: 5px;
          ${newRedesignMainHeaderSearchSortFontSize(theme)};
          display: table !important;
        }

        @media (max-width: ${sdsBreakpoint.small}) {
          &:before {
            font-size: 17px;
            padding-left: 0;
          }
        }

        .pr-multiselect {
          ${newRedesignDrawerMultiSelectFlex(theme)};
          ${newRedesignDrawerMultiSelectMarginRight(theme)};
          padding: 0 5px;

          @media (max-width: ${sdsBreakpoint.small}) {
            ${newRedesignDrawerMultiSelectMobileMarginRight(theme)};
            ${newRedesignDrawerMultiSelectMobilePadding(theme)};
          }

          @media (max-width: 840px) {
            ${newRedesignMobileWidthColumn(theme)}
          }

          .pr-multiselect-button {
            width: 100% !important;
            border-color: ${newRedesignMultiSelectButton(theme)};
            .pr-multiselect-button-label {
              color: ${newRedesignContent(theme)} !important;
              ${newRedesignMultiSelectButtonPadding(theme)};
              ${newRedesignMultiselctButtonLabelFontStyles(theme)};

              @media (max-width: ${sdsBreakpoint.small}) {
                font-size: 16px;
              }
            }
            .pr-caret-icon {
              .pr-caret-icon__line {
                stroke: ${newRedesignstroke(theme)} !important;
                stroke-width: 6 !important;
              }
            }
          }

          ul.pr-multiselect-options {
            li:hover {
              .pr-multiselect-item-label {
                color: #333;
              }
            }
          }
        }

        .pr-multiselect .pr-multiselect-button {
          ${newRedesignHeaderContentsMinWidth(theme)};
        }

        .pr-multiselect-button-chest,
        .pr-multiselect-button-hipsrear,
        .pr-multiselect-button-overallsize,
        .pr-multiselect-button-rise,
        .pr-multiselect-button-waist,
        .pr-multiselect-button-width,
        .pr-multiselect-button-length {
          display: block;
        }

        @media (min-width: 540px) {
          width: ${newRedesignMultiSelectWidth(theme)};
          padding: ${newRedesignPadding(theme)} !important;
          ${newRedesignBorderBottom(theme)};

          &:before {
            padding-left: 0;
          }

          .pr-multiselect {
            ${newRedesignMultiSelectFlex(theme)};
            padding: 0;

            &:first-child {
              ${newRedesignMultiSelectMarginRight(theme)};
            }
            .pr-multiselect-button {
              ${newRedesignMultiSelectButtonWidth(theme)};
            }
          }
        }
      }

      .pr-rd-main-header-search-sort {
        margin-left: ${newRedesignMainHeaderSearchSortMargin(theme)};
        padding: ${newRedesignMainHeaderSearchSortPadding(theme)} !important;
        order: ${newRedesignHeaderSearchSortOrder(theme)};
        display: flex;
        width: ${newRedesignMainHeaderWidth(theme)};
        flex-grow: ${newRedesignHeaderSearchFlexGrowth(theme)};
        justify-content: flex-end;

        @media (max-width: 841px) {
          display: flex;
          flex-direction: column;
          width: 100% !important;
          ${newRedesignMobileWidthColumn(theme)};
        }

        @media (min-width: 840px) {
          max-width: ${newRedesignHeaderSearchSortMaxWidth(theme)};
          width: ${newRedesignHeaderSearchSortWidth(theme)} !important;
          margin-left: auto;
        }

        @media (min-width: 540px) {
          order: 2;
          ${newRedesignHeaderSearchSortDisplay(theme)}
          padding: ${newRedesignMainHeaderPadding(theme)} !important;
        }

        @media (max-width: ${sdsBreakpoint.small}) {
          padding: ${newRedesignSnapshotHeaderPaddingMobile(theme)} !important;
        }

        ${showReviewSearchField()}

        .pr-rd-main-header-search {
          flex-grow: ${forBrands(theme, {
            at: '1',
            default: '',
            on: '1',
          })};
          width: ${reviewHeaderFieldWidth(theme)} !important;
          padding-top: ${forBrands(theme, {
            at: '0',
            default: '4px',
            on: '0',
          })} !important;
          order: 1;

          @media (min-width: 840px) {
            padding-top: ${forBrands(theme, {
              at: '27px !important',
              default: '!important',
              on: '27px !important',
            })};
            max-width: ${forBrands(theme, {
              at: '340px',
              default: '',
              on: '340px',
            })};
            margin-left: ${forBrands(theme, {
              at: '32px',
              default: '0',
              on: '32px',
            })} !important;
          }

          @media (max-width: 840px) {
            padding: ${reviewHeaderPadding(theme)} !important;
            width: ${reviewHeaderFieldWidth(theme)} !important;
            margin-top: ${forBrands(theme, {
              at: '9px',
              default: '',
              on: '9px',
            })};
          }

          input {
            @media (max-width: ${sdsBreakpoint.small}) {
              font-size: 16px;
            }
          }
        }

        ${reviewHeaderSearchField(theme)}

        .pr-rd-review-header-sorts {
          flex-grow: ${forBrands(theme, {
            at: '1',
            default: '',
            on: '1',
          })};
          width: ${reviewHeaderFieldWidth(theme)} !important;
          position: relative;
          float: none !important;
          padding: 0 !important;

          &:before {
            content: '${localize('pdp.Reviews.list.sortBy')}' !important;
            color: ${newRedesignSortTextColor(theme)};
            display: table;
            ${newRedesignMainHeaderSearchSortFontSize(theme)};
          }

          @media (min-width: 840px) {
            max-width: 340px;
          }

          @media (max-width: 840px) {
            padding: ${reviewHeaderPadding(theme)} !important;
            width: ${reviewHeaderFieldWidth(theme)} !important;
          }

          @media (max-width: ${sdsBreakpoint.small}) {
            &:before {
              font-size: 17px;
            }
          }

          .pr-rd-sort-group {
            width: 100% !important;

            &:before {
              position: absolute;
              content: '' !important;
              ${brandDropdownCaretPosition(theme)};
              transform: ${brResetStyle(theme, 'rotate(-90deg)')};
              pointer-events: none;
              height: 15px;
              width: 15px;
              ${caretIconTransform};
              background: url(data:image/svg+xml;base64,${newRedesignDrawerReviewStylesChevron(theme)}) no-repeat;

              @media (max-width: ${sdsBreakpoint.small}) {
                ${brandDropdownCaretPositionMobile(theme)};
              }
            }

            @media (min-width: 841px) {
              max-width: ${forBrands(theme, {
                at: '400px',
                default: '',
                on: '400px',
              })};
            }

            padding: 0 !important;

            @media (max-width: 840px) {
              padding: ${newRedesignReviewHeaderSortsPadding(theme)};
            }

            .pr-rd-sort {
              width: 100% !important;
              margin: ${forBrands(theme, {
                at: '5px 10px 0 0',
                default: '5px 10px 5px 0',
                on: '5px 10px 0 0',
              })} !important;
              border: 1px solid ${newRedesignSortBorder(theme)};
              appearance: none;
              padding: 8px !important;
              max-width: none;
              ${textStyles(theme)};
              color: ${newRedesignContent(theme)} !important;
              ${newRedesignSortSelectFontSize(theme)}

              @media (min-width: 540px) {
                max-width: ${newRedesignHeaderSortMaxWidth(theme)};
              }

              @media (max-width: ${sdsBreakpoint.small}) {
                font-size: 16px;
                &:before {
                  top: 40px;
                }
              }
            }
          }

          /* there is an unexpect dropdown in the sort-by content block */
          /* this style should be removed once Power Review fix this issue.*/
          div.pr-rd-sort-group:nth-of-type(2) {
            display: none;
          }
        }
      }

      .pr-review-filter-headline {
        color: rgb(var(--pdp-color-black-1200)) !important;
      }

      .pr-review-filter-clear-all {
        color: rgb(var(--pdp-color-black-1200)) !important;
      }

      .pr-rd-review-total {
        display: inline-block;
        text-align: ${newRedesignSnapshotReviewTotalTextAlign(theme, 'center')};
        width: 100%;
        order: ${newRedesignSnapshotReviewTotalOrderMobile(theme)};
        font-weight: ${redesignFontWeight(theme)} !important;
        font-size: 0.875rem !important;
        color: ${newRedesignReviewsTotal(theme)} !important;
        ${theme.font.primary};
        border-bottom: 1px solid ${newRedesignReviewsKeyline(theme)} !important;
        ${newRedesignReviewsTotalBorderTop(theme)}
        padding: 20px 0;
        margin: ${newRedesignReviewsTotalMargin(theme)};
        ${redesignLineHeight(theme)};

        @media (min-width: 540px) {
          order: ${newRedesignSnapshotReviewTotalOrderDesktop(theme)};
          padding: ${newRedesignReviewsTotalPadding(theme)};
          font-size: ${newRedesignReviewTotalFontSizeDesktop(theme)};
          margin: 0;
        }

        @media (min-width: 841px) {
          text-align: ${newRedesignSnapshotReviewTotalTextAlign(theme, 'left')};
        }
      }
    }

    .pr-review {
      display: flex;
      flex-wrap: wrap;
      padding: ${newRedesignReviewPadding(theme)} !important;
      margin: ${newRedesignReviewMargin(theme, '0 15px')} !important;
      border-bottom: 1px solid ${newRedesignReviewsKeyline(theme)} !important;
      position: relative;

      @media (max-width: 790px) {
        margin: ${newRedesignReviewMargin(theme, '0 15px 15px')} !important;
      }
      .pr-rd-header {
        width: 100%;
        ${newRedesignReviewHeaderStyles(theme)}

        @media (max-width: 568px) {
          margin-bottom: 0;
        }
        .pr-rd-review-headline {
          ${headerStyles(theme)};
          margin: 0 !important;
          float: none !important;
        }

        .pr-rd-star-rating {
          display: inline;
          float: none !important;
          ${newRedesignStarRatingHeight(theme)};
          .pr-rating-stars {
            .pr-star-v4 {
              width: ${fullStarWidth}px !important;
              height: ${newRedesignStarsHeight(theme)};
              background-size: ${newRedesignStarsBackgroundSize(theme)} !important;
              overflow: hidden !important;
              &:before,
              &:after {
                height: 20px !important;
                background-size: 20px 20px !important;
              }
            }

            .pr-star-v4.pr-star-v4-25-filled {
              ${partialStars(fullStarWidth)};
            }
            .pr-star-v4.pr-star-v4-50-filled {
              ${partialStars(fullStarWidth)};
            }
            .pr-star-v4.pr-star-v4-75-filled {
              ${partialStars(fullStarWidth)};
              ${newRedesignPartialStar75MarginTop(theme)};
            }
          }

          .pr-star-v4.pr-star-v4-25-filled {
            ${partialStars(25)};
          }

          .pr-star-v4.pr-star-v4-50-filled {
            ${partialStars(50)};
          }

          .pr-star-v4.pr-star-v4-75-filled {
            ${partialStars(75)};
          }

          .pr-snippet-rating-decimal {
            display: none;
          }
        }
      }

      .pr-rd-description {
        position: static;
        width: 100%;
        .pr-rd-side-content-block {
          display: ${newRedesignDescriptionDisplay(theme)};
          position: static !important;
          float: none !important;
          ${newRedesignDescriptionFlex(theme)};
          .pr-rd-reviewer-details {
            ${newRedesignReviewDrawerDetailsWidth(theme)};
            .pr-rd-author-nickname {
              float: left;
              &:after {
                content: ', ';
                ${reviewTextStyles(theme)}
                padding-right: ${newRedesignPaddingReviewer(theme)};
              }

              span {
                ${reviewTextStyles(theme)};

                @media (max-width: 568px) {
                  font-size: ${newRedesignReviewerDetailsFontSize(theme)};
                }
              }
              .pr-rd-bold {
                display: none;
              }
            }
            .pr-rd-author-submission-date {
              display: inline-block;
              float: none !important;
              ${newRedesignReviewerInfo(theme)}
              time {
                float: ${newRedesignAuthorSubmissionDateFloat(theme)};
                ${reviewTextStyles(theme)};
                ${newRedesignAuthorSubmissionDateMarginLeft(theme)};
                margin-left: 1px;

                @media (max-width: 568px) {
                  font-size: ${newRedesignReviewerDetailsFontSize(theme)};
                }
              }
              .pr-rd-bold {
                display: none;
              }
            }
            .pr-rd-demographic,
            .pr-rd-author-location {
              display: none;
            }
          }

          .pr-rd-reviewer-type {
            display: ${newReviewerTypeDiplay(theme)};
            &.pr-verified_buyer .pr-rd-badging-text {
              word-break: normal;
              ${newRedesignReviewerInfoAlignment(theme)};
            }
            span {
              ${reviewTextStyles(theme)};
              ${newRedesignReviewerTypeVerticalAlign(theme)};

              @media (max-width: 568px) {
                font-size: ${newRedesignReviewerTypeFontSizeMobile(theme)} !important;
              }
            }

            .pr-badging-icon {
              circle {
                fill: ${theme.color.bk};
              }
            }

            &.pr-verified_buyer {
              .pr-badging-icon {
                display: none;
              }
            }
          }

          .pr-verified_buyer,
          .pr-rd-review-disclosure {
            display: ${newRedesignDisclosureDisplay(theme)};
            width: ${newRedesignDisclosureWidth(theme)};
            position: ${newRedesignDisclosurePosition(theme)};
            bottom: ${newRedesignReviewDisclosureBottom(theme)};
            margin: ${newRedesignReviewDisclosureMargin(theme)};
            &:before {
              content: '';
              background: url(${brandImageColor(theme, grayCheckmark, crossBrandCheckmark)}) no-repeat;
              width: 14px;
              height: 14px;
              display: inline-block;
              position: relative;
              top: 2px;
              ${newRedesignVerifiedBuyerLeft(theme)};
            }
          }

          .pr-rd-review-disclosure {
            ${reviewTextStyles(theme)};
            &:before {
              left: ${newRedesignReviewDisclosureLeft(theme)};
            }
          }

          .pr-rd-reviewer-type:not(.pr-staff_reviewer) + .pr-rd-reviewer-type,
          .pr-rd-reviewer-type:not(.pr-staff_reviewer) + .pr-rd-review-disclosure {
            left: 8.9em;
            padding-left: 0.281rem;
          }
        }

        .pr-rd-description-text {
          ${reviewTextStyles(theme)};
          width: ${newRedesignSnapshotDescriptionTextWidth(theme)} !important;
          margin: ${newRedesignSnapshotDescriptionTextMargin(theme)} !important;
        }
      }

      .pr-accordion {
        &.pr-rd-content-block {
          margin-bottom: ${newRedesignAccordionMarginBottom(theme)} !important;
        }
        .pr-accordion-btn {
          span {
            color: ${redesignAccordionButton(theme)};
            text-decoration: underline;
            font-size: ${newRedesignAccordionBtnFontSize(theme)};
          }

          .pr-caret-icon svg {
            width: 14px;
            height: 14px;
            ${reviewsDetailsIcon(theme)};
            .pr-caret-icon__line {
              stroke: ${newRedesignstroke(theme)};
            }
          }

          &:focus {
            box-shadow: none !important;
          }
        }

        ${keepReviewsVotingVisible}

        .pr-accordion-content {
          display: flex;
          flex-direction: column-reverse;
          overflow: hidden !important;
          .pr-rd-sliders {
            order: 2;
            .pr-rd-sliders_slider {
              ${newRedesignSliderWidth(theme)}
              @media (max-width: 568px) {
                width: 100%;
              }
              .pr-rd-sliders_title {
                ${reviewTextStyles(theme)};
              }

              .pr-size-fit {
                .pr-size-fit_slider {
                  .pr-size-fit_slider_node-group {
                    .pr-size-fit_slider_node {
                      background-color: ${newRedesignSizeFitSliderNodeColor(theme)};
                      height: ${newRedesignSizeFitSliderNodeHeight(theme)};
                      width: ${newRedesignSizeFitSliderNodeWidth(theme)};
                    }

                    .pr-size-fit_slider_node--active {
                      background: ${newRedesignSizeFitSliderNodeActiveBackground(theme)};
                      height: ${newRedesignSizeFitSliderNodeActiveDimension(theme)};
                      width: ${newRedesignSizeFitSliderNodeActiveDimension(theme)};
                    }
                  }

                  .pr-size-fit_slider_line {
                    border-top-color: ${newRedesignSizeFitSliderLineColor(theme)};
                    border-top-width: ${newRedesignSizeFitSliderLineWidth(theme)};
                    top: 40%;
                  }
                }

                .pr-size-fit_label {
                  ${reviewSliderLabelStyles(theme)};
                }
              }
            }
          }

          .pr-rd-subratings {
            display: flex;
            flex-direction: row;
            order: 2;

            @media (max-width: ${sdsBreakpoint.medium}) {
              ${forBrands(theme, {
                default: '',
                on: `display: grid;`,
              })}
            }

            .pr-rd-def-list {
              dt,
              dd {
                min-width: 0;
              }

              dt {
                ${newRedesignSubratingsDefListDisplay(theme)} !important;
                font-weight: ${newRedesignSubratingsDefListFontWeight(theme)} !important;
                padding-right: ${newRedesignPadding(theme)};
                color: ${newRedesignSubratingsDefListColor(theme)};
                height: ${newRedesignindividualReviewHeight(theme)};

                &:after {
                  content: ':';
                  ${reviewTextStyles(theme)};
                }
              }

              dd {
                font-weight: 700;
                ${reviewTextStyles(theme)};
              }
              .pr-snippet-stars {
                .pr-snippet-stars-png {
                  ${forBrands(theme, {
                    br: 'overflow: hidden',
                    brfs: 'overflow: hidden',
                    default: '',
                    gap: 'overflow: hidden',
                    gapfs: 'overflow: hidden',
                  })};
                }
                .pr-rating-stars {
                  align-items: center;
                  display: flex;
                  justify-content: space-between;
                  margin: 8px 0 0 5px;
                  position: relative;
                  width: ${forBrands(theme, {
                    br: '90px',
                    brfs: '90px',
                    default: '200px',
                    gap: '90px',
                    gapfs: '90px',
                  })};
                  &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 97%;
                    height: ${forBrands(theme, {
                      br: '2px',
                      brfs: '2px',
                      default: '3px',
                    })};
                    background-color: ${forBrands(theme, {
                      br: theme.brGreyColor2,
                      brfs: theme.brGreyColor2,
                      default: theme.color.b1,
                      gap: theme.color.g3,
                      gapfs: theme.color.g3,
                    })};
                    transform: translateY(-50%);
                    z-index: 0;
                  }
                }
                .pr-star-v4 {
                  height: 8px !important;
                  width: 8px !important;
                  border-radius: 50%;
                  z-index: 1;
                  background-color: ${theme.color.gray20};
                  border: 1px solid transparent;
                  &.pr-star-v4-100-filled {
                    &:last-child {
                      position: relative;
                      &:before {
                        background-color: ${forBrands(theme, {
                          br: () => theme.whiteCreame,
                          brfs: () => theme.whiteCreame,
                          default: () => theme.color.b1,
                          on: () => theme.color.bk,
                        })};
                        border-radius: 50%;
                        content: '';
                        height: 16px;
                        position: absolute;
                        top: -5px;
                        width: 16px;
                        right: -3px;
                      }
                    }
                  }
                  &.pr-star-v4-0-filled {
                    position: relative;
                  }
                  &.pr-star-v4-100-filled + .pr-star-v4-0-filled {
                    &:before {
                      background-color: ${forBrands(theme, {
                        br: `rgb(var(--pdp-color-black-1400))`,
                        brfs: `rgb(var(--pdp-color-black-1400))`,
                        default: theme.color.b1,
                      })};
                      border-radius: 50%;
                      content: '';
                      height: 16px;
                      position: absolute;
                      top: -5px;
                      width: 16px;
                      right: ${forBrands(theme, {
                        br: '35px',
                        brfs: '35px',
                        default: '43px',
                      })};
                    }
                  }
                }
                .pr-snippet-rating-decimal {
                  display: none;
                }
              }
            }
          }
          .pr-rd-images {
            order: 2;
          }
          .pr-rd-footer {
            background: none;
            .pr-rd-helpful-action {
              & a {
                ${linkStyles(theme)};
              }
              ${showReviewVoting()}
            }
          }
          .pr-rd-bottomline {
            display: none;
          }
        }
      }
    }

    .pr-rd-main-footer {
      border: none !important;
      margin-bottom: ${forBrands(theme, {
        default: '20px;',
        on: '0px;',
      })};
      padding: 30px 0 !important;
      .pr-rd-content-block {
        width: 30% !important;
        margin: auto;
        text-align: center !important;
        min-width: 175px;
        .pr-rd-review-position {
          margin: auto;
          float: none !important;
          white-space: nowrap;
          ${reviewListPaginationTextMargin(theme)}
          & span {
            font-weight: 400;
            color: ${footerTextColor(theme)};
            font-size: ${forBrands(theme, {
              default: '',
              gap: '16px',
              gapfs: '16px',
            })};
            letter-spacing: ${forBrands(theme, {
              default: '',
              gap: '0.8px',
              gapfs: '0.8px',
            })};
          }
          .pr-rd-bold {
            font-weight: ${forBrands(theme, {
              default: '',
              gap: theme.fontWeight.bold,
              gapfs: theme.fontWeight.bold,
            })};
          }
        }
        .pr-rd-to-top {
          display: none;
        }
        .pr-rd-pagination {
          border: none !important;
          margin: 0 10px auto;
          & span {
            display: none;
          }
          @media (min-width: 540px) {
            width: 100%;
            margin: ${forBrands(theme, {
              default: '0 auto',
              gap: '16px 0 0',
              gapfs: '16px 0 0',
            })};
            ${forBrands(theme, {
              br: 'padding: 0 !important',
              brfs: 'padding: 0 !important',
              default: '',
            })};
          }
        }
        @media (min-width: 540px) {
          min-width: 340px;
          .pr-rd-review-position {
            padding-left: ${forBrands(theme, {
              default: '21px',
              gap: '0',
              gapfs: '0',
            })};
          }
          .pr-rd-pagination {
            a.pr-rd-pagination-btn.pr-rd-pagination-btn--previous {
              ${forBrands(theme, {
                default: 'float: left;',
                gap: '',
                gapfs: '',
              })}
              ${newRedesignPaginationBtnTextStyles(theme)}
              &:before {
                visibility: ${forBrands(theme, {
                  default: 'visible',
                  gap: 'hidden',
                  gapfs: 'hidden',
                })};
                background: url(data:image/svg+xml;base64,${brandImageColor(theme, lightChevron, brandPaginationArrow(theme))}) no-repeat;
                content: '';
                display: inline-block;
                height: 0.625rem;
                position: relative;
                ${reviewListPaginationCaretPositionLeft(theme)};
                vertical-align: top;
                width: 1rem !important;
                transform: rotate(${brandCheckStyleByNewRedesign(theme, '90', '0')}deg);
              }
            }
            a.pr-rd-pagination-btn.pr-rd-pagination-btn--next:last-child {
              ${forBrands(theme, {
                default: 'float: right;',
                gap: '',
                gapfs: '',
              })}
              ${newRedesignPaginationBtnTextStyles(theme)}
              &:after {
                visibility: ${forBrands(theme, {
                  default: 'hidden',
                  gap: 'visible',
                  gapfs: 'visible',
                })};
                background: url(data:image/svg+xml;base64,${brandImageColor(theme, lightChevron, brandPaginationArrow(theme))}) no-repeat !important;
                content: '';
                display: inline-block;
                height: 0.625rem;
                position: relative;
                ${reviewListPaginationCaretPositionRight(theme)};
                transform: rotate(${brandCheckStyleByNewRedesign(theme, '-90', '180')}deg) scaleX(${brandCheckStyleByNewRedesign(theme, '-1', '1')});
                vertical-align: top;
                width: 1rem !important;
              }
            }
          }
        }
      }
    }
    .pr-rd-main-footer a {
      ${linkStyles(theme)};
    }
    .pr-rd-bottomline {
      display: none;
    }
    .pr-read-review.pr-rd-display-desktop .pr-review-condensed .pr-rd-reviewed-at,
    .pr-read-review.pr-rd-display-tablet .pr-review-condensed .pr-rd-reviewed-at,
    .pr-review-display.pr-rd-display-desktop .pr-review-condensed .pr-rd-reviewed-at,
    .pr-review-display.pr-rd-display-tablet .pr-review-condensed .pr-rd-reviewed-at,
    .pr-rd-reviewed-at {
      display: none !important;
    }

    ${setStylesForBuyBoxRedesign(gapBuyBox2024RedesignReviewStyles(theme, localize))}
  `;
};
