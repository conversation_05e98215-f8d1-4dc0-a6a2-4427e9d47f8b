// @ts-nocheck
import { act, renderHook } from '@testing-library/react-hooks';
import React, { useContext } from 'react';

import { PDPReporterProvider } from '../../../pdp-reporter-provider';
import type { ConstructorArgs } from '../../../product-page/collaborators/pdp-reporter';
import { ReviewsDrawerContext, ReviewsDrawerProvider } from '.';

describe('GIVEN <ReviewsDrawerProvider />', () => {
  const datalayer = {
    link: jest.fn(),
  };
  const businessUnitId = '3';
  const productName = 'Jeans';
  const pageName = 'Soft Washed V-Neck';
  const pageType = 'product';
  const defaultParams = {
    business_unit_id: businessUnitId,
    page_name: pageName,
    page_type: pageType,
  };
  const defaultReporterProviderProps: ConstructorArgs = {
    abbrBrandForTealium: 'gp',
    brandName: 'gap' as Brands,
    businessUnitId,
    datalayer,
    market: 'us',
    pageName,
    pageType,
    productName,
  };
  describe('WHEN setReviewsDrawerIsOpen receives true', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });

    const container = ({ children }) => (
      <PDPReporterProvider pdpReporterInstanceParams={defaultReporterProviderProps}>
        <ReviewsDrawerProvider>{children}</ReviewsDrawerProvider>;
      </PDPReporterProvider>
    );
    test('isReviewsDrawerOpen should be true', () => {
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });

      act(() => {
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-snapshot');
      });

      expect(result.current.isReviewsDrawerOpen).toBeTruthy();
    });
    test('setReviewsDrawerIsOpen should call link once with correct params when id is pdp-pr-star-ratings-snapshot', () => {
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });
      const expectedParams = {
        ...defaultParams,
        event_name: 'review_ratings_click',
        review_ratings_conversion: 'review on the snapshot',
      };

      act(() => {
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-snapshot');
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-snapshot');
      });

      expect(datalayer.link.mock.calls).toHaveLength(1);
      expect(datalayer.link).toHaveBeenCalledWith(expectedParams);
    });
    test('setReviewsDrawerIsOpen should call link on with correct params when id is pdp-pr-star-ratings-buybox', () => {
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });
      const expectedParams = {
        ...defaultParams,
        event_name: 'review_ratings_click',
        review_ratings_conversion: 'review on ratings click',
      };

      act(() => {
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-buybox');
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-buybox');
      });

      expect(datalayer.link.mock.calls).toHaveLength(1);
      expect(datalayer.link).toHaveBeenCalledWith(expectedParams);
    });

    test('setReviewsDrawerIsOpen should call twice when ids are differents', () => {
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });

      act(() => {
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-buybox');
        result.current.setReviewsDrawerIsOpen(true, 'pdp-pr-star-ratings-snapshot');
      });

      expect(datalayer.link.mock.calls).toHaveLength(2);
    });
  });

  describe('WHEN setReviewsDrawerIsOpen receives false', () => {
    test('isReviewsDrawerOpen should be false', () => {
      const container = ({ children }) => <ReviewsDrawerProvider>{children}</ReviewsDrawerProvider>;
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });

      act(() => {
        result.current.setReviewsDrawerIsOpen(false);
      });

      expect(result.current.isReviewsDrawerOpen).toBeFalsy();
    });
  });

  describe('WHEN setFirstReview receives true', () => {
    test('forceReviewForm should be true', () => {
      const container = ({ children }) => <ReviewsDrawerProvider>{children}</ReviewsDrawerProvider>;
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });

      act(() => {
        result.current.setForceReviewForm(true);
      });

      expect(result.current.forceReviewForm).toBeTruthy();
    });
  });

  describe('WHEN setFirstReview receives false', () => {
    test('forceReviewForm should be false', () => {
      const container = ({ children }) => <ReviewsDrawerProvider>{children}</ReviewsDrawerProvider>;
      const { result } = renderHook(() => useContext(ReviewsDrawerContext), { wrapper: container });

      act(() => {
        result.current.setForceReviewForm(false);
      });

      expect(result.current.forceReviewForm).toBeFalsy();
    });
  });
});
