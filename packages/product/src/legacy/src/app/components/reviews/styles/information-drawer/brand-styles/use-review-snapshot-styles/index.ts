// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { drawerUseReviewSnapshotStylesForBR } from './br.styles';

export const snapshotStyles = (theme: Theme): InterpolationPrimitive =>
  forBrands(theme, {
    br: drawerUseReviewSnapshotStylesForBR,
    brfs: drawerUseReviewSnapshotStylesForBR,
  });
