// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import loaderPlaceholderStylesForAT from './at.styles';
import loaderPlaceholderStylesForBR from './br.styles';
import loaderPlaceholderStylesForGAP from './gap.styles';
import loaderPlaceholderStylesForON from './on.styles';

export const redesignLoaderPlaceholderStyles = (theme: Theme): InterpolationPrimitive => {
  return forBrands(theme, {
    at: loaderPlaceholderStylesForAT,
    br: loaderPlaceholderStylesForBR,
    brfs: loaderPlaceholderStylesForBR,
    gap: loaderPlaceholderStylesForGAP,
    gapfs: loaderPlaceholderStylesForGAP,
    on: loaderPlaceholderStylesForON,
  });
};
