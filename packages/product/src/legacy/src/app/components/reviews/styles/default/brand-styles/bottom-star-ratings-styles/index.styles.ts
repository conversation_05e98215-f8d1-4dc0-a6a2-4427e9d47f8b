// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { bottomStarRatingsStylesForAT } from './at.styles';
import { bottomStarRatingsStylesForBR } from './br.styles';
import { bottomStarRatingsStylesForGAP } from './gap.styles';
import { bottomStarRatingsStylesForON } from './on.styles';

export const bottomStarRatingsStyles = (theme: Theme): InterpolationPrimitive => {
  return forBrands(theme, {
    at: bottomStarRatingsStylesForAT,
    br: bottomStarRatingsStylesForBR,
    brfs: bottomStarRatingsStylesForBR,
    gap: bottomStarRatingsStylesForGAP,
    gapfs: bottomStarRatingsStylesForGAP,
    on: bottomStarRatingsStylesForON,
  });
};
