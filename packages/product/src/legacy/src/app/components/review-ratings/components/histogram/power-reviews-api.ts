// @ts-nocheck
'use client'

const fallbackResponse = { reviewRatings: {}, reviews: [] };
export type ReviewRatings = {
  reviewRatings: ReviewHistogram;
  reviews?: Record<string, any>[];
};

type ReviewHistogram =
  | {
      average_rating: number;
      ratingHistogram: Array<number>;
      rating_count: number;
      reviewHistogram?: Array<number>;
      review_count: number;
    }
  | Record<string, never>;

const mapFunction = (data): ReviewRatings => {
  return {
    reviewRatings: {
      average_rating: data?.results[0]?.rollup?.average_rating,
      ratingHistogram: data?.results[0]?.rollup?.rating_histogram,
      rating_count: data?.results[0]?.rollup?.rating_count,
      reviewHistogram: data?.results[0]?.rollup?.review_histogram,
      review_count: data?.results[0]?.rollup?.review_count,
    },
    reviews: data?.results[0]?.reviews,
  };
};

export const callPowerReviews = async (url, fetchOptions): Promise<ReviewRatings> => {
  return fetch(url, fetchOptions)
    .then(res => res.json())
    .then(data => mapFunction(data))
    .catch(e => {
      console.error(e);
      return fallbackResponse;
    });
};
