// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<QuickAddIcon /> renders correctly 1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <svg
    height="36"
    viewBox="0 0 36 36"
    width="36"
  >
    <g
      fill="none"
    >
      <circle
        css={
          {
            "map": undefined,
            "name": "8ytiej",
            "next": undefined,
            "styles": "
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  overflow: visible;
",
            "toString": [Function],
          }
        }
        cx="18"
        cy="18"
        fill="#FFFFFF"
        opacity=".8"
        r="18"
      />
      <path
        css={[Function]}
        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
      />
      <circle
        css={[Function]}
        cx="22.41"
        cy="22.754"
        r="6.692"
      />
      <circle
        cx="22.41"
        cy="22.754"
        r="4.818"
      />
      <path
        css={[Function]}
        d="M20.096 22.888h4.36M22.275 20.708v4.36"
        stroke="#FFFFFF"
      />
    </g>
  </svg>
</div>
`;
