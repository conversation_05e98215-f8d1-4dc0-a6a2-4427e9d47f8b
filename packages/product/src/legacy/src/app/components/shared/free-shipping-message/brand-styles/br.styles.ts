// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";

export const brFreeShipMessageStyles = (theme: Theme): SerializedStyles => {
  return css`
    color: ${theme.color.b1};
    ${theme.font.tertiary};
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    text-align: center;
    text-transform: capitalize;

    svg {
      display: none;
    }
  `;
};
