// @ts-nocheck
'use client';

import type { SerializedStyles, Theme } from '@ecom-next/core/react-stitch';
import { css } from '@ecom-next/core/react-stitch';
import { pdpUnbuttonize } from '@pdp/packages/styles/brand-styles/utils/util';

export const fullDetailComponent = (theme: Theme): SerializedStyles => {
  return css`
    ${pdpUnbuttonize()};
    margin-bottom: 3.5rem;
    color: rgb(var(--pdp-color-black-1300));
    font-size: 0.875rem;
    text-align: center;
    text-decoration: underline;
    min-width: 20%;
    font-variant-ligatures: none;
    text-underline-offset: auto;
    margin-top: 1.75rem;
    ${theme.font.primary}
  `;
};

export const center = (): SerializedStyles => {
  return css`
    display: flex;
    justify-content: center;
  `;
};
