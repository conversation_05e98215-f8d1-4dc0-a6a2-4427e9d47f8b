// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import TestApp from '@pdp/spec/test-app';
import { adaptStyleInventoryResponse } from '@product-page/legacy/chas-utils';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { useProductSelectionContext } from '@pdp/packages/product-selection/use-product-selection-context';
import { useColorAvailabilityQuery } from '@product-page/legacy/use-availability';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';

import { useBopisData } from '../../../../../../hooks/use-bopis-data';
import { usePDPReporter } from '../../../../../../hooks/use-pdp-reporter';
import { stores } from '../../../../../fulfillment/data/stores.mock';
import { BrFulfillmentController } from '../index';

jest.mock('../../../../../../hooks/use-bopis-data');
jest.mock('@product-page/legacy/use-availability');
jest.mock('@product-page/legacy/chas-utils');
jest.mock('../../../../../../hooks/use-pdp-reporter');
jest.mock('@pdp/packages/helpers/util/evaluate-brand');
jest.mock('@pdp/packages/product-selection/use-product-selection-context');

jest.mock('../components/product-detail', () => ({
  __esModule: true,
  // eslint-disable-next-line react/display-name
  default: () => <div>ProductDetail Mock</div>,
}));

const mockUseColorAvailabilityQuery = () => {
  const colorAvailabilityQueryMock = jest.fn(() => {
    return Promise.resolve({});
  });
  (useColorAvailabilityQuery as jest.Mock).mockImplementation(() => colorAvailabilityQueryMock);
  (adaptStyleInventoryResponse as jest.Mock).mockImplementation(() => colorAvailabilityQueryMock);
  return colorAvailabilityQueryMock;
};

const useProductSelectionContextMock = (isValidSizeSelection = true) => {
  (useProductSelectionContext as jest.Mock).mockReturnValue({
    isValidSizeSelection,
    selectedColor: { bopisInStock: true },
    selectedSize: { skuId: '123' },
    updateSelectedVariantAndDisplayedDimensions: () => {},
    updateVariants: () => {},
  });
};

const defaultBopisData = {
  active: false,
  enabled: true,
  postalCode: '',
  resetFetchStatus: jest.fn(),
  selectedStore: stores[0],
  setActive: jest.fn(),
  setEnabled: false,
  setPostalCode: () => {},
  setSelectedStore: () => {},
  setStores: () => {},
};

const mockUseBopisData = (bopisData = {}) => {
  (useBopisData as jest.Mock).mockImplementation(() => ({
    ...defaultBopisData,
    ...bopisData,
  }));
};

const mockReportStoreChange = jest.fn();
const mockReporter = () => {
  (usePDPReporter as jest.Mock).mockImplementation(() => ({
    reportStoreChange: mockReportStoreChange,
  }));
};

const resetReporterMock = () => {
  (usePDPReporter as jest.Mock).mockReset();
};
const resetUseBopisData = () => {
  (useBopisData as jest.Mock).mockReset();
};

const resetUseColorAvailabilityQuery = () => {
  (useColorAvailabilityQuery as jest.Mock).mockReset();
};

const getActualTranslations = () => {
  const enUs = jest.requireActual('../../../../../../../../state-builder/static-data/i18n/en_US.json');
  const translations = {
    'en-US': {
      translation: enUs,
    },
  };
  return translations;
};

function setup(props = {}, testAppProps = {}) {
  const translations = getActualTranslations();
  return (
    <TestApp
      abSegValue={testAppProps?.abSeg}
      appState={{
        appConfig: {
          apiConfig: {
            pasConfig: {
              url: 'https://gap.com',
            },
          },
        },
        brandName: testAppProps?.brandName || 'br',
        datalayer: {
          add: jest.fn(),
        },
        env: 'stage',
        locationConfig: {
          domain: 'https://gap.com',
        },
        market: 'us',
        params: { pid: '5775390220003' },
        productData: {
          selectedColor: { bopisInStock: true },
        },
        url: 'https://gap.com',
      }}
      brandMarketCtxValue={{
        brandName: testAppProps?.brandName || 'br',
        market: 'us',
      }}
      featureFlagsCtxValue={{
        enabledFeatures: { 'bopis-us-on': true, ...testAppProps?.enabledFeatures },
        featureVariables: {
          'bopis-us-on': {
            fulfillmentDisplay: true,
          },
        },
      }}
      translations={translations}
      {...testAppProps}
    >
      <BrFulfillmentController
        fulfillmentDisplay
        pid="5775390220003"
        renderShippingAndReturns={() => <div>ShippingAndReturns</div>}
        {...props}
      />
    </TestApp>
  );
}

const matchSnapsByBrands = setupSnapshots();

describe('<BrFulfillmentController />', () => {
  beforeEach(() => {
    useProductSelectionContextMock();
    resetUseBopisData();
    resetUseColorAvailabilityQuery();
    resetReporterMock();
    mockUseBopisData();
    mockUseColorAvailabilityQuery();
  });

  matchSnapsByBrands(
    (brandName, enabledFeatures, abSeg) =>
      setup(
        { bopisAwarenessInventoryEnabled: true, fulfillmentDisplay: true, selectedColor: { bopisInStock: true } },
        {
          abSeg,
          brandName,
          enabledFeatures,
        }
      ),
    'renders correctly',
    { brands: [Brands.BananaRepublic, Brands.BananaRepublicFactoryStore] }
  );

  test('calls reporter on opening change store modal', async () => {
    mockUseBopisData();
    mockUseColorAvailabilityQuery();
    mockReporter();

    const { getByRole } = render(setup());
    const changeStoreButton = getByRole('button', { name: 'Change Store' });
    await act(async () => { 

     fireEvent.click(changeStoreButton); 

     })
    expect(mockReportStoreChange).toHaveBeenCalled();
  });

  test('should not call colorLevelPasQuery when flag is on', () => {
    const colorLevelPasQuery = mockUseColorAvailabilityQuery();
    mockUseBopisData();

    render(setup({ fulfillmentDisplay: true }, { enabledFeatures: { 'pdp-oos-considered-bopis': true } }));

    expect(colorLevelPasQuery).toHaveBeenCalledTimes(0);
  });

  test('should call colorLevelPasQuey when flag is off', () => {
    const colorLevelPasQuery = mockUseColorAvailabilityQuery();
    mockUseBopisData();

    render(setup({ fulfillmentDisplay: true }, { enabledFeatures: { 'pdp-oos-considered-bopis': false } }));

    expect(colorLevelPasQuery).toHaveBeenCalledTimes(1);
  });

  test('change store button opens the modal', async () => {
    mockUseBopisData();
    mockUseColorAvailabilityQuery();

    const { getByRole } = render(setup());

    const changeStoreButton = getByRole('button', { name: 'Change Store' });
    await act(async () => { 

     fireEvent.click(changeStoreButton); 

     })
    const modalCloseButton = screen.getByRole('button', {
      name: (_, element) => element.classList.contains('drawer-close-button'),
    });
    expect(modalCloseButton).toBeInTheDocument();
  });

  test('close button closes the modal', async () => {
    mockReporter();
    mockUseBopisData();
    mockUseColorAvailabilityQuery();

    render(setup());

    const changeStoreButton = screen.getByRole('button', { name: 'Change Store' });
    await act(async () => { 

     fireEvent.click(changeStoreButton); 

     })
    const modalCloseButton = screen.getByRole('button', {
      name: (_, element) => element.classList.contains('drawer-close-button'),
    });
    await act(async () => { 

     fireEvent.click(modalCloseButton); 

     })

    await waitFor(() => {
      expect(modalCloseButton).not.toBeInTheDocument();
    });
  });
});
