// @ts-nocheck

import { createSerializer } from '@emotion/jest';
import TestApp from '@pdp/spec/test-app';
import type { AddToBagResponseStatus } from '@product-page/legacy/add-to-bag';
import { addToBagService } from '@pdp/packages/add-to-bag/service/add-to-bag-service';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import renderer, { act } from 'react-test-renderer';

import {
  useBrandAgnosticFeatureFlag,
  useFeatureVariables,
  useMultiBrandFeatureVariables,
} from '../../../../hooks/use-feature-flag';
import { useGetMarketingFlag } from '../../../../hooks/use-get-marketing-flag';
import { useGetProductData } from '../../../../hooks/use-get-product-data';
import { usePDPReporter } from '../../../../hooks/use-pdp-reporter';
import HybridLandingPage from '../index';
import initialDataMock from './mock/initial-data-mock.json';

expect.addSnapshotSerializer(createSerializer());
jest.mock('@ecom-next/core/legacy/i18n');
jest.mock('../../../../hooks/use-pdp-reporter');
jest.mock('@pdp/packages/add-to-bag/service/add-to-bag-service');
jest.mock('../../../../hooks/use-get-product-data');
jest.mock('../../../../hooks/use-feature-flag');
jest.mock('../../../../hooks/use-get-marketing-flag');

const leapFrogSuccessResponse = {
  inlineBagModelData: {
    inlineBagItems: [
      {
        colorDescription: 'Golly Gee Garnet',
        inventoryLevel: 12345,
        itemSubtotal: '$4.80',
        listPrice: '$8.00',
        priceType: 1,
        productLink: 'browse/product.do?pid=5075180620002',
        quantity: 1,
        returnType: null,
        selectedSize: '0002',
        sellPrice: '$4.80',
        shouldShowSalePrice: true,
        siImageURI: 'webcontent/0018/962/585/cn18962585.jpg',
        skuDescription: 'M',
        styleDescription: 'EveryWear Slub-Knit V-Neck Tee for Women ',
        styleId: '',
      },
    ],
    itemCount: 3,
    subTotal: '$407.80',
  },
  itemCount: 3,
  status: 'SUCCESS' as AddToBagResponseStatus,
  subTotal: '$407.80',
};

const productData = vetoAttributes => {
  const marketingFlag = vetoAttributes.styleMarketingFlags ?? 'product data marketing flag';

  return {
    displayedDimensions: [
      {
        dimensionGroupId: 'sizeDimension1',
        dimensions: [
          { bopisInStock: false, inStock: true, name: 'XXS' },
          { bopisInStock: false, inStock: true, name: 'XS' },
          { bopisInStock: false, inStock: true, name: 'S' },
          { bopisInStock: false, inStock: true, name: 'M' },
          { bopisInStock: false, inStock: true, name: 'L' },
          { bopisInStock: false, inStock: true, name: 'XL' },
          { bopisInStock: false, inStock: true, name: '1X' },
          { bopisInStock: false, inStock: true, name: '2X' },
          { bopisInStock: false, inStock: false, name: '3X' },
        ],
        label: 'Size',
        selectedDimension: '2X',
      },
    ],
    infoTabs: {
      overview: {
        bulletAttributes: ['SKINNY FIT: Mid-rise, modern, and form-fitting. Skinny from hip to hem.'],
        copyAttributes: [],
      },
    },
    marketingFlag,
    price: {
      currentMaxPrice: 79,
      currentMinPrice: 79,
      localizedCurrencySymbol: '$',
      maxPercentageOff: 0,
      minPercentageOff: 0,
      priceType: 1,
      regularMaxPrice: 79,
      regularMinPrice: 79,
    },
    productImages: initialDataMock.productImages,
    selectedColor: { ...initialDataMock.selectedColor, ...vetoAttributes },
    selectedSize: { skuId: '123456' },
    selectedVariant: initialDataMock.selectedVariant,
    variants: initialDataMock.variants,
    vendorId: '',
    vendorName: '',
  };
};

const mockProductData = (vetoAttr = {}) => (useGetProductData as jest.Mock).mockReturnValue(productData(vetoAttr));

const mockAddToBagService = () => {
  (addToBagService as jest.Mock).mockReturnValue(Promise.resolve(leapFrogSuccessResponse));
};

const mockUseBrandAgnosticFeatureFlag = (superPdpFlag = false) =>
  (useBrandAgnosticFeatureFlag as jest.Mock).mockReturnValue(superPdpFlag);

const mockUseMultiBrandFeatureVariables = (flag = false) =>
  (useMultiBrandFeatureVariables as jest.Mock).mockReturnValue(flag);

const mockUseFeatureVariables = (value = '') => (useFeatureVariables as jest.Mock).mockReturnValue(value);

const mockUseGetMarketingFlag = (flag = {}) =>
  (useGetMarketingFlag as jest.Mock).mockReturnValue({ ...productData(flag) });

const reportAddToBagClickMock = jest.fn();
const reportPhotoCarouselArrowClickMock = jest.fn();
const reportViewFullDetailsClickMock = jest.fn();
const reportSizeSelectionClickMock = jest.fn();
const reportColorSelectionClickMock = jest.fn();

const reporterMock = () => {
  (usePDPReporter as jest.Mock).mockImplementation(() => ({
    datalayer: { viewWith: jest.fn() },
    reportAddToBagClick: reportAddToBagClickMock,
    reportColorSelectionClick: reportColorSelectionClickMock,
    reportPhotoCarouselArrowClick: reportPhotoCarouselArrowClickMock,
    reportSizeSelectionClick: reportSizeSelectionClickMock,
    reportViewFullDetailsClick: reportViewFullDetailsClickMock,
  }));
};

const mockI18nProvider = () => {
  (I18nProvider as jest.Mock).mockImplementation(() => <div />);
};

const translations = {
  'en-US': {
    translation: {
      'pdp.currencySymbol': '$',
      'pdp.price.currentPriceRangeAriaLabel': 'Now {{minPrice}} to {{maxPrice}}',
      'pdp.price.nowCurrentPrice': 'Now {{price}}',
      'pdp.price.percentageOff': '{{value}}% off',
      'pdp.price.regularPriceAriaLabel': 'Was {{price}}',
      'pdp.price.regularPriceRangeAriaLabel': 'Was {{minPrice}} to {{maxPrice}}',
      'pdp.sizeLabel': 'Size',
    },
  },
};

describe('<HybridLandingPage />', () => {
  beforeEach(() => {
    reporterMock();
    mockProductData();
    mockUseBrandAgnosticFeatureFlag();
    mockUseMultiBrandFeatureVariables();
    mockI18nProvider();
    mockUseFeatureVariables();
    mockUseGetMarketingFlag();
  });
  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  const renderHybridLandingPage = (vetoAttributes = {}) => {
    return (
      <TestApp
        appState={{
          brandName: 'at',
          errorLogger: jest.fn(),
          market: 'us',
          productData: { ...productData(vetoAttributes) },
          url: '/browse/product.do?pid=123123',
        }}
        brandName="at"
        translations={translations}
      >
        <HybridLandingPage />
      </TestApp>
    );
  };

  test('renders correctly the HybridLandingPage', () => {
    const tree = renderer.create(renderHybridLandingPage()).toJSON();
    expect(tree).toMatchSnapshot();
  });

  describe('reports', () => {
    test('reportAddToBagClick', async () => {
      mockAddToBagService();
      render(renderHybridLandingPage());
      const addToBag = screen.getByText('pdp.addToBag.text');
      await act(async () => { 

       fireEvent.click(addToBag); 

       })
      await waitFor(() => {
        expect(reportAddToBagClickMock).toHaveBeenNthCalledWith(1, false);
      });
    });

    test('reportViewFullDetailsClick', async () => {
      render(renderHybridLandingPage());
      const fullDetailsLink = screen.getByText('pdp.lpo.fullDetailsLink');
      await act(async () => { 

       fireEvent.click(fullDetailsLink); 

       })
      await waitFor(() => {
        expect(reportViewFullDetailsClickMock).toHaveBeenCalledTimes(1);
      });
    });

    test('reportPhotoCarouselArrowClick', async () => {
      render(renderHybridLandingPage());

      const arrowRight = screen.getByTestId('Arrow-right');
      act(async () => {
        await act(async () => { 

         fireEvent.click(arrowRight); 

         })
      });

      await waitFor(() => {
        expect(reportPhotoCarouselArrowClickMock).toHaveBeenCalledTimes(1);
      });
    });

    test('reportSizeSelectionClick', async () => {
      render(renderHybridLandingPage());

      act(async () => {
        const btn = screen.getByLabelText('Size:38W');
        await act(async () => { 

         fireEvent.click(btn); 

         })
      });

      await waitFor(() => {
        expect(reportSizeSelectionClickMock).toHaveBeenCalledTimes(1);
      });
    });

    test.skip('reportColorSelectionClick', async () => {
      render(renderHybridLandingPage());

      act(() => {
        fireEvent.click(screen.getByLabelText('Black'));
      });

      await waitFor(() => {
        expect(reportColorSelectionClickMock).toHaveBeenCalledTimes(1);
      });
    });
    test('should display info tabs present in the product data', () => {
      const { container } = render(renderHybridLandingPage());
      expect(container.innerHTML).toContain('SKINNY FIT: Mid-rise, modern, and form-fitting. Skinny from hip to hem.');
    });
    test('should display veto info tabs', () => {
      const vetoAttr = {
        infoTabs: {
          overview: {
            bulletAttributes: ['veto bullet 1'],
            copyAttributes: ['veto copy attr 1'],
          },
        },
      };
      mockUseBrandAgnosticFeatureFlag(true);
      mockProductData(vetoAttr);
      const { container } = render(renderHybridLandingPage(vetoAttr));
      expect(container.innerHTML).toContain('veto bullet 1');
    });

    test('should display marketing flag from product data', () => {
      mockProductData();
      mockUseGetMarketingFlag();
      const { container } = render(renderHybridLandingPage());
      expect(container.innerHTML).toContain('product data marketing flag');
    });
    test('should display marketing flag from veto styleMarketingFlags', () => {
      const vetoAttr = {
        styleMarketingFlags: 'marketing flag from veto attr',
      };
      mockUseBrandAgnosticFeatureFlag(true);
      mockProductData(vetoAttr);
      mockUseGetMarketingFlag(vetoAttr);
      const { container } = render(renderHybridLandingPage(vetoAttr));
      expect(container.innerHTML).toContain(vetoAttr.styleMarketingFlags);
    });
    test('should not display as veto styleMarketingFlags flag content variable is different and also not cause any error', () => {
      const vetoAttr = {
        styleMarketingFlags: 'marketing flag from veto attr',
      };
      mockUseBrandAgnosticFeatureFlag(true);
      mockProductData(vetoAttr);
      mockUseGetMarketingFlag(vetoAttr);
      const { container } = render(renderHybridLandingPage(vetoAttr));
      expect(container.innerHTML).not.toContain('product data marketing flag');
    });
  });
});
