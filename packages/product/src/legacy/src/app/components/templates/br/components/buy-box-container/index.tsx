// @ts-nocheck
'use client'

import { useResizeObserver } from '@ecom-next/core/legacy/hooks';
import { useMediator } from '@ecom-next/core/legacy/mediator';
import { Brands } from '@ecom-next/core/legacy/utility';
import { RenderByBrands } from '@product-page/legacy/helpers';
import classNames from 'classnames';
import type { MutableRefObject } from 'react';
import React, { useEffect, useRef } from 'react';

import { clearfix } from '../../../../../../../packages/styles/brand-styles/utils/util';
import { usePersonalization } from '../../../../../hooks/use-personalization';
import getBuyBoxProps from '../../../../buy-box/adapters/buy-box-props';
import DropshipFlagsContainer from '../../../../buy-box/components/dropship-flags-component';
import FindYourFit from '../../../../drapr/find-your-fit';
import ZoomMediator from '../../../../photo-carousel/components/zoom-mediator';
import { useSmoothScroll } from '../../../../product-page/collaborators/use-smooth-scroll';
import { panelNoSpaceStyles, panelStyles } from '../../product-page.styles';
import { ProductInformationLinks } from '../product-information-links';
import {
  buyBoxContainerStyles,
  buyboxInnerPadding,
  buyBoxStyles,
  containerStyles,
  lBuyBoxStyles,
  stickyContainerWithLbuyBox,
} from './brand-styles/index.styles';
import BrBuyBoxwithBopis from './buy-box-with-bopis';
import type { BuyBoxContainerProps } from './types';

const BrBuyBoxContainer = ({
  brandName,
  datalayer,
  productData,
  isFullStacked,
  ...restOfTheProps
}: BuyBoxContainerProps): JSX.Element => {
  const { gidMessage, ...restOfProductData } = productData;
  const defaultBrand = Brands.BananaRepublic || Brands.BananaRepublicFactoryStore;
  const buyBoxProps = getBuyBoxProps({ datalayer });
  const { bottomBreadcrumbsEnabled, hideShippingEnabled, errorLogger, isImageScaling, ...restOfProps } = restOfTheProps;
  const personalizationData = usePersonalization();
  const buyBoxEl = useRef<HTMLDivElement>() as MutableRefObject<HTMLDivElement>;
  const zoomImgRef = useRef<HTMLDivElement>() as MutableRefObject<HTMLDivElement>;
  const { height: buyBoxHeight } = useResizeObserver(buyBoxEl);
  const { publish } = useMediator(ZoomMediator);

  useEffect(() => {
    publish('setZoomImgRef', [zoomImgRef]);
  }, [publish, zoomImgRef]);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  isFullStacked && useSmoothScroll();

  return (
    <div
      css={theme => [
        buyBoxContainerStyles(bottomBreadcrumbsEnabled),
        lBuyBoxStyles,
        stickyContainerWithLbuyBox,
        containerStyles(theme, buyBoxHeight, isImageScaling),
      ]}
      data-selector="buy-box"
      id="buy-box-wrapper-id"
    >
      <div ref={buyBoxEl} css={theme => buyBoxStyles(theme, buyBoxHeight)}>
        <div css={buyboxInnerPadding}>
          <div css={theme => [panelStyles(theme), panelNoSpaceStyles]} data-selector="panel">
            <div className={classNames('buybox-wrapper')}>
              <div css={clearfix()}>
                <div className="buybox-container">
                  <BrBuyBoxwithBopis
                    bottomBreadcrumbsEnabled={bottomBreadcrumbsEnabled}
                    brandName={brandName ?? defaultBrand}
                    customerId={personalizationData.customerId || ''}
                    errorLogger={errorLogger}
                    hideShippingEnabled={hideShippingEnabled}
                    personalizationData={personalizationData}
                    {...buyBoxProps}
                    {...restOfProductData}
                    {...restOfProps}
                    gidMessage={gidMessage}
                  />
                </div>

                <ProductInformationLinks errorLogger={errorLogger} />
              </div>
              <RenderByBrands brands={[Brands.BananaRepublic]}>
                <DropshipFlagsContainer />
              </RenderByBrands>
            </div>

            <FindYourFit />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrBuyBoxContainer;
