// @ts-nocheck
import { useMediator } from '@ecom-next/core/legacy/mediator';
import { Brands } from '@ecom-next/core/legacy/utility';
import TestApp from '@pdp/spec/test-app';
import { useAddToBagMediatorActions } from '@product-page/legacy/add-to-bag';
import { paramsExtraFlags, setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { useProductSelectionContext } from '@product-page/legacy/product-selection';
import type { RenderResult } from '@testing-library/react';
import { act, fireEvent, render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import { useBopisData } from '../../../../../../../../hooks/use-bopis-data';
import { usePDPReporter } from '../../../../../../../../hooks/use-pdp-reporter';
import { useFulfillmentV2 } from '../../../../../../../fulfillment/components/fulfillment-method/hooks/use-fulfillment-v2';
import FulfillmentMethod from '../index';

const matchSnapsByBrands = setupSnapshots();

const selectedStore = {
  storeAddress: {
    addressLine1: '150 NORTH STATE STREET',
    cityName: 'CHICAGO',
    countryCode: 'US',
    postalCode: '60601',
    stateProvinceCode: 'IL',
  },
  storeDistance: '0.0',
  storeId: '3997',
  storeName: '150 N. STATE ST.',
};

const fulfillmentMethodProps = {
  availabilityLoaded: false,
  bopisExclusionColor: false,
  children: undefined,
  customerId: '',
  fulfillmentDisplay: true,
  isShippingAvailable: true,
  loyaltyEnrollLinkEnabled: false,
  openModal: () => {},
  renderShippingAndReturns: function shippingAndReturns() {
    return <p>Shipping and returns</p>;
  },
  scarcityEnabled: false,
  storeChanged: false,
};

jest.mock('../../../../../../../fulfillment/components/fulfillment-method/hooks/use-fulfillment-v2');
jest.mock('../../../../../../../buy-box/mediator/mediator');
jest.mock('../../../../../../../../hooks/use-bopis-data');
jest.mock('../../../../../../../../hooks/use-pdp-reporter');
jest.mock('../../../../../../../../hooks/use-personalization');
jest.mock('@product-page/legacy/product-selection');
jest.mock('@pdp/packages/add-to-bag/hooks/use-add-to-bag-mediator-actions');
jest.mock('@ecom-next/core/legacy/mediator', () => {
  const originalModule = jest.requireActual('@ecom-next/core/legacy/mediator');
  return {
    __esModule: true,
    ...originalModule,
    useMediator: jest.fn(() => {
      return {
        publish: jest.fn(),
      };
    }),
  };
});

const getActualTranslations = () => {
  const enUs = jest.requireActual('../../../../../../../../../../state-builder/static-data/i18n/en_US.json');
  const translations = {
    'en-US': {
      translation: enUs,
    },
  };
  return translations;
};

const mockUseBopisData = (bopisData = {}) => {
  (useBopisData as jest.Mock).mockImplementation(() => ({
    active: false,
    setActive: jest.fn,
    ...bopisData,
  }));
};

const mockFulfillmentV2 = (active = false) => {
  (useFulfillmentV2 as jest.Mock).mockReturnValue({
    isFulfillmentV2Brand: true,
    isFulfillmentV2Enabled: active,
  });
};

function setupPublishBuyBoxMediator() {
  mockFulfillmentV2(true);
  const state = {
    isValidSizeSelection: true,
    selectedColor: { inStock: true },
    selectedSize: {
      bopisInStock: true,
      selectedSize: { skuId: '123' },
    },
  };
  const publishSpy = jest.fn();
  (useMediator as jest.Mock).mockImplementation(() => ({
    publish: publishSpy,
    state,
  }));
  return publishSpy;
}

const resetUseBopisData = () => {
  (useBopisData as jest.Mock).mockReset();
};

const mockBuyBoxMediator = (state = {}) => {
  (useMediator as jest.Mock).mockImplementation(() => ({
    publish: () => {},
    state,
  }));
};

const useProductSelectionContextMock = (hookState = {}): void => {
  (useProductSelectionContext as jest.Mock).mockImplementation(() => hookState);
};

const fulfillmentSelectionReporterMock = jest.fn();
const mockReporter = () => {
  (usePDPReporter as jest.Mock).mockImplementation(() => ({
    reportFullfillmentSelection: fulfillmentSelectionReporterMock,
  }));
};

const resetReporterMock = () => {
  (usePDPReporter as jest.Mock).mockReset();
};

const csmMock = <span data-testid="csmMock">Mock CSM Component</span>;
const setup = (props?: {
  bopisExclusionColor?: boolean;
  brand?: string;
  isShippingAvailable?: boolean;
  scarcityEnabled?: boolean;
  translations?: Record<string, unknown>;
}): RenderResult => {
  const brandProp = props?.brand || 'br';
  return render(
    <TestApp
      abSegValue={{ br43: props?.scarcityEnabled ? 'a' : 'x' }}
      brandMarketCtxValue={{
        brandName: brandProp,
        market: 'us',
      }}
      brandName={brandProp}
      featureFlagsCtxValue={{
        enabledFeatures: {
          ...{
            [`bopis-us-${brandProp}`]: true,
            'pdp-scarcity-bopis': props?.scarcityEnabled,
          },
        },
        featureVariables: {
          [`bopis-us-${brandProp}`]: {
            fulfillmentDisplay: true,
          },
        },
      }}
      translations={props?.translations}
    >
      <FulfillmentMethod {...fulfillmentMethodProps} {...props}>
        {csmMock}
      </FulfillmentMethod>
    </TestApp>
  );
};

const setupStore = ({ inStorePickup }: { inStorePickup: boolean }) => ({
  ...selectedStore,
  ...{ activeFeatureToggles: { inStorePickup } },
});

describe('<BrFulfillmentMethod />', () => {
  beforeEach(() => {
    resetUseBopisData();
    resetReporterMock();

    (useAddToBagMediatorActions as jest.Mock).mockReturnValue({
      addToBag: jest.fn(),
      backendErrorMessage: '',
      cleanBackendErrorMessage: jest.fn(),
      cleanUserErrorMessage: jest.fn(),
      disableAddToBag: jest.fn(),
      disabled: false,
      errorFlag: false,
      handleAddToBagResponse: jest.fn(),
      hasErrors: false,
      itemCount: 0,
      setErrorFlag: jest.fn(),
      shoppingBagItems: [],
      shouldSetAddToBagError: jest.fn(),
      subTotal: '',
      userErrorMessage: '',
    });
  });

  describe('BrFulfillmentMethod - snapshot for', () => {
    const brandArray = [{ brand: 'br' }, { brand: 'brfs' }];
    test.each(brandArray)('$brand:', ({ brand }) => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: { bopisInStock: true, inStock: true, skuId: '123' },
      });
      mockUseBopisData({ selectedStore: setupStore({ inStorePickup: true }) });
      mockReporter();
      const { container } = render(
        <TestApp
          brandMarketCtxValue={{
            brandName: brand,
            market: 'us',
          }}
          brandName={brand}
          featureFlagsCtxValue={{
            enabledFeatures: {
              'pdp-review-snapshot-drawer-us-br': false,
              'pdp-review-snapshot-drawer-us-brfs': false,
              ...{
                [`bopis-us-${brand}`]: true,
              },
            },
            featureVariables: {
              [`bopis-us-${brand}`]: {
                fulfillmentDisplay: true,
              },
            },
          }}
        >
          <FulfillmentMethod {...fulfillmentMethodProps}>{csmMock}</FulfillmentMethod>
        </TestApp>
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe('Accessibility features a', () => {
    test('radiogroup should have at least one radio element as child', () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: { inStock: true },
      });
      mockUseBopisData();
      setup({});
      const radiogroup = screen.getByRole('radiogroup');
      expect(within(radiogroup).getAllByRole('radio').length).toBeGreaterThan(0);
    });

    test('Component should be accessible', () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: { inStock: true },
      });
      mockUseBopisData();
      const { container } = setup({});
      expect(container).toBeAccessible();
    });
  });

  test('calls PDP reporter when bopis option is clicked', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: true },
      selectedSize: { bopisInStock: true, inStock: true, skuId: '123' },
    });
    mockUseBopisData({ selectedStore: setupStore({ inStorePickup: true }) });
    mockReporter();
    const { container } = setup();

    // TODO: USE SCREEN TO FIND A DESCRIPTIVE ELEMENT
    const pickupRadio = container.querySelector('#bopis-radio-bopis-active') as Element;

    act(async () => {
      await act(async () => { 

       fireEvent.click(pickupRadio); 

       })
    });

    expect(fulfillmentSelectionReporterMock).toHaveBeenCalledTimes(1);
    expect(fulfillmentSelectionReporterMock).toHaveBeenCalledWith({
      active: true,
    });
  });

  test('calls pdp reporter when Ship to address is selected', async () => {
    mockFulfillmentV2();
    fulfillmentSelectionReporterMock.mockRestore();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: true },
      selectedSize: { bopisInStock: true, inStock: true, skuId: '123' },
    });
    mockUseBopisData({
      active: true,
      selectedStore: setupStore({ inStorePickup: true }),
    });
    mockReporter();
    const { container } = setup();
    const shipToRadio = container.querySelector('#bopis-radio-bopis-inactive') as Element;
    await act(async () => { 

     fireEvent.click(shipToRadio); 

     })
    expect(fulfillmentSelectionReporterMock).toHaveBeenCalledTimes(1);
    expect(fulfillmentSelectionReporterMock).toHaveBeenCalledWith({
      active: false,
    });
  });

  test('should display select a store message if no store is selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: true },
      selectedSize: { inStock: true },
    });
    mockUseBopisData();
    setup({});
    expect(screen.queryByText('pdp.fulfillmentMethod.selectAStore')).toBeInTheDocument();
  });

  test('should displays select a store message if no store is selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: false,
      selectedColor: { inStock: true },
      selectedSize: { inStock: true },
    });
    mockUseBopisData();
    setup({});
    expect(screen.queryByText('pdp.fulfilment.needSize')).toBeInTheDocument();
  });

  test('should displays ship free message if no store is selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: true },
      selectedSize: { inStock: true },
    });
    mockUseBopisData();
    setup({});
    expect(screen.queryByText('pdp.freeShipping.title')).toBeInTheDocument();
    expect(screen.queryByText('pdp.availabilityMessageShipTo.freeMin')).toBeInTheDocument();
  });

  test('should displays pickup fulfillment method as disabled at first load', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData();
    const { container } = setup({});
    expect(screen.queryAllByRole('radio')).toHaveLength(2);
    expect(container.querySelector('[for=bopis-radio-bopis-active]')).toHaveClass('disabled');
  });

  test('should displays online fulfillment method as disabled if item at first load', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData();
    const { container } = setup({ isShippingAvailable: false });
    expect(screen.queryAllByRole('radio')).toHaveLength(2);
    expect(container.querySelector('[for=bopis-radio-bopis-inactive]')).toHaveClass('disabled');
  });

  test('should displays online fulfillment method as enabled if item in stock at first load', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData();
    const { container } = setup({});
    expect(screen.queryAllByRole('radio')).toHaveLength(2);
    expect(container.querySelector('[for=bopis-radio-bopis-inactive]')).not.toHaveClass('disabled');
  });

  test('should not display children when store is not selected and no size selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: false,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData();
    setup({});

    expect(screen.queryByText('Mock CSM Component')).not.toBeInTheDocument();
  });

  test('should not display children store is selected but no size selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: false,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData({ selectedStore: setupStore({ inStorePickup: true }) });
    setup({});
    expect(screen.queryByText('Mock CSM Component')).not.toBeInTheDocument();
  });

  test('should display children button if no size selected', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: true,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData({ selectedStore: setupStore({ inStorePickup: true }) });
    setup({});

    expect(screen.queryByText('Mock CSM Component')).toBeInTheDocument();
  });

  test('should not display notification at first load', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: false,
      selectedColor: { inStock: false },
      selectedSize: { inStock: false },
    });
    mockUseBopisData();
    setup({});
    expect(screen.queryByLabelText('pdp.fulfilment.needSize')).not.toBeInTheDocument();
  });

  test('should display selectedStore name if selectedStore exists in bopisdata', () => {
    mockFulfillmentV2();
    useProductSelectionContextMock({
      isValidSizeSelection: false,
      selectedColor: { inStock: false },
      selectedSize: {
        inStock: false,
        selectedSize: { skuId: '123' },
      },
    });
    mockUseBopisData({ selectedStore });
    setup({});
    expect(screen.queryByText(selectedStore.storeName, { exact: false })).toBeInTheDocument();
  });

  test('should call updateDeliveryLocationId event passing storeId when we have bopisActive true and storeId valid', () => {
    const publishSpy = setupPublishBuyBoxMediator();
    mockUseBopisData({ active: true, selectedStore });
    setup();
    expect(publishSpy).toHaveBeenCalledWith('updateDeliveryLocationId', [selectedStore.storeId]);
  });

  test('should call updateDeliveryLocationId event passing null when we have bopisActive false and storeId valid', () => {
    const publishSpy = setupPublishBuyBoxMediator();
    mockUseBopisData({ active: false, selectedStore });
    setup();
    expect(publishSpy).toHaveBeenCalledWith('updateDeliveryLocationId', [null]);
  });

  test('should call updateDeliveryLocationId event passing null when we have bopisActive true and storeId invalid', () => {
    const publishSpy = setupPublishBuyBoxMediator();
    const selectedStore = {
      storeAddress: {
        addressLine1: '150 NORTH STATE STREET',
        cityName: 'CHICAGO',
        countryCode: 'US',
        postalCode: '60601',
        stateProvinceCode: 'IL',
      },
      storeDistance: '0.0',
      storeId: null,
      storeName: '150 N. STATE ST.',
    };

    mockUseBopisData({ active: true, selectedStore });
    setup();
    expect(publishSpy).toHaveBeenCalledWith('updateDeliveryLocationId', [null]);
  });

  describe('BOPIS Radio button', () => {
    beforeEach(() => {
      resetUseBopisData();
    });

    describe('Should appear disabled', () => {
      test('and show "Select a store to check Pickup In-Store product availability" copy when store is not selected', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: {
            bopisInStock: false,
            inStock: false,
            selectedSize: { skuId: '123' },
          },
        });
        mockUseBopisData();
        const translations = getActualTranslations();
        setup({ translations });
        const freePickupRadio = screen.getByRole('radio', {
          name: /Pickup In-Store/i,
        });
        expect(freePickupRadio).toBeInTheDocument();

        const bopisStatusMsg = screen.getByText('Select a store to check Pickup In-Store product availability');
        expect(bopisStatusMsg).toBeInTheDocument();

        const addressButton = screen.queryByRole('button', {
          name: /current store is 150 N. STATE ST. - opens modal to change store/i,
        });
        expect(addressButton).not.toBeInTheDocument();
      });

      test('and show "Select a size for pickup options" copy when user clicks without select size', async () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: false,
          selectedColor: { inStock: true },
          selectedSize: undefined,
        });
        mockUseBopisData();
        const translations = getActualTranslations();
        setup({ translations });

        const freePickupRadio = screen.getByRole('radio', {
          name: /Pickup In-Store/i,
        });
        expect(freePickupRadio).toBeInTheDocument();

        await act(async () => { 

         userEvent.click(freePickupRadio); 

         })

        const bopisStatusMsg = await screen.findByText('Select a size for pickup options');
        expect(bopisStatusMsg).toBeInTheDocument();

        const addressButton = screen.queryByRole('button', {
          name: /current store is 150 N. STATE ST. - opens modal to change store/i,
        });
        expect(addressButton).not.toBeInTheDocument();
      });

      test('and show "Unavailable at 150 N. STATE ST." copy when store has no BOPIS available', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: {
            bopisInStock: false,
            inStock: false,
            selectedSize: { skuId: '123' },
          },
        });
        mockUseBopisData({ active: false, selectedStore });
        const translations = getActualTranslations();
        setup({ translations });

        const freePickupRadio = screen.getByRole('radio', {
          name: /Pickup In-Store/i,
        });
        expect(freePickupRadio).toBeInTheDocument();

        const bopisStatusMsg = screen.getByText('Unavailable at 150 N. STATE ST.');
        expect(bopisStatusMsg).toBeInTheDocument();
      });
    });

    describe('Should appear be enabled', () => {
      test('and show "Pickup In-Store" + hide "Order by 2pm to get today" + Store Address copies when Product is available at selected store', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: {
            bopisInStock: true,
            selectedSize: { skuId: '123' },
          },
        });
        mockUseBopisData({ active: true, selectedStore });
        const translations = getActualTranslations();
        setup({ translations });
        const freePickupRadio = screen.getByRole('radio', {
          name: /Pickup In-Store/i,
        });
        expect(freePickupRadio).toBeInTheDocument();
        expect(freePickupRadio).toBeEnabled();

        const bopisStatusMsg = screen.queryByText('Order by 2pm to get today');
        expect(bopisStatusMsg).not.toBeInTheDocument();

        const addressButton = screen.queryByRole('button', {
          name: /current store is 150 N. STATE ST. - opens modal to change store/i,
        });
        expect(addressButton).not.toBeInTheDocument();
      });
    });
  });

  describe('Low stock message for BOPIS', () => {
    beforeEach(() => {
      resetUseBopisData();
    });

    test('should display when feature and ab segment enabled', () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: {
          bopisInventoryStatusId: 1,
          inStock: true,
          selectedSize: { skuId: '123' },
        },
      });
      mockUseBopisData({ active: true, selectedStore });
      setup({ scarcityEnabled: true });
      expect(screen.queryByText('changeStoreModal.lowStock')).toBeInTheDocument();
    });

    test('should not display when feature disabled or ab segment disabled', () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: false,
        selectedColor: { inStock: false },
        selectedSize: { inStock: false },
      });
      mockUseBopisData();
      setup({ scarcityEnabled: false });
      expect(screen.queryByText('pdp.fulfillmentMethod.lowStockForPickup')).not.toBeInTheDocument();
    });
  });

  describe('Error Notifications', () => {
    beforeEach(() => {
      resetUseBopisData();
    });

    test('should display need size error', async () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: false,
        selectedColor: { inStock: false },
        selectedSize: { inStock: false },
      });
      mockUseBopisData();
      const { container } = setup({});
      // display message at rendering
      expect(screen.queryByText('pdp.fulfilment.needSize')).toBeInTheDocument();
      expect((screen.queryByText('pdp.fulfilment.needSize') as HTMLElement).closest('div')).not.toHaveClass(
        'fulfillment-method-pickup__notification'
      );

      await act(async () => { 

       // display error after clicking on BOPIS option
       fireEvent.click(container.querySelector('[for=bopis-radio-bopis-active]') as HTMLElement); 

       })
      expect((screen.queryByText('pdp.fulfilment.needSize') as HTMLElement).closest('div')).toHaveClass(
        'fulfillment-method-pickup__notification'
      );
    });

    test('should display need store error', async () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: { bopisInStock: false, inStock: true, skuId: '123' },
      });
      mockUseBopisData();
      const { container } = setup({});
      // display message at rendering
      expect(screen.queryByText('pdp.fulfillmentMethod.selectAStore') as HTMLElement).toBeInTheDocument();
      expect((screen.queryByText('pdp.fulfillmentMethod.selectAStore') as HTMLElement).closest('div')).not.toHaveClass(
        'fulfillment-method-pickup__notification'
      );

      await act(async () => { 

       // display error after clicking on BOPIS option
       fireEvent.click(container.querySelector('[for=bopis-radio-bopis-active]') as HTMLElement); 

       })
      expect((screen.queryByText('pdp.fulfillmentMethod.selectAStore') as HTMLElement).closest('div')).toHaveClass(
        'fulfillment-method-pickup__notification'
      );
    });
    describe('Local storage handling', () => {
      test('should select BOPIS due to localStorage', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: { bopisInStock: true, inStock: true, skuId: '123' },
        });
        mockUseBopisData({
          active: true,
          bopisAvailable: true,
          selectedStore: setupStore({ inStorePickup: true }),
        });
        setup({});

        expect(screen.queryAllByRole('radio')[0]).toBeChecked();
        expect(screen.queryAllByRole('radio')[1]).not.toBeChecked();
      });

      test('should select BOPIS due to shipping unavailable', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: { bopisInStock: false, inStock: true, skuId: '123' },
        });
        mockUseBopisData({
          active: false,
          bopisAvailable: true,
          selectedStore: setupStore({ inStorePickup: true }),
        });
        setup({ isShippingAvailable: false });
        expect(screen.queryAllByRole('radio')[0]).not.toBeChecked();
        expect(screen.queryAllByRole('radio')[1]).not.toBeChecked();
      });

      test('should select shipping due to localStorage', () => {
        mockFulfillmentV2();
        useProductSelectionContextMock({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: { bopisInStock: false, inStock: true, skuId: '123' },
        });
        mockUseBopisData({
          active: false,
          bopisAvailable: true,
          bopisInitialized: false,
          selectedStore: setupStore({ inStorePickup: true }),
        });
        setup({});

        expect(screen.queryAllByRole('radio')[0]).not.toBeChecked();
        expect(screen.queryAllByRole('radio')[1]).toBeChecked();
      });

      test('selects shipping when Bopis is active, but unavailable for selected size', () => {
        mockFulfillmentV2();
        mockBuyBoxMediator({
          isValidSizeSelection: true,
          selectedColor: { inStock: true },
          selectedSize: { bopisInStock: false, inStock: true, skuId: '123' },
        });
        mockUseBopisData({
          active: true,
          bopisAvailable: false,
          selectedStore: setupStore({ inStorePickup: false }),
        });
        setup({});

        expect(screen.queryAllByRole('radio')[0]).not.toBeChecked();
        expect(screen.queryAllByRole('radio')[1]).not.toBeChecked();
      });
    });
  });

  describe('Fulfillment V2', () => {
    const text = 'pdp.fulfillmentMethodV2.accordionLabel';

    test('accordion should NOT be shown if br redesign', () => {
      mockFulfillmentV2();
      useProductSelectionContextMock({
        isValidSizeSelection: true,
        selectedColor: { inStock: true },
        selectedSize: { inStock: true },
      });
      mockUseBopisData();
      setup({ brand: 'br' });

      expect(screen.queryByText('pdp.fulfillmentMethod.selectAStore')).toBeInTheDocument();

      const accordionButton = screen.queryByText(text) as Element;
      expect(accordionButton).not.toBeInTheDocument();
    });
  });
});

describe('<FulfillmentMethod /> styles', () => {
  const renderFuncStyle = (brandName, enabledFeatures, abSeg) => (
    <TestApp
      abSegValue={abSeg}
      brandMarketCtxValue={{
        brandName,
        market: 'us',
      }}
      brandName={brandName}
      featureFlagsCtxValue={{
        enabledFeatures: { 'bopis-us-on': true, ...enabledFeatures },
        featureVariables: {
          'bopis-us-on': {
            fulfillmentDisplay: true,
          },
        },
      }}
    >
      <FulfillmentMethod {...fulfillmentMethodProps}>{csmMock}</FulfillmentMethod>
    </TestApp>
  );

  matchSnapsByBrands(renderFuncStyle, 'renders correctly', {
    brands: [Brands.BananaRepublic],
    flags: { ...paramsExtraFlags.isBrRedesign2024Ph2 },
  });
});
