// @ts-nocheck
import { useFulfillmentClassnames } from '../use-fulfillment-classnames';

describe('useFulfillmentClassnames', () => {
  test('display fulfillment when flag variable set to true', () => {
    const { fulfillmentMethodDisplay } = useFulfillmentClassnames({ fulfillmentDisplay: true });
    expect(fulfillmentMethodDisplay).toBe('bopis-fulfillment');
  });

  test('hide fulfillment when flag variable set to false', () => {
    const { fulfillmentMethodDisplay } = useFulfillmentClassnames({ fulfillmentDisplay: false });
    expect(fulfillmentMethodDisplay).toBe('bopis-fulfillment hide');
  });

  test('pickupUnavailable is true', () => {
    const { pickupAvailabilityMessageClassNames, pickupAvailabilityStatusClassNames } = useFulfillmentClassnames({
      pickupUnavailable: true,
    });

    expect(pickupAvailabilityMessageClassNames).toBe(
      'fulfillment-method-pickup__message fulfillment-method-pickup__message--unavailable'
    );
    expect(pickupAvailabilityStatusClassNames).toBe(
      'fulfillment-method-pickup__info fulfillment-method-pickup__info--unavailable'
    );
  });

  test('pickupUnavailable is false', () => {
    const { pickupAvailabilityMessageClassNames, pickupAvailabilityStatusClassNames } = useFulfillmentClassnames({
      pickupUnavailable: false,
    });

    expect(pickupAvailabilityMessageClassNames).toBe('fulfillment-method-pickup__message');
    expect(pickupAvailabilityStatusClassNames).toBe('fulfillment-method-pickup__info ');
  });

  test('ship to address is available', () => {
    const { shipAvailabilityStatusClassNames } = useFulfillmentClassnames({ isShippingAvailable: true });
    expect(shipAvailabilityStatusClassNames).toBe('fulfillment-method-ship__info ');
  });

  test('ship to address is unavailable', () => {
    const { shipAvailabilityStatusClassNames } = useFulfillmentClassnames({ isShippingAvailable: false });
    expect(shipAvailabilityStatusClassNames).toBe(
      'fulfillment-method-ship__info fulfillment-method-ship__info--unavailable'
    );
  });
});
