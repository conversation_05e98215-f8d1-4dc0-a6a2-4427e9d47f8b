// @ts-nocheck
'use client';

import type { SerializedStyles, Theme } from '@ecom-next/core/react-stitch';
import { css } from '@ecom-next/core/react-stitch';
import { sdsBreakpoint, setupRedesignStyles } from '@product-page/legacy/styles';

import { fulfillmentStatusStyleDefault } from './default.styles';

export const fulfillmentStatusStylesForGap = (theme: Theme): SerializedStyles => {
  const getStyles = setupRedesignStyles(theme).isGapBuyBoxRedesign2024;

  const typographyStyles = css`
    font-size: 0.875rem;
    font-weight: 500;
    font-style: normal;
    line-height: 1rem;
    letter-spacing: 0.014rem;

    @media (min-width: ${sdsBreakpoint.medium}) {
      font-size: 1rem;
      font-weight: 500;
      line-height: 1.375rem;
      letter-spacing: 0.02rem;
    }
  `;

  return getStyles(
    css`
      ${typographyStyles}

      .fulfillment-method-pickup__price {
        text-transform: lowercase;

        &:first-letter {
          text-transform: uppercase;
        }
      }

      &.fulfillment-method-pickup__status--unavailable {
        opacity: 1;
        color: rgb(var(--color-neutrals-700));
        margin-bottom: 0.25rem;
        text-transform: lowercase;

        &:first-letter {
          text-transform: uppercase;
        }
      }
    `,
    fulfillmentStatusStyleDefault
  );
};
