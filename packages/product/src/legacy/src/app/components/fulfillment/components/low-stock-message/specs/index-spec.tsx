// @ts-nocheck
/* eslint-disable react/display-name */

import type { Brand<PERSON> } from "@ecom-next/core/react-stitch";
import { paramsExtraFlags, setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { render } from '@testing-library/react';
import React from 'react';

import TestApp from '../../../../../../../spec/test-app';
import LowStockMessage from '../index';

const LOW_STOCK_INVENTORY_STATUS_ID = 1;
const IN_STOCK_INVENTORY_STATUS_ID = 0;
const BOPIS_ACTIVE = true;
const BOPIS_INACTIVE = false;
const matchSnapshotsAllBrands = setupSnapshots();

const renderLowStockMessage = (bopisActive, inventoryStatusId) =>
  render(
    <TestApp>
      <LowStockMessage bopisActive={bopisActive} inventoryStatusId={inventoryStatusId} />
    </TestApp>
  );

describe('#LowStockMessage', () => {
  test('should display the message for pickup when BOPIS active', () => {
    const { container } = renderLowStockMessage(BOPIS_ACTIVE, LOW_STOCK_INVENTORY_STATUS_ID);
    expect(container.innerHTML).toContain('changeStoreModal.lowStock');
  });

  test('should display the message for shipment when BOPIS not active', () => {
    const { container } = renderLowStockMessage(BOPIS_INACTIVE, LOW_STOCK_INVENTORY_STATUS_ID);
    expect(container.innerHTML).toContain('pdp.fulfillmentMethod.lowStockForShipment');
  });

  test('should not display the message when inventoryStatusId is different of low inventory id', () => {
    const { container } = renderLowStockMessage(BOPIS_ACTIVE, IN_STOCK_INVENTORY_STATUS_ID);
    // eslint-disable-next-line jest-dom/prefer-empty
    expect(container.innerHTML).toBe('');
  });
});

const getLowStockMessage = bopisActive => (brandName: Brands, enabledFeatures, abSeg) => {
  return (
    <TestApp
      abSegValue={abSeg}
      brandMarketCtxValue={{
        brandName,
        market: 'us',
      }}
      brandName={brandName}
      featureFlagsCtxValue={{
        enabledFeatures: { 'bopis-us-on': true, ...enabledFeatures },
        featureVariables: {
          'bopis-us-on': {
            fulfillmentDisplay: true,
          },
        },
      }}
    >
      <LowStockMessage bopisActive={bopisActive} inventoryStatusId={LOW_STOCK_INVENTORY_STATUS_ID} />
    </TestApp>
  );
};

describe('<LowStockMessage /> styles', () => {
  describe('message for shipment', () => {
    matchSnapshotsAllBrands(getLowStockMessage(BOPIS_INACTIVE), 'renders correctly:', {
      flags: paramsExtraFlags.isBrRedesign2024Ph2,
    });
  });

  describe('message for pickup', () => {
    matchSnapshotsAllBrands(getLowStockMessage(BOPIS_ACTIVE), 'renders correctly:');
  });
});
