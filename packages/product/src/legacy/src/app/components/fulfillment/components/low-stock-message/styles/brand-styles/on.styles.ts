// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";

export const messageForShipmentStylesForON = (theme: Theme): SerializedStyles =>
  css`
    align-items: center;
    justify-content: center;
    display: flex;
    margin-bottom: 16px;
    margin-top: 10px;
    align-items: flex-end;
    padding-left: 9px;

    .low-stock-message--label {
      color: ${theme.color.r1};
      ${theme.font.primary};
      font-size: 1.125rem;
      font-weight: 700;
      margin-left: 8px;
    }
  `;

export const messageForPickupStylesForON = (theme: Theme): SerializedStyles =>
  css`
    display: flex;
    margin-left: calc(1.5em + 0.75rem);
    margin-top: 8px;

    .low-stock-message--label {
      color: ${theme.color.gray80};
      ${theme.font.primary};
      font-size: 1.125rem;
      font-weight: 700;
      line-height: 23px;
      margin-right: 6px;
    }

    .tooltip-container:before {
      margin-left: -1.313rem;
    }
  `;
