// @ts-nocheck
'use client';

import type { SerializedStyles, Theme } from '@ecom-next/core/react-stitch';
import { css } from '@ecom-next/core/react-stitch';
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

import { carouselFadeHeight, carouselPadding, carouselPaginationWidth } from './constants.styles';

export const onHoverZoomDisplayStyles = (): SerializedStyles => {
  return css`
    display: none;
    pointer-events: none;

    @media (min-width: ${sdsBreakpoint.xLarge}) {
      display: block;
    }

    position: absolute;
    top: 0;
    left: 100%;
    width: 50%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    opacity: 0;
    margin-left: 0.5rem;

    .zoom-image {
      max-width: none;
      min-width: 300%;
      height: auto;
    }
  `;
};

export const onCarouselStyles = (): SerializedStyles => {
  return css`
    position: relative;
    line-height: 0.75em;
    min-height: ${carouselFadeHeight};

    @media (min-width: ${sdsBreakpoint.large}) {
      padding-left: ${carouselPadding};
    }

    @media (min-width: ${sdsBreakpoint.xLarge}) {
      padding-left: ${carouselPaginationWidth};

      &::before,
      &::after {
        width: ${carouselPaginationWidth};
      }
    }

    @media (min-width: ${sdsBreakpoint.large}) and (min-aspect-ratio: 1 / 1), (min-width: ${sdsBreakpoint.xLarge}) {
      &::before,
      &::after {
        width: ${carouselPadding};
      }
    }

    &::before,
    &::after {
      display: none;
      content: '';
      position: absolute;
      left: -15px;
      height: ${carouselFadeHeight};
      z-index: 100;
      pointer-events: none;
      opacity: 0;
      transition: all 0.2s;
    }

    &::before {
      top: 0;
      background-image: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }

    &::after {
      bottom: 0;
      background-image: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    }

    .pointerevents &::before,
    .pointerevents &::after {
      display: block;
    }

    .carousel_is-overflowing {
      &:not(.carousel_is-scrolled-to-top) {
        &::before {
          opacity: 1;
        }
      }

      &:not(.carousel_is-scrolled-to-bottom) {
        &::after {
          opacity: 1;
        }
      }
    }
  `;
};

export const onProductPhotoHoverStyles = (): SerializedStyles => {
  return css`
    position: absolute;
    top: 0;
    left: ${carouselPaginationWidth};
    vertical-align: top;
    text-align: center;
    width: ${`calc(100% - ${carouselPaginationWidth})`};
    visibility: hidden;
    opacity: 0;
    transition:
      visibility 150ms,
      opacity 150ms ease-in;

    img {
      width: 100%;
    }

    &.product-photo--item-hover__hovering {
      z-index: 2;
      visibility: visible;
      opacity: 1;
    }

    @media (max-width: ${sdsBreakpoint.xLarge}) {
      left: ${carouselPadding};
      width: ${`calc(100% - ${carouselPadding})`};
    }
  `;
};

export const onCarouselWrapperStyles = (theme: Theme): SerializedStyles => {
  return css`
    position: relative;

    .model-size-selector {
      margin-top: 1rem;
      justify-content: center;
      align-items: center;
      margin-left: 15%;
    }

    .model-size-selector__text {
      margin: 0 1.625rem 0 1rem;
    }

    .model-size-selector__button {
      margin-right: 1.25rem;
      padding: 0.75rem 1.625rem;
      background-color: ${theme.color.gray05};
      font-size: 1.125rem;
      line-height: 1.3rem;

      &[aria-pressed='true'] {
        background-color: ${theme.color.b1};
        color: #fff;
        font-weight: 700;
      }
    }
  `;
};

export const onProductPhotoStyles = (): SerializedStyles => {
  return css`
    box-sizing: border-box;
    text-align: center;
    overflow: hidden;
    position: relative;
    max-height: none;

    @media (min-width: 768px) {
      padding-bottom: 0;
    }

    & .slick-list {
      height: 0;
      padding-bottom: 133.33333%;
    }

    &.product-photo .slick-slide {
      display: flex;
      justify-content: center;

      &.slick-active {
        z-index: 1;
      }

      @media (min-width: 1024px) {
        max-width: 793px !important;
      }

      > div {
        display: block !important;
        position: relative;
        width: 100%;
        max-width: none;
      }

      img {
        width: 100%;
        height: auto;
      }
    }
  `;
};
