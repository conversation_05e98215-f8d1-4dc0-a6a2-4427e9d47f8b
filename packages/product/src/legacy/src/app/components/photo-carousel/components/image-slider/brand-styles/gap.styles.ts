// @ts-nocheck
'use client'

import { css } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

export const paginationSlickSliderStylesForGap = (): InterpolationPrimitive => {
  return css`
    ul.pdp-slick-dots.pdp-slick-thumb {
      z-index: 1;
      white-space: nowrap;
      display: flex !important;
      position: absolute;
      transform: translateX(-50%);
      max-width: 30rem;
      width: 100%;
      left: 50%;
      bottom: 0;
      text-align: center;
      -webkit-box-pack: center;
      justify-content: center;
      list-style: none;
      margin-top: 0px;
      min-height: 0px;
      flex-flow: row nowrap;
      opacity: 0.8;
    }

    .pdp-slick-dots > li {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 0px 0px 0.25rem;

      &.slick-active div {
        background-color: #444444;

        &.is-video {
          background-color: transparent;
          border-color: transparent transparent transparent #444444;
        }
      }
    }
  `;
};

export const indicatorsSlickSliderStylesForGap = (): InterpolationPrimitive => {
  return css`
    .pagination__item.video_is-ready {
      @media (max-width: 768px) {
        border-radius: 0.3125em 0px 0px 0.3125em;
        overflow: hidden;
        touch-action: manipulation;
        display: inline-block;
        line-height: 0.5em;
        width: 41px;
        margin: 0px 1px 0px 0px;
        border: 0px;
        height: 2px;
        opacity: 0.8;
        min-width: 0.25em;
        -webkit-box-flex: 1;
        flex-grow: 1;
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
  `;
};
