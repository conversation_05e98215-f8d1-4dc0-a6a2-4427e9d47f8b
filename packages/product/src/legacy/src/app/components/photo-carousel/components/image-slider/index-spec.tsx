// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import { createSerializer } from '@emotion/jest';
import TestApp, { defaultBreakpointData } from '@pdp/spec/test-app';
import { fireEvent, render, waitFor } from '@testing-library/react';
import React, { createRef } from 'react';
import renderer from 'react-test-renderer';

import type { BrandAndMarket } from '../../../brand-market-provider';
import CarouselMediator from '../../mediator';
import ImageSlider from './index';
import useHoverZoom from './use-hover-zoom';

jest.mock('./use-hover-zoom');
jest.mock('../video-player', () => {
  return function VideoPlayer() {
    return <>VideoPlayer</>;
  };
});
const productImages = [
  {
    colorId: '',
    id: 'image1',
    imageMapId: 'imageId1',
    large: 'http://test/large-1.jpg',
    medium: 'http://test/medium-1.jpg',
    small: 'http://test/small-1.jpg',
    thumbnail: 'http://test/thumb-1.jpg',
    xlarge: 'http://test/xlarge-1.jpg',
  },
  {
    colorId: '',
    id: 'image2',
    imageMapId: 'imageId2',
    large: 'http://test/large-2.jpg',
    medium: 'http://test/medium-2.jpg',
    small: 'http://test/small-2.jpg',
    thumbnail: 'http://test/thumb-2.jpg',
    xlarge: 'http://test/xlarge-2.jpg',
  },
  {
    colorId: '',
    id: 'image3',
    imageMapId: 'imageId3',
    large: 'http://test/large-3.jpg',
    medium: 'http://test/medium-3.jpg',
    small: 'http://test/small-3.jpg',
    thumbnail: 'http://test/thumb-3.jpg',
    xlarge: 'http://test/xlarge-3.jpg',
  },
  {
    colorId: '',
    id: 'image4',
    imageMapId: 'imageId4',
    large: 'http://test/large-4.jpg',
    medium: 'http://test/medium-4.jpg',
    small: 'http://test/small-4.jpg',
    thumbnail: 'http://test/thumb-4.jpg',
    video: 'http://test/video.mp4',
    xlarge: 'http://test/xlarge-4.jpg',
  },
];

const publishSpy = jest.fn();
const sliderRef = createRef<any>();

const defaultProps = {
  errorLogger: () => {},
  images: [],
  productTitle: 'product title',
};

const mediatorState = {
  activeAltImage: createRef<HTMLLIElement>(),
  activeImageIndex: 0,
  carouselLock: false,
  images: productImages,
  isLocked: false,
  isZoomed: false,
  slider: sliderRef,
};
const mockTrackingEvent = jest.fn();

function renderImageSlider(props = {}, testAppProps = {}) {
  return render(
    <TestApp {...testAppProps}>
      <CarouselMediator.Context.Provider value={{ publish: publishSpy, state: mediatorState }}>
        <ImageSlider {...defaultProps} {...props} />
      </CarouselMediator.Context.Provider>
    </TestApp>
  );
}

expect.addSnapshotSerializer(createSerializer());
describe.skip('<ImageSlider />', () => {
  beforeEach(() => {
    global.window.FS = mockTrackingEvent;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    delete global.window.FS;
    jest.resetModules();
  });

  describe('<ImageSlider/> snaps', () => {
    test.each([
      { brand: Brands.Athleta },
      { brand: Brands.OldNavy },
      { brand: Brands.BananaRepublic },
      { brand: Brands.BananaRepublicFactoryStore },
      { brand: Brands.Gap },
      {
        brand: Brands.GapFactoryStore,
      },
    ])('Renders ImageSlider styles for $brand', async ({ brand }) => {
      const component = renderer.create(
        <TestApp brandMarketCtxValue={{ brandName: brand, market: 'us' } as BrandAndMarket} brandName={brand}>
          <CarouselMediator.Context.Provider value={{ publish: publishSpy, state: mediatorState }}>
            <ImageSlider {...defaultProps} />
          </CarouselMediator.Context.Provider>
        </TestApp>
      );
      await new Promise(resolve => setTimeout(resolve));
      const tree = component.toJSON();
      expect(tree).toMatchSnapshot();
    });

    describe('Banana Republic Redesign 2024', () => {
      test('when the flag is TRUE should match new styles for SM devices', async () => {
        const component = renderer.create(
          <TestApp
            brandMarketCtxValue={{ brandName: Brands.BananaRepublic, market: 'us' } as BrandAndMarket}
            brandName={Brands.BananaRepublic}
            breakpointCtxValue={{ ...defaultBreakpointData, size: 'small' }}
            featureFlagsCtxValue={{ enabledFeatures: { 'br-redesign-2024-ph2': true } }}
          >
            <CarouselMediator.Context.Provider value={{ publish: publishSpy, state: mediatorState }}>
              <ImageSlider {...defaultProps} />
            </CarouselMediator.Context.Provider>
          </TestApp>
        );
        await new Promise(resolve => setTimeout(resolve));
        const tree = component.toJSON();
        expect(tree).toMatchSnapshot();
      });

      test('when the flag is FALSE should match default styles for SM devices', async () => {
        const component = renderer.create(
          <TestApp
            brandMarketCtxValue={{ brandName: Brands.BananaRepublic, market: 'us' } as BrandAndMarket}
            brandName={Brands.BananaRepublic}
            breakpointCtxValue={{ ...defaultBreakpointData, size: 'small' }}
            featureFlagsCtxValue={{ enabledFeatures: { 'br-redesign-2024-ph2': false } }}
          >
            <CarouselMediator.Context.Provider value={{ publish: publishSpy, state: mediatorState }}>
              <ImageSlider {...defaultProps} />
            </CarouselMediator.Context.Provider>
          </TestApp>
        );
        await new Promise(resolve => setTimeout(resolve));
        const tree = component.toJSON();
        expect(tree).toMatchSnapshot();
      });
    });
  });

  test('operates the slider in "fade" mode on large screens', () => {
    renderImageSlider();
    expect(sliderRef.current.props.fade).toBe(true);
  });

  test('disabled swiping on large screens', () => {
    renderImageSlider();
    expect(sliderRef.current.props.draggable).toBe(false);
  });

  test('operates the slider in regular (non-fade) mode on small screens', () => {
    renderImageSlider(undefined, {
      breakpointCtxValue: { greaterOrEqualTo: () => false, smallerThan: () => true },
    });
    expect(sliderRef.current.props.fade).toBe(false);
  });

  test('enables swiping on small screens', () => {
    renderImageSlider(undefined, {
      breakpointCtxValue: { greaterOrEqualTo: () => false, smallerThan: () => true },
    });
    expect(sliderRef.current.props.draggable).toBe(true);
  });

  test('lazy loads large images', () => {
    const { container } = renderImageSlider();
    const thirdImage = container.querySelectorAll('img')[3];
    expect(thirdImage.src).toBe('http://test/small-3.jpg');
  });

  test('initializes hoverZoom', () => {
    renderImageSlider();
    expect(useHoverZoom).toHaveBeenCalled();
  });

  test('publishes fullstory event', async () => {
    const { container } = renderImageSlider(
      {},
      {
        breakpointCtxValue: { greaterOrEqualTo: () => false, smallerThan: () => true },
      }
    );
    await act(async () => { 

     fireEvent.click(container.querySelectorAll('.pdp-slick-dots .pagination__item')[1]); 

     })

    await waitFor(() => {
      expect(mockTrackingEvent.mock.calls[0][0]).toStrictEqual('trackEvent');
    });
  });

  test('logs error when fullstory fails', async () => {
    global.window.FS = undefined;
    const { container } = renderImageSlider(
      {},
      {
        breakpointCtxValue: { greaterOrEqualTo: () => false, smallerThan: () => true },
      }
    );
    await act(async () => { 

     fireEvent.click(container.querySelectorAll('.pdp-slick-dots .pagination__item')[1]); 

     })

    await waitFor(() => {
      expect(mockTrackingEvent).not.toHaveBeenCalled();
    });
  });
});
