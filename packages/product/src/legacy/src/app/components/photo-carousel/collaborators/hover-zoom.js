// @ts-nocheck
import { addPageAction } from '@ecom-next/core/reporting';
import debounce from 'lodash/debounce';
import throttle from 'lodash/throttle';

class HoverZoom {
  constructor() {
    this.cache = new WeakMap();

    this.zoomDisplay = null;

    this.engaged = false;
    this.curImage = null;
    this.bounds = {};
    this.isSuspended = false;

    this.zoomables = [];
    this.zoomImages = [];
    this.engage = this.engage.bind(this);
    this.disengage = this.disengage.bind(this);
    this.clickHandler = this.clickHandler.bind(this);
    this.moveHandler = this.moveHandler.bind(this);
    this.setZoomContainerWidth = this.setZoomContainerWidth.bind(this);
    this.updateBounds = this.updateBounds.bind(this);
    this.reset = this.reset.bind(this);
    this.suspend = this.suspend.bind(this);
    this.resume = this.resume.bind(this);
  }

  init({ zoomables, zoomDisplay, shouldHoverZoom }) {
    if (zoomables.length) {
      window.removeEventListener('scroll', this.updateBounds);
      window.removeEventListener('resize', this.setZoomContainerWidth);
    }

    this.zoomDisplay = zoomDisplay;
    this.zoomables = zoomables;
    this.setZoomContainerWidth();
    window.addEventListener('resize', this.setZoomContainerWidth);
    window.addEventListener('scroll', debounce(this.updateBounds, 100));
    if (shouldHoverZoom) {
      this.setZoomDisplayVisibility(false);
      this.stageImage(0);
      this.zoomables.forEach(zoomable => {
        zoomable.classList.add('hover-zoom-in');
        this.addEventHandlers(zoomable);
      });
    }

    return this;
  }

  reset() {
    this.cache = new WeakMap();

    this.engaged = false;
    this.curImage = null;
    this.bounds = {};
  }

  queue(index, callback) {
    const zoomable = this.zoomables[index];
    let result;
    if (this.cache.has(zoomable)) {
      // Don't load image more than once
      if (this.cache.get(zoomable) === 'PENDING') {
        setTimeout(() => {
          this.queue(index, callback);
        }, 50);
      } else if (this.cache.get(zoomable)) {
        this.curImage = this.cache.get(zoomable);
        result = callback(null, this.cache.get(zoomable));
      } else {
        const errorMessage = `Failed to load zoom image for zoomable: ${zoomable}`;
        result = callback(new Error(errorMessage));
      }
    } else {
      this.cache.set(zoomable, 'PENDING');
      result = this.loadZoomImage(index, callback);
    }

    return result;
  }

  stageImage(index) {
    return this.queue(index, (err, loadedZoomable) => {
      if (err) {
        return console.error(err);
      }
      return this.attachImage(loadedZoomable.zoomImage);
    });
  }

  loadZoomImage(index, callback) {
    const zoomable = this.zoomables[index];
    const image = zoomable.querySelector('img');
    const zoomImage = document.createElement('img');
    const indicator = HoverZoom.attachIndicator(zoomable);

    const newImage = {
      image,
      indicator,
      zoomImage,
      zoomable,
    };
    this.zoomImages[index] = zoomImage;
    zoomImage.addEventListener('load', () => {
      this.curImage = newImage;
      zoomImage.classList.add('zoom-image');
      zoomImage.width = this.curImage.zoomImage.width;
      zoomImage.height = this.curImage.zoomImage.height;
      this.attachImage(zoomImage);
      this.updateBounds();
      this.setIndicatorSize(indicator);
      // update the this.cache with the loaded zoomable
      this.cache.set(zoomable, newImage);
      return callback(null, newImage);
    });

    zoomImage.addEventListener('error', err => {
      // if the image fails to load, we'll this.cache it as false
      // so subsequent queue calls don't need to wait for the load error
      this.cache.set(zoomable, false);
      return callback(new Error(err));
    });
    // kick off the loading
    zoomImage.src = HoverZoom.zoomSrc(zoomable);
  }

  /**
   * attachImage
   * @memberof HoverZoom
   *
   * @param {Object} zoomImage: reference to a DOM element, the current zoom image
   */

  attachImage(zoomImage) {
    if (this.zoomDisplay) {
      try {
        while (this.zoomDisplay.firstChild) {
          this.zoomDisplay.removeChild(this.zoomDisplay.firstChild);
        }
        this.zoomDisplay.appendChild(zoomImage);
      } catch (e) {
        addPageAction('HoverZoomError', { errorMessage: e, zoomDisplay: this.zoomDisplay });
        console.error('Hover Zoom - attachImage Error: ', e);
      }
    }
  }

  /**
   * updateBounds
   * @memberof HoverZoom
   *
   * updates the size and positioning data held in the global variable _this.bounds
   */

  updateBounds() {
    if (this.curImage && this.zoomDisplay) {
      this.bounds = {
        image: this.curImage.image.getBoundingClientRect(),
        indicator: this.curImage.indicator.getBoundingClientRect(),
        zoomDisplayBounds: this.zoomDisplay.getBoundingClientRect(),
        zoomImage: this.curImage.zoomImage.getBoundingClientRect(),
        zoomable: this.curImage.zoomable.getBoundingClientRect(),
      };
    }
  }

  /**
   * indicatorFollow
   * @memberof HoverZoom
   *
   * @param {Object} e: a native browser event object
   */
  indicatorFollow(e) {
    if (!this.engaged) {
      return false;
    }
    const { zoomable, image, indicator } = this.bounds;
    const imageOffset = image.left - zoomable.left;
    const indicatorMAX = {
      x: imageOffset + image.width - indicator.width,
      y: image.height - indicator.height,
    };
    const x = Math.max(Math.min(e.clientX - image.left + imageOffset - indicator.width / 2, indicatorMAX.x), imageOffset);
    const y = Math.max(Math.min(e.clientY - image.top - indicator.height / 2, indicatorMAX.y), 0);
    HoverZoom.positionElement(this.curImage.indicator, `${x}px`, `${y}px`);

    return true;
  }

  /**
   * addEventHandlers
   * @memberof HoverZoom
   *
   * @param {Object} zoomable: an element marked as zoomable
   */

  addEventHandlers(zoomable) {
    zoomable.addEventListener('click', this.clickHandler);
    zoomable.addEventListener('mousemove', throttle(this.moveHandler, 16));
    zoomable.addEventListener('mouseleave', this.disengage);
  }

  /**
   * removeEventHandlers
   * @memberof HoverZoom
   *
   * @param {Object} zoomable: an element marked as zoomable
   */

  removeEventHandlers(zoomable) {
    zoomable.removeEventListener('click', this.clickHandler);
    zoomable.removeEventListener('mousemove', this.moveHandler);
    zoomable.removeEventListener('mouseleave', this.disengage);
  }

  /**
   * suspend
   * @memberof HoverZoom
   *
   * removes all event handlers and the zoom cursor
   */

  suspend() {
    window.removeEventListener('scroll', this.updateBounds);
    window.removeEventListener('resize', this.setZoomContainerWidth);
    this.zoomables.forEach(zoomable => {
      this.removeEventHandlers(zoomable);
    });
    this.isSuspended = true;
  }

  /**
   * resume
   * @memberof HoverZoom
   *
   * adds all event handlers and the zoom cursor
   */

  resume() {
    this.setZoomContainerWidth();
    window.addEventListener('resize', this.setZoomContainerWidth);
    window.addEventListener('scroll', debounce(this.updateBounds, 100));
    this.zoomables.forEach(zoomable => {
      this.addEventHandlers(zoomable);
    });
    this.isSuspended = false;
  }

  /**
   * engage
   * @memberof HoverZoom
   *
   * Attaches the current zoom image to the DOM,
   * updates size and positioning data,
   * and shows the zoom display
   */

  engage() {
    if (!this.engaged) {
      this.engaged = true;
      this.attachImage(this.curImage.zoomImage);
      this.curImage.zoomable.classList.remove('hover-zoom-in');
      this.curImage.zoomable.classList.add('hover-zoom-out');
      this.updateBounds();
      this.setIndicatorSize(this.curImage.indicator);
      this.show();
    }
  }

  /**
   * disengage
   * @memberof HoverZoom
   *
   * Hides the zoom display element
   */

  disengage() {
    if (this.engaged) {
      this.engaged = false;
      this.curImage.zoomable.classList.remove('hover-zoom-out');
      this.curImage.zoomable.classList.add('hover-zoom-in');
      this.hide();
    }
  }

  setZoomContainerWidth() {
    const buyBox = document.querySelector('[data-selector=buy-box]');
    const panel = buyBox && buyBox.querySelector('[data-selector=panel]');
    if (panel && this.zoomDisplay) {
      const { width } = panel.getBoundingClientRect();
      this.zoomDisplay.style.width = `${width}px`;
      this.updateBounds();
    }
  }

  /**
   * zoom
   * @memberof HoverZoom
   *
   * @param {Object} e: a native browser event object
   */

  zoom(e) {
    if (!this.engaged) {
      return false;
    }
    const { image, indicator, zoomDisplayBounds, zoomImage } = this.bounds;

    const zoomImageMIN = {
      x: zoomDisplayBounds.width - zoomImage.width,
      y: zoomDisplayBounds.height - zoomImage.height,
    };

    const basis = {
      height: image.height - indicator.height,
      width: image.width - indicator.width,
      x: image.left + indicator.width / 2,
      y: image.top + indicator.height / 2,
    };

    const xPercent = (e.clientX - basis.x) / basis.width;
    const yPercent = (e.clientY - basis.y) / basis.height;

    const x = Math.min(Math.max(xPercent * zoomImageMIN.x, zoomImageMIN.x), 0);
    const y = Math.min(Math.max(yPercent * zoomImageMIN.y, zoomImageMIN.y), 0);

    HoverZoom.positionElement(this.curImage.zoomImage, `${x.toFixed(2)}px`, `${y.toFixed(2)}px`);
    return true;
  }

  /**
   * _updateIndicatorSize
   * @memberof HoverZoom
   *
   * @param {Object} indicator: reference to a DOM element, the current hover indicator
   * @param {Object} zoomImage: reference to a DOM element, the current zoom image
   */

  setIndicatorSize(indicator) {
    // set the size of the indicator based on the ratio
    // of zoomImage size to the display container
    const { image, zoomImage, zoomDisplayBounds } = this.bounds;
    /* eslint-disable no-param-reassign */
    if (image && zoomImage && zoomDisplayBounds) {
      indicator.style.width = `${(zoomDisplayBounds.width / zoomImage.width) * image.width}px`;
      indicator.style.height = `${(zoomDisplayBounds.height / zoomImage.height) * image.height}px`;
    }
    /* eslint-enable no-param-reassign */
  }

  /**
   * show
   * @memberof HoverZoom
   *
   * shows the zoom display and the hover indicator
   */

  show() {
    this.zoomDisplay.style.zIndex = '11';
    this.zoomDisplay.style.opacity = '1';
    this.curImage.indicator.style.opacity = '1';
    this.setZoomDisplayVisibility(true);
  }

  /**
   * hide
   * @memberof HoverZoom
   *
   * hides the zoom display and the hover indicator
   */

  hide() {
    this.zoomDisplay.style.zIndex = '-1';
    this.zoomDisplay.style.opacity = '0';
    this.curImage.indicator.style.opacity = '0';
    this.setZoomDisplayVisibility(false);
  }

  setZoomDisplayVisibility(isVisible) {
    this.zoomDisplay.style.visibility = isVisible ? 'visible' : 'hidden';
  }

  /* ------- Handlers --------*/

  clickHandler(e) {
    e.preventDefault();
    if (this.engaged) {
      return this.disengage();
    }

    const index = this.zoomables.indexOf(e.currentTarget);
    this.queue(index, err => {
      if (err) {
        return console.error(err);
      }
      this.engage();
      this.zoom(e);
      return this.indicatorFollow(e);
    });

    return false;
  }

  moveHandler(e) {
    requestAnimationFrame(() => {
      this.zoom(e);
      this.indicatorFollow(e);
    });
  }

  /** ** static method *** */

  /**
   * attachIndicator
   * @memberof HoverZoom
   *
   * add a hover indicator to a zoomable element
   *
   * @param {Object} zoomable: reference to a DOM element that marks a "zoomable"
   */

  static attachIndicator(zoomable) {
    const indicatorClass = 'hover-zoom-indicator';

    // only add the indicator if it doesn't exist
    let indicator = zoomable.querySelector(indicatorClass);
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.classList.add(indicatorClass);
      zoomable.appendChild(indicator);
    }

    return indicator;
  }

  /**
   * zoomSrc
   * @memberof HoverZoom
   *
   * used to find the source path of a large zoom image from a zoomable element
   *
   * @param {Object} zoomable: refernece to a DOM element, a zoomable
   */

  static zoomSrc(zoomable) {
    return zoomable.getAttribute('href');
  }

  /**
   * positionElement
   * @memberof HoverZoom
   *
   * @param {Object} elem: a DOM element to set positioning on
   * @param {String} x: the x coordinate for poisitioning
   * @param {String} y: the y coordinate for poisitioning
   */

  static positionElement(elem, x, y) {
    /* eslint-disable-next-line no-param-reassign */
    elem.style.transform = `translate3d(${x}, ${y}, 0)`;
  }
}

export default HoverZoom;
