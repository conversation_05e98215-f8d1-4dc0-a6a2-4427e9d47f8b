# < WrapWhen /> Component

This simple component has only one purpose: To either render its contents with a wrapper or not depending if the conditions are met.

## Examples

When the `when` prop resolves to `true` and the wrapper is not `null`, then the contents will be wrapped within a functional component passed to wrapper.

```jsx
const renderPanel = ({ children }: { children: JSX.Element }): JSX.Element => {
    return (
      <Panel>
        <PanelToggle>Want it sooner? Find in store</PanelToggle>
        <PanelDetails>{children}</PanelDetails>
      </Panel>
    );
};

<WrapWith when={true} wrapper={({ children }) => renderPanel({ children })}>
  <h1>I will display in panel details.</h1>
</WrapWith>
```

When the `when` prop resolves to `false` or `wrapper` is not defined/null, then the children content will be shown in the DOM.

```jsx
<WrapWith when={false}>
  <h1>I will NOT display</h1>
</WrapWith>
```

## Why?

This component will serve as a wrapper to any enclosed jsx.

Instead of using Boolean logic to wrap a set of content, like this:

```jsx
<>{showWithWrapper && <h1>Header</h1>}</>
    <p>Display</p>
<>{showWithWrapper && <div>Other Content</div>}</>
```

We can be achieve the same behavior, like this:

```jsx
const addHeader = ({ children }: { children: JSX.Element }): JSX.Element => {
    return (
      <>
        <h1>Header</h1>
        {children}
        <div>Other Content</div>
      </>
    );
};

<WrapWith when={showWithWrapper} wrapper={({ children }) => addHeader({ children })}>
  <p>Display</p>
</WrapWith>
```
