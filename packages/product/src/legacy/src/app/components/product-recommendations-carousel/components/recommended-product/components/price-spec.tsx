// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import TestApp, { defaultBrandMarketData } from '@pdp/spec/test-app';
import { render } from '@testing-library/react';
import React from 'react';

import * as percentageOffHook from '../../../../../hooks/use-percentage-off-on-pricing';
import { Price } from './price';

const usePercentageOffOnPricingMock = (useFromCapi = false) => {
  jest.spyOn(percentageOffHook, 'usePercentageOffOnPricing').mockImplementation(() => {
    return { show: true, useFromCapi };
  });
};

const product = {
  CurrentPrice: '24.99',
  DetailURL: '/browse/product.do?pid=545554002&rrec=true',
  ID: '545554002',
  ImageURL: '/webcontent/0052/679/701/cn52679701.jpg',
  ImageURL_AV1: '/webcontent/0052/679/709/cn52679709.jpg',
  LightWeightImageURL: '/webcontent/0052/679/691/cn52679691.jpg',
  MarketingFlag: 'Extra 30% Off Taken at Checkout',
  OriginalPrice: '24.99',
  ProductName: 'Go-Dry Mesh Basketball Shorts for Men -- 9-inch inseam',
  PromotionDisplay: '',
  Rating: '4.8',
  ReviewCount: '387',
  SizeRangeA: '/webcontent/0052/679/701/cn52679701.jpg',
  SizeRangeB: '/webcontent/0052/679/701/cn52679701.jpg',
  SizeRangeC: '/webcontent/0052/679/701/cn52679701.jpg',
  instock: 'False',
  variantId: '25450',
};

const renderPrice = (testProps = { brandName: Brands.BananaRepublic }, priceProps = {}) => {
  return render(
    <TestApp
      {...testProps}
      brandMarketCtxValue={{ ...defaultBrandMarketData, brandName: testProps.brandName }}
      translations={{
        'en-US': {
          translation: { 'pdp.recommendedProduct.percentOff': '{{percentage}}% off' },
        },
      }}
    >
      <Price brand={testProps.brandName} product={product} {...priceProps} />
    </TestApp>
  );
};

describe('Product Recommendation <Price/>', () => {
  test('should be accessible', () => {
    const { container } = renderPrice();
    expect(container).toBeAccessible();
  });

  const productWithDiscount = {
    ...product,
    CurrentPrice: '3',
    OriginalPrice: product.OriginalPrice,
    Percentage: '89',
  };

  describe('For ON brand', () => {
    test('should not render percentage off when flag and/or segment is off', () => {
      const { container } = renderPrice(
        {
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-on': false } },
        },
        { product: productWithDiscount }
      );

      const percentageOffElms = container.getElementsByClassName('recommended-product__percent-off');

      expect(percentageOffElms).toHaveLength(0);
    });

    test('should render percentage off with Math.round 24.99 and 3 = 88%, when flag and segment is on BUT useFromCapi feature variable is false', () => {
      const { container } = renderPrice(
        {
          abSegValue: { on197: 'a' },
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: {
            enabledFeatures: { 'pdp-percentage-off-us-on': true },
            featureVariables: { 'pdp-percentage-off-us-on': { useFromCapi: false } },
          },
        },
        { product: productWithDiscount }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('88% off');
    });
  });

  describe('For GAPFS brand', () => {
    test('should render percentage off with Math.round 24.99 and 3 = 88%, regardless flag and segment', () => {
      const { container } = renderPrice(
        {
          abSegValue: { gapfs197: 'a' },
          brandName: Brands.GapFactoryStore,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-gapfs': true } },
        },
        { product: productWithDiscount }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('88% off');
    });
  });

  describe('For BR and BRFS brand', () => {
    test('should not render percentage off, regardless flag and segment', () => {
      const { container } = renderPrice(
        {
          abSegValue: { brfs197: 'a' },
          brandName: Brands.BananaRepublicFactoryStore,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-brfs': true } },
        },
        { product: productWithDiscount }
      );

      const percentageOffElms = container.getElementsByClassName('recommended-product__percent-off');

      expect(percentageOffElms).toHaveLength(0);
    });
  });

  describe('percentage off source', () => {
    test('should render percentage off from CAPI when useFromCapi is true and Percetange is available and it is greater than 0', () => {
      usePercentageOffOnPricingMock(true);

      const { container } = renderPrice(
        {
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-on': true } },
        },
        { product: productWithDiscount }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('89% off');
    });

    test('should calculate percentage off when useFromCapi is false even if Percetange is available and it is greater than 0', () => {
      usePercentageOffOnPricingMock(false);

      const { container } = renderPrice(
        {
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-on': true } },
        },
        { product: productWithDiscount }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('88% off');
    });

    test('should calculate percentage off when useFromCapi is true but Percentage is undefined', () => {
      usePercentageOffOnPricingMock(true);

      const { container } = renderPrice(
        {
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-on': true } },
        },
        { product: { ...productWithDiscount, Percentage: undefined } }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('88% off');
    });

    test('should calculate percentage off when useFromCapi is true but Percentage is value is 0', () => {
      usePercentageOffOnPricingMock(true);

      const { container } = renderPrice(
        {
          brandName: Brands.OldNavy,
          featureFlagsCtxValue: { enabledFeatures: { 'pdp-percentage-off-us-on': true } },
        },
        { product: { ...productWithDiscount, Percentage: 0 } }
      );

      const percentageOffElm = container.getElementsByClassName('recommended-product__percent-off')[0];

      expect(percentageOffElm).toHaveTextContent('88% off');
    });
  });
});
