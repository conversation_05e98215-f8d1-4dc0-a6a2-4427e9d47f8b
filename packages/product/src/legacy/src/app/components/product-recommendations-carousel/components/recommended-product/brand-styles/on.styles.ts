// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import { pdpPseudoFocus, sdsBreakpoint, sdsLabelA, setupRedesignStyles } from '@product-page/legacy/styles';

type OptionalSerializedStyles = SerializedStyles | string;
type ConditionalStylesType = {
  originalPricePadding: OptionalSerializedStyles;
  percentageOffStyles: OptionalSerializedStyles;
  priceGroupStyles: OptionalSerializedStyles;
  singlePriceStyles: OptionalSerializedStyles;
  textStyles: OptionalSerializedStyles;
};

const getConditionalStyles = (theme: Theme) => {
  const setStyles = setupRedesignStyles(theme).isPercentageOffOn;

  const percentageOffOnStyles = {
    originalPricePadding: css`
      padding-bottom: 2px;
    `,
    percentageOffStyles: css`
      color: ${theme.color.g2};
      text-transform: capitalize;
    `,
    priceGroupStyles: css`
      .recommended-product__price-group-wrapper {
        display: flex;
        align-items: flex-start;
        padding-bottom: 0.125rem;
        flex-direction: column;
      }

      .recommended-product__price-group {
        width: fit-content;
        display: flex;
        flex-wrap: wrap;
      }
    `,
    singlePriceStyles: css`
      padding: 4px 0px;
    `,
    textStyles: css`
      line-height: 0.9rem;
      font-size: 0.75rem;
    `,
  };

  const defaultStyles = {
    originalPricePadding: '',
    percentageOffStyles: css`
      color: #d72c32;
      margin-left: 5px;
      text-transform: lowercase;
    `,
    priceGroupStyles: '',
    singlePriceStyles: css`
      font-size: 0.9375rem;
    `,
    textStyles: css`
      line-height: 1.4;
      font-size: 0.8125rem;
    `,
  };

  return setStyles(
    percentageOffOnStyles as unknown as SerializedStyles,
    defaultStyles as unknown as SerializedStyles
  ) as unknown as ConditionalStylesType;
};

export const recommendedProductStylesForON = (theme: Theme): SerializedStyles => {
  const { priceGroupStyles, percentageOffStyles, textStyles, originalPricePadding, singlePriceStyles } =
    getConditionalStyles(theme) as ConditionalStylesType;

  return css`
    ${theme.font.primary}
    color: ${theme.color.gray60};
    ${textStyles}
    margin: 0.5rem;

    ${priceGroupStyles}

    .recommended-product__text {
      ${sdsLabelA(theme, { fontSize: '' as string })}
      color: ${theme.color.gray60};
      font-weight: 400;
      padding-top: 0.4rem;
      font-family: ${theme.font.primary};
      white-space: normal;
      text-transform: none;
    }

    .recommended-product__original-price {
      color: ${theme.color.g2};
      text-decoration: line-through;
      ${originalPricePadding}
      margin-right:0.31rem;
    }

    .recommended-product__percent-off {
      ${percentageOffStyles}
    }

    .recommended-product__new-price {
      color: ${theme.color.r1};
    }

    .recommended-product__price {
      text-transform: none;
      ${singlePriceStyles}
      font-weight: ${theme.fontWeight.light};

      @media (max-width: ${sdsBreakpoint.medium}) {
        font-size: 0.8125rem;
      }
    }

    .recommended-product__marketing-flag {
      ${sdsLabelA(theme)}
      font-weight: ${theme.fontWeight.bold};
      color: ${theme.color.g2};
      font-size: 0.8125rem;
      text-transform: none;
      white-space: normal;
      margin-top: 0.25rem;
    }

    .recommended-product__anchor {
      display: block;
      height: fit-content;
      width: 100%;

      ${pdpPseudoFocus(theme)}
    }
  `;
};

export const recommendedProductImageStylesForON =
  (isGridEnabled?: boolean) =>
  (theme: Theme): SerializedStyles => {
    const gridStyles = css`
      position: relative;
      background-color: ${theme.color.gray05};
      width: 100%;
      height: 0;
      padding-bottom: ${133.333}%;

      .recommended-product__img-src {
        display: block;
        width: 100%;
        height: auto;
      }
    `;

    const defaultStyles = css`
      position: relative;
      background-color: ${theme.color.gray05};
      height: 0;
      padding-bottom: ${133.333}%;
    `;

    return isGridEnabled ? gridStyles : defaultStyles;
  };
