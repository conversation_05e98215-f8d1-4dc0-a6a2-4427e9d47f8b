// @ts-nocheck
import type { Brands } from "@ecom-next/core/react-stitch";
import TestApp from '@pdp/spec/test-app';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { render } from '@testing-library/react';
import React from 'react';

import { images } from '../../../product-images-container/data/images-mock';
import type { Variant } from '.';
import { ProductImages } from '.';

const productTitle = 'Product Title';

const matchSnapshotsAllBrands = setupSnapshots();

const renderProductImages = (variant: Variant = 'stack', mockImages = images) => {
  return render(
    <TestApp>
      <ProductImages images={mockImages} productTitle={productTitle} variant={variant} />
    </TestApp>
  );
};

const getStackStyles = (brandName: Brands, enabledFeatures, abSegValue) => {
  return (
    <TestApp abSegValue={abSegValue} brandName={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
      <ProductImages images={images} productTitle={productTitle} variant="stack" />
    </TestApp>
  );
};
const getGridStyles = (brandName: Brands, enabledFeatures, abSegValue) => {
  return (
    <TestApp abSegValue={abSegValue} brandName={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
      <ProductImages images={images} productTitle={productTitle} variant="grid" />
    </TestApp>
  );
};
const imagesWithoutVideo = images.filter(img => !img.video);

describe('<ProductImages />', () => {
  describe('<ProductImages Stack snapshots/>', () => {
    matchSnapshotsAllBrands(getStackStyles, 'renders correctly');
  });
  describe('<ProductImages Grid snapshots/>', () => {
    matchSnapshotsAllBrands(getGridStyles, 'renders correctly');
  });
  test('renders all images', () => {
    const { getAllByRole } = renderProductImages();

    expect(getAllByRole('img')).toHaveLength(imagesWithoutVideo.length);
  });

  test('renders images with alt attribute equal to product title', () => {
    const { getAllByRole } = renderProductImages();

    const images = getAllByRole('img');
    images.forEach(image => {
      expect(image).toHaveAttribute('alt', productTitle);
    });
  });

  test('renders images with xlarge image size', () => {
    const { getAllByRole } = renderProductImages();

    const imagesEl = getAllByRole('img');
    imagesEl.forEach((imageEl, i) => {
      expect(imageEl).toHaveAttribute('src', imagesWithoutVideo[i].xlarge);
    });
  });
});
