// @ts-nocheck
'use client'

import type { SerializedStyles } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

export const onBuyBoxContainerStyles = () => (): SerializedStyles => {
  return css`
    @media (min-width: ${sdsBreakpoint.xLarge}) {
      @supports (position: sticky) {
        position: sticky;
        top: 0;
      }
    }

    .buybox-wrapper {
      transition: opacity 225ms;

      @media (min-width: ${sdsBreakpoint.xLarge}) {
        padding: 4rem 0 0.8rem;
      }
    }
  `;
};

export const onHoverZoomDisplayStyles = (buyBoxHeight: number): SerializedStyles => {
  return css`
    display: none;
    @media (min-width: ${sdsBreakpoint.xLarge}) {
      display: block;
    }
    position: absolute;
    top: 0;
    left: 100%;
    width: 50%;
    overflow: hidden;
    z-index: -1;
    opacity: 0;
    margin-left: 0.5rem;
    height: ${buyBoxHeight - 55}px;
    .zoom-image {
      max-width: none;
      min-width: 300%;
      height: auto;
    }
  `;
};

const getBuyboxContainerStyles = (buyBoxHeight: number) => {
  /* isImageScaling is a state variable that is true whenever the scaling is happening, and turns
    to false after it finishes scaling. This value is different from the
    theme.isScalingImageEnabled flag which indicates if the feature is turned on or off in
    it's totality
  */
  const stickyBanner = document.querySelector('.promoDrawer__topContainer');
  let containerStyleDesktop: any = {};

  const isBuyBoxHigherThanViewport = buyBoxHeight > window?.innerHeight;
  if (isBuyBoxHigherThanViewport) {
    containerStyleDesktop = css`
      margin-top: calc(${buyBoxHeight}px - 100vh);
    `;
  }

  if (stickyBanner) {
    containerStyleDesktop.top = `${stickyBanner.scrollHeight}px`;
  }
  return { containerStyleDesktop };
};

export const onContainerStyles = (buyBoxHeight: number): SerializedStyles => {
  if (typeof document === 'undefined' && typeof window === 'undefined') {
    return css``;
  }

  const { containerStyleDesktop } = getBuyboxContainerStyles(buyBoxHeight);

  return css`
    ${containerStyleDesktop};
  `;
};

export const onBuyBoxStyles = (buyBoxHeight: number): SerializedStyles => {
  if (typeof window !== 'undefined') {
    return (
      !(window.innerHeight >= buyBoxHeight) &&
      css`
        margin-top: calc((${buyBoxHeight}px - 100vh) * -1);
      `
    );
  }
  return css``;
};

export const onLBuyBoxStyles = (): SerializedStyles => {
  return css`
    float: right;
    width: 100%;
    /* 1/3 of the distance between $sds_breakpoint--large and $sds_breakpoint--x-large */
    @media (min-width: 768 + (((1024 - 768) / 3) * 1)) and (orientation: landscape) {
      width: calc((2.666 / 6) * 100%);
    }
    /* 2/3 of the distance between $sds_breakpoint--large and $sds_breakpoint--x-large */
    @media (min-width: 768 + (((1024 - 768) / 3) * 2)) and (orientation: landscape) {
      width: calc((2.333 / 6) * 100%);
    }
    @media (min-width: ${sdsBreakpoint.xLarge}) {
      width: calc((1 / 3) * 100%);
    }
  `;
};

export const onStickyContainerWithLbuyBox = (): SerializedStyles => {
  return css`
    position: sticky;
    top: 0;
    .hover-zoom-display {
      top: auto;
      left: 0;
      margin-top: 3rem;
    }
  `;
};

export const onBuyboxInnerPadding = (): SerializedStyles => {
  return css`
    position: relative;
    padding: 0;

    @media (min-width: ${sdsBreakpoint.xLarge}) {
      padding: 0 0.5em;
    }
  `;
};
