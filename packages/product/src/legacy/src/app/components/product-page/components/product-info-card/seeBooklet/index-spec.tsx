// @ts-nocheck
import TestApp from '@pdp/spec/test-app';
import { screen } from '@testing-library/dom';
import { render } from '@testing-library/react';
import React from 'react';
import renderer, { act } from 'react-test-renderer';

import { SeeBookletButton } from '.';

const props = { pdfLink: '/something' };
describe('<SeeBookletButton />', () => {
  test('The component renders in the DOM correctly and it matches the snapshot', () => {
    const tree = renderer
      .create(
        <TestApp>
          <SeeBookletButton {...props} />
        </TestApp>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  test('Component should find the button by testId and role, should have attribute href and target assigned with correct values', () => {
    act(() => {
      render(<SeeBookletButton {...props} />);
    });
    expect(screen.queryByTestId('seeBookletButton')).toBeInTheDocument();
    expect(screen.queryByTestId('seeBookletButton')).toHaveAttribute('href');
    expect(screen.getByRole('link')).toHaveAttribute('href');
    expect(screen.queryByTestId('seeBookletButton')?.getAttribute('href')).toBe(props.pdfLink);
    expect(screen.getByRole('link')?.getAttribute('target')).toBe('_blank');
  });
});
