// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import { pdpUnbuttonize, sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';

export const shippingReturnsStylesForBR = (theme: Theme, shouldShow: boolean): SerializedStyles => {
  return css`
    display: flex;
    ${shouldShow && 'padding-top: 1rem;'}
    justify-content: flex-end;
    width: 100%;
    margin-bottom: 0;

    @media (min-width: ${sdsBreakpoint.medium}) {
      width: 56%;
    }

    @media (min-width: ${sdsBreakpoint.medium}) and (min-aspect-ratio: 1 / 1), (min-width: ${sdsBreakpoint.xLarge}) {
      width: 100%;
    }

    .shipping-returns__link {
      ${pdpUnbuttonize()}
      ${theme.font.primary}
      color: ${theme.color.gray60};
      text-transform: uppercase;
      font-size: 0.875rem;
      letter-spacing: 1px;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  `;
};

export const iframeShippingAndReturnsInformationStylesForBR = (): SerializedStyles => {
  return css`
    height: 100%;

    @media (min-width: ${sdsBreakpoint.medium}) {
      height: 65vh;
    }
  `;
};
