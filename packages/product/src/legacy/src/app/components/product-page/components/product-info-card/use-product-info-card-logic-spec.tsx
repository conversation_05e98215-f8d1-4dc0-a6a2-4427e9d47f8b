// @ts-nocheck
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { renderHook } from '@testing-library/react-hooks';

import { useAppState } from '../../../../hooks/use-app-state';
import { useFeatureFlag } from '../../../../hooks/use-feature-flag';
import ProductInformationItems from '../../../product-information/data/product-info-tabs-data.json';
import { useProductInfoCardLogic } from './use-product-info-card-logic';

jest.mock('../../../../hooks/use-feature-flag');
jest.mock('../../../abseg-provider');
jest.mock('@ecom-next/core/legacy/localization-provider');
jest.mock('../../../../hooks/use-app-state');

describe('Given use-product-info-card-logic', () => {
  const productInfoCardLogicMock = (pdpReviewSnapshotDrawerFlag = true, brand = 'gap') => {
    const productInformationString = JSON.stringify({ ...ProductInformationItems, brand });
    const createProductInfo = () => JSON.parse(productInformationString);
    (useFeatureFlag as jest.Mock).mockReturnValue(pdpReviewSnapshotDrawerFlag);
    (useLocalize as jest.Mock).mockReturnValue({ localize: () => 'Reviews' });
    (useAppState as jest.Mock).mockReturnValue({ productData: { isGiftCard: false } });
    const productInfo = createProductInfo();
    return productInfo;
  };

  const getShowResult = (pdpReviewSnapshotDrawerFlag = true, brand = 'gap') => {
    const productInfo = productInfoCardLogicMock(pdpReviewSnapshotDrawerFlag, brand);
    const { result } = renderHook(() => useProductInfoCardLogic(productInfo, 2));
    return result.current.reviews.show;
  };

  describe('When the redesign is on', () => {
    describe('And reviewSnapshotDrawer is on too', () => {
      test('if the brand is GAP then should show Reviews accordion', () => {
        expect(getShowResult()).toBeTruthy();
      });

      test('if the brand is GAPFS then should show Reviews accordion', () => {
        expect(getShowResult(true, 'gapfs')).toBeTruthy();
      });

      test('if the brand is AT then should show Reviews accordion', () => {
        expect(getShowResult(true, 'at')).toBeTruthy();
      });

      test('if the brand is BR then should show Reviews accordion', () => {
        expect(getShowResult(true, 'br')).toBeFalsy();
      });

      test('if the brand is BRFS then should show Reviews accordion', () => {
        expect(getShowResult(true, 'brfs')).toBeFalsy();
      });

      test('if the brand is ON then should show Reviews accordion', () => {
        expect(getShowResult(true, 'on')).toBeFalsy();
      });
    });

    describe('And reviewSnapshotDrawer is off', () => {
      test('if the brand is GAP then should not show Reviews accordion', () => {
        expect(getShowResult(false)).toBeFalsy();
      });

      test('if the brand is GAPFS then should not show Reviews accordion', () => {
        expect(getShowResult(false, 'gapfs')).toBeFalsy();
      });

      test('if the brand is AT then should not show Reviews accordion', () => {
        expect(getShowResult(false, 'at')).toBeFalsy();
      });

      test('if the brand is BR then should not show Reviews accordion', () => {
        expect(getShowResult(false, 'br')).toBeFalsy();
      });

      test('if the brand is BRFS then should not show Reviews accordion', () => {
        expect(getShowResult(false, 'brfs')).toBeFalsy();
      });

      test('if the brand is ON then should not show Reviews accordion', () => {
        expect(getShowResult(false, 'on')).toBeFalsy();
      });
    });
  });
});
