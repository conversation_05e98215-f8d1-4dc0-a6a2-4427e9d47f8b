#pr-write .p-w-r {
  $star: 'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiMwQTU2OTQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';
  $grayStar: 'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiNFREVDRUMiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';

  .pr-star-v4.pr-star-v4-100-filled {
    background: url(data:image/svg+xml;base64,#{$star}) no-repeat;
    background-size: 2.5rem 2.5rem;
  }

  .pr-star-v4.pr-star-v4-0-filled {
    background: url(data:image/svg+xml;base64,#{$grayStar}) no-repeat;
    background-size: 2.5rem 2.5rem;
  }

  .pr-star-v4,
  .pr-star-v4-0-filled {
    height: 2.5rem;
    width: 2.5rem;
    margin-left: 0.5rem;
  }

  .pr-alert-container,
  .pr-war {
    margin: auto;
    max-width: 480px;
  }

  .pr-war {
    padding: 0 1rem;

    @media (min-width: 496px) {
      padding: 0;
    }
  }

  h1 {
    max-width: 720px;
    margin: auto;

    @include sds-cb_font--primary();
    color: sds-cb_color(g2);
    font-size: 1.375rem;
    padding-left: 0.5rem;

    @media (min-width: 480px) {
      font-size: 2.5rem;
      padding: 0;
    }
  }

  .pr-ryp-list {
    max-width: 720px;
    margin: 1.5rem auto 4rem;
    padding: 0 0.5rem;

    @media (min-width: 480px) {
      margin: 2rem auto 4rem;
      padding: 0;
    }
  }

  .ryp-purchase {
    border: none;
    border-top: 1px solid sds-cb_color(g4);
    margin-bottom: 0;
    padding: 2rem 1.5rem 2rem 0;

    .col-sm-3 {
      padding: 0;
    }

    .ryp-stars + .col-sm-12 {
      margin-top: 1rem;
    }

    .form-control {
      border-radius: 0;
    }

    .ryp-product-img {
      margin: 0 !important;
      margin-bottom: 0.5rem !important;
      display: inline-block;

      @media (min-width: 480px) {
        margin-right: 20px !important;
        display: block;
      }
    }

    .form-group {
      margin-bottom: 2.5rem;
    }

    .pr-control-label span {
      @include sds-cb_font--primary();
      font-size: 1.125rem;
      color: sds_color--grayscale(gray80);
    }

    .ryp-stars .form-group {
      margin-bottom: 0;
    }

    .ryp-product-title {
      color: sds_color--grayscale(gray80);
      margin-bottom: 0.875rem;
      font-size: 1rem;
      line-height: 1.5;

      @media (min-width: 480px) {
        font-size: 1.375rem;
      }
    }

    .pr-file-input-btn {
      margin-bottom: 2rem;
    }

    .pr-file-input-btn-group {
      display: block;
    }

    .pr-btn-fileinput {
      @include sds-cb_font--secondary();
      background-color: sds-cb_color(b1);
      color: sds_color--grayscale(wh);
      border: none;
      border-radius: 5px;
      width: 100%;
      padding: 0.875rem 0;
      text-align: center;
      font-size: 1rem;
      margin-left: 0;
      display: flex;
      justify-content: center;
    }

    .pr-btn-fileinput:hover {
      background-color: sds-cb_color(b2);
      color: sds-cb_color(g1);
    }

    .pr-file-input-btn-group .pr-file-input-label {
      @include sds-cb_font--primary();
      font-size: 1.125rem;
    }

    .btn-group-radio-horizontal-linked {
      position: relative;
      display: table;
      table-layout: fixed;

      .pr-btn {
        @include sds-cb_font--primary();
        border: none;
        border-top: 3px solid sds_color--grayscale(gray10);
        padding: 0;
        white-space: normal;
        display: table-cell;
        text-align: center;
        text-transform: capitalize;
      }

      .pr-btn::before {
        content: '';
        position: relative;
        top: -0.75rem;
        left: 43%;
        display: flex;
        height: 1.25rem;
        width: 1.25rem;
        background-color: sds-cb_color(wh);
        border: 2px solid sds_color--grayscale(gray54);
        border-radius: 50%;
      }

      .pr-btn:hover {
        color: sds-cb_color(g1);
        background-color: inherit;
        border: none;
        border-top: 3px solid sds_color--grayscale(gray10);
      }

      .pr-btn.active {
        color: sds-cb_color(g1);
        background-color: inherit;
      }

      .pr-btn.active::before {
        border-color: sds-cb_color(b1);
        background-color: sds-cb_color(b1);
      }

      .pr-btn:hover::before {
        background-color: sds_color--grayscale(gray54);
      }

      input.focus-visible + label {
        outline: none;
        box-shadow: 0 0 0 0 !important;
      }

      input[type=radio]:focus,
      input[type=radio]:focus + label {
        outline: none;
      }

      input[type=radio]:focus + label::before {
        outline: 0;
        box-shadow: 0 0 0 3px sds-cb_color(b2);
      }
    }

    .pr-accessible-btn {
      background-color: sds_color--grayscale(bk);
      border: none;
      width: 100%;
      padding: 0.875rem 0;
      margin-bottom: 2rem;
    }

    .pr-accessible-btn:hover {
      background-color: sds-cb_color(g2);
      color: sds-cb_color(wh);
    }

    .ryp-footer {
      background-color: transparent;
      border-top: none;
    }
  }
}
