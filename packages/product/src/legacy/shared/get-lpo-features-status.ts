// @ts-nocheck
'use client'

import { isSupportedTid } from '../src/app/helpers/util/tid-utils';
import { isValidPid, transformPidBatches } from './helper/special-oos-helper';

type Args = {
  abSeg: ABSeg;
  brand: any;
  featureFlags: any;
  featureVariables: FeatureVariables;
  inStock: boolean;
  market: Market;
  styleId?: string;
  tid: string;
};

type Status = {
  isLPOCertonaPlusPDPEnabled: boolean;
  isLPOHybridEnabled: boolean;
  isLPOOOSEnabled: boolean;
  isSpecialCollabOOOSEnabled: boolean;
  isSpecialOOSEnabled: boolean;
};

export default function getLPOFeaturesStatus(args: Args): Status {
  if (!args?.featureFlags || !args.featureVariables) {
    return {
      isLPOCertonaPlusPDPEnabled: false,
      isLPOHybridEnabled: false,
      isLPOOOSEnabled: false,
      isSpecialCollabOOOSEnabled: false,
      isSpecialOOSEnabled: false,
    };
  }

  const { featureVariables, featureFlags, market, brand, abSeg, inStock, tid, styleId } = args;

  const lpoVariables = featureVariables[`pdp-lpo-certona-${market}-${brand}`];
  const hybridVariables = featureVariables[`pdp-lpo-plp-${market}-${brand}`];
  const isLPOFeatureFlagEnabled = featureFlags[`pdp-lpo-certona-${market}-${brand}`];
  const isHybridFeatureFlagEnabled = featureFlags[`pdp-lpo-plp-${market}-${brand}`];
  const oosFeatureFlag = featureFlags['pdp-optimized-oos'];
  const isOOSFeatureEnabled = !inStock && oosFeatureFlag;
  const isLPOSegment = abSeg[`${brand}179`] === 'a';
  const isHybridSegment = abSeg[`${brand}179`] === 'b';

  const specialOOSFeatureFlag = `pdp-special-oos-${market}-${brand}`;
  const isSpecialOOSFlagEnabled = featureFlags[specialOOSFeatureFlag];
  const specialOOSVariables = featureVariables[specialOOSFeatureFlag];
  const pidBatches = transformPidBatches(specialOOSVariables);
  const isSpecialOOSEnabled = isSpecialOOSFlagEnabled && isValidPid(pidBatches, styleId) && !inStock;
  const specialCollabOOOSEnabledFlagName = `special-collection-oos-pdp-${market}-${brand}`;
  const isSpecialCollabOOOSFlagEnable = featureFlags[specialCollabOOOSEnabledFlagName];
  const specialCollabOOOSVariables = featureVariables[specialCollabOOOSEnabledFlagName];
  const pidBatchesCollabOOS = transformPidBatches(specialCollabOOOSVariables);
  const isSpecialCollabOOOSEnabled =
    isSpecialCollabOOOSFlagEnable && isValidPid(pidBatchesCollabOOS, styleId) && !inStock;

  return {
    isLPOCertonaPlusPDPEnabled: !!(isLPOFeatureFlagEnabled && isLPOSegment && isSupportedTid(lpoVariables?.TID, tid)),
    isLPOHybridEnabled: !!(isHybridFeatureFlagEnabled && isHybridSegment && isSupportedTid(hybridVariables?.TID, tid)),
    isLPOOOSEnabled: !!isOOSFeatureEnabled,
    isSpecialCollabOOOSEnabled,
    isSpecialOOSEnabled,
  };
}
