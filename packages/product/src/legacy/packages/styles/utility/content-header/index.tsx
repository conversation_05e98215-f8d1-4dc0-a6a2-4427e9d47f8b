// @ts-nocheck
'use client'

/***/
/***/
import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css, jsx } from "@ecom-next/core/react-stitch";

import { carouselSeparatorStyles, sectionHeader } from './brand-styles/default/index.styles';
import { drawerCarouselSeparatorStyles, drawerSectionHeader } from './brand-styles/drawer/index.styles';

const ContentHeader = ({
  title,
  drawerEnabled,
  id,
  customStyling = () => css``,
}: {
  customStyling?: (theme: Theme) => SerializedStyles;
  drawerEnabled: boolean;
  id?: string;
  title: string;
}): JSX.Element => {
  const sectionHeaderValue = drawerEnabled ? drawerSectionHeader : sectionHeader;
  const carouselSeparatorStylesValue = (theme: Theme) =>
    drawerEnabled ? drawerCarouselSeparatorStyles : carouselSeparatorStyles(theme);

  return (
    <h2 css={sectionHeaderValue} id={id}>
      <span className="review-separator" css={theme => [carouselSeparatorStylesValue(theme), customStyling]}>
        {title}
      </span>
    </h2>
  );
};

export default ContentHeader;
