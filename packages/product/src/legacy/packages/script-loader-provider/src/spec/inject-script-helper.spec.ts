// @ts-nocheck
import { injectScriptHelper } from '../inject-script-helper';

describe('injectScriptHelper', () => {
  const onLoadSpy = jest.fn();
  const onErrorSpy = jest.fn();

  const scriptConfig = {
    onError: onErrorSpy,
    onLoad: onLoadSpy,
    optionalProps: {
      type: 'text/javascript',
    },
    url: 'http://mock-script-link.js',
  };

  const HTMLScriptElementCached = HTMLScriptElement;

  const scriptFixture = ({ onload, onerror }) => {
    Object.defineProperty(global.HTMLScriptElement.prototype, 'src', {
      set() {
        if (onload) {
          setTimeout(() => this.onload());
        }
        if (onerror) {
          setTimeout(() => this.onerror('error'));
        }
      },
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    global.HTMLScriptElement = HTMLScriptElementCached;
    document.getElementsByTagName('script')[0].remove();
  });

  test('adds the script with the url to the page with the props passed', () => {
    injectScriptHelper(scriptConfig);
    const scriptAdded = document.getElementsByTagName('script')[0];
    expect(scriptAdded).toBeDefined();
    expect(scriptAdded).toHaveAttribute('src', scriptConfig.url);
    expect(scriptAdded).toHaveAttribute('type', scriptConfig.optionalProps.type);
  });

  test('resolves promise when the script is added succees', async () => {
    scriptFixture({ onerror: false, onload: true });
    const scriptLoader = await injectScriptHelper(scriptConfig);

    expect(scriptLoader).toStrictEqual('Success');
  });

  test('rejects promise when the script is errored', async () => {
    scriptFixture({ onerror: true, onload: false });
    await injectScriptHelper(scriptConfig).catch(error => {
      expect(error).toBe('error');
    });
  });
});
