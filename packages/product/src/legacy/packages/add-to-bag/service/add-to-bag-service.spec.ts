// @ts-nocheck
import type { AddToBagServiceType } from '../types';
import { addToBagRequest } from './add-to-bag-request';
import { addToBagService } from './add-to-bag-service';

jest.mock('./add-to-bag-request', () => {
  return {
    addToBagRequest: jest.fn(),
  };
});

describe('AddToBagService', () => {
  const createAddItemToBagRequest = (quantity: number, deliveryLocationId = ''): AddToBagServiceType => {
    return {
      brandName: 'gap' as Brands,
      deliveryLocationId,
      locale: 'en_US',
      market: 'us',
      name: 'Toddler 100% Recycled Lightweight Puffer Jacket',
      oidcUrl: 'https://internal-azeus-ecom-api.live.test.{{domain}}.com',
      quantity,
      sku: '5075180620000',
    };
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should pass to item object the quantity and sku to AddToBag when deliveryLocationId is empty', () => {
    addToBagService({ ...createAddItemToBagRequest(1) });

    expect(addToBagRequest).toHaveBeenCalledWith({
      brand: 'gap',
      clientId: 'PDP',
      items: [{ quantity: 1, sku: '5075180620000' }],
      locale: 'en_US',
      market: 'us',
      name: 'Toddler 100% Recycled Lightweight Puffer Jacket',
      oidcUrl: 'https://internal-azeus-ecom-api.live.test.{{domain}}.com',
    });
  });

  test('should pass to item object the fulfillment, quantity and sku to AddToBag deliveryLocationId is defined', () => {
    addToBagService({ ...createAddItemToBagRequest(1, '4146') });

    expect(addToBagRequest).toHaveBeenCalledWith({
      brand: 'gap',
      clientId: 'PDP',
      items: [{ fulfillment: { deliveryLocationId: '4146', type: 'PICKUP' }, quantity: 1, sku: '5075180620000' }],
      locale: 'en_US',
      market: 'us',
      name: 'Toddler 100% Recycled Lightweight Puffer Jacket',
      oidcUrl: 'https://internal-azeus-ecom-api.live.test.{{domain}}.com',
    });
  });
});
