// @ts-nocheck
import fetchMock from 'fetch-mock';

import { addToBagRequest } from '../index';
import { updateBag } from '../leapfrog-add-to-bag/leapfrog-add-to-bag';
import { addToBagSuccessResponse, getBagSuccess } from './add-to-bag-service-mocks';

jest.mock('../leapfrog-add-to-bag/leapfrog-add-to-bag');

describe('addToBag Service', () => {
  const items = [
    {
      quantity: 1,
      sku: '0123456789123',
    },
  ];
  const clientId = 'PDP';
  const params = {
    brand: 'gap',
    clientId,
    items,
    locale: 'en_US',
    market: 'us',
    oidcUrl: '',
  };
  test('calls updateBag with expected params', async () => {
    const locale = 'en_US';
    const addToBagUrl = `/commerce/shopping-bags/items?locale=${locale}`;
    const getBagUrl = `/commerce/shopping-bags?locale=${locale}`;

    fetchMock.post(addToBagUrl, addToBagSuccessResponse);
    fetchMock.mock(getBagUrl, getBagSuccess);
    const expectedData = {
      brand: 'GP',
      clientId,
      items,
      locale: 'en_US',
      market: 'US',
      name: '',
      oidcUrl: '',
    };

    await addToBagRequest({ name: '', ...params });

    expect(updateBag).toHaveBeenLastCalledWith(expectedData);
  });

  test('calls updateBag with default params if no locales, market or brand is passed', async () => {
    const locale = 'en_US';
    const addToBagUrl = `/commerce/shopping-bags/items?locale=${locale}`;
    const getBagUrl = `/commerce/shopping-bags?locale=${locale}`;

    fetchMock.post(addToBagUrl, addToBagSuccessResponse, { overwriteRoutes: false });
    fetchMock.mock(getBagUrl, getBagSuccess, { overwriteRoutes: false });
    const expectedData = {
      brand: 'GP',
      clientId,
      items,
      locale: 'en_US',
      market: 'US',
      name: '',
      oidcUrl: '',
    };

    await addToBagRequest({ name: '', ...params, items, oidcUrl: '' });

    expect(updateBag).toHaveBeenLastCalledWith(expectedData);
  });
});
