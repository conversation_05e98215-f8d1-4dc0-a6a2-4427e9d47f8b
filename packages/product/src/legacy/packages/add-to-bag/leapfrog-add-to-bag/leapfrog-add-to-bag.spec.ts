// @ts-nocheck
import fetchMock from 'fetch-mock';

import {
  getTranslations,
  MAX_QUANTITY_LIMIT_ERR,
  OUT_OF_STOCK_ERR,
  PARTIAL_SUCCESS_ERR,
} from '../collaborators/response-translations';
import { addToBagPartialSuccess, addToBagSuccessResponse } from '../service/add-to-bag-service-mocks';
import config from '../translations';
import type { LeapFrogAddToBagBrandCode } from '../types';
import { updateBag } from './leapfrog-add-to-bag';

describe('leapfrog-addToBag', () => {
  const items = [
    {
      quantity: 2,
      sku: '1242030010024',
    },
  ];
  const clientId = 'PDP';
  const oidcUrl = 'https://secure-internal-azeus-ecom-api.live.stage.factory-gaptechol.com';
  const locale = 'en_US';

  const props = {
    brand: 'GP' as LeapFrogAddToBagBrandCode,
    items,
    locale,
    market: 'US',
    name: 'T-shirt &#xE0; poche',
    oidcUrl,
  };

  const addToBagUrl = `${oidcUrl}/commerce/shopping-bags/items/summary?locale=${locale}`;

  describe('updateBag', () => {
    afterEach(fetchMock.reset);

    test('invokes add to bag service', async () => {
      fetchMock.post(addToBagUrl, addToBagSuccessResponse);

      await updateBag({
        ...props,
      });

      expect(fetchMock.calls()).toHaveLength(1);
    });

    test('makes addToBag with expected headers', async () => {
      fetchMock.post(addToBagUrl, addToBagSuccessResponse);
      await updateBag({
        ...props,
      });

      expect(fetchMock.calls()[0][1]?.headers).toStrictEqual({
        'Content-Type': 'application/json',
        'X-Gap-ApiMode': 'leapfrog',
        brand: 'GP',
        channel: 'WEB',
        clientId,
        market: 'US',
      });
    });
    test('returns Out of stock error message when ATB service fails with partial success error', async () => {
      const errorResponse = {
        itemErrors: [
          {
            errorCode: PARTIAL_SUCCESS_ERR,
          },
        ],
      };

      fetchMock.post(addToBagUrl, {
        throws: errorResponse,
      });
      const response = await updateBag({
        ...props,
      });

      const message = getTranslations('en_US');

      expect(response).toStrictEqual({
        errors: [
          {
            developerMessage: `Add to Bag - ${JSON.stringify(errorResponse)}`,
            errorCode: OUT_OF_STOCK_ERR,
            moreInfo: message({ errorCode: OUT_OF_STOCK_ERR }),
          },
        ],
        status: 'ERROR',
      });
    });

    test('returns Maximum allowed quantity limit reached error message when ATB service fails with BAGCAT-8007', async () => {
      const errorResponse = {
        itemErrors: [
          {
            errorCode: MAX_QUANTITY_LIMIT_ERR,
            maxQuantityAllowed: 100,
          },
        ],
      };

      fetchMock.post(addToBagUrl, {
        throws: errorResponse,
      });
      const response = await updateBag({
        ...props,
      });

      const message = getTranslations('en_US');

      expect(response).toStrictEqual({
        errors: [
          {
            errorCode: MAX_QUANTITY_LIMIT_ERR,
            moreInfo: message({ errorCode: MAX_QUANTITY_LIMIT_ERR, maxQuantityAllowed: 100, name: 'T-shirt à poche' }),
          },
        ],
        status: 'ERROR',
      });
    });

    test('returns Maximum allowed quantity limit reached error message when ATB service returns with success and error code as BAGCAT-8007', async () => {
      const response = {
        ...addToBagSuccessResponse,
        itemErrors: [
          {
            errorCode: MAX_QUANTITY_LIMIT_ERR,
            maxQuantityAllowed: 100,
          },
        ],
        items: [
          {
            maxQuantityAllowed: 100,
          },
        ],
      };
      fetchMock.post(addToBagUrl, response);

      const ATBresponse = await updateBag({
        ...props,
      });

      const message = getTranslations('en_US');

      expect(ATBresponse).toStrictEqual({
        errors: [
          {
            errorCode: MAX_QUANTITY_LIMIT_ERR,
            moreInfo: message({ errorCode: MAX_QUANTITY_LIMIT_ERR, maxQuantityAllowed: 100, name: 'T-shirt à poche' }),
          },
        ],
        status: 'ERROR',
      });
    });

    test('returns default error message when service fails with generic error with no error code', async () => {
      const genericError = 'Service Unavailable';
      fetchMock.post(addToBagUrl, {
        throws: genericError,
      });
      const response = await updateBag({
        ...props,
      });

      expect(response).toStrictEqual({
        errors: [
          {
            developerMessage: `Add to Bag - ${genericError}`,
            errorCode: genericError,
            moreInfo: 'We are sorry, there has been an error adding this item to bag.',
          },
        ],
        status: 'ERROR',
      });
    });

    test('calls add to bag with gap brand, en_US locale and us market as default parameters if not present in request', async () => {
      fetchMock.post(addToBagUrl, addToBagSuccessResponse);

      await updateBag({
        ...props,
        items,
        oidcUrl,
      });

      expect(fetchMock.calls()[0][1]?.headers).toStrictEqual({
        'Content-Type': 'application/json',
        'X-Gap-ApiMode': 'leapfrog',
        brand: 'GP',
        channel: 'WEB',
        clientId,
        market: 'US',
      });
    });

    test('adds previewType as wip in request header if the environment is preview', async () => {
      const oidcPreviewUrl = 'https://secure-internal-azeus-ecom-api.preview.wip.stage.gaptecholapps.com';
      const addToBagUrl = `${oidcPreviewUrl}/commerce/shopping-bags/items/summary?locale=${locale}`;

      fetchMock.post(addToBagUrl, addToBagSuccessResponse);
      await updateBag({
        ...props,
        oidcUrl: oidcPreviewUrl,
      });

      expect(fetchMock.calls()[0][1]?.headers).toStrictEqual({
        'Content-Type': 'application/json',
        'X-Gap-ApiMode': 'leapfrog',
        brand: 'GP',
        channel: 'WEB',
        clientId,
        market: 'US',
        previewType: 'WIP',
      });
    });

    test('adds previewType as APR in request header if the environment is app preview', async () => {
      const oidcPreviewUrl = 'https://secure-internal-azeus-ecom-api.preview.app.stage.gaptecholapps.com';
      const addToBagUrl = `${oidcPreviewUrl}/commerce/shopping-bags/items/summary?locale=${locale}`;

      fetchMock.post(addToBagUrl, addToBagSuccessResponse);
      await updateBag({
        ...props,
        oidcUrl: oidcPreviewUrl,
      });

      expect(fetchMock.calls()[0][1]?.headers).toStrictEqual({
        'Content-Type': 'application/json',
        'X-Gap-ApiMode': 'leapfrog',
        brand: 'GP',
        channel: 'WEB',
        clientId,
        market: 'US',
        previewType: 'APR',
      });
    });

    test('adds warnings for partial success scenarios', async () => {
      const oidcPreviewUrl = 'https://secure-internal-azeus-ecom-api.preview.app.stage.gaptecholapps.com';
      const addToBagUrl = `${oidcPreviewUrl}/commerce/shopping-bags/items/summary?locale=${locale}`;

      fetchMock.post(addToBagUrl, addToBagPartialSuccess);
      const response = await updateBag({
        ...props,
        oidcUrl: oidcPreviewUrl,
      });

      const {
        items: [{ addedQuantity }],
      } = addToBagPartialSuccess;
      const enUSConfig = config('en_US');
      const message = enUSConfig['addToBag.partialSuccess'].replace('{{quantityAdded}}', addedQuantity);
      expect(response.warnings).toMatchObject({
        addedQuantity,
        errorCode: 'BAGICM-3070',
        message,
        partialSuccess: true,
      });
    });

    test('adds warnings for partial success scenarios with message in french', async () => {
      const frenchLocale = 'fr_CA';
      const oidcPreviewUrl = 'https://secure-internal-azeus-ecom-api.preview.app.stage.gaptecholapps.com';
      const addToBagUrl = `${oidcPreviewUrl}/commerce/shopping-bags/items/summary?locale=${frenchLocale}`;

      fetchMock.post(addToBagUrl, addToBagPartialSuccess);
      const response = await updateBag({
        ...props,
        locale: frenchLocale,
        oidcUrl: oidcPreviewUrl,
      });

      const {
        items: [{ addedQuantity }],
      } = addToBagPartialSuccess;
      const enFRConfig = config(frenchLocale);
      const message = enFRConfig['addToBag.partialSuccess'].replace('{{quantityAdded}}', addedQuantity);

      expect(response.warnings).toMatchObject({
        addedQuantity,
        errorCode: 'BAGICM-3070',
        message,
        partialSuccess: true,
      });
    });
  });
});
