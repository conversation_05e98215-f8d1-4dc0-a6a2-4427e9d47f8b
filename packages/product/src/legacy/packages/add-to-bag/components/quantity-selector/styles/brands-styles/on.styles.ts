// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";

import { pdpPseudoFocus, visuallyHide } from '../../../../../styles';
import { colorMap } from '../../../../../styles/utility/mappings/colorMap';

const imageUrl = (fillColor = 'F6F4EB') => `
"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23${fillColor};fill-rule:evenodd' /%3E%3C/svg%3E"
`;

export const ONQuantitySelectorStyles = (theme: Theme): SerializedStyles => {
  return css`
    ${visuallyHide()}

    & + .pdp-quantity {
      width: 4.125rem;
      height: 2.75rem;
      align-self: flex-end;
      position: relative;

      .pdp-quantity__dropdown {
        height: 100%;
      }

      .pdp-quantity__control {
        border: none;
        box-shadow: inset 0 0 0 1px ${theme.color.gray20};
        padding: 0 1.125rem;
        height: 100%;

        ${pdpPseudoFocus(theme)}
      }

      .pdp-quantity__control::before {
        top: 0.25rem;
        right: 4%;
        background-size: 36%;
        background-color: transparent;
      }

      .pdp-quantity__option {
        text-align: center;
      }

      .fui_dropdown__menu {
        transform: translate3d(0, 0, 0);
      }

      .pdp-quantity-native__dropdown {
        height: 100%;
      }

      .pdp-quantity-native__select {
        border: none;
        height: 100%;
        padding: 0 1.125rem;
        font-size: 1.0625rem;
        box-shadow: inset 0 0 0 1px ${theme.color.g4};
        border-bottom: none;
      }

      .pdp-quantity-native::after {
        top: 0.25rem !important;
        right: 11% !important;
        background-size: 36% !important;
        background-color: transparent !important;
      }
    }

    & + .pdp-quantity-native {
      margin-top: 0;
      width: 4.125rem;
    }

    & + .pdp-quantity {
      width: 3.43rem;
      height: 2.5rem;
      color: ${theme.color.wh};

      .pdp-quantity__control {
        color: ${theme.color.bk};
        font-size: 0.875rem;
        line-height: 1.1875rem;
        height: 2.5rem;
        width: 3.563rem;
        &::before {
          top: 1rem;
          right: -10%;
          background: url(${imageUrl(colorMap.qtyControlFilter)}) no-repeat;
        }
      }
      .pdp-quantity__option {
        color: ${theme.color.bk};
        font-style: normal;
        font-size: 1rem;
        line-height: 1.375rem;
        text-align: center;
      }
      .pdp-quantity-native__select {
        height: 2.5rem;
        width: 3.438rem;
        font-size: 0.875rem;
      }
    }
  `;
};
