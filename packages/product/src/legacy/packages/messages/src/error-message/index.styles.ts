// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { css, forBrands, getFontWeight } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { brandLightColorFactoryStyles } from '../../../styles/brand-factory.styles';
import { pdpNotification, pdpNotificationError, sdsBreakpoint } from '../../../styles/brand-styles/utils/util';
import { setupRedesignStyles } from '../../../styles/setupRedesignStyles';
import { colorMap } from '../../../styles/utility/mappings';

export const errorMessageStyles =
  (brand: string) =>
  (theme: Theme): InterpolationPrimitive => {
    const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;
    const setGapRedesignStyles = setupRedesignStyles(theme).isGapBuyBoxRedesign2024;
    const setAtRedesignStyles = setupRedesignStyles(theme).isAtBuyBoxRedesign2024;

    const hasFreeShippingMessage = document.querySelector('.freeShippingMessageWrapper');
    const marginPDFNotification = hasFreeShippingMessage ? '0px' : '12px 0px 8px 0px';
    const fontExtrasTheme2019 = forBrands(theme, {
      br: () => `
      font-size: 0.875rem;
      letter-spacing: 1px;
    `,
    });

    const defaultStyles = css`
      ${pdpNotification(theme)}
      ${pdpNotificationError(theme)}
      ${fontExtrasTheme2019}
      background: ${brandLightColorFactoryStyles(theme, 'inherit')};
      align-self: ${setAtRedesignStyles('stretch')};
      margin: ${setAtRedesignStyles(marginPDFNotification)};
      width: ${brand === 'at' && 'unset !important'};
    `;

    const brRedesignStyles = css`
      font-size: 0.75rem;
      line-height: 1rem;
      color: rgb(var(--color-red-500));
      margin-bottom: 3rem;
    `;

    const gapRedesign2024styles = css`
      ${theme.brandFont};
      font-size: 0.75rem;
      color: ${colorMap.gap.red};
      ${getFontWeight('medium')};
      line-height: 1.125rem;
      padding: 0;
      letter-spacing: 0.015rem;
      border: none;
      margin-bottom: 0.5rem;
      @media (max-width: ${sdsBreakpoint.large}) {
        line-height: 1rem;
        font-size: 0.6875rem;
        letter-spacing: 0.01375rem;
      }
      &::before {
        display: none;
      }
    `;

    return forBrands(theme, {
      br: setStyles(brRedesignStyles, defaultStyles),
      brfs: setStyles(brRedesignStyles, defaultStyles),
      default: defaultStyles,
      gap: setGapRedesignStyles(gapRedesign2024styles, defaultStyles),
      gapfs: setGapRedesignStyles(gapRedesign2024styles, defaultStyles),
    });
  };

export const errorMessageBorderStyles = (theme: Theme): InterpolationPrimitive => {
  const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;
  const defaultStyles = css`
    border-color: ${setStyles('', theme.color.r1)};
    span svg path {
      fill: ${setStyles('', theme.color.r1)};
    }
  `;
  return forBrands(theme, {
    br: defaultStyles,
    brfs: defaultStyles,
    default: defaultStyles,
  });
};
