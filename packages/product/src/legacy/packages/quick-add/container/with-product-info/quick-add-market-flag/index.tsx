// @ts-nocheck
'use client'

/* eslint-disable react/no-array-index-key */
/***/
/***/
import { jsx } from "@ecom-next/core/react-stitch";

import { messageFlagStyles } from './index.styles';

type Props = {
  messageFlag: string;
};

export const QuickAddMarketFlag = (props: Props): JSX.Element => {
  const { messageFlag } = props;
  return (
    <div className="quick-add__marketing-flag" css={theme => messageFlagStyles(theme)}>
      {messageFlag}
    </div>
  );
};
