// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css, getFontWeight } from "@ecom-next/core/react-stitch";

export const addToBagButtonStylesForAT = (theme: Theme): SerializedStyles => {
  return css`
    &.add-to-bag {
      width: 100%;
      height: 3.313rem;
      font-size: 0.875rem;
      border-width: 1px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 1rem;
      background-color: ${theme.color.b1};
      color: ${theme.color.wh};
      ${theme.font.primary}
      font-size: 0.875rem !important;
      font-weight: ${getFontWeight('bold').fontWeight};
      letter-spacing: 0.5px;
      text-transform: uppercase;
    }

    svg {
      height: 14px;
      width: 14px;
      margin-top: 0.18rem;
      margin-left: 0.23rem;
    }

    &.add-to-bag--disabled {
      background-color: ${theme.color.wh};
      color: ${theme.color.gray54};
      border-color: ${theme.color.gray54};

      .regular-price::before {
        color: ${theme.color.gray54};
      }
    }

    &.focus-visible {
      outline: 0;
      box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
    }

    .regular-price {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .regular-price__pipe {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }

    .regular-price__line-through::before {
      padding-right: 1.25rem;
    }

    .regular-price__line-through {
      font-weight: 400;
      text-decoration-line: line-through;
    }

    @media (max-width: 693px) {
      width: 100%;
    }
  `;
};
