// @ts-nocheck
import { renderHook } from '@testing-library/react-hooks';

import type { Locale } from '../../chas-utils';
import { useFetch } from '../../use-fetch';
import { useProductStyle } from './use-product-style';

jest.mock('../../use-fetch');

describe('useProductStyle', () => {
  const requestType = 'wip';

  const defaultParams = {
    brand: 'GAP',
    clientId: 'PDPQuickAddCertona',
    contentType: requestType,
    country: 'US',
    locale: 'en_US' as Locale,
    market: 'US',
    pid: '443224002',
  };

  test('call useFetch with CHAS endpoint', () => {
    const oidc = 'https://api.gap.com';
    const CHAS_ENDPOINT = '/commerce/catalog/aggregation/v2/products/customer-choice/';
    const BASE_URL = oidc + CHAS_ENDPOINT;

    const params = {
      ...defaultParams,
      oidc,
      pssUrl: '',
    };

    const endpoint = `${BASE_URL + params.pid}?channel=ONL&market=${params.market}&brand=${params.brand}&locale=${
      params.locale
    }`;

    renderHook(() => useProductStyle(params));

    expect(useFetch).toHaveBeenCalledWith(endpoint, { headers: { 'X-Client-Application-Name': 'PDPQuickAddCertona' } });
  });
});
