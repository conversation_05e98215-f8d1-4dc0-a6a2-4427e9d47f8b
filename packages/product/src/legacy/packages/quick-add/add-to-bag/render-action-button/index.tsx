// @ts-nocheck
'use client'

import { Capitalization, FixedButton } from '@ecom-next/core/legacy/fixed-button';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import classnames from 'classnames';
import React from 'react';

import { addToBagButtonStyles } from '../../styles/brand-styles/render-action-button-styles/index.styles';
import { PriceLabel } from '../price-label/price-label';
import getButtonAnimationStatus from './button-animation-status';
import getButtonLabel from './button-label';
import getButtonLabelLocalization from './button-label-localization';
import type { RenderActionButtonProps } from './types/render-action-button-props';

const RenderActionButton = ({
  inProgress,
  response,
  onButtonClick,
  htmlProps,
  selectedSku,
  price,
  isGiftCard,
  disableButton,
  isBrBrfsRedesign,
}: RenderActionButtonProps): JSX.Element => {
  const { localize } = useLocalize();
  const buttonLabels = getButtonLabelLocalization(localize);
  const successMessage = buttonLabels.addToBagSuccess;
  const failureMessage = buttonLabels.addToBagFailure;

  const addToBagClasses = classnames('quick-add add-to-bag', {
    'add-to-bag--disabled': !selectedSku || disableButton,
  });

  return (
    <FixedButton
      animationStatus={getButtonAnimationStatus(inProgress, response)}
      capitalization={Capitalization.uppercase}
      className={`${addToBagClasses}`}
      css={theme => addToBagButtonStyles(theme, inProgress)}
      disabled={disableButton}
      failureMessage={failureMessage}
      fullWidth
      onClick={onButtonClick}
      roundedCorners
      successMessage={successMessage}
      {...htmlProps}
    >
      <span>{getButtonLabel(selectedSku, inProgress, buttonLabels)}</span>
      {!isBrBrfsRedesign && selectedSku && !isGiftCard && <PriceLabel price={price} />}
    </FixedButton>
  );
};

export default RenderActionButton;
