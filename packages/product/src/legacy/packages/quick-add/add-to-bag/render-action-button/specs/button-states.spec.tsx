// @ts-nocheck
import { fireEvent, screen } from '@testing-library/react';

import { renderAddToBag } from '../../specs/render-add-to-bag';

describe('<RenderActionButton /> Button states', () => {
  test('Should render the button with right label an item is not selected', () => {
    const { container } = renderAddToBag({ selectedSku: null });
    const button = container.querySelector('button');
    expect(button).toHaveTextContent('Select A Size');
  });

  test('Should render the button with right label an item is selected', () => {
    const { container } = renderAddToBag({ selectedSku: { skuId: '123456001' } });
    const button = container.querySelector('button');
    expect(button).toHaveTextContent('Add To Bag');
  });

  test('Should render the button with add to bag label for product info version', () => {
    const { container } = renderAddToBag();
    const button = container.querySelector('button');
    expect(button).toHaveTextContent('Add To Bag');
  });

  test('Should render the button with Added To Bag label when dimensions is selected with success response', async () => {
    const response = {
      status: 'SUCCESS',
    };
    const selectedSku = {
      skuId: '123456001',
    };
    const { container } = renderAddToBag({ response, selectedSku });
    const button = container.querySelector('button');
    await act(async () => { 

     fireEvent.click(screen.getByRole('button')); 

     })
    expect(button).toHaveTextContent('Added To Bag');
  });
});
