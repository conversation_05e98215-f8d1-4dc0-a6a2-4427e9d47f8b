// @ts-nocheck
// This spec file extends packages/product-price/index.spec.tsx without mocking the components/modules
/* eslint-disable react/display-name */
import { Brands } from '@ecom-next/core/legacy/utility';
import { render } from '@testing-library/react';
import React from 'react';

import TestApp from '../../../spec/test-app';
import Price from '../';

const priceProps = {
  price: {
    currentMaxPrice: 11.97,
    currentMinPrice: 11.97,
    localizedCurrencySymbol: '$',
    maxDiscountPercentageFromCAPI: 52,
    maxPercentageOff: 10,
    minDiscountPercentageFromCAPI: 52,
    minPercentageOff: 10,
    priceType: 2,
    regularMaxPrice: 24.99,
    regularMinPrice: 24.99,
  },
};

const renderPrice = (brandName, props = {}) => {
  return render(
    <TestApp
      brandName={brandName}
      translations={{
        'en-US': {
          translation: { 'pdp.price.percentageOff': '{{value}}% off' },
        },
      }}
    >
      <Price {...props} />
    </TestApp>
  );
};

const brandsWithoutON = [
  { brand: Brands.Athleta },
  { brand: Brands.BananaRepublic },
  { brand: Brands.BananaRepublicFactoryStore },
  { brand: Brands.Gap },
  { brand: Brands.GapFactoryStore },
];

const allBrands = [...brandsWithoutON, { brand: Brands.OldNavy }];

describe('Percentage off config', () => {
  test('should render percentage off for ON when percentageOffConfig.show is true', () => {
    const props = {
      ...priceProps,
      percentageOffConfig: { show: true, useFromCapi: true },
      selectedColorPriceInfo: { priceType: 2 },
    };
    const { container } = renderPrice(Brands.OldNavy, props);
    const percentageOffElm = container.getElementsByClassName('product-price__percentage-off')[0];

    expect(percentageOffElm).toHaveTextContent('52% Off');
  });

  test.each(allBrands)(
    'should not display percentage off for $brand when percentageOffConfig.show is false',
    ({ brand }) => {
      const props = {
        ...priceProps,
        percentageOffConfig: { show: false, useFromCapi: true },
      };
      const { container } = renderPrice(brand, props);
      const percentageOffElm = container.getElementsByClassName('product-price__percentage-off')[0];

      expect(percentageOffElm).toBeUndefined();
    }
  );

  test('should not display percentage off for GAP when percentageOffConfig.show is true', () => {
    const props = {
      ...priceProps,
      percentageOffConfig: { show: true, useFromCapi: true },
    };
    const { container } = renderPrice(Brands.Gap, props);
    const percentageOffElm = container.getElementsByClassName('product-price__percentage-off')[0];

    expect(percentageOffElm).toBeUndefined();
  });

  test('should not display price range for ON when percentageOffConfig.show is true', () => {
    const price = {
      currentMaxPrice: 29.99,
      currentMinPrice: 14.97,
      localizedCurrencySymbol: '$',
      maxDiscountPercentageFromCAPI: 50,
      maxPercentageOff: 0,
      minDiscountPercentageFromCAPI: 50,
      minPercentageOff: 0,
      priceType: 1,
      regularMaxPrice: 29.99,
      regularMinPrice: 29.99,
    };
    const rangeProps = { ...priceProps, price };

    const props = {
      ...rangeProps,
      percentageOffConfig: { show: true, useFromCapi: true },
    };
    const { container } = renderPrice(Brands.OldNavy, props);
    const rangeElem = container.querySelector('[data-testid="simpleRangeDisplay"]');

    expect(rangeElem).not.toBeInTheDocument();
  });

  describe('ConditionalStrikethroughPrice when percentageOffConfig.show is true', () => {
    test('should render ConditionalStrikethroughPrice when hover price differs from regularMinPrice', () => {
      const price = {
        ...priceProps,
        priceType: 1,
      };
      const conditionalPriceProps = { ...price, hoverColorPriceInfo: { priceType: 2 }, hoverPrice: '$14.99' };
      const props = {
        ...conditionalPriceProps,
        percentageOffConfig: { show: true, useFromCapi: true },
      };
      const { container } = renderPrice(Brands.OldNavy, props);
      const elem = container.getElementsByClassName('product-price__strike')[0];

      expect(elem).toBeInTheDocument();
    });

    test('should render ConditionalStrikethroughPrice when hover price is empty and selected price differs from regularMinPrice', () => {
      const price = {
        ...priceProps,
        priceType: 1,
      };
      const conditionalPriceProps = {
        ...price,
        selectedColorPriceInfo: { priceType: 2 },
        selectedPrice: '$14.99',
      };
      const props = {
        ...conditionalPriceProps,
        percentageOffConfig: { show: true, useFromCapi: true },
      };
      const { container } = renderPrice(Brands.OldNavy, props);
      const elem = container.getElementsByClassName('product-price__strike')[0];

      expect(elem).toBeInTheDocument();
    });

    test('should not render ConditionalStrikethroughPrice when price type is not promo/markdown', () => {
      const conditionalPriceProps = { ...priceProps, hoverPrice: '$24.99', priceType: 1 };
      const props = {
        ...conditionalPriceProps,
        percentageOffConfig: { show: true, useFromCapi: true },
      };
      const { container } = renderPrice(Brands.OldNavy, props);
      const elems = container.getElementsByClassName('product-price__strike');

      expect(elems).toHaveLength(0);
    });

    test('should not render ConditionalStrikethroughPrice when hover price is empty and price type is 1', () => {
      const conditionalPriceProps = { ...priceProps, priceType: 1, selectedPrice: '$24.99' };
      const props = {
        ...conditionalPriceProps,
        percentageOffConfig: { show: true, useFromCapi: true },
      };
      const { container } = renderPrice(Brands.OldNavy, props);
      const elems = container.getElementsByClassName('product-price__strike');

      expect(elems).toHaveLength(0);
    });
  });

  test('should render ConditionalStrikethroughPrice for ON when percentageOffConfig.show is false', () => {
    const props = {
      ...priceProps,
      percentageOffConfig: { show: false, useFromCapi: true },
      selectedColorPriceInfo: { priceType: 2 },
    };
    const { container } = renderPrice(Brands.OldNavy, props);
    const elem = container.getElementsByClassName('product-price__strike')[0];

    expect(elem).toBeInTheDocument();
  });

  test.each(brandsWithoutON)('should render ConditionalStrikethroughPrice for $brand', ({ brand }) => {
    const props = {
      ...priceProps,
      selectedColorPriceInfo: { priceType: 2 },
    };
    const { container } = renderPrice(brand, props);
    const elem = container.getElementsByClassName('product-price__strike')[0];

    expect(elem).toBeInTheDocument();
  });
});
