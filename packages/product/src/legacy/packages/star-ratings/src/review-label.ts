// @ts-nocheck
'use client'

const getReviewCountLabel = (
  reviewCount: number,
  singularLabel: string,
  pluralLabel: string,
  hideRatingLabel = false
): string => {
  let labelText = '';

  if (hideRatingLabel) {
    return `(${reviewCount})`;
  }

  if (reviewCount === 1) {
    labelText = singularLabel;
  }

  if (reviewCount > 1) {
    labelText = pluralLabel;
  }

  return labelText;
};
export default getReviewCountLabel;
