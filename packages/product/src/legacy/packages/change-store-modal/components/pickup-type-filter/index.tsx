// @ts-nocheck
'use client'

/***/
/***/
import { jsx } from "@ecom-next/core/react-stitch";
import { Dropdown } from '@ecom-next/core/legacy/dropdown';
import type { Localize } from '@ecom-next/sitewide/localization-provider';

import { getPropTranslations } from '../../helpers/getPropTranslations';
import type { TranslationMapKeysType } from '../../types';
import { pickupFilterStyles } from './brand-styles/index.styles';

const pickUpOptions = {
  allPickupTypes: 'changeStoreModal.pickupFilter.allTypes',
  cuberside: 'changeStoreModal.pickupFilter.cuberside',
  inStore: 'changeStoreModal.pickupFilter.inStore',
};

export const getPickupTypesOptions = (
  translationMap: Record<TranslationMapKeysType, string>,
  localize: Localize
): string[] => {
  return [
    localize(getPropTranslations(pickUpOptions.allPickupTypes, translationMap)),
    localize(getPropTranslations(pickUpOptions.cuberside, translationMap)),
    localize(getPropTranslations(pickUpOptions.inStore, translationMap)),
  ];
};

type PickupTypeFilterProps = {
  defaultOption?: string;
  onSelect?: (pickupSelected: any) => void;
  options: string[];
};

const defaultProps = {
  className: 'pdp-pickup',
  classNamePrefix: 'pdp-pickup',
  crossBrand: false,
  defaultMenuIsOpen: false,
  disabled: false,
  getOptionLabel: (option: any) => option,
  hasError: false,
  inverse: false,
  label: 'Dropdown',
  native: false,
  noOptionsMessage: 'No options found',
  optionKeyGenerator: (option: any, index: number): string => `${option}__${index}`,
  optionProps: {},
  placeholder: '',
  selectName: '',
  selectProps: {},
  styles: {},
};

const PickupTypeFilter = ({
  onSelect = () => {},
  options,
  defaultOption = options[0],
}: PickupTypeFilterProps): JSX.Element => {
  return (
    <Dropdown
      {...defaultProps}
      css={pickupFilterStyles}
      defaultValue={defaultOption}
      onChange={onSelect}
      options={options}
    />
  );
};

export default PickupTypeFilter;
