// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";

const arrow = (theme: Theme): SerializedStyles => css`
  border-right: 2px solid ${theme.color.g2};
  border-bottom: 2px solid ${theme.color.g2};
  content: '';
  height: 6px;
  position: absolute;
  right: -15px;
  top: 4px;
  transition: transform 0.5s;
  width: 6px;
`;

export const storeCardStylesForON = (theme: Theme): SerializedStyles => {
  return css`
    box-sizing: border-box;
    padding: 1rem;
    box-shadow: 0 1px 2px 0 rgba(102, 102, 102, 0.5);
    position: relative;
    ${theme.font.primary}

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: ${theme.color.gray50};
    }

    &.in-stock::before {
      background: ${theme.crossBrand.color.s1};
    }

    &.low-stock::before,
    &.low-inventory::before {
      background: ${theme.crossBrand.color.s2};
    }

    .store-distance {
      color: ${theme.color.g1};
      font-size: 0.9375rem;
      text-transform: uppercase;
      position: absolute;
      right: 0;
      top: 0;
      line-height: 1.75rem;
    }

    .store-toggle {
      order: 1;
      background: none;
      border: none;
      color: ${theme.color.g1};
      font-size: 0.875rem;
      padding: 0;
      position: relative;
      max-width: 5.625rem;
      text-align: left;
      ${theme.font.primary}

      &[aria-expanded='false'] {
        &:before {
          ${arrow(theme)};
          transform: rotate(45deg);
        }
      }

      &[aria-expanded='true'] {
        &:before {
          ${arrow(theme)};
          top: 6px;
          transform: rotate(225deg);
        }
      }
    }

    .store-availability {
      font-weight: bold;
      margin-top: 0.5rem;
    }

    .store-features {
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .store-details {
      font-size: 0.875rem;
      margin-top: 0.5rem;
      display: grid;

      &__wrapper {
        order: 0;
        font-size: 0.75rem;

        &[aria-hidden='true'] {
          display: none;
        }

        &[aria-hidden='false'] {
          display: block;
        }
      }

      &__address {
        color: ${theme.color.g2};
        line-height: 1.5;
        margin-bottom: 8px;
        width: 100%;
      }

      &__info-wrapper {
        display: block;
        margin-bottom: 8px;
      }

      &__phone {
        color: ${theme.color.b2};
        margin-bottom: 8px;
      }

      &__hours-title {
        color: ${theme.color.g1};
        font-weight: bold;
        margin-bottom: 4px;
      }

      &__hours {
        color: ${theme.color.g2};
        line-height: 1.78;
      }
    }
  `;
};
