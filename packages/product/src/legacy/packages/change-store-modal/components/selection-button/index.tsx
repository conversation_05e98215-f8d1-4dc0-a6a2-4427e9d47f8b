// @ts-nocheck
'use client'

/***/
/***/
import { jsx } from "@ecom-next/core/react-stitch";

import { selectionStyles } from './brand-styles/index.styles';

type SelectionButtonProps = {
  buttonLabel: string;
  children?: JSX.Element;
  disabled: boolean;
  index?: number;
  onChange: () => void;
  selected: boolean;
};

export const SelectionButton = ({
  selected,
  disabled,
  buttonLabel,
  onChange,
  children,
  index,
}: SelectionButtonProps): JSX.Element => (
  <div css={selectionStyles}>
    <button
      aria-label={buttonLabel}
      className={selected ? 'pdp-button--radio selected' : 'pdp-button--radio'}
      disabled={disabled}
      onClick={onChange}
      type="button"
    />
    <div className="pdp-button--content">
      <p aria-hidden="true" className="pdp-button--label" id={`pdp-changeStoreModal-label${index}`}>
        {buttonLabel.toLocaleLowerCase()}
      </p>
      {children}
    </div>
  </div>
);
