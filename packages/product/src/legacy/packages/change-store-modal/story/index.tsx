// @ts-nocheck
'use client'

/***/
/***/
import { useStoreFetch } from '@ecom-next/core/legacy/bopis';
import { AppStateProvider } from '@ecom-next/sitewide/app-state-provider';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { Provider as FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import type { Locale } from '@ecom-next/sitewide/localization-provider';
import { LocalizationProvider } from '@ecom-next/sitewide/localization-provider';
import type { Brands } from "@ecom-next/core/react-stitch";
import { jsx, StitchStyleProvider } from "@ecom-next/core/react-stitch";
import loadPolyfills from '@ecom-next/plp-ui/legacy/browse-polyfill-loader';
import { useEffect, useRef, useState } from 'react';
import { render } from 'react-dom';

import * as AppStateProviderHolder from '@ecom-next/sitewide/app-state-provider';
import { getOIDCUrl } from '../helpers/get-urls';
import ChangeStoreModal from '../index';
import type { ChangeStoreModalRefType } from '../types';

type AppProps = {
  brand: Brands;
  contentType: string;
  env: string;
  locale: Locale;
  market: string;
  pasConfig: ApiKeysConfig;
  pid: string;
  translations: Resource;
};

function App(props: AppProps): JSX.Element {
  const enabledFeatures = {};
  const { brand, locale, market, translations, pasConfig, pid, env, contentType } = props;
  const initialMedia = '(max-width%3A%20567px)%20and%20(max-aspect-ratio%3A%201%2F1)';
  const initialSizeClass = 'Mobile';

  const [zipCode, setZipCode] = useState('94123');
  const [selectedStoreId, setSelectedStoreId] = useState<string | undefined>();
  const [isUseStorageOn, setIsUseStorageOn] = useState(false);

  const defaultConfig = AppStateProviderHolder.useAppState().appConfig;
  const environmentConfig = AppStateProviderHolder.useAppState().appConfig;
  const finalConfig = { ...defaultConfig, ...environmentConfig } as EnvironmentConfig;
  const { oidcUrl } = getOIDCUrl(finalConfig, env, brand, market, contentType);

  const resetProps = () => {
    setZipCode('94123');
    setSelectedStoreId(undefined);
  };

  const handleUseStorageControl = () => {
    setIsUseStorageOn(!isUseStorageOn);
  };

  const { getStores } = useStoreFetch({
    brand,
    market: market.toUpperCase(),
    options: {
      bopisCurbsideEnabled: true,
      domain: oidcUrl,
      size: 40,
      storageEnabled: true,
    },
  });

  useEffect(() => {
    if (isUseStorageOn) {
      getStores(zipCode, true, true);
    } else if (typeof localStorage !== 'undefined') {
      localStorage.clear();
    }
  }, [isUseStorageOn]);

  const changeStoreModalRef = useRef<ChangeStoreModalRefType>();

  return (
    <AppStateProvider
      value={{
        ...props,
        brandName: brand,
        env: 'stage',
        errorLogger: console.error,
      }}
    >
      <BreakpointProvider initialMedia={initialMedia} initialSizeClass={initialSizeClass}>
        <FeatureFlagsProvider value={{ enabledFeatures }}>
          <LocalizationProvider locale={locale as Locale} translations={translations}>
            <StitchStyleProvider brand={brand}>
              <div>
                <button onClick={resetProps} type="button">
                  <h3>Reset props</h3>
                </button>
              </div>
              <div style={{ marginBottom: '20px' }}>
                <label htmlFor="useStorageControl">
                  <input
                    checked={isUseStorageOn}
                    id="useStorageControl"
                    name="useStorageControl"
                    onChange={handleUseStorageControl}
                    type="checkbox"
                    value="useStorageControl"
                  />
                  useStorage prop is: {isUseStorageOn ? 'on' : 'off'}
                </label>
              </div>
              <div style={{ marginBottom: '20px' }}>
                <button
                  onClick={() => {
                    console.log('open from ref');
                    changeStoreModalRef.current.openModal();
                  }}
                  type="button"
                >
                  <h3>Open modal from ref</h3>
                </button>
              </div>
              <ChangeStoreModal
                ref={changeStoreModalRef}
                // eslint-disable-next-line react/jsx-sort-props
                key={`change-store-modal--useStorageControl-${isUseStorageOn ? 'on' : 'off'}`}
                container=".package-modal"
                LASConfig={{
                  query: {
                    size: 40,
                  },
                }}
                onClose={() => {
                  console.log('close');
                }}
                onDone={({ zipCode: zip, selectedStore }) => {
                  setZipCode(zip);
                  if (selectedStore) {
                    setSelectedStoreId(selectedStore.storeId);
                    console.log('done', zip, selectedStore.storeId);
                  }
                }}
                onOpen={() => {
                  console.log('open');
                }}
                openButtonClassName="your-custom-class"
                PASConfig={pasConfig}
                selectedStoreId={selectedStoreId}
                sku={pid}
                translation={{
                  'changeStoreModal.title': 'changeStoreModal.title',
                }}
                useStorage={isUseStorageOn}
                zipCode={zipCode}
              >
                {null}
              </ChangeStoreModal>
              {/* target for PDP modals that use a portal */}
              <div className="package-modal" />
            </StitchStyleProvider>
          </LocalizationProvider>
        </FeatureFlagsProvider>
      </BreakpointProvider>
    </AppStateProvider>
  );
}

loadPolyfills((window as any).__PRODUCT_PAGE_POLYFILL__.promisePolyfillPath)(() => {
  render(<App {...(window as any).__PRODUCT_PAGE_STATE__} />, document.getElementById('product-page'));
});
