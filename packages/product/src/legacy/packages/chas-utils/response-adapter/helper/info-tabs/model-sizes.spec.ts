// @ts-nocheck
import { createModelSizes, removeModelSizes } from './model-sizes';

describe('Given createModelSizes', () => {
  const defaulLocale = 'en_US';

  test('returns model size annotations as an array', () => {
    const overviewBullets = [
      `@modelsizes 5'9":4 | 5'11":10 @modelsizes`,
      'Front slant pockets',
      'Elasticized waist',
      `@modelsizes 5'8":3 | 5'10":9 @modelsizes`,
    ];
    const modelSizes = createModelSizes(overviewBullets, defaulLocale);

    expect(modelSizes).toStrictEqual([
      [
        { height: `5'9"`, size: `4` },
        { height: `5'11"`, size: `10` },
      ],
      [
        { height: `5'8"`, size: `3` },
        { height: `5'10"`, size: `9` },
      ],
    ]);
  });

  test('When the annotation contains newlines, then returns model size annotations as an array', () => {
    const overviewBullets = [
      `
        @modelsizes 5'9":4 |
        5'11":10 @modelsizes
      `,
      'Front slant pockets',
      'Elasticized waist',
    ];
    const modelSizes = createModelSizes(overviewBullets, defaulLocale);

    expect(modelSizes).toStrictEqual([
      [
        { height: `5'9"`, size: `4` },
        { height: `5'11"`, size: `10` },
      ],
    ]);
  });

  test('When there is no match, then returns an empty array', () => {
    const overviewBullets = ['Front slant pockets', 'Elasticized waist'];
    const modelSizes = createModelSizes(overviewBullets, defaulLocale);

    expect(modelSizes).toStrictEqual([]);
  });

  test('When the locale is fr_CA, then returns model size annotations as an array', () => {
    const locale = 'fr_CA';
    const overviewBullets = [
      `
        @modelsizes 1,75 m taille 4 |
        2,89 m taille 10 @modelsizes
      `,
      'Pantalon hybride',
      'Enfiler pour Enfant',
    ];
    const modelSizes = createModelSizes(overviewBullets, locale);

    expect(modelSizes).toStrictEqual([
      [
        { height: `1,75`, size: `4` },
        { height: `2,89`, size: `10` },
      ],
    ]);
  });
});

describe('Given removeModelSizes', () => {
  test('When argument has model sizes annotations, then removes those annotations', () => {
    const modelAnnotatedDescription = `Before text @modelsizes 5'9":4 | 5'11":10 @modelsizes`;
    const description = removeModelSizes(modelAnnotatedDescription);
    expect(description).toBe('Before text ');
  });
});
