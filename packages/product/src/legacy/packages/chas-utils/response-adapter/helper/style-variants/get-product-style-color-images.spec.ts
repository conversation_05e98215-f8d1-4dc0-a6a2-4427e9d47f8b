// @ts-nocheck
import type { Brand, CustomerChoice } from '../../../types';
import ChasBaseResponse from '../../mocks/chas-cc-response.mock.json';
import { getProductStyleColorImages } from './get-product-style-color-images';

const { customerChoices } = ChasBaseResponse.styles[0].variants[0];

const mockedCustomerChoice = {
  ...customerChoices[0],
  customerChoiceNumber: '770874024',
  legacyCustomerChoiceNumber: '770874024',
};

const mixedCustomerChoices = [customerChoices[0], mockedCustomerChoice];

const createGetProductStyleColorImages = (customerChoices: CustomerChoice[], brand: Brand = 'GAP') => {
  return getProductStyleColorImages(customerChoices[0], customerChoices, brand);
};

const result = [
  '770874023_main',
  '770874023_AV1',
  '770874023_AV2',
  '770874023_AV3',
  '770874023_AV4',
  '770874023_AV5',
  '770874023_AV6',
  '770874023_AV7',
];

const secondResult = [
  ...result,
  '770874024_AV1',
  '770874024_AV2',
  '770874024_AV3',
  '770874024_AV4',
  '770874024_AV5',
  '770874024_AV6',
  '770874024_AV7',
];

describe('GIVEN getProductStyleColorImages', () => {
  test('WHEN brand IS NOT athleta THEN return a correct list of color images', () => {
    const productStyleColorImages = createGetProductStyleColorImages(customerChoices as CustomerChoice[]);
    expect(productStyleColorImages).toEqual(result);
  });
  test('WHEN brand IS NOT athleta and receives data with more than one customerChoices THEN return a correct list of color images', () => {
    const productStyleColorImages = createGetProductStyleColorImages(mixedCustomerChoices as CustomerChoice[]);
    expect(productStyleColorImages).toEqual(secondResult);
  });
  test('WHEN brand IS athleta THEN return a correct list of color images', () => {
    const productStyleColorImages = createGetProductStyleColorImages(customerChoices as CustomerChoice[], 'AT');
    expect(productStyleColorImages).toEqual(result);
  });
  test('WHEN brand IS athleta and receives data with more than one customerChoices THEN return a correct list of color images', () => {
    const productStyleColorImages = createGetProductStyleColorImages(mixedCustomerChoices as CustomerChoice[], 'AT');
    expect(productStyleColorImages).toEqual(result);
  });
});
