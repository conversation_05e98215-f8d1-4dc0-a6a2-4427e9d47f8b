// @ts-nocheck
import type { VetoAttributes } from '../../../../types/product-data/style-level-raw';
import type { CustomerChoice } from '../../types';
import { getFitInformation } from './get-fit-information';
import { getInfoTabs } from './info-tabs/get-info-tabs';

export const getVetoAttributes = (
  vetoAttributes: VetoAttributes,
  { manufacturingStyleNumber = '' }: CustomerChoice,
  legacyStyleNumber: string,
  isDropship: boolean,
  locale: Locale,
  sizeChartId: string
) => {
  const styleAttributes = vetoAttributes[manufacturingStyleNumber];
  if (styleAttributes) {
    return {
      fitInformation: getFitInformation(styleAttributes.copyHeaders, sizeChartId),
      infoTabs: getInfoTabs(styleAttributes.copyHeaders, legacyStyleNumber, isDropship, locale),
      styleMarketingFlags: styleAttributes.marketingFlags?.[0]?.flagContent || null,
    };
  }
  return { fitInformation: null, infoTabs: null, styleMarketingFlags: null };
};
