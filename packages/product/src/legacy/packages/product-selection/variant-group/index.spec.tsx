// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import type { ProductVariant } from '@pdp/types/product-data/style-level'; // NOSONAR
import { fireEvent, render, screen, within } from '@testing-library/react';
import React from 'react';

import { setupSnapshots } from '../../helpers/util/setup-snapshots';
import { TestApp } from '../index';
import { useProductSelectionContext } from '../use-product-selection-context';
import type { VariantGroupFeatureConfigTypes } from '.';
import { VariantGroup } from '.';

jest.mock('../use-product-selection-context');

const matchSnapshots = setupSnapshots();
const renderComponent = (featureConfig: VariantGroupFeatureConfigTypes) => {
  const variantGroupComponent = (brandName: Brands, enabledFeatures: Record<string, boolean>) => {
    return (
      <TestApp brandName={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
        <VariantGroup
          featureConfig={featureConfig}
          idPrefix="test"
          onBlurListener={jest.fn()}
          onChangeListener={jest.fn()}
        />
      </TestApp>
    );
  };

  return variantGroupComponent;
};

describe('<VariantGroup />', () => {
  const selectedVariant = {
    dimensions: [
      { dimensions: [{ bopisInStock: false, inStock: true, name: '44W' }], label: 'waist' },
      { dimensions: [{ bopisInStock: false, inStock: true, name: '30L' }], label: 'length' },
    ],
    id: 1,
    name: 'Regular',
  } as ProductVariant;
  const otherVariant = {
    dimensions: [
      { dimensions: [{ bopisInStock: false, inStock: true, name: '44W' }], label: 'waist' },
      { dimensions: [{ bopisInStock: false, inStock: true, name: '30L' }], label: 'length' },
    ],
    id: 2,
    name: 'Tall',
  } as ProductVariant;
  const variantGroup = [selectedVariant, otherVariant];

  const useProductSelectionContextMock = (variants = variantGroup, selected = selectedVariant) => {
    const updateDimensionSpy = jest.fn();
    const updateVariantSpy = jest.fn();
    (useProductSelectionContext as jest.Mock).mockReturnValue({
      selectedVariant: selected,
      updateDimension: updateDimensionSpy,
      updateVariant: updateVariantSpy,
      variants,
    });
    return { updateDimensionSpy, updateVariantSpy };
  };

  const setup = (props: any = {}) => {
    const { isBrBrfsRedesign } = props;
    const defaultProps = {
      featureConfig: {
        isBrBrfsRedesign: isBrBrfsRedesign || false,
        isVariantAsSizeProduct: false,
        shouldDisplayMergedVariantTooltip: false,
      },
      onBlurListener: jest.fn(),
      ...props,
    };
    return render(
      <TestApp>
        <VariantGroup {...defaultProps} />
      </TestApp>
    );
  };

  describe('styles', () => {
    useProductSelectionContextMock();
    const featureConfig: VariantGroupFeatureConfigTypes = {
      isVariantAsSizeProduct: false,
      shouldDisplayMergedVariantTooltip: false,
    };
    matchSnapshots(renderComponent(featureConfig), 'renders correctly');
    matchSnapshots(renderComponent({ ...featureConfig }), 'renders correctly for BR', {
      brands: [Brands.BananaRepublic, Brands.BananaRepublicFactoryStore],
    });
    matchSnapshots(renderComponent(featureConfig), 'renders correctly for BR redesign 2024', {
      brands: [Brands.BananaRepublic],
      flags: { 'br-redesign-2024-ph2': true },
    });
    matchSnapshots(
      renderComponent({ ...featureConfig, isVariantAsSizeProduct: true }),
      'renders correctly when isVariantAsSizeProduct is TRUE',
      { brands: [Brands.BananaRepublic] }
    );
  });

  describe('feature config', () => {
    test('should display merged variant when shouldDisplayMergedVariantTooltip is TRUE and variant has link', () => {
      const variantWithLink = {
        dimensions: [
          { dimensions: [{ bopisInStock: false, inStock: true, name: '44W' }], label: 'waist' },
          { dimensions: [{ bopisInStock: false, inStock: true, name: '30L' }], label: 'length' },
        ],
        id: 3,
        link: 'some-link',
        name: 'Short',
      } as ProductVariant;
      const variantsGroup = [variantWithLink];
      useProductSelectionContextMock(variantsGroup);
      setup({
        featureConfig: {
          shouldDisplayMergedVariantTooltip: true,
        },
      });
      expect(screen.getByRole('link', { name: /Short/i })).toBeInTheDocument();
    });

    test('should NOT display merged variant when shouldDisplayMergedVariantTooltip is FALSE', () => {
      const variantWithLink = {
        dimensions: [
          { dimensions: [{ bopisInStock: false, inStock: true, name: '44W' }], label: 'waist' },
          { dimensions: [{ bopisInStock: false, inStock: true, name: '30L' }], label: 'length' },
        ],
        id: 3,
        link: 'some-link',
        name: 'Short',
      } as ProductVariant;
      const variantsGroup = [variantWithLink];
      useProductSelectionContextMock(variantsGroup);
      setup({
        featureConfig: {
          shouldDisplayMergedVariantTooltip: false,
        },
      });
      expect(screen.queryByRole('link', { name: /Short/i })).not.toBeInTheDocument();
    });
  });

  test('renders nothing when given 5 or more variants', () => {
    const variants = [
      { id: 1, name: 'Ragular' },
      { id: 2, name: 'Tall' },
      { id: 3, name: 'Petite' },
      { id: 4, name: 'Plus' },
      { id: 5, name: 'Hipster' },
    ] as ProductVariant[];
    useProductSelectionContextMock(variants);
    setup();
    expect(screen.queryByRole('radiogroup')).not.toBeInTheDocument();
  });

  test('renders nothing when given no variants', () => {
    const variants: ProductVariant[] = [];
    useProductSelectionContextMock(variants);
    setup();
    expect(screen.queryByRole('radiogroup')).not.toBeInTheDocument();
  });

  test('renders nothing when the only variant is Regular', () => {
    const variants = [
      {
        id: 1,
        name: 'Regular',
      },
    ] as ProductVariant[];
    useProductSelectionContextMock(variants);
    setup();
    expect(screen.queryByRole('radiogroup')).not.toBeInTheDocument();
  });

  test('renders Size when the only variant is Regular and isBrBrfsRedesign is true', () => {
    const variants = [
      {
        id: 1,
        name: 'Regular',
      },
    ] as ProductVariant[];
    useProductSelectionContextMock(variants);
    setup({ isBrBrfsRedesign: true });
    expect(screen.getByLabelText('pdp.sizeLabel')).toBeInTheDocument();
  });

  test('renders the variant when the only variant is not Regular', () => {
    const variants = [
      {
        id: 2,
        name: 'Tall',
      },
    ] as ProductVariant[];
    useProductSelectionContextMock(variants);
    setup();
    const variantGroup = screen.getByRole('radiogroup');
    const variant = within(variantGroup).getByRole('radio', { name: /Tall/i });
    expect(variant).toBeInTheDocument();
  });

  test('should call publish method with "updateDimension" parameter when the user clicks in a variant radio', async () => {
    const { updateDimensionSpy, updateVariantSpy } = useProductSelectionContextMock(variantGroup, selectedVariant);
    const featureConfig: VariantGroupFeatureConfigTypes = {
      isVariantAsSizeProduct: true,
      shouldDisplayMergedVariantTooltip: true,
    };
    setup({ featureConfig });
    const radioRegular = screen.getByRole('radio', { name: /Regular/i });
    const radioTall = screen.getByRole('radio', { name: /Tall/i });
    await act(async () => { 

     fireEvent.click(radioRegular); 

     })
    await act(async () => { 

     fireEvent.click(radioTall); 

     })
    expect(updateDimensionSpy).toHaveBeenCalled();
    expect(updateDimensionSpy.mock.calls[0][0]).toBe('length');
    expect(updateDimensionSpy.mock.calls[0][1]).toBe('30L');
    expect(updateVariantSpy).toHaveBeenCalled();
    expect(updateVariantSpy.mock.calls[0][0].name).toBe('Tall');
  });

  test('should call onBlurListener on blur (variant changing from regular to tall)', () => {
    const onBlurListenerSpy = jest.fn();
    useProductSelectionContextMock(variantGroup, selectedVariant);
    setup({ onBlurListener: onBlurListenerSpy });
    const radioRegular = screen.getByRole('radio', { name: /Regular/i });
    const radioTall = screen.getByRole('radio', { name: /Tall/i });
    radioRegular.focus();
    radioTall.focus();
    expect(onBlurListenerSpy).toHaveBeenCalled();
  });
});
