// @ts-nocheck
'use client'

import type { Brands } from '@ecom-next/core/react-stitch/types';

import type { ProductColorGroup } from '../../../../../../types/product-data/style-level';
import type { ProductDimension } from '../../../../../types/dimensions';
import type { OnColorUpdate, OnHoverColorTrigger } from '../../../..';

export type ColorPickerGroupsProps = {
  brandName: Brands;
  businessCatalogItemId: string;
  dimensions: ProductDimension[];
  displayPrice: boolean;
  fullPriceGroup: ProductColorGroup[];
  handleScroll: () => void;
  markdownGroup: ProductColorGroup[];
  mode: number;
  onColorUpdate: OnColorUpdate;
  onHoverColorTrigger: OnHoverColorTrigger;
};
