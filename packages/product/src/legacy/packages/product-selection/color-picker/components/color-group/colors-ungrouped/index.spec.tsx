// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import { createSerializer } from '@emotion/jest';
import { render } from '@testing-library/react';
import React from 'react';
import renderer from 'react-test-renderer';

import { TestApp } from '../../../../index';
import colorInStock from '../../../collaborators/color-in-stock';
import { colorGroup } from '../../../data/color-group-mock';
import { ColorsUngrouped } from '.';
import type { ColorsUngroupedProps } from './types/color-group-redesign-on-props';

jest.mock('../../../collaborators/color-in-stock');
expect.addSnapshotSerializer(createSerializer());

const defaultProps: ColorsUngroupedProps = {
  brandName: Brands.OldNavy,
  colors: colorGroup.colors,
  dimensions: [],
  displayPrice: false,
  handleScroll: jest.fn(),
  selectedColorId: '',
};

const colorInStockMock = (blueIsInStock = false, blackIsInStock = true): void => {
  (colorInStock as jest.Mock).mockImplementation(color =>
    color.colorName === 'Blue' ? blueIsInStock : blackIsInStock
  );
};

const renderColorGroup = (props = defaultProps) =>
  render(
    <TestApp>
      <ColorsUngrouped {...props} />
    </TestApp>
  );

describe('<ColorsUngrouped />', () => {
  beforeEach(() => {
    colorInStockMock();
  });

  // note: based on code, this version is only used by ON
  test('when brand is ON and displayPrice is TRUE it should match snapshot', () => {
    const propsWithDisplayPrice = {
      ...defaultProps,
      displayPrice: true,
    };
    const tree = renderer
      .create(
        <TestApp brandName={Brands.OldNavy}>
          <ColorsUngrouped {...propsWithDisplayPrice} />
        </TestApp>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  test('when brand is BR and br-redesign-2024-ph2 is TRUE it should match snapshot', () => {
    const propsWithDisplayPrice = {
      ...defaultProps,
      brandName: Brands.BananaRepublic,
      displayPrice: true,
    };
    const tree = renderer
      .create(
        <TestApp
          brandName={Brands.BananaRepublic}
          featureFlagsCtxValue={{
            enabledFeatures: { 'br-redesign-2024-ph2': true },
          }}
        >
          <ColorsUngrouped {...propsWithDisplayPrice} />
        </TestApp>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  test('<ColorsUngrouped /> should be accessible', () => {
    const { container } = renderColorGroup();
    expect(container).toBeAccessible();
  });

  test('when brand is ON and displayPrice is FALSE it should match snapshot', () => {
    const propsWithDisplayPrice = {
      ...defaultProps,
      displayPrice: false,
    };
    const tree = renderer
      .create(
        <TestApp brandName={Brands.OldNavy}>
          <ColorsUngrouped {...propsWithDisplayPrice} />
        </TestApp>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  describe('GIVEN Price component', () => {
    test('WHEN displayPrice is true THEN should render the price component', () => {
      const propsWithDisplayPrice = {
        ...defaultProps,
        displayPrice: true,
      };

      const { container } = renderColorGroup(propsWithDisplayPrice);
      expect(container.querySelector('.swatch-price')).toBeDefined();
    });

    test('WHEN displayPrice is true THEN should render the price component after color swatch', () => {
      const propsWithDisplayPrice = {
        ...defaultProps,
        displayPrice: true,
      };
      const colorSwatchFollowedByPrice = '.swatch--color + .swatch-price';

      const { container } = renderColorGroup(propsWithDisplayPrice);
      expect(container.querySelector(colorSwatchFollowedByPrice)).toBeDefined();
    });

    test('WHEN displayPrice is false THEN should not render the price component', () => {
      const { container } = renderColorGroup();
      expect(container.querySelector('.swatch-price')).toBeFalsy();
    });
  });

  describe('GIVEN ColorSwatch component', () => {
    test('WHEN the colors list has more than one item THEN should display multiple color swatches', () => {
      const { container } = renderColorGroup();
      expect(container.querySelectorAll('.swatch')).toHaveLength(colorGroup.colors.length);
    });

    test('WHEN a color is out of stock THEN should set out of stock message in a span', () => {
      colorInStockMock(false, true);

      const { container } = renderColorGroup();
      const OOSSpan = container.querySelectorAll('.swatch ~ span')[0].textContent;
      expect(OOSSpan).toEqual('fui.color_swatch.out_of_stock');
    });

    test('WHEN the selected dimension is out of stock for a color THEN should set out of stock message in a span', () => {
      colorInStockMock(true, false);

      const { container } = renderColorGroup(defaultProps);
      const OOSSpan = container.querySelectorAll('.swatch ~ span')[0].textContent;
      expect(OOSSpan).toEqual('fui.color_swatch.out_of_stock');
    });

    test('WHEN a dimension is out of stock at the color level THEN should set out of stock along with price in aria-decribedby', () => {
      colorInStockMock(true, false);

      const { container } = renderColorGroup(defaultProps);
      const blackInput = container.querySelectorAll('.swatch input')[1].getAttribute('aria-describedby');
      expect(blackInput).toContain('buybox-color-swatch--Black-outofstock');
    });
  });

  describe('GIVEN colorswatch component', () => {
    test('WHEN the there is a selected color THEN adds a class name to the selected swatch', () => {
      const props = {
        ...defaultProps,
        displayPrice: true,
        selectedColorId: colorGroup.colors[0].businessCatalogItemId,
      };
      const { container } = renderColorGroup(props);
      const expectedClassName = '.swatch-color__selected';
      expect(container.querySelector(expectedClassName)).toBeInTheDocument();
    });
  });
});
