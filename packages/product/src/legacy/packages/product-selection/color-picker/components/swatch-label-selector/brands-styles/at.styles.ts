// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";

import { setupRedesignStyles } from '../../../../../styles';

export const ATSwatchLabelContainerStyles = (theme: Theme): SerializedStyles => {
  const setStyles = setupRedesignStyles(theme).isAtBuyBoxRedesign2024;

  return css`
    position: relative;
    max-width: 350px;
    margin: ${setStyles('0px', 'auto')};
    padding-bottom: ${setStyles('1.5rem', '1rem')};
    text-align: ${setStyles('left', 'center')};
  `;
};

export const ATswatchLabelTextAlign = (): SerializedStyles => {
  return css`
    text-align: center;

    @media (min-width: 569px) {
      text-align: center;
    }
  `;
};
