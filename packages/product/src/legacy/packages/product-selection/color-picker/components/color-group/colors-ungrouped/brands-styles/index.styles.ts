// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { BRColorsUngrouped } from './br.styles';
import { ONColorsUngrouped, ONColorUngroupedBaseStyles, ONPdpColorSwatchStyles } from './on.styles';

export const colorsUngroupedStyles = (theme: Theme, displayPrice: boolean) => (): InterpolationPrimitive =>
  forBrands(theme, {
    br: BRColorsUngrouped,
    brfs: BRColorsUngrouped,
    on: ONColorsUngrouped(theme, displayPrice),
  });

export const colorUngroupedBaseStyles = (theme: Theme) => (): InterpolationPrimitive =>
  forBrands(theme, {
    on: ONColorUngroupedBaseStyles,
  });

export const pdpColorSwatchStyles = (theme: Theme) => (): InterpolationPrimitive =>
  forBrands(theme, {
    on: ONPdpColorSwatchStyles,
  });
