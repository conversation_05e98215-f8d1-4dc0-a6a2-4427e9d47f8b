// @ts-nocheck
'use client'

import React, { createContext } from 'react';

import type { ColorPickerContextType, ColorPickerProviderProps } from './color-picker-types';

export const ColorPickerContext = createContext<ColorPickerContextType>({
  featureConfig: {
    isBrBrfsRedesign: false,
    isBrRedesign2024Ph2: true,
    isSuperPdpEnabled: false,
    isSuperPdpMergeStyle: false,
    percentageOffConfig: { show: false, useFromCapi: false },
    shouldDisplayColorLabel: true,
    tertiaryFontEnabled: true,
  },
});

export const ColorPickerProvider = ({ children, featureConfig }: ColorPickerProviderProps): JSX.Element => {
  return <ColorPickerContext.Provider value={{ featureConfig }}>{children}</ColorPickerContext.Provider>;
};
