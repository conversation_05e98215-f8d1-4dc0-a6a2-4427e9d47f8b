// @ts-nocheck
'use client'

import type { ReactNode } from 'react';

import type { PercentageOffConfigProps } from '../../../product-price';

export type ColorPickerContextType = {
  featureConfig: ColorPickerFeatureConfig;
};

export type ColorPickerFeatureConfig = {
  isAtRedesign2024?: boolean;
  isBrBrfsRedesign?: boolean;
  isBrRedesign2024Ph2?: boolean;
  isBrfsRedesign2024Ph2?: boolean;
  isGapBuyBoxRedesign2024?: boolean;
  isSuperPdpEnabled: boolean;
  isSuperPdpMergeStyle?: boolean;
  percentageOffConfig?: PercentageOffConfigProps;
  shouldDisplayColorLabel: boolean;
  tertiaryFontEnabled: boolean;
};

export type ColorPickerProviderProps = {
  children: ReactNode;
  featureConfig: ColorPickerFeatureConfig;
};
