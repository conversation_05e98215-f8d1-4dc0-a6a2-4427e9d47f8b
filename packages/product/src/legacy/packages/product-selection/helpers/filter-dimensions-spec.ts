// @ts-nocheck
import type { ProductColor } from '@pdp/types/product-data/style-level';

import { filterDimensions } from './filter-dimensions';

const dimensions1 = {
  dimensionGroupId: 'sizeDimension1',
  dimensions: [
    {
      bopisInStock: true,
      inStock: true,
      name: '26W',
    },
    {
      bopisInStock: true,
      inStock: true,
      name: '28W',
    },
    {
      bopisInStock: true,
      inStock: true,
      name: '30W',
    },
  ],
  label: 'waist',
  selectedDimension: '',
};

const dimensions2 = {
  dimensionGroupId: 'sizeDimension2',
  dimensions: [
    {
      bopisInStock: true,
      inStock: true,
      name: '26L',
    },
    {
      bopisInStock: true,
      inStock: true,
      name: '28L',
    },
    {
      bopisInStock: true,
      inStock: true,
      name: '30L',
    },
    {
      bopisInStock: true,
      inStock: true,
      name: '32L',
    },
  ],
  label: 'Length',
  selectedDimension: '',
};

const color = {
  sizes: [
    {
      sizeDimension1: '26W',
      sizeDimension2: '26L',
    },
    {
      sizeDimension1: '26W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '26W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '26L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '32L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '32L',
    },
  ],
} as ProductColor;

describe('filterDimensions', () => {
  test('dimensinon should not be filtered if length of dimensions less than 1', () => {
    const result = filterDimensions([dimensions1], color);
    expect(result).toStrictEqual([dimensions1]);
  });

  test('dimensinon should not be filtered if there is no selection', () => {
    const result = filterDimensions([dimensions1, dimensions2], color);
    expect(result).toStrictEqual([dimensions1, dimensions2]);
  });

  test('dimension2 should be filtered when dimension1 is selected', () => {
    const selectedDimensions1 = { ...dimensions1, selectedDimension: '26W' };
    const filteredDimensions = [
      {
        bopisInStock: true,
        inStock: true,
        name: '26L',
      },
      {
        bopisInStock: true,
        inStock: true,
        name: '28L',
      },
      {
        bopisInStock: true,
        inStock: true,
        name: '30L',
      },
    ];
    const expectedDimensions = [selectedDimensions1, { ...dimensions2, dimensions: filteredDimensions }];
    const result = filterDimensions([selectedDimensions1, dimensions2], color);
    expect(result).toStrictEqual(expectedDimensions);
  });

  test('dimensions should be filtered when dimensions selected', () => {
    const selectedDimensions1 = { ...dimensions1, selectedDimension: '26W' };
    const selectedDimensions2 = { ...dimensions2, selectedDimension: '26L' };
    const filteredDimensions1 = [
      {
        bopisInStock: true,
        inStock: true,
        name: '26W',
      },
      {
        bopisInStock: true,
        inStock: true,
        name: '28W',
      },
    ];
    const filteredDimensions2 = [
      {
        bopisInStock: true,
        inStock: true,
        name: '26L',
      },
      {
        bopisInStock: true,
        inStock: true,
        name: '28L',
      },
      {
        bopisInStock: true,
        inStock: true,
        name: '30L',
      },
    ];

    const expectedDimensions = [
      { ...selectedDimensions1, dimensions: filteredDimensions1 },
      { ...selectedDimensions2, dimensions: filteredDimensions2 },
    ];
    const result = filterDimensions([selectedDimensions1, selectedDimensions2], color);
    expect(result).toStrictEqual(expectedDimensions);
  });
});
