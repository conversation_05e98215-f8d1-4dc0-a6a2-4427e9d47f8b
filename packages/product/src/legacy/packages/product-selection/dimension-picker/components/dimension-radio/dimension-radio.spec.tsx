// @ts-nocheck
import { Brands } from "@ecom-next/core/react-stitch";
import { createSerializer } from '@emotion/jest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import renderer from 'react-test-renderer';

import { QuickAddTestContext } from '../../../../quick-add/spec/test-context';
import type { BackOrderMessage } from '../../../../types/back-order-message';
import { DimensionRadio } from './dimension-radio';
import { dimensionRadioFontStyles, dimensionRadioStyles, quickAddWithImageStyles, textStyles } from './index';

jest.mock('@ecom-next/core/legacy/localization-provider', () => {
  const originalModule = jest.requireActual('@ecom-next/core/legacy/localization-provider');

  const useLocalize = () => {
    return {
      localize: (key: string, option?: any) => {
        return {
          'pdp.dimensionPicker.outOfStock': `${option?.dimension} out of stock`,
          'pdp.sizeLabel': 'Size',
        }[key];
      },
    };
  };

  return {
    __esModule: true,
    ...originalModule,
    useLocalize,
  };
});

const baseProps = {
  backOrderMessage: {} as BackOrderMessage,
  className: '',
  id: '123',
  name: 'quick-add-dimension',
  onBlur: () => {},
  onChange: () => {},
  onMouseEnter: () => {},
  onMouseLeave: () => {},
  selected: true,
  shouldDisplayInStock: true,
  triggerProps: {},
  value: 'size',
  visibleBackOrderTooltip: false,
};

function setup(props = {}, brFlag = false, brand?) {
  return render(
    <QuickAddTestContext
      brandName={brand}
      featureFlagsCtxValue={{
        enabledFeatures: { 'br-redesign-2024-ph2': brFlag },
      }}
    >
      <DimensionRadio {...baseProps} {...props} isBrBrfsRedesign={brFlag} />
    </QuickAddTestContext>
  );
}

describe('<DimensionRadio />', () => {
  test('DimensionRadio should be accessible when not in stock', async () => {
    const { container } = setup({ inStock: false });
    await expect(container).toBeAccessible();
  });

  test('DimensionRadio should be accessible when in stock', async () => {
    const { container } = setup({ inStock: false });
    await expect(container).toBeAccessible();
  });

  test('renders correctly', () => {
    const tree = renderer
      .create(
        <QuickAddTestContext>
          <DimensionRadio {...baseProps} selected={false} />
        </QuickAddTestContext>
      )
      .toJSON();
    expect(tree).toMatchSnapshot();
  });

  describe('item is out of stock', () => {
    test('displays the out of stock indicator', () => {
      setup({ inStock: false });
      expect(screen.getByLabelText('Size:size out of stock')).toBeInTheDocument();
    });
  });

  describe('item is in stock', () => {
    test('does not display the out of stock indicator', () => {
      setup({ inStock: true });
      expect(screen.queryByLabelText('Size:size out of stock')).not.toBeInTheDocument();
    });
  });

  describe('item is selected', () => {
    test('checks the radio button', () => {
      setup({ selected: true });
      const checkbox = screen.getByLabelText('Size:size', { selector: 'input' });
      expect(checkbox).toBeChecked();
    });
  });

  describe('item is not selected', () => {
    test('does not check the radio button', () => {
      setup({ selected: false });
      const checkbox = screen.getByLabelText('Size:size', { selector: 'input' });
      expect(checkbox).not.toBeChecked();
    });
  });

  describe('item with short name (<= 3 chars)', () => {
    test('displays the correct class', () => {
      const { container } = setup({ selected: true, value: 'abc' });
      expect(container.querySelector('.pdp-dimension.pdp-dimension--auto-width')).not.toBeInTheDocument();
    });
  });

  describe('item with long name (> 3 chars)', () => {
    test('displays the correct class', () => {
      const { container } = setup({ selected: true });
      expect(container.querySelector('.pdp-dimension.pdp-dimension--auto-width')).toBeInTheDocument();
    });
  });

  describe('any value with long name (> 2 chars)', () => {
    test('displays the correct class, when br-redesign-2024-ph2 = true', () => {
      const { container } = setup({ isLong: true, selected: true }, true, Brands.BananaRepublic);
      expect(container.querySelector('.pdp-dimension.pdp-dimension--auto-width')).toBeInTheDocument();
    });
  });

  describe('backOrderMessage visibility', () => {
    const backOrderMessage = {
      ariaLabelText: 'back order message',
      mainText: 'back order message',
    };

    test('should display backorderMessage when receive it in props', async () => {
      setup({ backOrderMessage, visibleBackOrderTooltip: true });
      expect(screen.getByText('back order message')).toBeInTheDocument();
    });

    test('should not display backorderMessage when did not receive it in props', () => {
      setup();
      expect(screen.queryByText('back order message')).not.toBeInTheDocument();
    });

    test('should not display backorderMessage when visibleBackOrderTooltip is false', () => {
      setup({ backOrderMessage, visibleBackOrderTooltip: false });
      expect(screen.queryByText('back order message')).not.toBeInTheDocument();
    });
  });
});

expect.addSnapshotSerializer(createSerializer());

describe('<DimensionRadio /> snaps', () => {
  test.each([
    {
      brand: Brands.Athleta,
    },
    {
      brand: Brands.BananaRepublic,
    },
    {
      brand: Brands.BananaRepublicFactoryStore,
    },
    {
      brand: Brands.OldNavy,
    },
    { brand: Brands.Gap },
    {
      brand: Brands.GapFactoryStore,
    },
    { brand: Brands.OldNavy, inStock: true, selected: true, shouldDisplayInStock: true, visibleBackOrderTooltip: true },
  ])('Renders DimensionRadio styles for $brand', async ({ brand }) => {
    const component = renderer.create(
      <QuickAddTestContext brandName={brand}>
        <div>
          <div css={theme => quickAddWithImageStyles(theme)} />
          <div css={theme => dimensionRadioFontStyles(theme)} />
          <div css={theme => dimensionRadioStyles(theme, true)} />
          <div css={theme => textStyles(theme)} />
        </div>
      </QuickAddTestContext>
    );
    await new Promise(resolve => setTimeout(resolve));
    const tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
