// @ts-nocheck
import { isTrueFitInDOM } from '../../../../../helpers/util/open-truefit-helper';
import hasAdjacentSizes from '../helpers/adjacent-size-helper';
import type { UseSizeSamplingType } from '../types/use-size-sampling-type';
import { useSizeSamplingSession } from './use-size-sampling-session';

export const useSizeSampling = (
  sizeSamplingSegment: string,
  sizeSamplingFeatureFlag: boolean,
  minReviewThreshold: number,
  enabledCategories: string,
  primaryCategoryId: string,
  reviewRatings: { review_count: number }
): UseSizeSamplingType => {
  const { getSessionSizeValue, getSessionLengthValue, getSessionItem } = useSizeSamplingSession();

  const enableSizeSampling =
    !!sizeSamplingSegment &&
    sizeSamplingSegment !== 'x' &&
    sizeSamplingFeatureFlag &&
    enabledCategories !== '' &&
    (enabledCategories.split(',').includes(primaryCategoryId) || enabledCategories === 'ALL');

  const isPassingReviewThreshold = !!reviewRatings?.review_count && reviewRatings?.review_count >= minReviewThreshold;
  const hideReviews = !isPassingReviewThreshold && sizeSamplingSegment === 'b';
  const hideTrueFit = !isTrueFitInDOM() && sizeSamplingSegment === 'a';
  const hideTrueFitAndReviews = !isPassingReviewThreshold && !isTrueFitInDOM() && sizeSamplingSegment === 'c';
  const isNotEnabled = !enableSizeSampling || hideTrueFit || hideReviews || hideTrueFitAndReviews;

  const isSameSizeAndDifferentVariant = (
    styleId: string,
    variantId?: number | null,
    selectedDimensionValue?: string
  ): boolean => {
    const sessionItem = getSessionItem(styleId);

    if (!sessionItem || isNotEnabled) {
      return false;
    }

    const { variantValue, sizeValue, lengthValue } = sessionItem;

    if (
      selectedDimensionValue &&
      [sizeValue, lengthValue].includes(selectedDimensionValue) &&
      variantId !== variantValue
    ) {
      return true;
    }

    return false;
  };

  const showSizeSamplingIntercept = (
    sizesDimensionsValue: string[],
    styleId: string,
    selectedDimensionValue: string,
    dimensionGroupId: string,
    selectedVariantId: number | null
  ) => {
    if (isNotEnabled || (dimensionGroupId !== 'sizeDimension1' && dimensionGroupId !== 'sizeDimension2')) {
      return false;
    }

    if (isSameSizeAndDifferentVariant(styleId, selectedVariantId, selectedDimensionValue)) {
      return true;
    }

    const sessionValue =
      dimensionGroupId === 'sizeDimension1' ? getSessionSizeValue(styleId) : getSessionLengthValue(styleId);
    return hasAdjacentSizes(sessionValue, selectedDimensionValue, sizesDimensionsValue);
  };

  return { enableSizeSampling, isPassingReviewThreshold, isSameSizeAndDifferentVariant, showSizeSamplingIntercept };
};
