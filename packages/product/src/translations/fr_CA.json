{"pdp.currencySymbol": "$ CA", "pdp.product.sale": "Soldes", "pdp.technicalError": "Nous éprouvons un problème technique sur cette page. Veuillez actualiser la page et réessayer votre sélection ou trouver un autre produit.", "pdp.closeModal.altText": "fermer la fenêtre contextuelle", "pdp.quantitySelector.title": "Quantité", "pdp.quantitySelector.label.qty": "Qté", "pdp.quantityGroup.ariaLabel": "Sélectionner une quantité", "pdp.selectASize.text": "Sélectionner une Taille", "pdp.addToBag.errorMessage": "Nous éprouvons un problème technique qui nous empêche d'ajouter cet article à votre panier. Veuillez actualiser la page ou réessayer plus tard. Si le problème persiste, contactez le service à la clientèle au 1&nbsp;800&nbsp;427-7895 pour obtenir de l'aide.", "pdp.addToBag.text": "A<PERSON>ter au panier", "pdp.selection.unavailable.text": "Article non disponible", "pdp.addToBag.ariaLabel": "Ajouter au panier. Ce bouton ajoutera l'article à votre panier et ouvrira une fenêtre de dialogue. Une fois la fenêtre ouverte, appuyez sur Tab pour déplacer le contexte dans le dialogue.", "pdp.dimensionPicker.errorMessage.generalError": "Nous éprouvons un problème technique sur cette page. Veuillez actualiser la page et réessayer votre sélection ou trouver un autre produit.", "pdp.buyBox.errorMessage.onBackOrder": "En rupture de stock - date d'expédition prévue", "pdp.buyBox.errorMessage.onBackOrderAlternate": "En rupture de stock - date d'expédition prévue", "pdp.buyBox.errorMessage.onBackOrder.ariaLabel": "En rupture de stock. Date d'expédition prévue :", "pdp.buyBox.mergedVariantMessage": "vous amènera à une nouvelle page de produit", "pdp.buyBox.productSelections.title": "Sélections de produits", "pdp.priceNow": "Promo", "pdp.modal.opens": "ouvre une fenêtre de dialogue.", "pdp.drawer.openButton": "Ouvre un tiroir", "pdp.drawer.closeButton": "<PERSON><PERSON><PERSON> le tiroir", "pdp.availabilityMessageInline.text": "", "pdp.availabilityMessageShipTo.shipTo": "Expédier à une adresse", "pdp.availabilityMessageShipTo.availableShipTo": "Disponible - Expédier à une adresse", "pdp.availabilityMessageShipTo.unavailableShipTo": "Non disponible - Expédier à une adresse", "pdp.availabilityMessageShipTo.unavailableShipToAddress": "L'expédition à cette adresse n'est pas offerte", "pdp.availabilityMessageShipTo.shippingUnavailable": "Expédition non disponible", "pdp.availabilityMessageShipTo.freeMin": "Gratuit (min. 50 $)", "pdp.fulfillmentMethod.freeShipping": "<PERSON><PERSON><PERSON>", "pdp.fulfillmentMethod.insStore": "Cueillette en magasin", "pdp.fulfillmentMethod.ariaLabel": "Sélectionner un mode de traitement.", "pdp.fulfillmentMethod.selectAStore": "Sélectionner un magasin pour vérifier la disponibilité du produit pour cueillette en magasin", "pdp.fulfillmentMethod.shipFree": "Expédier à une adresse - GRATUIT (MIN. 50 $)", "pdp.fulfillmentMethod.canadaMarket.shipFree": "Expédier à une adresse - Les Membres du Programme de Récompenses peuvent bénéficier de la livraison GRATUITE", "pdp.fulfillmentMethod.orderBy": "Commandez avant 16 h pour l'obtenir aujourd'hui", "pdp.fulfillmentMethod.mapLinkAriaLabel": "indications pour se rendre au {{store.storeName}} - à {{store.storeDistance}} km, ouvre un nouvel onglet", "pdp.fulfillmentMethodV2.accordionLabel": "Il vous le faut rapidement? Trouvez-le en magasin", "pdp.fulfillmentMethodV2.membersShippingMessage": "LIVRAISON GRATUITE pour les membres du programme de récompenses (achat 50 $ +)", "pdp.variantGroupLabel": "<PERSON><PERSON><PERSON>", "pdp.colorPickerGroupLabel": "<PERSON><PERSON><PERSON>", "pdp.colorLabel": "<PERSON><PERSON><PERSON>", "pdp.sizeLabel": "<PERSON><PERSON>", "pdp.fitLabel": "Coupe", "pdp.dropship.assemblyInstructions": "Instructions d'assemblage", "pdp.ContinueShoppingButton.buttonText": "Continuer les achats", "pdp.customerInformationForm.firstNameLabel": "Prénom", "pdp.customerInformationForm.firstNameError": "Veuillez saisir un prénom.", "pdp.customerInformationForm.lastNameLabel": "Nom de famille", "pdp.customerInformationForm.lastNameError": "Veuillez saisir un nom de famille.", "pdp.customerInformationForm.emailLabel": "<PERSON><PERSON><PERSON>", "pdp.customerInformationForm.emailError": "<PERSON><PERSON><PERSON>z saisir une adresse courriel valide.", "pdp.reservationCancel.ariaLabel": "Annuler la réservation et fermer la fenêtre de dialogue de recherche en magasin", "pdp.emailSignUpCheckbox": "Inscrivez-vous afin de recevoir des courriels vous permettant d'avoir le premier choix sur les nouveaux arrivages, les offres à ne pas manquer et plus encore.", "pdp.selection.error.zipCode": "Veuillez entrer un code postal valide.", "pdp.selection.label.zipCode": "Code postal", "pdp.selection.postalCodeInput.ariaLabel": "Vérifier la disponibilité en magasin avec le code postal. Les magasins s'afficheront lorsqu'un code postal valide sera saisi.", "pdp.changeStoreButton.buttonText": "<PERSON>r <PERSON> ma<PERSON>in", "pdp.photoCarousel.altImages.productImageAltText": "Voir une image plus grande du produit {{imageNumber}} de {{imagesTotal}}", "pdp.photoCarousel.altImages.productVideoAltText": "Voir une vidéo du produit", "pdp.photoCarousel.imageAltText": "Détails sur la couleur du produit", "pdp.photoCarousel.modelSizeText": "<PERSON><PERSON><PERSON> de taille {{size}}", "pdp.photoCarousel.modelSizeNumber": "<PERSON><PERSON> {{size}}", "pdp.photoCarousel.viewOnModel": "Voir sur le mannequin:", "pdp.photoBrick.photoBrickAltText": "L'image numéro {{imageNumber}} présente {{productTitle}}", "pdp.photoBrick.imagesShowingProductTitle": "les images présentent {{productTitle}}", "pdp.modelSizeSelector.text": "<PERSON><PERSON><PERSON><PERSON> de taille du mannequin", "pdp.modelSizeSelector.seeOn": "Voir", "pdp.modelSizeSelector.buttonLabel": "Voir {{size}}", "pdp.modelSizeSelectorSlider.textLineOne": "Le mannequin est", "pdp.modelSizeSelectorSlider.textLineTwo": "<PERSON><PERSON> {{size}}", "pdp.modelSizeSelectorSlider.buttonLabel": "Voir sur un mannequin de taille {{size}}", "pdp.recommendedProduct.price": "{{price}} {{currencySymbol}}", "pdp.recommendedProduct.percentOff": "{{percentage}} % de rabais", "pdp.recommendedProduct.newPrice": "Promo {{price}} {{currencySymbol}}", "pdp.seeReviews": "Voir les avis", "pdp.writeReviews": "Écrire le premier avis", "pdp.swatchLabel.ariaLabel": "La sélection actuelle de {{label}} est {{value}}", "pdp.sizeGuide": "Guide des tailles", "pdp.sizeGuide.fitAndSizingShort": "Coupe et taille", "pdp.sizeGuide.fitAndSizing": "Coupe et taille - Ouvre un tiroir", "pdp.sizeGuideAndFit": "Taille et coupe", "pdp.insitu.title.altAddedToBag": "produit a<PERSON><PERSON> au panier", "pdp.insitu.title.addToBag": "Ajouté à votre panier", "pdp.insitu.title.addToBag.light": "Ajouté à panier", "pdp.insitu.label.color": "<PERSON><PERSON><PERSON>", "pdp.insitu.label.size": "<PERSON><PERSON>", "pdp.insitu.label.qty": "Qté", "pdp.insitu.label.pickup": "Cueillette en magasin", "pdp.insitu.oneItemInBag": "1 article dans le panier", "pdp.insitu.multipleItemsInBag": "articles dans le panier", "pdp.insitu.inBag": "dans le panier", "pdp.insitu.item_count_text": "{{count}} article", "pdp.insitu.item_count_text_plural": "{{count}} articles", "pdp.insitu.noReturns": "Vente finale : aucun échange ou remboursement sur cet article", "pdp.insitu.returnsByMailOnly": "Retour par la poste uniquement", "pdp.insitu.label.subtotal": "Sous-total", "pdp.insitu.button.keepShopping": "continuer vos achats", "pdp.insitu.button.checkout": "passer la commande", "pdp.insitu.button.viewBag": "Afficher à panier", "pdp.insitu.freeShippingCompleted": "Vous vous qualifiez pour l'expédition GRATUITE!", "pdp.insitu.freeShippingNotCompleted": "Plus que <span class='free-shipping-label-amount'>{{remainingAmount}} {{currencySymbol}}</span> avant d'obtenir l'expédition GRATUITE!", "pdp.insitu.notSignedUnderThreshold.aboveFreeShippingBar": "Votre commande est de {{subtotalNoSymbol}} {{currencySymbol}}!", "pdp.insitu.notSignedOverThreshold.aboveFreeShippingBar": "Votre commande est de plus de {{priceLimit}} {{currencySymbol}}!", "pdp.insitu.signedInUnderThreshold.aboveFreeShippingBar": "Dépensez {{priceLimit}} {{currencySymbol}} pour obtenir l’expédition GRATUITE", "pdp.insitu.signedInUnderThreshold.belowFreeShippingBar": "otre commande est de {{subtotalNoSymbol}} {{currencySymbol}}... il ne manque que <span class='free-shipping-label-amount'>{{remainingAmount}} {{currencySymbol}}</span>!", "pdp.insitu.signedInOverThreshold": "Vous vous qualifiez pour l’expédition GRATUITE!", "pdp.insitu.minToGetFreeShipping": "Pour obtenir l’expédition GRATUITE pour les achats de {{priceLimit}} {{currencySymbol}} et plus", "pdp.insitu.signIn": "ouvrez une session", "pdp.insitu.joinNow": "inscrivez-vous maintenant", "pdp.insitu.signedIn.freeForRewardsMembers": "Les Membres du Programme de Récompenses peuvent bénéficier de la livraison GRATUITE", "pdp.insitu.notSigned.freeForRewardsMembers": "Les Membres du Programme de Récompenses peuvent bénéficier de la livraison", "pdp.insitu.notSigned.freeForRewardsMembers.signIn": "<PERSON><PERSON><PERSON><PERSON><PERSON> une <PERSON>", "pdp.insitu.notSigned.freeForRewardsMembers.or": "ou", "pdp.insitu.notSigned.freeForRewardsMembers.join": "S’inscrire", "pdp.atbConfirmation.title.addToBag.light": "Ajouté à panier", "pdp.atbConfirmation.button.viewBag": "Afficher à Panier", "pdp.atbConfirmation.label.subtotal": "Sous-total", "pdp.atbConfirmation.inBag": "dans le panier", "pdp.atbConfirmation.item_count_text": "{{count}} article", "pdp.atbConfirmation.item_count_text_plural": "{{count}} articles", "pdp.atbConfirmation.label.pickup": "Cueillette en magasin", "pdp.atbConfirmation.label.storePickup": "Retrait en magasin", "pdp.atbConfirmation.label.size": "<PERSON><PERSON>", "pdp.atbConfirmation.label.color": "<PERSON><PERSON><PERSON>", "pdp.atbConfirmation.label.qty": "Qté", "pdp.atbConfirmation.title.altAddedToBag": "produit a<PERSON><PERSON> au panier", "pdp.atbConfirmation.noReturns": "Vente finale : aucun échange ou remboursement sur cet article", "pdp.atbConfirmation.finalSale": "Vente finale", "pdp.atbConfirmation.returnsByMailOnly": "Retour par la poste uniquement", "pdp.reservationView.errorMessage": "Nous éprouvons actuellement des difficultés techniques. Veuillez réessayer plus tard ou nous appeler pour obtenir de l'aide.", "pdp.reservationView.contactInfo": "Veuillez saisir vos coordonnées", "pdp.reservationView.confirmation": "Nous vous enverrons une confirmation lorsque nous aurons trouvé votre article, habituellement en moins d'une heure.", "pdp.productDetails.tabList.title": "Détails du produit", "pdp.productDetails.slide.detailsHeading": "Détails", "pdp.productDetails.slide.additionalDetails": "Autres détails", "pdp.productInfoTabs.tabListLabel": "Informations sur le produit", "pdp.productInfoLinks.details.title": "Détails et entretien", "pdp.productInfoLinks.details.drawer.title": "Détails", "pdp.productInfoLinks.details.itemLabel": "Article", "pdp.reviews.title.text": "AVIS", "pdp.reviews.title.text-singular": "AVIS", "pdp.Reviews.rateReview": "AVIS", "pdp.reviews.title.customerPhotos": "Photos des clients", "pdp.no.customer.reviews.text": "Aucun avis reçu", "pdp.reviews.accordion.trigger.ariaOpenText": "Ouv<PERSON>r l'accordéon des avis", "pdp.reviews.accordion.trigger.ariaCloseText": "<PERSON><PERSON><PERSON> l'accordéon des avis", "pdp.writeReview.text": "Rédiger un Avis", "pdp.reviews.snippet.title": "Évaluer cet article", "pdp.reviews.snippet.form.title": "Donner votre avis", "pdp.availabilityMessagePickup.freePickUp": "Cueillette en magasin", "pdp.availabilityMessagePickup.pickupCost": "<PERSON><PERSON><PERSON>", "pdp.availabilityMessagePickup.getItTodayText": "Commandez avant 14 h pour l'obtenir aujourd'hui", "pdp.availabilityMessagePickup.selectionUnavailable": "Votre sélection n'est pas disponible à ce magasin.", "pdp.short.availabilityMessagePickup.selectionUnavailableAt": "Indisponible à", "pdp.short.availabilityMessagePickup.selectionAvailableAt": "Disponible à", "pdp.availabilityPickupTitle.freePickUp": "Cueillette sans frais", "pdp.addToBag.selectionError": "Veuillez faire une sélection de {{labelString}} avant d'ajouter au panier.", "pdp.addToBag.selectionErrorAlternate": "Veuillez faire une sélection de{{labelString}} avant d'ajouter au panier.", "pdp.addToBag.giftCardselectionError": "Veuillez faire une sélection avant de l'ajouter au panier.", "pdp.addToBag.giftCardselectionErrorAlternate": "Veuillez faire une sélection avant de l'ajouter au panier.", "pdp.storeLocator.confirmationThanks": "<PERSON><PERSON><PERSON>", "pdp.storeLocator.confirmationRequestReceived": "Votre demande a été envoyée.", "pdp.storeLocator.confirmationLookingforItem": "Nous cherchons votre article à l'instant.", "pdp.storeLocator.confirmationTimeText": "Nous vous enverrons une confirmation lorsque votre article sera prêt, habituellement en moins d'une heure.", "pdp.storeLocator.confirmationEmailText": "Consultez le courriel que nous vous avons envoyé pour savoir où vous présenter dans le magasin afin de récupérer vos articles.", "pdp.storeLocator.confirmationBagText": "Votre article sera mis de côté jusqu'à la fermeture.", "pdp.storeLocator.legalPleaseNote": "VEUILLEZ PRENDRE NOTE : l'emplacement du produit est mis à jour toutes les 20 à 30 minutes.", "pdp.storeLocator.legalText": "Les prix peuvent varier d’un magasin à l’autre et peuvent être différents de ceux en ligne.", "pdp.storeLocator.reservationPleaseNote": "Veuillez prendre note : vous pouvez réserver cinq articles par jour.", "pdp.storeLocator.reservationCopyText": "Après avoir confirmé la disponibilité d'un article, nous le mettons de côté jusqu'à la fermeture du magasin le jour suivant. Les prix en magasin peuvent être différents de ceux en ligne.", "pdp.outfitRecommendations.wearItWith": "À porter avec", "pdp.shippingAndReturns.title": "Livraison et retours", "pdp.shippingAndReturns.freeShipping": "GRATUIT tous les jours (50 $ CA et plus)", "pdp.shippingAndReturns.flatRate": "Quatre à cinq jours ouvrables : 8 $ CA", "pdp.shippingAndReturns.TwoDaysRate": "Deux jours ouvrables : 17 $ CA", "pdp.shippingAndReturns.OneDayRate": "Un jour ouvrable : 22 $ CA", "pdp.shippingAndReturns.freeReturns": "Retours GRATUITS (en magasin ou par la poste)", "pdp.shippingAndReturns.freeExchanges": "Échanges GRATUITS", "pdp.shippingAndReturns.learnMore": "Pour en savoir plus", "pdp.shippingAndReturns.returnByMail": "Retours GRATUITS par la poste", "pdp.shippingAndReturns.mailReturns": "retour par courrier uniquement", "pdp.shippingAndReturns.mailReturns.on": "retour par la poste seulement", "pdp.shippingAndReturns.freeReturns.gap": "retour gratuit par la poste", "pdp.shippingAndReturns.freeReturns.on": "retour gratuit par courrier", "pdp.shippingAndReturns.noReturns": "Vente finale : aucun échange ou remboursement sur cet article", "core.currency": "{{currencyValue, currency.CAD}}", "price.current_price_range_aria_label": "Promo {{minPrice}} à {{maxPrice}}", "price.now_current_price": "Promo {{price}}", "price.percentage_off": "{{value}} % de rabais", "price.regular_price_aria_label": "Prix courant {{price}}", "price.regular_price_range_aria_label": "Prix courant {{minPrice}} à {{maxPrice}}", "pdp.price.currentPriceRangeAriaLabel": "Promo {{minPrice}} à {{maxPrice}}", "pdp.price.nowCurrentPrice": "Promo {{price}}", "pdp.price.percentageOff": "{{value}} % de rabais", "pdp.price.percentageOffWithUppercase": "{{value}} % De <PERSON>", "pdp.price.regularPriceAriaLabel": "Prix courant {{price}}", "pdp.price.regularPriceRangeAriaLabel": "Prix courant {{minPrice}} à {{maxPrice}}", "changeStoreModal.storeListLabel": "SÉLECTIONNEZ UN MAGASIN POUR LE RAMASSAGE", "changeStoreModal.postCodeValidationError": "Veuillez entrer un code postal valide.", "changeStoreModal.noStoreMatch": "Nous n'avons trouvé aucun magasin correspondant à vos critères de recherche. Veuillez essayer de nouveau.", "changeStoreModal.pickUpUnavailable": "La cueillette en magasin n'est pas disponible. Veuillez réessayer plus tard.", "changeStoreModal.selectStoreCheckAvailability": "Sélectionner un magasin pour vérifier la disponibilité du produit pour cueillette en magasin.", "changeStoreModal.bopisAndCurbside.selectStoreCheckAvailability": "Sélectionner un magasin pour vérifier la disponibilité du produit", "pdp.fulfillmentMethod.lowStockForShipment": "Il n'en reste que quelques-uns!", "pdp.fulfillmentMethod.lowStockForShipmentNoExclamationPoint": "Il n'en reste que quelques-uns", "pdp.fulfillmentMethod.lowStockForPickup": "Faibles stocks", "pdp.fulfillmentMethod.lowStockTooltipForPickup": "Vous recevrez un courriel si votre commande est annulée en raison d’un manque de stocks. Les articles non expédiés ne vous seront pas facturés, et tout prélèvement temportaire sur votre compte sera annulé et disparaîtra de vos relevés dans un délai de 2 à 10 jours ouvrables.", "pdp.fulfillmentMethod.lowStockOpenTooltipAriaLabel": "ouvre un dialogue comportant de l'information sur l'état des stocks faibles", "pdp.fulfillmentMethod.lowStockCloseTooltipAriaLabel": "ferme le dialogue comportant de l'information sur les stocks faibles", "pdp.fulfillmentMethod.rewardsSignIn": "Ouvrir une session", "pdp.fulfillmentMethod.rewardsLinkJoin": "Inscrivez-vous", "pdp.conjunction.or": "ou", "pdp.fulfillmentMethod.or": "ou", "pdp.freeShipping.title": "Expédition rapide gratuite", "pdp.ship.title": "Expédition", "pdp.freeOrders.minPrice": "(Gratuit tous les jours 50 $ CA et plus)", "pdp.fulfillmentMethod.freeOnRewardsMembers": "Gratuit sur 50 $ CA et plus pour les membres Rewards", "pdp.fulfillmentMethod.rewardsMembers": "Pour les membres du programme de récompenses $50+", "pdp.fulfillmentMethod.freeForRewardsMembers": "GRATUITE pour les membres du programme de récompenses (minimum 50 $)", "pdp.fulfillmentMethod.canadaMarket.freeShippingForRewardsMembers": "Les Membres du Programme de Récompenses peuvent bénéficier de la livraison GRATUITE", "changeStoreModal.curbside.selectStoreCheckAvailability": "Sélectionnez un magasin pour vérifier la disponibilité du produit pour cueillette en bordure de magasin.", "changeStoreModal.curbside.pickUpUnavailable": "La cueillette en bordure de magasin n'est pas disponible. Veuillez réessayer plus tard.", "pdp.fulfilment.needSize": "Sélectionner une taille pour voir les options de cueillette", "pdp.fulfillment.needSizeAlternate": "Sé<PERSON><PERSON>ner une taille pour voir les options de cueillette.", "pdp.fulfilment.outOfStock.warning": "Votre sélection n’est pas en stock à ce magasin. <PERSON><PERSON><PERSON><PERSON> changer de magasin, choisir une autre taille ou couleur ou expédier la sélection à une adresse.", "pdp.fulfillment.outOfStock.bopis.warning": "Cette sélection n’est pas en stock à ce magasin. <PERSON><PERSON><PERSON><PERSON> changer de magasin, choisir une autre taille ou couleur ou expédier la sélection à une adresse.", "pdp.fulfillment.outOfStock.shipping.warning": "Cette sélection est en rupture de stock. Essayez une autre couleur ou taille.", "changeStoreModal.store.instore": "Cueillette en magasin seulement", "changeStoreModal.store.curbside": "Cueillette en bordure de magasin seulement", "changeStoreModal.store.instoreAndCurbside": "Cueillette en bordure de magasin et cueillette en magasin", "quickAdd.colorLabel": "<PERSON><PERSON><PERSON>", "quickAdd.variantGroupLabel": "<PERSON><PERSON><PERSON>", "quickAdd.selectASize": "Sé<PERSON><PERSON><PERSON> une taille", "quickAdd.openModal": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "quickAdd.closeModal": "<PERSON><PERSON><PERSON> rapide", "quickAdd.addToBag": "A<PERSON>ter au panier", "quickAdd.addToBagSuccess": "Ajouté au panier", "quickAdd.addToBagFailure": "N’a pas été ajouté", "quickAdd.error.general": "Nous éprouvons un problème technique sur cette page. Veuillez actualiser la page et réessayer votre sélection ou trouver un autre produit.", "quickAdd.error.sizeSelection": "Veuillez faire une sélection de {{unselectedDimensions}} avant d'ajouter au panier.", "quickAdd.error.itemOutOfStock": "Nous sommes désolés, cet article est maintenant en rupture de stock. Veuillez essayer un autre modèle.", "quickAdd.onBackOrderMessage": "En rupture de stock - date d'expédition prévue {{date}}", "quickAdd.onBackOrderMessage.ariaLabel": "En rupture de stock. Date d'expédition prévue : {{date}}", "tag_link_group.see_more_related_categories_aria_label": "Voir Plus (catégories associées)", "tag_link_group.see_less_related_categories_aria_label": "Voir Moins (catégories associées)", "tag_link_group.see_more_related_categories_text": "Voir Plus", "tag_link_group.see_less_related_categories_text": "Voir Moins", "tag_link_group.related_categories_heading": "Catégories associées", "tag_link_group.related_items_heading": "Articles connexes", "fui.ReviewRatings.pluralLabel": "{{reviewCount}} <PERSON><PERSON>", "fui.ReviewRatings.singularLabel": "{{reviewCount}} <PERSON><PERSON>", "pdp.ratings.title.textPlural": "{{ratingCount}} Notes", "pdp.ratings.title.textSingular": "{{ratingCount}} Note", "pdp.ratings.title.textPlural.informationLinks": "Notes", "pdp.ratings.title.textSingular.informationLinks": "Note", "fui.ReviewRatings.new-ariaLabel": "Image de 5 étoiles, {{starRating}} sont colorées, {{ratingCount}} Avis ", "fui.ReviewRatings.ariaLabel": "Image de 5 étoiles, {{starRating}} sont colorées", "fui.ReviewRatings.ariaLabelNoRatings": "Image de 5 étoiles, 0 sont colorées, 0 Notes", "fui.ReviewRatings.ariaLabelNoReviews": "Image de 5 étoiles, 0 sont colorées, 0 Avis", "pdp.afterpay.paypalText": "PayPal", "pdp.afterpay.afterpayText": "Afterpay", "pdp.afterpay.paypalPayInText": "PayPal Pay en 4", "pdp.afterpay.afterpayEligibleContent": "4 versements sans intérêts de {{installments}} avec", "pdp.afterpay.giftCardAfterpayEligibleContent": "4 versements sans intérêts avec", "pdp.afterpay.afterpayNotEligibleContent": "disponible pour les commandes de {{minPrice}} à {{maxPrice}}", "pdp.afterpay.afterpayNotEligibleAboveMinThresholdContent": "disponible pour les commandes de plus de {{minPrice}}", "pdp.afterpay.afterpayNotEligibleBelowMaxThresholdContent": "disponible pour les commandes de moins de {{maxPrice}}", "pdp.Reviews.list.filterBy": "Filtrer les commentaires par", "pdp.Reviews.list.filterBySentenceCase": "Filtrer les commentaires par", "pdp.Reviews.list.sortBy": "Trier par", "pdp.Reviews.list.sortBySentenceCase": "Trier par", "pdp.Reviews.list.paginationPrevious": "Précédent", "pdp.Reviews.list.paginationNext": "Suivant", "pdp.photo.playVideoAriaLabel": "Jouer la vidéo de {{productTitle}}", "pdp.photo.pauseVideoAriaLabel": "Interrompre la vidéo de {{productTitle}}", "fui.color_swatch.out_of_stock": "en rupture de stock", "pdp.changeStoreModal.title": "<PERSON>r <PERSON> ma<PERSON>in", "pdp.changeStoreModal.oneStoreFound": "1 magasin trouvé", "pdp.changeStoreModal.storesFound": "{{quantity}} magasins trouvé", "pdp.changeStoreModal.inStockFilter": "Afficher uniquement les magasins en stock", "pdp.changeStoreModal.inStockFilter.on": "ACTIVÉ", "pdp.changeStoreModal.inStockFilter.off": "DÉSACTIVÉ", "pdp.changeStoreModal.itemNotFoundMessage": "Nous sommes désolés, mais cet article n’est pas en stock dans ce secteur. Essayez avec une autre taille, couleur ou un autre code postal.", "pdp.changeStoreModal.changeStoreUnavailable": "La fonction changement de magasin n'est pas disponible. Veuillez réessayer plus tard.", "changeStoreModal.orderPickupTime": "Passez une commande avant 14 h pour la ramasser aujourd’hui", "pdp.changeStoreModal.zipCodeNotFound": "Code postal introuvable. Essayez de nouveau.", "pdp.changeStoreModal.closeModal": "<PERSON><PERSON><PERSON>in", "pdp.changeStoreModal.doneButton": "<PERSON><PERSON><PERSON><PERSON>", "pdp.changeStoreModal.toggleButtonStoreDetails": "<PERSON><PERSON><PERSON> du magasin", "pdp.changeStoreModal.bothAvailable": "La cueillette en bordure de magasin et la cueillette en magasin sont disponibles", "pdp.changeStoreModal.curbsideAvailable": "La cueillette en bordure de magasin uniquement", "pdp.changeStoreModal.inStoreAvailable": "La cueillette en magasin uniquement", "pdp.changeStoreModal.storeDistance": "{{storeDistance}} km", "pdp.changeStoreModal.findAStore": "Trouver un magasin", "pdp.changeStoreModal.selectedStore": "{{storeName}} sélectionné", "pdp.changeStoreModal.noStoreFound": "Nous sommes désolés, mais cet article n’est pas en stock dans ce secteur. Essayez avec une autre taille, couleur ou un autre code postal.", "pdp.changeStoreModal.outOfStock": "En rupture de stock", "pdp.changeStoreModal.lowStock": "Faibles stocks", "pdp.changeStoreModal.inStock": "En stock", "pdp.changeStoreModal.showLabel": "<PERSON><PERSON><PERSON><PERSON>", "pdp.changeStoreModal.hideLabel": "Masquer", "product_breadcrumbs.product_breadcrumbs_aria_label": "traces numériques", "pdp.livechat.buttonText": "Clavarder avec un expert", "changeStoreModal.pickupFilter.allTypes": "Tous les services de cueillette", "changeStoreModal.pickupFilter.cuberside": "Cueillette en bordure de magasin", "changeStoreModal.pickupFilter.inStore": "Cueillette en magasin", "changeStoreModal.title": "<PERSON>r <PERSON> ma<PERSON>in", "changeStoreModal.oneStoreFound": "1 magasin trouvé", "changeStoreModal.storesFound": "{{quantity}} magasins trouvé", "changeStoreModal.inStockFilter.availabitiy": "Afficher uniquement les magasins ayant des disponibilités", "changeStoreModal.inStockFilter": "Afficher uniquement les magasins en stock", "changeStoreModal.inStockFilter.on": "ACTIVÉ", "changeStoreModal.inStockFilter.off": "DÉSACTIVÉ", "changeStoreModal.itemNotFoundMessage": "Nous sommes désolés, mais cet article n’est pas en stock dans ce secteur. Essayez avec une autre taille, couleur ou un autre code postal.", "changeStoreModal.changeStoreUnavailable": "La fonction changement de magasin n'est pas disponible. Veuillez réessayer plus tard.", "changeStoreModal.zipCodeNotFound": "Code postal introuvable. Essayez de nouveau.", "changeStoreModal.closeModal": "<PERSON><PERSON><PERSON>in", "changeStoreModal.doneButton": "<PERSON><PERSON><PERSON><PERSON>", "changeStoreModal.toggleButtonStoreDetails": "<PERSON><PERSON><PERSON> du magasin", "changeStoreModal.bothAvailable": "La cueillette en bordure de magasin et la cueillette en magasin sont disponibles", "changeStoreModal.curbsideAvailable": "La cueillette en bordure de magasin uniquement", "changeStoreModal.inStoreAvailable": "La cueillette en magasin uniquement", "changeStoreModal.storeDistance": "{{storeDistance}} km", "changeStoreModal.findAStore": "Trouver un magasin", "changeStoreModal.selectedStore": "{{storeName}} sélectionné", "changeStoreModal.noStoreFound": "Nous sommes désolés, mais cet article n’est pas en stock dans ce secteur. Essayez avec une autre taille, couleur ou un autre code postal.", "changeStoreModal.bopisOutOfStock": "En rupture de stock dans le magasin sélectionné", "changeStoreModal.outOfStock": "En rupture de stock", "changeStoreModal.lowStock": "Faibles stocks", "changeStoreModal.inStock": "En stock", "changeStoreModal.openModalButton": "<PERSON>r <PERSON> ma<PERSON>in", "changeStoreModal.selectStore": "Sélectionner un magasin", "pdp.socialgallery.heading": "Chacun le porte à sa façon", "pdp.socialgallery.new.heading": "CHACUNE LE PORTE À SA FAÇON", "pdp.dimensionPicker.outOfStock": "{{dimension}} en rupture de stock", "changeStoreModal.lowStockTooltipForPickup": "Vous recevrez un courriel si votre commande est annulée en raison d’un manque de stocks. Les articles non expédiés ne vous seront pas facturés, et tout prélèvement temportaire sur votre compte sera annulé et disparaîtra de vos relevés dans un délai de 2 à 10 jours ouvrables.", "changeStoreModal.lowStockOpenTooltipAriaLabel": "ouvre un dialogue comportant de l'information sur l'état des stocks faibles", "changeStoreModal.lowStockCloseTooltipAriaLabel": "ferme le dialogue comportant de l'information sur les stocks faibles", "pdp.dimensionPicker.sizeSampling.tooltipMessage.reviewsLink": "avis des clients", "pdp.dimensionPicker.sizeSampling.tooltipMessage.checkOut": "Vérifier", "pdp.dimensionPicker.sizeSampling.tooltipMessage.tool": "outil", "pdp.dimensionPicker.sizeSampling.tooltipMessage.below": "ci-dessous", "pdp.dimensionPicker.sizeSampling.tooltipMessage.findBestFit": "pour découvrir votre coupe préférée", "pdp.dimensionPicker.sizeSampling.closeTooltip": "<PERSON><PERSON><PERSON>", "pdp.dimensionPicker.sizeSampling.tooltipMessage.usePrefix": "<PERSON><PERSON><PERSON><PERSON> le", "pdp.dimensionPicker.sizeSampling.tooltipMessage.findMySize": "Trouver ma taille", "pdp.dimensionPicker.sizeSampling.tooltipMessage.or": "ou", "changeStoreModal.error.zipCode": "Veuillez entrer un code postal valide.", "changeStoreModal.label.zipCode": "Code postal", "changeStoreModal.postalCodeInput.ariaLabel": "Vérifier la disponibilité en magasin avec le code postal. Les magasins s'afficheront lorsqu'un code postal valide sera saisi.", "pdp.drapr.title": "TROUVER MA COUPE", "pdp.drapr.title.redesign": "TROUVER MA COUPE", "pdp.drapr.title.redesign.3D": "TROUVER MA COUPE", "pdp.drapr.title.3D": "TROUVER MA COUPE", "pdp.drapr.title.redesign.FitOnly": "Trouver ma coupe", "pdp.drapr.title.FitOnly": "Trouver ma coupe", "pdp.drapr.recommendedSize": "<PERSON><PERSON>", "pdp.drapr.fitOnly.title.unsureAboutYourSize": "VOUS HÉSITEZ ENTRE 2 TAILLES?", "pdp.drapr.fitOnly.title.findYourFit": "Trouver votre coupe", "pdp.drapr.fitOnly.description.findYourFit": "<PERSON><PERSON><PERSON> rapidement un profil pour déterminer quelle taille vous convient le mieux.", "pdp.conjunction.and": " et ", "pdp.productInfoCard.showMore": "Voir Plus", "pdp.productInfoCard.showLess": "Voir Moins", "pdp.availabilityPickupTitle.getItTodayText": "Passez une commande avant 14 h pour la ramasser aujourd’hui", "pdp.lpo.fullDetailsLink": "Aff<PERSON>r les détails", "productPhotoSlider.message.soldout": "Article Épuisé", "pdp.addToBag.soldOut": "Article Épuisé", "pdp.Afterpay.learnMore": "En savoir plus sur Afterpay", "pdp.Paypal.learnMore": "En savoir plus sur PayPal", "pdp.hybridTemplate.recommendationHeading": "Tu Aimerais Peut-Être Aussi", "pdp.reviewSummary.info": "Ce que la clientèle en pense", "pdp.reviewSummary.title": "Récapitulatif des avis", "pdp.reviewSummary.disclaimer": "Ce récapitulatif a été produit par un outil d’intelligence artificielle générative.", "pdp.superPdp.at.info": "Les détails du tissu peuvent varier selon la couleur", "pdp.superPdp.info": "Les détails sur le produit peuvent varier selon la couleur", "sortBy.desktop.placeholder": "trier par", "sortBy.desktop.price.options.featured": "en vedette", "sortBy.desktop.price.options.asc": "prix croissant", "sortBy.desktop.price.options.desc": "prix d<PERSON><PERSON><PERSON>san<PERSON>", "sortBy.desktop.price.options.featured.caps": "<PERSON>", "sortBy.desktop.price.options.asc.caps": "Prix Croissant", "sortBy.desktop.price.options.desc.caps": "Prix Décroissant", "sortBy.desktop.reviewScore.options.desc": "Les Plus Appréciés", "sortBy.desktop.new.options.desc": "NOUVEAUTÉ", "sortBy.mobile.placeholder": "trier par", "sortBy.mobile.placeholder.caps": "<PERSON><PERSON>", "sortBy.mobile.price.options.featured": "en vedette", "sortBy.mobile.price.options.asc": "prix croissant", "sortBy.mobile.price.options.desc": "prix d<PERSON><PERSON><PERSON>san<PERSON>", "sortBy.mobile.price.options.featured.caps": "<PERSON>", "sortBy.mobile.price.options.asc.caps": "Prix Croissant", "sortBy.mobile.price.options.desc.caps": "Prix Décroissant", "sortBy.mobile.reviewScore.options.desc": "Les Plus Appréciés", "sortBy.mobile.new.options.desc": "NOUVEAUTÉ", "sortby.desktop.placeholder": "trier par", "sortby.desktop.options.featured": "en vedette", "sortby.desktop.options.low": "prix croissant", "sortby.desktop.options.high": "prix d<PERSON><PERSON><PERSON>san<PERSON>", "sortby.mobile.options.featured": "en vedette", "sortby.mobile.options.low": "prix croissant", "sortby.mobile.options.high": "prix d<PERSON><PERSON><PERSON>san<PERSON>", "dashboard.filter": "FILTRER", "dashboard.filter_plural": "FILTRES", "mobile_facets_wrapper.clear_all_text": "supprimer", "mobile_facets_wrapper.done_text": "<PERSON><PERSON><PERSON><PERSON>", "facet_bar.facet_tag_aria_label": "<PERSON><PERSON><PERSON>", "facet_bar.accordion_header_aria_label_open": "{{facetLabel}} options de filtres, cliquer pour ouvrir l'accordéon", "facet_bar.accordion_header_aria_label_close": "{{facetLabel}} options de filtres, cliquer pour fermer l'accordéon", "facet_bar.clear_all_button": "supprimer", "facet_bar.done_button": "terminé", "facet_bar.size_facet_one_size_variant": "taille unique", "facet_bar.modal_aria_label_close": "cliquer pour fermer la fenêtre de dialogue du filtre", "facet_bar.product_filters_aria_label": "Filtres de produits", "facet_reference.facet_label_department": "rayon", "facet_reference.facet_label_style": "<PERSON><PERSON><PERSON><PERSON>", "facet_reference.facet_label_color": "couleur", "facet_reference.facet_label_price": "prix", "facet_reference.facet_label_size": "taille", "facet_reference.facet_label_rating": "évaluation", "left_hand_facet_size.size_facet_regular_variant": "Standard", "left_hand_facet_size.size_facet_one_size": "<PERSON><PERSON> unique", "left_hand_facet_size.size_facet_title": "<PERSON><PERSON><PERSON><PERSON>", "left_hand_facet_rating.rating_facet_all_reviewed": "Tous les produits évalués", "left_hand_facet_rating.rating_facet_option": "{{starNumber}} éto<PERSON> sur 5", "left_rail.item_count_text": "{{count}} article", "left_rail.item_count_text_plural": "{{count}} articles", "left_rail.item_count_aria_label_plural": "{{count}} articles dans le tableau des produits", "left_rail.item_count_aria_label": "{{count}} article dans le tableau des produits", "left_rail.clear_all_button": "Effacer les filtres", "left_rail_lhf.filter_button": "FILTRE {{selectedFacetsCountString}}", "inline-facet-tags.tag-close-button-aria-label": "Supp<PERSON>er le filtre", "inline_facet_tags.in_stock_facet_label": "en stock", "inline_facet_tags.reviews_facet_label": "analyses", "inline_facet_tags.clear_filters_button": "Supprimer les filtres", "addToBag.outOfStock": "Veuillez nous excuser, l`article que vous désirez ajouter à votre panier n`est plus disponible dans la taille et la couleur demandées. Veuillez faire un autre choix.", "addToBag.outOfStockAlternate": "Veuillez nous excuser, l`article que vous désirez ajouter à votre panier n`est plus disponible dans la taille et la couleur demandées. Veuillez faire un autre choix.", "addToBag.genericError": "Veuillez nous excuser, une erreur s`est produite lors de l`ajout de cet article à votre panier.", "addToBag.genericErrorAlternate": "Veuillez nous excuser, une erreur s`est produite lors de l`ajout de cet article à votre panier.", "addToBag.maxLineItemLimitReached": "Vous avez ajouté le numéro maximum d`articles à votre panier. Faites de l`espace dans votre panier en enlevant les articles non désirés ou passez votre commande dès maintenant.", "addToBag.maxQuantityReached": "Nous sommes d<PERSON>, mais {{maxQuantityAllowed}} {{name}} uniquement peuvent être achetés par commande.", "addToBag.partialSuccess": "Seulement {{quantityAdded}} article est en stock et a été ajouté à votre panier.", "pdp.giftCardAmountLabel": "<PERSON><PERSON>", "pdp.imageGallery.clickToShowPreviousImage": "Cliquer pour afficher la photo précédente", "pdp.imageGallery.clickToShowNextImage": "Cliquer pour afficher la photo suivante", "pdp.imageGallery.clickToCloseModal": "Cliquer pour fermer la fenêtre", "pdp.imageGallery.zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "pdp.imageGallery.zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "pdp.imageGallery.zoomInIndex": "<PERSON><PERSON><PERSON><PERSON> photo {{index}}", "pdp.imageGallery.zoomOutIndex": "<PERSON><PERSON><PERSON><PERSON> photo {{index}}", "pdp.imageGallery.productImageIndex": "Photo {{index}} du produit"}