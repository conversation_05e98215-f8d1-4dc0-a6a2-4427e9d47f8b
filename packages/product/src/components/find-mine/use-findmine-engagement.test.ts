import { renderHook } from '@testing-library/react-hooks';

import { useFindMineEngagement } from './use-findmine-engagement';

describe('use-find-mine-engagement', () => {
  test('should set the new pid value to key in localStorage', () => {
    const { result } = renderHook(() => useFindMineEngagement());
    result.current.setFindMineEngagement('123');
    expect(localStorage.getItem('findMineEngagement-123')).toBeDefined();
  });

  test('should get the correct key that is in localStorage', () => {
    const { result } = renderHook(() => useFindMineEngagement());
    result.current.setFindMineEngagement('123');
    result.current.getFindMineEngagement('123');
    expect(localStorage.getItem('findMineEngagement-123')).toBeDefined();
  });
});
