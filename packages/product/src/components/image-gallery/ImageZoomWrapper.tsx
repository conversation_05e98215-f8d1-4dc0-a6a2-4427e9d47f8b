import PinchZoom from 'pinch-zoom-js';
import { useEffect, useRef } from 'react';
import { AdaptedImage } from '../../pages/helpers/mediaSequencer';
import { Image } from './Image';

type ImageZoomWrapperProps = {
  altText: string;
  isLoadOnPriority: boolean;
  item: AdaptedImage;
};
const ImageZoomWrapper = (props: ImageZoomWrapperProps) => {
  const { altText, item, isLoadOnPriority } = props;
  const containerRef = useRef<HTMLDivElement | null>(null);
  const imgSrc = item?.['ZOOM'] || item?.['VIEW_LARGE_IMAGE'] || item?.['PRIMARY'] || '';

  useEffect(() => {
    let pinchZoomInstance: PinchZoom | null = null;
    if (containerRef.current) {
      pinchZoomInstance = new PinchZoom(containerRef.current, {
        draggableUnzoomed: false,
        maxZoom: 2.5,
        minZoom: 0.99,
        tapZoomFactor: 2.5,
        setOffsetsOnce: true,
      });
    }
    return () => {
      if (pinchZoomInstance) {
        pinchZoomInstance.destroy();
      }
    };
  }, [imgSrc]);

  return (
    <div ref={containerRef} style={{ width: '100%', height: '100%', overflow: 'hidden' }}>
      <Image alt={altText} fill image={item} priority={isLoadOnPriority} src={imgSrc} />
    </div>
  );
};
export default ImageZoomWrapper;
