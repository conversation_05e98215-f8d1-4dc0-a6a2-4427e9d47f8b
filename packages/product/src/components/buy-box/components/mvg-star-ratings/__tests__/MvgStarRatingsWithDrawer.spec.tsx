import { render } from '@testing-library/react';
import { MvgStarRatingsWithDrawerClient } from '../MvgStarRatingsWithDrawerClient';
import { MVGReviewsDrawerProvider } from '../../../../reviews/ReviewsDrawer/reviews-drawer-provider/MVGReviewsDrawerProvider';
import { MVGReviewsDrawer } from '../../../../reviews/ReviewsDrawer/MVGReviewDrawer';
import { MvgStarRatingsWrapper } from '../MvgStartRatingsWrapper';
import { MVGBuyBoxStoreProvider } from '../../../../../providers/mvg-buybox-provider';
import { selectedMultiVariantData } from '../../../../../providers/mvg-buybox-provider/__tests__/mock';

jest.mock('../../../../reviews/ReviewsDrawer/reviews-drawer-provider/MVGReviewsDrawerProvider', () => ({
  MVGReviewsDrawerProvider: jest.fn(({ children }) => children),
}));

jest.mock('../MvgStartRatingsWrapper', () => ({
  MvgStarRatingsWrapper: jest.fn(),
}));

jest.mock('../../../../reviews/ReviewsDrawer/MVGReviewDrawer', () => ({
  MVGReviewsDrawer: jest.fn(),
}));

const mockCapiData = {
  selectedCustomerChoice: {},
  selectedVariant: {
    rating: {
      average_rating: 4.5,
      rating_count: 150,
      review_count: 120,
    },
  },
  variants: {
    'REGULAR|FULL|POCKET': selectedMultiVariantData,
  },
  selectedSku: null,
  customer_choices: {},
  variant_definition: { values: [] },
  selectedStyle: {
    rating: {
      average_rating: 4.5,
      rating_count: 150,
      review_count: 120,
    },
  },
  selectedMultiVariantKey: 'REGULAR|FULL|POCKET',
  selectedMultiVariantData: selectedMultiVariantData,
  styles: {},
};

describe('MvgStarRatingsWithDrawerClient', () => {
  it('should render children inside ReviewsDrawer', () => {
    render(
      <MVGBuyBoxStoreProvider data={mockCapiData}>
        <MvgStarRatingsWithDrawerClient
          brandName={'gap'}
          reviewRatings={{
            average_rating: 100,
            ratingHistogram: [],
            rating_count: 100,
            reviewHistogram: [],
            review_count: 100,
          }}
        >
          <div data-testid='test-children'>Test Children</div>
        </MvgStarRatingsWithDrawerClient>
      </MVGBuyBoxStoreProvider>
    );

    expect(MVGReviewsDrawerProvider).toHaveBeenCalled();
    expect(MVGReviewsDrawer).toHaveBeenCalled();
    expect(MvgStarRatingsWrapper).toHaveBeenCalled();
  });

  it('should use default medium size when ratingSize is not provided', () => {
    render(
      <MVGBuyBoxStoreProvider data={mockCapiData}>
        <MvgStarRatingsWithDrawerClient
          brandName={'gap'}
          reviewRatings={{
            average_rating: 100,
            ratingHistogram: [],
            rating_count: 100,
            reviewHistogram: [],
            review_count: 100,
          }}
        >
          <div data-testid='test-children'>Test Children</div>
        </MvgStarRatingsWithDrawerClient>
      </MVGBuyBoxStoreProvider>
    );

    expect(MvgStarRatingsWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        ratingSize: 'medium',
      }),
      expect.anything()
    );
  });
});
