import { render } from '@testing-library/react';
import { MvgStarRatingsWithDrawerClient } from '../MvgStarRatingsWithDrawerClient';
import { MVGReviewsDrawerProvider } from '../../../../reviews/ReviewsDrawer/reviews-drawer-provider/MVGReviewsDrawerProvider';
import { MvgStarRatingsWrapper } from '../MvgStartRatingsWrapper';
import { MVGBuyBoxStoreProvider } from '../../../../../providers/mvg-buybox-provider';
import { atMock } from '../../../../../pages/services/capi-aggregation-service/v3/__fixtures__/atMock';

jest.mock('../../../../reviews/ReviewsDrawer/reviews-drawer-provider/MVGReviewsDrawerProvider', () => ({
  MVGReviewsDrawerProvider: jest.fn(({ children }) => children),
}));

jest.mock('../MvgStartRatingsWrapper', () => ({
  MvgStarRatingsWrapper: jest.fn(),
}));

jest.mock('../../../../reviews/ReviewsDrawer/MVGReviewDrawer', () => ({
  MVGReviewsDrawer: jest.fn(),
}));

const mockReviewRatings = {
  averageRating: 4.5,
  totalReviews: 100,
  ratingHistogram: [
    { rating: 5, count: 50 },
    { rating: 4, count: 30 },
    { rating: 3, count: 10 },
    { rating: 2, count: 5 },
    { rating: 1, count: 5 },
  ],
};

describe('MvgStarRatingsWithDrawerClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with ReviewsDrawerProvider and correct props', () => {
    const props = {
      brandName: 'Test Brand',
      reviewRatings: mockReviewRatings,
      ratingSize: 'medium',
      children: <div>Test Children</div>,
    };
    const selectedStyle = { rating: { average_rating: 4.5, rating_count: 100, review_count: 50 } };

    render(
      <MVGBuyBoxStoreProvider
        data={{
          ...atMock,
          selectedStyle: selectedStyle,
        }}
      >
        <MvgStarRatingsWithDrawerClient brandName={props.brandName} reviewRatings={selectedStyle.rating}>
          {props.children}
        </MvgStarRatingsWithDrawerClient>
      </MVGBuyBoxStoreProvider>
    );

    expect(MVGReviewsDrawerProvider).toHaveBeenCalled();
    expect(MvgStarRatingsWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        brandName: props.brandName,
        reviewRatings: selectedStyle.rating,
      }),
      expect.any(Object)
    );
  });

  it('should use default medium size when ratingSize is not provided', () => {
    const props = {
      brandName: 'Test Brand',
      reviewRatings: mockReviewRatings,
      children: <div>Test Children</div>,
    };

    const selectedStyle = { rating: { average_rating: 4.5, rating_count: 100, review_count: 50 } };

    render(
      <MVGBuyBoxStoreProvider
        data={{
          ...atMock,
          selectedStyle: selectedStyle,
        }}
      >
        <MvgStarRatingsWithDrawerClient brandName={props.brandName} reviewRatings={selectedStyle.rating}>
          {props.children}
        </MvgStarRatingsWithDrawerClient>
      </MVGBuyBoxStoreProvider>
    );

    expect(MvgStarRatingsWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        brandName: props.brandName,
        reviewRatings: selectedStyle.rating,
      }),
      expect.any(Object)
    );
  });
});
