import { render, screen, waitFor } from '@testing-library/react';
import { cacheableReviewRatingsPromise } from '../../../../../pages/getReWrittenData';
import { MvgStarRatingsClient } from '../MvgStarRatingsClient';
import { MvgStarRatings } from '../MvgStarRatings';

jest.mock('../../../../../pages/getReWrittenData', () => ({
  cacheableReviewRatingsPromise: jest.fn(),
}));

jest.mock('../MvgStarRatingsClient', () => ({
  MvgStarRatingsClient: jest.fn(() => <div data-testid='mvg-star-ratings-client' />),
}));

describe('MvgStarRatings', () => {
  const mockProps = {
    brandName: 'Test Brand',
    requestParamString: 'test-params',
    ratingSize: 'medium',
  };

  const mockReviewRatings = {
    averageRating: 4.5,
    totalReviews: 100,
    ratingCounts: { 5: 50, 4: 30, 3: 10, 2: 5, 1: 5 },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (cacheableReviewRatingsPromise as jest.Mock).mockResolvedValue({
      reviewRatings: mockReviewRatings,
    });
  });

  it('should fetch and pass data to MvgStarRatingsClient', async () => {
    render(await MvgStarRatings(mockProps));

    expect(cacheableReviewRatingsPromise).toHaveBeenCalledWith(mockProps.requestParamString);

    await waitFor(() => {
      expect(MvgStarRatingsClient).toHaveBeenCalledWith(
        expect.objectContaining({
          brandName: mockProps.brandName,
          reviewRatings: mockReviewRatings,
          ratingSize: mockProps.ratingSize,
        }),
        expect.anything()
      );
    });
  });

  it('should render MvgStarRatingsClient', async () => {
    render(await MvgStarRatings(mockProps));
    expect(screen.getByTestId('mvg-star-ratings-client')).toBeInTheDocument();
  });

  it('should handle default ratingSize', async () => {
    const propsWithoutSize = {
      brandName: 'Test Brand',
      requestParamString: 'test-params',
    };

    render(await MvgStarRatings(propsWithoutSize));

    await waitFor(() => {
      expect(MvgStarRatingsClient).toHaveBeenCalledWith(
        expect.objectContaining({
          ratingSize: 'medium',
        }),
        expect.anything()
      );
    });
  });
});
