import { render } from '@testing-library/react';
import { MvgStarRatingsClient } from '../MvgStarRatingsClient';
import { MvgStarRatingsWrapper } from '../MvgStartRatingsWrapper';

jest.mock('../MvgStartRatingsWrapper', () => ({
  MvgStarRatingsWrapper: jest.fn(() => null),
}));

const mockReviewRatings = {
  average_rating: 4.5,
  rating_count: 100,
  review_count: 100,
  ratingHistogram: [
    { rating: 5, count: 50 },
    { rating: 4, count: 30 },
    { rating: 3, count: 10 },
    { rating: 2, count: 5 },
    { rating: 1, count: 5 },
  ],
};

describe('MvgStarRatingsClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render MvgStarRatingsWrapper with correct props', () => {
    const props = {
      brandName: 'Test Brand',
      reviewRatings: mockReviewRatings,
      ratingSize: 'medium',
    };

    render(<MvgStarRatingsClient {...props} />);

    expect(MvgStarRatingsWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        brandName: props.brandName,
        reviewRatings: props.reviewRatings,
        ratingSize: props.ratingSize,
      }),
      expect.any(Object)
    );
  });

  it('should use default medium size when ratingSize is not provided', () => {
    const props = {
      brandName: 'Test Brand',
      reviewRatings: mockReviewRatings,
    };

    render(<MvgStarRatingsClient {...props} />);

    expect(MvgStarRatingsWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        brandName: props.brandName,
        reviewRatings: props.reviewRatings,
        ratingSize: 'medium',
      }),
      expect.any(Object)
    );
  });
});
