'use client';

import { ReviewHistogram } from '../../../../pages/services/reviews';
import { MvgStarRatingsWrapper } from './MvgStartRatingsWrapper';

type StarRatingProps = {
  brandName: string;
  ratingSize?: 'small' | 'medium' | 'large';
  reviewRatings: ReviewHistogram;
};

export const MvgStarRatingsClient = ({ brandName, ratingSize = 'medium', reviewRatings }: StarRatingProps) => {
  return <MvgStarRatingsWrapper brandName={brandName} reviewRatings={reviewRatings} ratingSize={ratingSize} />;
};
