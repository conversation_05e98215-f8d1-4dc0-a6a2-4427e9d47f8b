import { cacheableReviewRatingsPromise } from '../../../../pages/getReWrittenData';
import { MvgStarRatingsClient } from './MvgStarRatingsClient';

export type MvgStarRatingProps = {
  brandName: string;
  ratingSize?: 'small' | 'medium' | 'large';
  requestParamString: string;
};

export const MvgStarRatings = async ({ requestParamString, brandName, ratingSize = 'medium' }: MvgStarRatingProps) => {
  const { reviewRatings } = await cacheableReviewRatingsPromise(requestParamString);
  return <MvgStarRatingsClient brandName={brandName} reviewRatings={reviewRatings} ratingSize={ratingSize}></MvgStarRatingsClient>;
};
