'use client';

import { useShallow } from 'zustand/react/shallow';
import { ScrollReferenceType, StickyContainer } from '@ecom-next/core/fabric/sticky-container';
import { useEffect, useState } from 'react';
import { useMVGBuyBoxStore } from '../../../../providers/mvg-buybox-provider';

type MvgStickyContainerClientProps = {
  children: React.ReactNode;
};

export const MvgStickyContainerClient = ({ children }: MvgStickyContainerClientProps) => {
  const scrollReference: ScrollReferenceType = { type: 'anchor', anchorId: 'pdp-sticky-container-breakpoint' };
  const [shouldShowSticky, setShouldShowSticky] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const { selectedStyle } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedStyle: state?.selectedStyle,
    }))
  );

  useEffect(() => {
    if (isFirstLoad) {
      setIsFirstLoad(false);
      return;
    }

    setShouldShowSticky(true);
  }, [selectedStyle]);

  const handleRemove = () => {
    setShouldShowSticky(false);
  };

  if (isFirstLoad) {
    return <></>;
  }

  return (
    <StickyContainer
      type='click'
      id='click-sticky-container'
      className='pdp-sticky-container'
      isAnimated={true}
      isClickVisible={shouldShowSticky}
      placement='top'
      onRemove={handleRemove}
      scrollReference={scrollReference}
    >
      {children}
    </StickyContainer>
  );
};
