.pdp-sticky-container {
  display: flex;
  flex-flow: column;
  gap: theme('spacing.utk-spacing-s');
  width: calc(100% - (theme('spacing.utk-spacing-l') * 2));

  .pdp-header__title {
    font-size: theme('fontSize.font-size-0');
  }

  .fds_product-image {
    border-radius: theme('borderRadius.style-product-image-thumbnail-border-radius');
    overflow: hidden;
  }

  &--details-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: theme('spacing.utk-spacing-xs');

    .pdp-title-price-wrapper .title-price {
      .markdown-price {
        font-size: theme('fontSize.font-size--1');
      }

      .percentage-off {
        font-size: theme('fontSize.font-size--2');
      }

      .current-sale-price,
      .current-regular-price {
        font-size: theme('fontSize.font-size-0');
      }
    }
  }

  &--details-info {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 145px;
    font-size: theme('fontSize.font-size--1');
  }

  &--image {
    margin-right: theme('spacing.utk-spacing-m');
  }
}

.pdp-sticky-container-breakpoint {
  position: absolute;
  height: 1px;
  width: 100%;
  top: 1rem;
  pointer-events: none;
}
