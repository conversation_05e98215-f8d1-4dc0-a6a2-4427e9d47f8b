'use client';

import { useShallow } from 'zustand/react/shallow';
import { ProductImage } from '@ecom-next/core/components/fabric/product-image';
import { useMVGBuyBoxStore } from '../../../../providers/mvg-buybox-provider';

type MvgStickyContainerDetailsProps = {
  children: React.ReactNode;
};

export const MvgStickyContainerDetails = ({ children }: MvgStickyContainerDetailsProps) => {
  const { selectedCustomerChoice, selectedVariantDimensions } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedCustomerChoice: state?.selectedCustomerChoice,
      selectedVariantDimensions: state?.selectedMultiVariantData?.dimensions,
    }))
  );

  const selectedCustomerChoiceName = selectedCustomerChoice?.description || '';
  const images = selectedCustomerChoice?.images_P01 || {};
  const imagePath = images['QUICK_LOOK_IMAGE'] || images['THUMBNAIL'] || '';

  const selectedDimensions = selectedVariantDimensions.map(dimension => dimension.selectedDimension);

  const selectedDimensionsDetail = [...selectedDimensions].filter(Boolean).join(' ');

  return (
    <>
      <ProductImage
        id='pdp-sticky-container-image'
        className='pdp-sticky-container--image'
        width='40px'
        height='53px'
        imageUrl={imagePath}
        imageAltText={selectedCustomerChoiceName}
      />
      <div className='pdp-sticky-container--details-wrapper'>
        <div className='pdp-sticky-container--details-info'>
          <span>{selectedDimensionsDetail ? `${selectedDimensionsDetail} | ` : ''}</span>
          <span>{selectedCustomerChoiceName}</span>{' '}
        </div>
        {children}
      </div>
    </>
  );
};
