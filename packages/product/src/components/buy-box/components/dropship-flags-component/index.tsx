'use client';

import React, { useMemo } from 'react';
import { useBuyBoxStore } from '../../../../providers/buybox-provider';

const DropshipFlagsContainer = ({ className }: { className: string }): JSX.Element => {
  const { selectedColor } = useBuyBoxStore(state => state);

  const selectedSizeDropshipFlags = useMemo(() => selectedColor?.sizes?.[0]?.dropshipFlags, [selectedColor]);
  return selectedSizeDropshipFlags && (selectedSizeDropshipFlags as Array<string>).length > 0 ? (
    <ul data-testid='dropshipFlagsContainer'>
      {selectedSizeDropshipFlags?.map((dropshipFlag: string) => (
        <li className={className} key={dropshipFlag}>
          {dropshipFlag?.includes('.') ? dropshipFlag : dropshipFlag?.concat('.')}
        </li>
      ))}
    </ul>
  ) : (
    <></>
  );
};

export default DropshipFlagsContainer;
