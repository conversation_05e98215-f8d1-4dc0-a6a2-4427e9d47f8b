'use client';

import React, { useMemo } from 'react';
import { AdaptedVariantCustomerChoice } from '../../../../pages/services/capi-aggregation-service';

type MVGDropshipFlagsContainerProps = { className: string; selectedCustomerChoice: AdaptedVariantCustomerChoice };

export const MvgDropshipFlagsContainer = ({ selectedCustomerChoice, className }: MVGDropshipFlagsContainerProps): JSX.Element => {
  const selectedSizeDropshipFlags = useMemo(() => selectedCustomerChoice?.skus?.[0]?.dropshipFlags, [selectedCustomerChoice]);
  return selectedSizeDropshipFlags && selectedSizeDropshipFlags.length > 0 ? (
    <ul data-testid='dropshipFlagsContainer'>
      {selectedSizeDropshipFlags?.map((dropshipFlag: string) => (
        <li className={className} key={dropshipFlag}>
          {dropshipFlag?.includes('.') ? dropshipFlag : dropshipFlag?.concat('.')}
        </li>
      ))}
    </ul>
  ) : (
    <></>
  );
};
