// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<MvgDropshipFlagsComponent /> when the dropship flag is empty matches the snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`<MvgDropshipFlagsComponent /> when we have 1 dropship flag that does not have a period matches the snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <ul
      data-testid="dropshipFlagsContainer"
    >
      <li
        class="dropship-shipping-and-returns__flags"
      >
        Return by Mail Only.
      </li>
    </ul>
  </div>
</div>
`;

exports[`<MvgDropshipFlagsComponent /> when we have 1 dropship flag that has a period matches the snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <ul
      data-testid="dropshipFlagsContainer"
    >
      <li
        class="dropship-shipping-and-returns__flags"
      >
        Return by Mail Only.
      </li>
    </ul>
  </div>
</div>
`;
