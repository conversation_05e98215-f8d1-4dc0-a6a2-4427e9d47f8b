import { screen, waitFor } from '@testing-library/react';
import { wrapMvgInTestApp } from '../../../../../test-utils/appWrapper';
import { MvgDropshipFlagsContainer } from '../MvgDropshipFlagsComponent';
import { AdaptedVariantCustomerChoice } from '@/src/pages/services/capi-aggregation-service';

const selectedCustomerChoice = {
  customer_choice_id: '722433002',
  alternate_ids: {
    universal_style_number: '000722433',
    legacy_style_number: '722433',
    universal_customer_choice_number: '000722433000',
    online_legacy_customer_choice_number: '722433002',
  },
  description: 'Navy',
  sort_order: 999,
  season_code: 'BAS',
  season_year: '0',
  color_palette_season_code: 'Q4',
  color_palette_season_year: '2022',
  backorderable: false,
  online_exclusive: false,
  bopis_eligible: false,
  vendor_details: {
    vendor_name: 'Delta Children',
    vendor_number: '2065597',
    vendor_customer_choice_number: null,
  },
  po_details: null,
  search_color: {
    id: 'BLUE',
    name: '<PERSON>',
  },
  images: [
    {
      sequence_number: 1,
      legacy_placement: 'P01',
      pristine_url: 'webcontent/0056/318/095/cn56318095.png',
      pristine_media_type: 'PNG',
      crops: [
        {
          id: '56317880',
          type: 'VIEW LARGE IMAGE',
          legacy_type: 'VLI',
          url: 'webcontent/0056/317/880/cn56317880.jpg',
          media_type: 'JPG',
          width: 520,
          height: 693,
        },
        {
          id: '56317906',
          type: 'PRIMARY',
          legacy_type: 'P01',
          url: 'webcontent/0056/317/906/cn56317906.jpg',
          media_type: 'JPG',
          width: 258,
          height: 344,
        },
        {
          id: '56317928',
          type: 'OUTFIT IMAGE 1',
          legacy_type: 'OVI1',
          url: 'webcontent/0056/317/928/cn56317928.jpg',
          media_type: 'JPG',
          width: 58,
          height: 77,
        },
        {
          id: '56317945',
          type: 'OUTFIT IMAGE 2',
          legacy_type: 'OVI2',
          url: 'webcontent/0056/317/945/cn56317945.jpg',
          media_type: 'JPG',
          width: 138,
          height: 184,
        },
        {
          id: '56317962',
          type: 'THUMBNAIL',
          legacy_type: 'T',
          url: 'webcontent/0056/317/962/cn56317962.jpg',
          media_type: 'JPG',
          width: 39,
          height: 52,
        },
        {
          id: '56317986',
          type: 'ZOOM',
          legacy_type: 'Z',
          url: 'webcontent/0056/317/986/cn56317986.jpg',
          media_type: 'JPG',
          width: 1500,
          height: 2000,
        },
        {
          id: '56318012',
          type: 'CROP',
          legacy_type: 'VI',
          url: 'webcontent/0056/318/012/cn56318012.jpg',
          media_type: 'JPG',
          width: -1,
          height: -1,
        },
        {
          id: '56318039',
          type: 'QUICK LOOK IMAGE',
          legacy_type: 'QL',
          url: 'webcontent/0056/318/039/cn56318039.jpg',
          media_type: 'JPG',
          width: 202,
          height: 270,
        },
        {
          id: '56318058',
          type: 'SUGGESTED IMAGE',
          legacy_type: 'SI',
          url: 'webcontent/0056/318/058/cn56318058.jpg',
          media_type: 'JPG',
          width: 120,
          height: 160,
        },
        {
          id: '56318076',
          type: 'CROP',
          legacy_type: 'VI_ONESITE',
          url: 'webcontent/0056/318/076/cn56318076.jpg',
          media_type: 'JPG',
          width: -1,
          height: -1,
        },
        {
          id: '56317872',
          type: 'SWATCH',
          legacy_type: 'S',
          url: 'webcontent/0056/317/872/cn56317872.jpg',
          media_type: 'JPG',
          width: 18,
          height: 18,
        },
      ],
    },
  ],
  videos: [],
  marketing_flags: [],
  badges: [],
  style_id: '722433',
  swatchImage: '/webcontent/0056/317/872/cn56317872.jpg',
  legacyImages: ['722433002_main'],
  universal_customer_choice_id: '000722433000',
  universal_style_id: '000722433',
  inventory_status: 'IN_STOCK',
  price: {
    currency: 'USD',
    max_effective_price: 349.95,
    min_effective_price: 349.95,
    max_regular_price: 349.95,
    min_regular_price: 349.95,
    min_discount_percentage: null,
    max_discount_percentage: null,
    price_types: ['REGULAR'],
    isRegularPrice: true,
    isSalePrice: false,
    isPromoPrice: false,
    localized_max_effective_price: '$349.95',
    localized_min_effective_price: '$349.95',
    localized_max_regular_price: '$349.95',
    localized_min_regular_price: '$349.95',
  },
  final_sale: false,
  return_type_code: 'MAIL_ONLY',
  skus: [
    {
      sku_id: '7224330020000',
      alternate_ids: {
        universal_style_number: '000722433',
        legacy_style_number: '722433',
        universal_customer_choice_number: '000722433000',
        online_legacy_customer_choice_number: '722433002',
        universal_sku_number: '321759865',
        online_legacy_sku_number: '7224330020000',
        online_upc_code: '080213122393',
      },
      description: 'One Size',
      estimated_days_to_ship: null,
      inventory_status: {
        status: 'IN_STOCK',
        estimated_ship_ts: null,
      },
      made_to_order: false,
      price: {
        currency: 'USD',
        effective_price: 349.95,
        price_type: 'REGULAR',
        price_details: {
          regular_price: 349.95,
          markdown_price: null,
          discount_percentage: null,
          discount_saving: null,
          on_clearance: null,
          final_markdown: null,
          offers: null,
        },
        isRegularPrice: true,
        isSalePrice: false,
        isPromoPrice: false,
        localized_effective_price: '$349.95',
        localized_regular_price: '$349.95',
      },
      size_code: '0000',
      size_dimension1: 'One Size',
      size_dimension2: null,
      universal_sku_id: '321759865',
      backOrderDate: '',
      isInstock: true,
      isLowStock: false,
      isBackOrdered: false,
      isOutOfStock: false,
      dropshipFlags: ['Return by Mail Only'],
    },
  ],
  backOrderDate: '',
  isInstock: true,
  isLowStock: false,
  isBackOrdered: false,
  isOutOfStock: false,
} as unknown as AdaptedVariantCustomerChoice;

describe('<MvgDropshipFlagsComponent />', () => {
  describe('when we have 1 dropship flag that does not have a period', () => {
    it('matches the snapshot', async () => {
      const { container } = wrapMvgInTestApp(
        <MvgDropshipFlagsContainer className='dropship-shipping-and-returns__flags' selectedCustomerChoice={selectedCustomerChoice} />
      );
      await waitFor(() => {
        expect(screen.getByTestId('dropshipFlagsContainer')).toBeInTheDocument();
      });
      expect(container).toMatchSnapshot();
    });
  });
  describe('when we have 1 dropship flag that has a period', () => {
    it('matches the snapshot', async () => {
      const flagWithPeriod = { ...selectedCustomerChoice, skus: [{ ...selectedCustomerChoice, dropshipFlags: ['Return by Mail Only.'] }] };
      const { container } = wrapMvgInTestApp(
        <MvgDropshipFlagsContainer className='dropship-shipping-and-returns__flags' selectedCustomerChoice={flagWithPeriod} />
      );
      await waitFor(() => {
        expect(screen.getByTestId('dropshipFlagsContainer')).toBeInTheDocument();
      });
      expect(container).toMatchSnapshot();
    });
  });
  describe('when the dropship flag is empty', () => {
    it('matches the snapshot', async () => {
      const flagWithNoData = { ...selectedCustomerChoice, skus: [{ ...selectedCustomerChoice, dropshipFlags: [] }] };
      const { container } = wrapMvgInTestApp(
        <MvgDropshipFlagsContainer className='dropship-shipping-and-returns__flags' selectedCustomerChoice={flagWithNoData} />
      );
      await waitFor(() => {
        expect(screen.queryByTestId('dropshipFlagsContainer')).toBeNull();
      });
      expect(container).toMatchSnapshot();
    });
  });
});
