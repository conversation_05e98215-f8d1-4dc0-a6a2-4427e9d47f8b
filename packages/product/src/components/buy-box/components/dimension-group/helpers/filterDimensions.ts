import { ProductDimension, ProductDimensionItem } from '../types';
import { ProductColorRaw, ProductSkuRaw } from '../../../../../legacy/types/product-data/style-level-raw';

/**
 * Filters dimensions based on the selected options and available sizes for a given color.
 *
 * @param {ProductDimension[]} dimensions - The array of dimensions to be filtered.
 * @param {ProductColorRaw} color - The selected product color containing size information.
 * @returns {ProductDimension[]} - The updated array of dimensions with filtered options.
 *
 * This method processes a set of dimensions and adjusts them based on the available sizes for the provided color.
 * If there's only one dimension, it is returned as-is. Otherwise, it iterates through the dimensions, filtering the
 * options for each dimension based on the selection of the other dimensions and the sizes available for the current selected color.
 *
 * Key steps:
 * - If `dimensions` contains only one item, return it without modification.
 * - For each dimension, identify the alternate group (the other dimension in the array).
 * - Filter the available sizes based on the selected dimension in the alternate group.
 * - Narrow down the items in the current dimension to match the sizes filtered from the alternate group.
 * - Return the updated dimension with filtered items.
 *
 * Note:
 * - Assumes that dimensions can have nested `dimensions` arrays with items to be filtered.
 * - Uses dimension group IDs to associate sizes with their respective dimensions.
 */

export const filterDimensions = (dimensions: ProductDimension[], color: ProductColorRaw): ProductDimension[] => {
  if ((dimensions && dimensions?.length <= 1) || !color?.sizes) {
    return dimensions;
  }

  return dimensions?.map((dimension, index) => {
    const alternateGroup = dimensions[index === 0 ? 1 : 0];
    const filteredSizes = color.sizes.filter(
      size => size[alternateGroup?.dimensionGroupId as keyof ProductSkuRaw] === alternateGroup?.selectedDimension || alternateGroup?.selectedDimension === ''
    );

    const filteredDimensions = dimension?.dimensions?.filter(item => {
      if (filteredSizes.some(size => item.name === size[dimension.dimensionGroupId as keyof ProductSkuRaw])) {
        return item as ProductDimensionItem;
      }

      return false;
    });

    return { ...dimension, dimensions: filteredDimensions };
  });
};
