import { DimensionsList } from '@product-page/pages/services/capi-aggregation-service/v3/types';

export type SelectedDimension = {
  bopis_in_stock: boolean | undefined;
  isInstock: boolean | undefined;
  selectedDimension: string | undefined;
  size_dimension: number;
};

/**
 * returns the inStock flag for the selected dimensions
 * @param {Array} selectedDimensions - Array of dimensions
 * @returns {Boolean} - True if the selected dimension is in stock, otherwise false
 */
function selectedDimensionInStock(selectedDimensions: DimensionsList): boolean | undefined {
  const { selectedDimension, dimensions } = selectedDimensions;
  const dimension = dimensions?.find(dim => dim.dimension === selectedDimension);
  return !!dimension?.isInstock;
}

function selectedDimensionBopisInStock(selectedDimensions: DimensionsList): boolean | undefined {
  const { selectedDimension, dimensions } = selectedDimensions;
  const dimension = dimensions?.find(dim => dim.dimension === selectedDimension);
  return !!dimension?.bopis_in_stock;
}

/**
 * Maps the given dimensions into a useable array containing information
 * on which dimension is selected
 * @param {Array} dimensions - Array of Objects defining the dimensions state
 * @returns {Array} - Array of Objects filtered to only dimensions with selections
 */
function filterSelectedDimensions(dimensions: DimensionsList[] | undefined): SelectedDimension[] {
  return dimensions
    ? dimensions
        .map((selectedDimensions, index) => ({
          bopis_in_stock: selectedDimensionBopisInStock(selectedDimensions),
          isInstock: selectedDimensionInStock(selectedDimensions),
          selectedDimension: selectedDimensions.selectedDimension,
          size_dimension: index + 1,
        }))
        .filter(dimension => dimension.selectedDimension)
    : [];
}

export { filterSelectedDimensions };
