/* eslint-disable jest/no-disabled-tests */
import { selectedDimensionInStock, selectedDimensionBopisInStock, hasDimensions, getSelectedDimensions } from '../collaborators/mvg-size-dimensions';

describe('selectedDimensionInStock', () => {
  it('returns true if selected dimension is in stock', () => {
    const dimensionsList = {
      selectedDimension: 'M',
      dimensions: [
        { dimension: 'M', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 0 },
        { dimension: 'L', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 1 },
      ],
    };
    expect(selectedDimensionInStock(dimensionsList)).toBe(true);
  });

  it('returns false if selected dimension is not found', () => {
    const dimensionsList = {
      selectedDimension: 'S',
      dimensions: [
        { dimension: 'M', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 0 },
        { dimension: 'L', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 1 },
      ],
    };
    expect(selectedDimensionInStock(dimensionsList)).toBe(false);
  });
});

describe('selectedDimensionBopisInStock', () => {
  it('returns true if selected dimension has bopis_in_stock', () => {
    const dimensionsList = {
      selectedDimension: 'L',
      dimensions: [
        { dimension: 'M', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 0 },
        { dimension: 'L', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 1 },
      ],
    };
    expect(selectedDimensionBopisInStock(dimensionsList)).toBe(true);
  });

  it('returns false if selected dimension not in list', () => {
    const dimensionsList = {
      selectedDimension: 'XL',
      dimensions: [
        { dimension: 'M', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 0 },
        { dimension: 'L', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 1 },
      ],
    };
    expect(selectedDimensionBopisInStock(dimensionsList)).toBe(false);
  });
});

describe('hasDimensions', () => {
  it('returns true when SKU matches all selected dimensions', () => {
    const sku = {
      size_dimension1: 'M',
      size_dimension2: 'Red',
    };

    const dimensions = [
      { selectedDimension: 'M', sizeDimension: 1, inStock: true, bopisInStock: true },
      { selectedDimension: 'Red', sizeDimension: 2, inStock: true, bopisInStock: true },
    ];

    expect(hasDimensions(sku, dimensions)).toBe(true);
  });

  it('returns false if any dimension does not match the SKU', () => {
    const sku = {
      size_dimension1: 'L',
      size_dimension2: 'Red',
    };

    const dimensions = [
      { selectedDimension: 'M', sizeDimension: 1, inStock: true, bopisInStock: true },
      { selectedDimension: 'Red', sizeDimension: 2, inStock: true, bopisInStock: true },
    ];

    expect(hasDimensions(sku, dimensions)).toBe(false);
  });
});

describe('getSelectedDimensions', () => {
  it('returns filtered dimensions with correct metadata', () => {
    const input = [
      {
        selectedDimension: 'M',
        dimensions: [
          { dimension: 'M', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 0 },
          { dimension: 'L', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 1 },
        ],
      },
      {
        selectedDimension: '',
        dimensions: [
          { dimension: 'Red', bopis_in_stock: true, availableSkus: [], isInstock: true, sort_order: 1 },
          { dimension: 'Blue', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 0 },
        ],
      },
    ];

    const result = getSelectedDimensions(input);

    expect(result).toEqual([
      {
        selectedDimension: 'M',
        inStock: true,
        bopisInStock: true,
        sizeDimension: 1,
      },
    ]);
  });

  it('filters out dimensions with no selectedDimension', () => {
    const input = [
      {
        selectedDimension: '',
        dimensions: [{ dimension: 'S', bopis_in_stock: false, availableSkus: [], isInstock: true, sort_order: 0 }],
      },
    ];
    expect(getSelectedDimensions(input)).toEqual([]);
  });
});
