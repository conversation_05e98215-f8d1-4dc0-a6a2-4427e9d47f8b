import { render, screen } from '@testing-library/react';
import { prevVariantMock } from '../../variant-group/__tests__/variantMock';
import { LayoutType } from '../collaborators/get-config';
import { ONLINE_AND_BOPIS_INVENTORY, BOPIS_INVENTORY } from '../types';
import { ColorPicker } from '../ColorPicker';
import { ColorPickerGrouped } from '../ColorPickerGrouped';

jest.mock('../../../../../../../sitewide/src/providers/localization', () => ({
  useLocalize: () => ({
    localize: jest.fn().mockReturnValue('Color Label'),
  }),
}));

jest.mock('../../../../../providers/buybox-provider', () => ({
  useBuyBoxStore: jest.fn(() => ({
    selectedVariant: prevVariantMock,
    selectedColor: {
      colorName: 'red',
      colorCode: '#FF0000',
    },
    updateSelectedColor: jest.fn(),
    handleHoverColor: jest.fn(),
  })),
}));

jest.mock('../ColorPickerGrouped', () => ({
  ColorPickerGrouped: jest.fn(() => <div data-testid='mock-color-picker-grouped' />),
}));

const defaultProps = {
  parentComponent: 'buybox' as 'buybox' | 'csm',
  layout: 'stacked' as LayoutType,
  brandName: 'gap' as Brands,
  isPercentageEnabled: false,
};

describe('<ColorPicker />', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show the color label when parentComponent is not csm', () => {
    render(<ColorPicker {...defaultProps} />);
    expect(screen.getByTestId('pdp-color-label-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('pdp-color-value')).toHaveTextContent('Red');
  });

  it('should pass ONLINE_AND_BOPIS_INVENTORY mode to ColorPickerGrouped when parentComponent is buybox', () => {
    render(<ColorPicker {...defaultProps} />);

    expect(ColorPickerGrouped).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: ONLINE_AND_BOPIS_INVENTORY,
      }),
      {}
    );
  });

  it('should pass BOPIS_INVENTORY mode to ColorPickerGrouped when parentComponent is csm', () => {
    render(<ColorPicker {...defaultProps} parentComponent='csm' />);

    expect(ColorPickerGrouped).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: BOPIS_INVENTORY,
      }),
      {}
    );
  });
});
