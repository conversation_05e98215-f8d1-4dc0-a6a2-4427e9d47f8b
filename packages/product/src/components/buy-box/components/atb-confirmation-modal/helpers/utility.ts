// @ts-nocheck
'use client';

import { AppConfig } from '@ecom-next/sitewide/state-builder-configs';
import { ProductStore, DisplayAddToBagItemProps } from '../types';

export function decodeAmpersands(str = '') {
  // ampersands can be encoded as an HTML entity in four ways
  // 1. &amp all lowercase keyword
  // 2. &AMP all uppercase keyword
  // 3. &#38 a decimal identifier
  // 4. &#x26 the hexidecimal equivalent
  const encoded = /(&amp|&AMP|&#38|&#x26);?/gm;
  const result = str?.replace(encoded, '&') || '';

  // In the event that a piece of data is encoded more than once,
  // decoding needs to happen the same amount of times.
  // If our input string does not match the decoded result,
  // we will recursively call decode until it does.
  return result !== str ? decodeAmpersands(result) : result;
}

const prefixProtocol = (url: string): string => {
  return url.startsWith('https://') ? url : `https://${url}`;
};

export const shoppingBagUrl = (brandCodeUrls: AppConfig['brandCodeUrls']): string => {
  const isSemTrue = window.location.search.indexOf('sem=true') >= 0;

  const domain = isSemTrue ? brandCodeUrls.unsecureUrl : prefixProtocol(brandCodeUrls.secureUrl);
  const prefix = domain || '';
  const checkoutUrl = '/shopping-bag';
  return `${prefix}${checkoutUrl}`;
};

// when tabbing through elements in modal, keep focus inside modal when last element reached
export const focusFirstElement = (event: KeyboardEvent, brandCodeUrls: AppConfig['brandCodeUrls']) => {
  if (event.key === 'Tab') {
    if (event.shiftKey) {
      const el = document.getElementById('keepShoppingButton');
      if (el && el.focus) {
        el.focus();
      }
      return false;
    }
    const el = document.getElementById('closeModalButton');
    if (el && el.focus) {
      el.focus();
    }
    return false;
  }
  if (event.key === 'Enter') {
    // eslint-disable-next-line no-restricted-globals
    location.href = shoppingBagUrl(brandCodeUrls);
  }

  return true;
};

export const getPickupInfo = (store?: ProductStore): string =>
  store?.storeName && store?.storeAddress?.cityName ? `${store.storeName} - ${store.storeAddress.cityName}` : '';

export const getReturnsMessage = (returnMessageType: string | null, localize: DisplayAddToBagItemProps['localize']) => {
  if (returnMessageType === 'N') {
    return localize('pdp.atbConfirmation.finalSale');
  }
  if (returnMessageType === 'M') {
    return localize('pdp.atbConfirmation.returnsByMailOnly');
  }
  return '';
};
