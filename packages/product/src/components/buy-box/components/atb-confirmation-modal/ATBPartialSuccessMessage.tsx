import React from 'react';
import { Notification } from '@ecom-next/core/components/fabric/notification';
import { AdaptedAddToBagResponse } from '../add-to-bag/types';

export const ATBPartialSuccessMessage = ({ warnings }: Pick<AdaptedAddToBagResponse, 'warnings'>) => {
  return (
    <>
      {warnings?.message && (
        <Notification kind='warning' isDismissible={false} className='atb-confirmation-modal__product-detail-container' data-testid='success-notification'>
          <div className='atb-confirmation-modal-partial-success__notification'>{warnings?.message}</div>
        </Notification>
      )}
    </>
  );
};
