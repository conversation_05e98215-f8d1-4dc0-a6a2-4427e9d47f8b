'use Client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useBopisData } from '@pdp/src/app/hooks/use-bopis-data';
import ATBConfirmationMarketingContainer from './ATBConfirmationMarketingContainer';
import { ATBConfirmationModalContentProps } from './types';
import { DisplayAddToBagItem } from './DisplayAddToBagItem';
import { ATBPartialSuccessMessage } from './ATBPartialSuccessMessage';
import { ATBItemCountSubTotal } from './ATBItemCountSubTotal';
import { ATBViewBagButton } from './ATBViewBagButton';

export const ATBConfirmationModalContent = ({
  brandCodeUrls,
  isPercentageEnabled,
  displayPercentageOffFromCapi,
  cid,
  atbResponse,
  isGiftCard,
  recommendationsConfig,
}: ATBConfirmationModalContentProps): JSX.Element => {
  const { localize } = useLocalize();
  const bopisData = useBopisData();
  const { selectedStore, active, enabled } = bopisData;
  const { warnings = null, productData: [{ colorStyleNumber } = {}] = [] } = atbResponse;

  return (
    <div id='modalWindow' role='dialog' className={`atb-confirmation-modal__section-wrapper h-full w-full`}>
      <div className='atb-confirmation-modal__body '>
        <DisplayAddToBagItem
          active={active}
          enabled={enabled}
          atbResponse={atbResponse}
          localize={localize}
          selectedStore={selectedStore}
          isPercentageEnabled={isPercentageEnabled}
          displayPercentageOffFromCapi={displayPercentageOffFromCapi}
          isGiftCard={isGiftCard}
        />
      </div>
      <ATBPartialSuccessMessage warnings={warnings} />
      <div className='atb-confirmation-modal__item-count-view-bag-container'>
        <ATBItemCountSubTotal atbResponse={atbResponse} />
        <ATBViewBagButton brandCodeUrls={brandCodeUrls} />
      </div>
      <ATBConfirmationMarketingContainer
        cid={cid}
        isPercentageEnabled={isPercentageEnabled}
        colorStyleNumber={colorStyleNumber || ''}
        recommendationsConfig={recommendationsConfig}
      ></ATBConfirmationMarketingContainer>
    </div>
  );
};
