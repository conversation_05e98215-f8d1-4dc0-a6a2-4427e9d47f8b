import React, { Fragment } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { BuyBoxState } from '../../../../providers/buybox-provider/BuyBoxContext';

type ATBItemCountSubTotalProps = {
  atbResponse: BuyBoxState['atbResponse'];
};

export const ATBItemCountSubTotal = ({ atbResponse }: ATBItemCountSubTotalProps) => {
  const { localize } = useLocalize();
  const subtotalLabel = localize('pdp.atbConfirmation.label.subtotal');

  const { charges, totalBagQuantity = 0 } = atbResponse;
  const localizedSubTotal = charges?.localizedSubTotal ?? '0';

  const formatItemCountWrapping = (count: number) => {
    const inBagLabel = localize('pdp.atbConfirmation.inBag');
    const itemsCountText = localize('pdp.atbConfirmation.item_count_text', { count });

    return (
      <Fragment>
        <span className='label atb-confirmation-modal__item-count'>{itemsCountText}</span>
        <span className='value atb-confirmation-modal__item-count pl-1'>{inBagLabel}</span>
      </Fragment>
    );
  };

  return (
    <div className='atb-confirmation-modal__item-count-subtotal-container'>
      <div className='atb-confirmation-modal__item-count-container'>{formatItemCountWrapping(totalBagQuantity)}</div>
      <div className='atb-confirmation-modal__subtotal'>
        <span className='label'>{subtotalLabel}</span>
        <span className='value pl-1'>{localizedSubTotal}</span>
      </div>
    </div>
  );
};
