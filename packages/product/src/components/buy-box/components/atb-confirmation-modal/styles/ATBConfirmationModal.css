.fds_modal__container .fds_modal.crossbrand.fds_modal--skinny-header.atb-confirmation-modal-wrapper {
  .fds_modal__header-container {
    background: theme('colors.color-background-default--white');
    padding: theme('padding.utk-spacing-xl');
    border-bottom: theme('borderWidth.border-width-default') solid theme('colors.color-border-disabled');

    .fds_modal__header {
      overflow: hidden;
      color: theme('colors.color-type-copy');
      text-align: center;
      text-overflow: ellipsis;
      font-size: theme('fontSize.font-size-1');
      font-weight: theme('fontWeight.font-weight-base-heavier');
      font-family: theme('fontFamily.brand-base');
      letter-spacing: theme('letterSpacing.font-letter-spacing-base');
      font-style: normal;
      line-height: normal;
    }
  }

  .fds_modal__modal-content {
    padding: 0px;
    width: 100%;
    box-sizing: border-box;

    .fds_modal__content {
      width: 100%;
      box-sizing: border-box;

      .atb-confirmation-modal__section-wrapper {
        .atb-confirmation-modal__body .atb-confirmation-modal-image-product-details-wrapper,
        .atb-confirmation-modal__product-backorder-message {
          display: flex;
          align-items: flex-start;
          gap: theme('spacing.utk-spacing-s');
          align-self: stretch;
          color: theme('colors.color-type-copy');
          font-size: theme('fontSize.font-size--1');
          font-family: theme('fontFamily.brand-base');
          letter-spacing: theme('letterSpacing.font-letter-spacing-base');
          font-style: normal;
          line-height: normal;

          .atb-confirmation-modal__product-link .atb-confirmation-modal__image {
            display: flex;
            width: 5.4375rem;
            height: 7.25rem;
            max-width: 5.4375rem;
            max-height: 7.25rem;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-end;
            aspect-ratio: 3/4;
          }

          .atb-confirmation-modal-product-details-wrapper {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: theme('spacing.utk-spacing-2xs');
            flex: 1 0 0;

            .atb-confirmation-modal__product-name-number .atb-confirmation-modal__product-anchor .atb-confirmation-modal-product-title {
              color: theme('colors.color-type-copy');
              font-size: theme('fontSize.font-size--1');
              font-weight: theme('fontWeight.font-weight-base-heavier');
              font-family: theme('fontFamily.brand-base');
              letter-spacing: theme('letterSpacing.font-letter-spacing-base');
              font-style: normal;
              line-height: normal;
            }

            .atb-confirmation-modal__product-details-wrapper {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              gap: theme('spacing.utk-spacing-2xs');

              .atb-confirmation-modal__product-description-wrapper {
                font-weight: theme('fontWeight.font-weight-base-default');

                .atb-confirmation-modal__product-description-label,
                .atb-confirmation-modal__product-description {
                  color: theme('colors.color-type-copy');
                  font-size: theme('fontSize.font-size--1');
                  margin-right: theme('spacing.utk-spacing-2xs');
                  font-family: theme('fontFamily.brand-base');
                  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
                  font-style: normal;
                  line-height: normal;
                }
              }

              .atb-confirmation-modal__product-description.quantity {
                font-size: theme('fontSize.font-size-0');
              }
            }

            .atb-confirmation-modal-price-wrapper {
              .atb-confirmation-modal-regular-price,
              .atb-confirmation-modal-regular-price-strike-through,
              .atb-confirmation-modal-sale-price-wrapper .atb-confirmation-modal-sale-price,
              .atb-confirmation-modal__percent-off {
                font-weight: theme('fontWeight.font-weight-base-default');
                font-family: theme('fontFamily.brand-base');
                letter-spacing: theme('letterSpacing.font-letter-spacing-base');
                font-style: normal;
                line-height: normal;
              }

              .atb-confirmation-modal-regular-price-strike-through {
                color: theme('colors.style-product-price-strikethrough');
              }

              .atb-confirmation-modal-sale-price-wrapper .atb-confirmation-modal-sale-price {
                color: theme('colors.color-type-sale');
                font-family: theme('fontFamily.brand-base');
                letter-spacing: theme('letterSpacing.font-letter-spacing-base');
                font-style: normal;
                line-height: normal;
              }
            }

            .atb-confirmation-modal__product-return-message,
            .atb-confirmation-modal__pickup-info {
              font-weight: theme('fontWeight.font-weight-base-default');
              line-height: normal;
              letter-spacing: theme('letterSpacing.font-letter-spacing-base');
            }

            .atb-confirmation-modal__pickup-info {
              display: flex;
              flex-direction: row;
              gap: theme('spacing.utk-spacing-2xs');
              align-items: flex-start;
            }
          }
        }

        .fds_notification.atb-confirmation-modal__product-detail-container {
          margin-top: theme('spacing.utk-spacing-s');

          .fds_notification__wrapper .notification--content .atb-confirmation-modal-partial-success__notification {
            color: #000;
            font-size: theme('fontSize.font-size--2');
            font-family: theme('fontFamily.brand-base');
            font-weight: theme('fontWeight.font-weight-base-heavier');
            font-style: normal;
            line-height: normal;
            letter-spacing: theme('letterSpacing.font-letter-spacing-base');
          }
        }

        .atb-confirmation-modal__product-backorder-message {
          font-size: theme('fontSize.font-size--2');
          font-weight: theme('fontWeight.font-weight-base-heavier');
          margin-top: theme('spacing.utk-spacing-s');
        }

        .atb-confirmation-modal__item-count-view-bag-container {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: theme('spacing.utk-spacing-s');
          align-self: stretch;
          margin-top: theme('spacing.utk-spacing-l');

          .atb-confirmation-modal__item-count-subtotal-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            align-self: stretch;
            color: theme('colors.color-type-copy');
            font-family: theme('fontFamily.brand-base');
            font-size: theme('fontSize.font-size--1');
            font-style: normal;
            font-weight: theme('fontWeight.font-weight-base-heavier');
            line-height: normal;
            letter-spacing: theme('letterSpacing.font-letter-spacing-base');
          }

          .atb-confirmation-modal__view-bag-link {
            width: 100%;

            .atb-confirmation-modal__buttons-wrapper {
              .atb-confirmation-modal__button .atb-confirmation-modal__view-bag-button {
                .fds_button__label {
                  border-bottom: none;
                }
              }
            }
          }
        }
      }
    }
  }

  .recommendations-container {
    .recs-carousel-wrapper {
      @media screen and (min-width: 1280px) {
        margin-top: 0;
      }
    }

    .recs-section {
      padding: theme('spacing.utk-spacing-l') 0 0;

      .recs-carousel-title {
        color: theme('colors.color-type-copy');
        font-family: theme('fontFamily.font-family-display');
        letter-spacing: theme('letterSpacing.font-letter-spacing-display');
        font-style: normal;
        line-height: normal;
        font-weight: theme('fontWeight.font-weight-display-default');
        font-size: theme('fontSize.font-size-2');
      }
    }

    .product-recommendation-wrapper {
      overflow-x: auto;
    }

    .recs-carousel-product-card--container {
      max-width: 10.1rem;
      flex: unset;

      .recs-carousel-product-card--image {
        width: 10.1rem;
        height: 13.47rem;
      }

      a {
        width: 100%;
        height: 100%;

        .fds_product-image {
          width: 100% !important;
          height: 100% !important;

          img {
            width: 100% !important;
            height: auto !important;
          }
        }
      }
    }

    @media screen and (min-width: 1280px) {
      .recs-carousel-product-card--container {
        max-width: 8rem;

        .recs-carousel-product-card--image {
          width: 7.93rem;
          height: 10.58rem;
        }
      }
    }
  }
}
