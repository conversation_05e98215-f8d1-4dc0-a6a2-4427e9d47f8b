import React from 'react';
import { render, screen } from '@testing-library/react';
import { AddToBagProductPrice } from '../AddToBagProductPrice';
import { AddToBagProductPriceProps } from '../types';

describe('AddToBagProductPrice', () => {
  const defaultProps: AddToBagProductPriceProps = {
    isSalePrice: false,
    localizedRegularPrice: '$100.00',
    isPercentageEnabled: false,
    percentageOffText: '20% OFF',
    localizedSalePrice: '$80.00',
  };

  it('renders the regular price when isSalePrice is false', () => {
    render(<AddToBagProductPrice {...defaultProps} />);

    const regularPrice = screen.getByTestId('list-price');
    expect(regularPrice).toBeInTheDocument();
    expect(regularPrice).toHaveTextContent('$100.00');
    expect(regularPrice).toHaveClass('atb-confirmation-modal-regular-price');
  });

  it('renders the sale price with percentage off when isSalePrice and isPercentageEnabled are true', () => {
    render(<AddToBagProductPrice {...defaultProps} isSalePrice={true} isPercentageEnabled={true} />);

    const saleListPrice = screen.getByTestId('sale-list-price');
    expect(saleListPrice).toBeInTheDocument();
    expect(saleListPrice).toHaveTextContent('$100.00');
    expect(saleListPrice).toHaveClass('atb-confirmation-modal-regular-price-strike-through line-through');

    const percentageOff = screen.getByText('20% OFF');
    expect(percentageOff).toBeInTheDocument();
    expect(percentageOff).toHaveClass('atb-confirmation-modal__percent-off ml-1');

    const salePrice = screen.getByText('$80.00');
    expect(salePrice).toBeInTheDocument();
    expect(salePrice).toHaveClass('atb-confirmation-modal-sale-price');
  });

  it('renders the sale price without percentage off when isSalePrice is true and isPercentageEnabled is false', () => {
    render(<AddToBagProductPrice {...defaultProps} isSalePrice={true} isPercentageEnabled={false} />);

    const saleListPrice = screen.getByTestId('sale-list-price');
    expect(saleListPrice).toBeInTheDocument();
    expect(saleListPrice).toHaveTextContent('$100.00');
    expect(saleListPrice).toHaveClass('atb-confirmation-modal-regular-price-strike-through line-through');

    const salePrice = screen.getByText('$80.00');
    expect(salePrice).toBeInTheDocument();
    expect(salePrice).toHaveClass('atb-confirmation-modal-sale-price ml-1');
  });

  it('renders the localizedSalePrice in an h4 tag when isPercentageEnabled is true', () => {
    render(<AddToBagProductPrice {...defaultProps} isSalePrice={true} isPercentageEnabled={true} />);

    const salePriceHeading = screen.getByRole('heading', { level: 4 });
    expect(salePriceHeading).toBeInTheDocument();
    expect(salePriceHeading).toHaveTextContent('$80.00');
    expect(salePriceHeading).toHaveClass('atb-confirmation-modal-sale-price');
  });
});
