import React from 'react';
import { render, screen } from '@testing-library/react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ATBItemCountSubTotal } from '../ATBItemCountSubTotal';

jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: jest.fn(),
}));

describe('ATBItemCountSubTotal', () => {
  const mockLocalize = jest.fn();

  beforeEach(() => {
    (useLocalize as jest.Mock).mockReturnValue({ localize: mockLocalize });
  });

  it('renders correctly with valid atbResponse', () => {
    mockLocalize.mockImplementation((key, params) => {
      switch (key) {
        case 'pdp.atbConfirmation.label.subtotal':
          return 'Subtotal';
        case 'pdp.atbConfirmation.inBag':
          return 'In Bag';
        case 'pdp.atbConfirmation.item_count_text':
          return `${params.count} items`;
        default:
          return key;
      }
    });

    const atbResponse = {
      charges: { localizedSubTotal: '$100' },
      totalBagQuantity: 3,
      isSuccess: true,
    };

    render(<ATBItemCountSubTotal atbResponse={atbResponse} />);

    expect(screen.getByText('3 items')).toBeInTheDocument();
    expect(screen.getByText('In Bag')).toBeInTheDocument();
    expect(screen.getByText('Subtotal')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();
  });

  it('renders correctly when charges are undefined', () => {
    mockLocalize.mockImplementation((key, params) => {
      switch (key) {
        case 'pdp.atbConfirmation.label.subtotal':
          return 'Subtotal';
        case 'pdp.atbConfirmation.inBag':
          return 'In Bag';
        case 'pdp.atbConfirmation.item_count_text':
          return `${params.count} items`;
        default:
          return key;
      }
    });

    const atbResponse = {
      charges: undefined,
      totalBagQuantity: 0,
    };

    render(<ATBItemCountSubTotal atbResponse={atbResponse} />);

    expect(screen.getByText('0 items')).toBeInTheDocument();
    expect(screen.getByText('In Bag')).toBeInTheDocument();
    expect(screen.getByText('Subtotal')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('renders correctly with totalBagQuantity as 0', () => {
    mockLocalize.mockImplementation((key, params) => {
      switch (key) {
        case 'pdp.atbConfirmation.label.subtotal':
          return 'Subtotal';
        case 'pdp.atbConfirmation.inBag':
          return 'In Bag';
        case 'pdp.atbConfirmation.item_count_text':
          return `${params.count} items`;
        default:
          return key;
      }
    });

    const atbResponse = {
      charges: { localizedSubTotal: '$0' },
      totalBagQuantity: 0,
    };

    render(<ATBItemCountSubTotal atbResponse={atbResponse} />);

    expect(screen.getByText('0 items')).toBeInTheDocument();
    expect(screen.getByText('In Bag')).toBeInTheDocument();
    expect(screen.getByText('Subtotal')).toBeInTheDocument();
    expect(screen.getByText('$0')).toBeInTheDocument();
  });

  it('renders default subtotal when localizedSubTotal is missing', () => {
    mockLocalize.mockImplementation((key, params) => {
      switch (key) {
        case 'pdp.atbConfirmation.label.subtotal':
          return 'Subtotal';
        case 'pdp.atbConfirmation.inBag':
          return 'In Bag';
        case 'pdp.atbConfirmation.item_count_text':
          return `${params.count} items`;
        default:
          return key;
      }
    });

    const atbResponse = {
      charges: {},
      totalBagQuantity: 2,
    };

    render(<ATBItemCountSubTotal atbResponse={atbResponse} />);

    expect(screen.getByText('2 items')).toBeInTheDocument();
    expect(screen.getByText('In Bag')).toBeInTheDocument();
    expect(screen.getByText('Subtotal')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument();
  });
});
