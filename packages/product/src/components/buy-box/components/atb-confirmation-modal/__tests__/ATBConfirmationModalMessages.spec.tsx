// ATBConfirmationModalMessages.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ATBBackOrderMessage, ATBConfirmationModalReturnMessage } from '../ATBConfirmationModalMessages';
import * as utility from '../helpers/utility';

jest.mock('../helpers/utility', () => ({
  decodeAmpersands: jest.fn(),
}));

describe('ATBConfirmationModalMessages', () => {
  describe('ATBBackOrderMessage', () => {
    it('renders the backorder message with decoded HTML', () => {
      const mockDecodeAmpersands = jest.spyOn(utility, 'decodeAmpersands');
      const backorderHTML = 'Test &amp; Message';
      const decodedHTML = 'Test & Message';
      mockDecodeAmpersands.mockReturnValue(decodedHTML);

      render(<ATBBackOrderMessage backorderHTML={backorderHTML} />);

      // Verify decodeAmpersands is called with the correct argument
      expect(mockDecodeAmpersands).toHaveBeenCalledWith(backorderHTML);

      // Verify the dangerouslySetInnerHTML content
      const spanElement = screen.getByText(decodedHTML);
      expect(spanElement).toBeInTheDocument();
    });
  });

  describe('ATBConfirmationModalReturnMessage', () => {
    it('renders the return type message', () => {
      const returnTypeMessage = 'This is a return message';

      render(<ATBConfirmationModalReturnMessage returnTypeMessage={returnTypeMessage} />);

      // Verify the returnTypeMessage is rendered correctly
      const spanElement = screen.getByText(returnTypeMessage);
      expect(spanElement).toBeInTheDocument();
      expect(spanElement).toHaveClass('atb-confirmation-modal__product-return-message');
    });
  });
});
