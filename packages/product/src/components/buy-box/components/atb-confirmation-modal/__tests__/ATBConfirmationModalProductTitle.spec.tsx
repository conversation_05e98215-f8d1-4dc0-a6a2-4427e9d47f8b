import React from 'react';
import { render, screen } from '@testing-library/react';
import { ATBConfirmationModalProductTitle } from '../ATBConfirmationModalProductTitle';
import * as utility from '../helpers/utility';

jest.mock('../helpers/utility', () => ({
  decodeAmpersands: jest.fn(),
}));

describe('ATBConfirmationModalProductTitle', () => {
  const mockProductStyleDescription = 'Product &amp; Description';
  const mockDecodedDescription = 'Product & Description';
  const mockProductURL = 'https://example.com/product';

  beforeEach(() => {
    (utility.decodeAmpersands as jest.Mock).mockReturnValue(mockDecodedDescription);
  });

  it('calls decodeAmpersands with the correct argument', () => {
    render(<ATBConfirmationModalProductTitle productStyleDescription={mockProductStyleDescription} productURL={mockProductURL} />);

    expect(utility.decodeAmpersands).toHaveBeenCalledWith(mockProductStyleDescription);
  });

  it('sets the correct tabIndex and id on the anchor tag', () => {
    render(<ATBConfirmationModalProductTitle productStyleDescription={mockProductStyleDescription} productURL={mockProductURL} />);

    const anchorElement = screen.getByRole('link');
    expect(anchorElement).toHaveAttribute('id', 'productTitle');
    expect(anchorElement).toHaveAttribute('tabIndex', '0');
  });
});
