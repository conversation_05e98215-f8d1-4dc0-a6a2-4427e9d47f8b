import React from 'react';
import { render, screen } from '@testing-library/react';
import { ATBPartialSuccessMessage } from '../ATBPartialSuccessMessage';

describe('ATBPartialSuccessMessage', () => {
  it('renders the Notification component when warnings.message is provided', () => {
    const warnings = {
      message: 'Partial success message',
      addedQuantity: 0,
      errorCode: '',
      partialSuccess: false,
    };

    render(<ATBPartialSuccessMessage warnings={warnings} />);

    // Check if the Notification component is rendered
    const notification = screen.getByTestId('success-notification');
    expect(notification).toBeInTheDocument();

    // Check if the message is displayed
    expect(screen.getByText('Partial success message')).toBeInTheDocument();
  });

  it('does not render anything when warnings is undefined', () => {
    render(<ATBPartialSuccessMessage warnings={undefined} />);

    // Ensure no Notification component is rendered
    expect(screen.queryByTestId('success-notification')).not.toBeInTheDocument();
  });

  it('does not render anything when warnings.message is undefined', () => {
    const warnings = {
      message: '',
      addedQuantity: 0,
      errorCode: '',
      partialSuccess: false,
    };

    render(<ATBPartialSuccessMessage warnings={warnings} />);

    // Ensure no Notification component is rendered
    expect(screen.queryByTestId('success-notification')).not.toBeInTheDocument();
  });

  it('does not render anything when warnings.message is null', () => {
    const warnings = {
      message: '',
      addedQuantity: 0,
      errorCode: '',
      partialSuccess: false,
    };
    render(<ATBPartialSuccessMessage warnings={warnings} />);

    // Ensure no Notification component is rendered
    expect(screen.queryByTestId('success-notification')).not.toBeInTheDocument();
  });
});
