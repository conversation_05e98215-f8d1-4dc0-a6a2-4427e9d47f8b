import { decodeAmpersands } from './helpers/utility';

export const ATBConfirmationModalProductTitle = ({ productStyleDescription, productURL }: { productStyleDescription: string; productURL: string }) => {
  return (
    <div className='atb-confirmation-modal__product-name-number'>
      <a className='atb-confirmation-modal__product-anchor' href={productURL} id={`productTitle`} tabIndex={0}>
        <h4
          className='atb-confirmation-modal-product-title'
          dangerouslySetInnerHTML={{
            __html: decodeAmpersands(productStyleDescription),
          }}
        />
      </a>
    </div>
  );
};
