import { decodeAmpersands } from './helpers/utility';

export const ATBBackOrderMessage = ({ backorderHTML }: { backorderHTML: string }) => {
  return (
    <div className='atb-confirmation-modal__product-backorder-message'>
      {/* eslint-disable react/no-danger */}
      <span
        dangerouslySetInnerHTML={{
          __html: decodeAmpersands(backorderHTML),
        }}
      />
    </div>
  );
};

export const ATBConfirmationModalReturnMessage = ({ returnTypeMessage }: { returnTypeMessage: string }) => {
  return (
    <div className='atb-confirmation-modal__product-message-container'>
      <span className='atb-confirmation-modal__product-return-message'>{returnTypeMessage}</span>
    </div>
  );
};
