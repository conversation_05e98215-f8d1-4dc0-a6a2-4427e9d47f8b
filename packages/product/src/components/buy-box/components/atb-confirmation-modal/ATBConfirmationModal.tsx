'use client';
import React, { useContext, useMemo } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ABSegProvider } from '@pdp/src/app/components/abseg-provider';
import { Modal } from '@ecom-next/core/components/fabric/modal';
import { BreakpointContext, MEDIUM } from '@ecom-next/core/breakpoint-provider';
import { ATBConfirmationModalContent } from './ATBConfirmationModalContent';
import { ATBConfirmationModalProps } from './types';

const ATBConfirmationModal = ({
  isOpen,
  closeButtonClickHandler,
  brandName,
  brandCodeUrls,
  abSeg,
  cid,
  isPercentageEnabled,
  displayPercentageOffFromCapi,
  atbResponse,
  isGiftCard,
  recommendationsConfig,
}: ATBConfirmationModalProps) => {
  const { localize } = useLocalize();
  const title =
    brandName === 'br' || brandName === 'brfs'
      ? localize('pdp.atbConfirmation.title.addToBag.light').toUpperCase()
      : localize('pdp.atbConfirmation.title.addToBag.light');
  const closeModalAriaText = localize('pdp.closeModal.altText');
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = useMemo(() => smallerThan(MEDIUM), [smallerThan]);

  return (
    <Modal
      closeButtonAriaLabel={closeModalAriaText}
      callbackFn={closeButtonClickHandler}
      headerContent={title}
      headerAlignment={'center'}
      isCrossBrand={true}
      isOpen={isOpen}
      hasSkinnyHeader={true}
      className={`atb-confirmation-modal-wrapper ${isMobile ? `h-auto w-[21.625rem]` : `max-h-[47.063rem] w-[45.688rem] `}`}
    >
      <ABSegProvider abSeg={abSeg} brandName={brandName}>
        {atbResponse && (
          <ATBConfirmationModalContent
            brandCodeUrls={brandCodeUrls}
            brandName={brandName}
            displayPercentageOffFromCapi={displayPercentageOffFromCapi}
            isPercentageEnabled={isPercentageEnabled}
            cid={cid}
            atbResponse={atbResponse}
            isGiftCard={isGiftCard}
            recommendationsConfig={recommendationsConfig}
          />
        )}
      </ABSegProvider>
    </Modal>
  );
};

export default ATBConfirmationModal;
