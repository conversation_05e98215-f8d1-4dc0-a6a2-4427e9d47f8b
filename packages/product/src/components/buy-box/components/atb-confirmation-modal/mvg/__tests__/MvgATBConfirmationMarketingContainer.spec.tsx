import { render, screen } from '@testing-library/react';
import { ProductRecommendationsProvider } from '@ecom-next/sitewide/product-recs-provider';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { MvgATBConfirmationMarketingContainer } from '../MvgATBConfirmationMarketingContainer';
import { useMVGBuyBoxStore } from '../../../../../../providers/mvg-buybox-provider';
import { useMvgAddToBagContext } from '../../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider';

jest.mock('../../../../../../providers/mvg-buybox-provider', () => ({
  useMVGBuyBoxStore: jest.fn(),
}));

jest.mock('../../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider', () => ({
  useMvgAddToBagContext: jest.fn(),
}));

jest.mock('@ecom-next/marketing-ui/product', () => ({
  InlineMarketing: jest.fn(() => <div data-testid='inline-marketing' />),
}));

jest.mock('../../../../../pdp-recs-carousel-wrapper', () => ({
  PDPRecsCarouselWrapper: jest.fn(() => <div data-testid='pdp-recs-carousel-wrapper' />),
}));

describe('<MvgATBConfirmationMarketingContainer />', () => {
  const mockUseMVGBuyBoxStore = useMVGBuyBoxStore as jest.Mock;
  const mockUseMvgAddToBagContext = useMvgAddToBagContext as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMVGBuyBoxStore.mockReturnValue({
      selectedCustomerChoiceId: '12345',
    });

    mockUseMvgAddToBagContext.mockReturnValue({
      cid: 'test-cid',
      showInsituMarketingContainer: false,
      isPercentageEnabled: true,
      recommendationsConfig: { configKey: 'value' },
    });
  });

  test('renders the carousel when greaterOrEqualTo(LARGE) is true', () => {
    render(
      <BreakpointContext.Provider
        value={{
          greaterOrEqualTo: jest.fn(() => true),
        }}
      >
        <ProductRecommendationsProvider>
          <MvgATBConfirmationMarketingContainer />
        </ProductRecommendationsProvider>
      </BreakpointContext.Provider>
    );

    expect(screen.getByTestId('inline-marketing')).toBeInTheDocument();
    expect(screen.getByTestId('pdp-recs-carousel-wrapper')).toBeInTheDocument();
  });

  test('renders the carousel when showInsituMarketingContainer is true', () => {
    mockUseMvgAddToBagContext.mockReturnValueOnce({
      cid: 'test-cid',
      showInsituMarketingContainer: true,
      isPercentageEnabled: true,
      recommendationsConfig: { configKey: 'value' },
    });

    render(
      <BreakpointContext.Provider
        value={{
          greaterOrEqualTo: jest.fn(() => false),
        }}
      >
        <ProductRecommendationsProvider>
          <MvgATBConfirmationMarketingContainer />
        </ProductRecommendationsProvider>
      </BreakpointContext.Provider>
    );

    expect(screen.getByTestId('inline-marketing')).toBeInTheDocument();
    expect(screen.getByTestId('pdp-recs-carousel-wrapper')).toBeInTheDocument();
  });

  test('does not render the carousel when both greaterOrEqualTo(LARGE) and showInsituMarketingContainer are false', () => {
    render(
      <BreakpointContext.Provider
        value={{
          greaterOrEqualTo: jest.fn(() => false),
        }}
      >
        <ProductRecommendationsProvider>
          <MvgATBConfirmationMarketingContainer />
        </ProductRecommendationsProvider>
      </BreakpointContext.Provider>
    );

    expect(screen.getByTestId('inline-marketing')).toBeInTheDocument();
    expect(screen.queryByTestId('pdp-recs-carousel-wrapper')).not.toBeInTheDocument();
  });
});
