'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useBopisData } from '@pdp/src/app/hooks/use-bopis-data';
import { ATBConfirmationModalContentProps } from '../types';
import { useMvgAddToBagContext } from '../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider';
import { ATBPartialSuccessMessage } from '../ATBPartialSuccessMessage';
import { ATBItemCountSubTotal } from '../ATBItemCountSubTotal';
import { ATBViewBagButton } from '../ATBViewBagButton';
import { MvgATBConfirmationMarketingContainer } from './MvgATBConfirmationMarketingContainer';
import { MvgDisplayAddToBagItem } from './MvgDisplayAddToBagItem';

export const MvgATBConfirmationModalContent = ({
  atbResponse,
}: Omit<
  ATBConfirmationModalContentProps,
  'brandName' | 'locale' | 'cid' | 'market' | 'abSeg' | 'recommendationsConfig' | 'brandCodeUrls' | 'displayPercentageOffFromCapi' | 'isPercentageEnabled'
>): JSX.Element => {
  const { localize } = useLocalize();
  const bopisData = useBopisData();
  const { selectedStore, active, enabled } = bopisData;
  const { warnings = null } = atbResponse;
  const { brandCodeUrls } = useMvgAddToBagContext();

  return (
    <div id='modalWindow' role='dialog' className='atb-confirmation-modal__section-wrapper h-full w-full'>
      <div className='atb-confirmation-modal__body '>
        <MvgDisplayAddToBagItem
          active={active}
          enabled={enabled}
          atbResponse={atbResponse}
          warnings={warnings}
          localize={localize}
          selectedStore={selectedStore}
        />
      </div>
      <ATBPartialSuccessMessage warnings={warnings} />
      <div className='atb-confirmation-modal__item-count-view-bag-container'>
        <ATBItemCountSubTotal atbResponse={atbResponse} />
        <ATBViewBagButton brandCodeUrls={brandCodeUrls} />
      </div>
      <MvgATBConfirmationMarketingContainer />
    </div>
  );
};
