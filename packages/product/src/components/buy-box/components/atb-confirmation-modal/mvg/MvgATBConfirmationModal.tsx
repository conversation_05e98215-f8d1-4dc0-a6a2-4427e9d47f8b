'use client';
import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Modal } from '@ecom-next/core/components/fabric/modal';
import { ATBConfirmationModalProps } from '../types';
import { useMvgAddToBagContext } from '../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider';
import { MvgATBConfirmationModalContent } from './MvgATBConfirmationModalContent';

export const MvgATBConfirmationModal = ({
  isOpen,
  closeButtonClickHandler,
  atbResponse,
}: Omit<
  ATBConfirmationModalProps,
  'brandName' | 'locale' | 'market' | 'abSeg' | 'cid' | 'brandCodeUrls' | 'displayPercentageOffFromCapi' | 'isPercentageEnabled' | 'recommendationsConfig'
>) => {
  const { localize } = useLocalize();
  const title = localize('pdp.atbConfirmation.title.addToBag.light');
  const closeModalAriaText = localize('pdp.closeModal.altText');
  const { isMobile } = useMvgAddToBagContext();

  return (
    <Modal
      closeButtonAriaLabel={closeModalAriaText}
      callbackFn={closeButtonClickHandler}
      headerContent={title}
      headerAlignment={'center'}
      isCrossBrand={true}
      isOpen={isOpen}
      hasSkinnyHeader={true}
      className={`atb-confirmation-modal-wrapper ${isMobile ? `h-auto w-[21.625rem]` : `max-h-[47.063rem] w-[45.688rem] `}`}
    >
      {atbResponse && <MvgATBConfirmationModalContent atbResponse={atbResponse} />}
    </Modal>
  );
};
