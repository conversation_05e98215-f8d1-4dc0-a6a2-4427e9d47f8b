'use client';

import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { InlineMarketing } from '@ecom-next/marketing-ui/product';
import React from 'react';
import { ScriptLoaderProvider } from '@pdp/packages/script-loader-provider';
import { CID } from '@ecom-next/marketing-ui';
import { CertonaProvider } from '@ecom-next/core/components/legacy/certona-provider';
import { ProductRecommendationsProvider } from '@ecom-next/sitewide/product-recs-provider';
import { useBrandAgnosticFeatureFlag } from '../../../../hooks/use-feature-flag';
import { PDPRecsCarouselWrapper } from '../../../pdp-recs-carousel-wrapper';
import { FeaturesConfig } from '../../../../pages/getFeatureConfig';

type ATBConfirmationMarketingContainerProps = {
  cid: CID;
  colorStyleNumber: string;
  isPercentageEnabled: boolean;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
};

const ATBConfirmationMarketingContainer = ({ cid, isPercentageEnabled, colorStyleNumber, recommendationsConfig }: ATBConfirmationMarketingContainerProps) => {
  const atbConfirmationMarketingData = {
    instanceName: 'insitu-modal-marketing',
    name: 'Optimize',
    type: 'product',
  };

  const enableATBConfirmationMarketingContent = useBrandAgnosticFeatureFlag('pdp-insitu-marketing-container');
  // eslint-disable-next-line no-console
  const errorLogger = console.error;
  const fallBackContent = (
    <>
      <InlineMarketing marketing={atbConfirmationMarketingData} cid={cid} />
      <BreakpointContext.Consumer>
        {({ greaterOrEqualTo }) => {
          if (greaterOrEqualTo(LARGE) || enableATBConfirmationMarketingContent) {
            return (
              <div className='recommendations-container relative z-0'>
                <CertonaProvider errorLogger={errorLogger} globalCertonaConfig={{ pagetype: 'ADDTOCART' }}>
                  <ScriptLoaderProvider>
                    <ProductRecommendationsProvider>
                      <PDPRecsCarouselWrapper
                        isPercentageEnabled={isPercentageEnabled}
                        recommendationsConfig={recommendationsConfig}
                        selectedCustomerChoiceId={colorStyleNumber}
                        pageType='ADDTOCART'
                      />
                    </ProductRecommendationsProvider>
                  </ScriptLoaderProvider>
                </CertonaProvider>
              </div>
            );
          }
          return null;
        }}
      </BreakpointContext.Consumer>
    </>
  );

  return fallBackContent;
};

export default ATBConfirmationMarketingContainer;
