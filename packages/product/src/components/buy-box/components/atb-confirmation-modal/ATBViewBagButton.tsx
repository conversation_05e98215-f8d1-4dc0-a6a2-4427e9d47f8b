import React from 'react';
import { Button } from '@ecom-next/core/components/fabric/button';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { AppConfig } from '@ecom-next/sitewide/state-builder-configs';
import { focusFirstElement, shoppingBagUrl } from './helpers/utility';

type ATBViewBagButtonProps = {
  brandCodeUrls: AppConfig['brandCodeUrls'];
};

export const ATBViewBagButton = ({ brandCodeUrls }: ATBViewBagButtonProps) => {
  const { localize } = useLocalize();
  const buttonViewBag = localize('pdp.atbConfirmation.button.viewBag');
  return (
    <a
      aria-label='checkout'
      data-testid='checkoutLink'
      href={shoppingBagUrl(brandCodeUrls)}
      onKeyDown={(event: any) => focusFirstElement(event, brandCodeUrls)} //eslint-disable-line
      className='atb-confirmation-modal__view-bag-link'
    >
      <div className='atb-confirmation-modal__buttons-wrapper'>
        <div className='atb-confirmation-modal__button'>
          <Button
            aria-hidden='true'
            data-testid='checkoutButton'
            id='checkoutButton'
            type='button'
            className='atb-confirmation-modal__view-bag-button'
            kind='critical'
          >
            {buttonViewBag}
          </Button>
        </div>
      </div>
    </a>
  );
};
