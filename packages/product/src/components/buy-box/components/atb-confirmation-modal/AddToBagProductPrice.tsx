'use Client';

import { AddToBagProductPriceProps } from './types';

export const AddToBagProductPrice = ({
  isSalePrice,
  localizedRegularPrice,
  isPercentageEnabled,
  percentageOffText,
  localizedSalePrice,
}: AddToBagProductPriceProps) => {
  return (
    <div className='atb-confirmation-modal-price-wrapper'>
      {isSalePrice && (
        <div className={`atb-confirmation-modal-sale-price-wrapper`}>
          <span aria-hidden='true' className={'atb-confirmation-modal-regular-price-strike-through line-through'} data-testid='sale-list-price'>
            {localizedRegularPrice}
          </span>
          <span className='atb-confirmation-modal-sale-price-wrapper' data-testid='sale-price'>
            {isPercentageEnabled ? (
              <span className='atb-confirmation-modal__percent-off ml-1'>{percentageOffText}</span>
            ) : (
              <span className='atb-confirmation-modal-sale-price ml-1'>{localizedSalePrice}</span>
            )}
          </span>
          {isPercentageEnabled && <h4 className='atb-confirmation-modal-sale-price'>{localizedSalePrice}</h4>}
        </div>
      )}
      {!isSalePrice && (
        <h4 data-testid='list-price' className='atb-confirmation-modal-regular-price'>
          {localizedRegularPrice}
        </h4>
      )}
    </div>
  );
};
