'use client';

import { ChevronUpIcon } from '@ecom-next/core/components/fabric/icons';

export function AssemblyInstructions({
  assemblyInstructionsLink,
  assemblyInstructionsTitle,
}: {
  assemblyInstructionsLink: string;
  assemblyInstructionsTitle: string;
}): JSX.Element | null {
  return (
    <div data-testid='pdp-assembly-instructions' className='pdp-assembly-instructions'>
      <a target='_blank' rel='noreferrer' href={assemblyInstructionsLink} className='flex items-center justify-between normal-case'>
        {assemblyInstructionsTitle}
        <ChevronUpIcon className='toggle-icon chevron rotate-90 fill-black' />
      </a>
    </div>
  );
}
