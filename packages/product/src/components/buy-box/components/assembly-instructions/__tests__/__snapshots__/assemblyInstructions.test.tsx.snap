// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<AssemblyInstructions /> matches snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="pdp-assembly-instructions"
      data-testid="pdp-assembly-instructions"
    >
      <a
        class="flex items-center justify-between normal-case"
        href="gap.com/assembly-instructions.pdf"
        rel="noreferrer"
        target="_blank"
      >
        Assembly Instructions
        <svg
          aria-hidden="true"
          class="toggle-icon chevron rotate-90 fill-black"
          data-testid="chevron-up-icon"
          fill="none"
          height="6"
          stroke-width="2"
          viewBox="0 0 12 6"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M9.99731e-09 5.16164L5 -4.17213e-07L10 5.16164L9.1879 6L5 1.67671L0.812103 6L9.99731e-09 5.16164Z"
            fill-rule="evenodd"
          />
        </svg>
      </a>
    </div>
  </div>
</div>
`;
