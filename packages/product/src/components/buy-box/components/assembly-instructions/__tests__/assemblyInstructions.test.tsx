import { screen } from '@testing-library/react';
import { AssemblyInstructions } from '../assemblyInstructions';
import { wrapInTestApp } from '../../../../../test-utils/appWrapper';

describe('<AssemblyInstructions />', () => {
  const defaultProps = {
    assemblyInstructionsLink: 'gap.com/assembly-instructions.pdf',
    assemblyInstructionsTitle: 'Assembly Instructions',
  };

  it('renders assembly instructions link with correct props', () => {
    wrapInTestApp(<AssemblyInstructions {...defaultProps} />);

    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', defaultProps.assemblyInstructionsLink);
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noreferrer');
  });

  it('renders the title text from props', () => {
    wrapInTestApp(<AssemblyInstructions {...defaultProps} />);
    expect(screen.getByText(defaultProps.assemblyInstructionsTitle)).toBeInTheDocument();
  });

  it('renders chevron icon with correct classes', () => {
    wrapInTestApp(<AssemblyInstructions {...defaultProps} />);
    const icon = screen.getByRole('link').querySelector('.chevron');
    expect(icon).toHaveClass('toggle-icon', 'chevron', 'rotate-90', 'fill-black');
  });

  it('matches snapshot', () => {
    const { container } = wrapInTestApp(<AssemblyInstructions {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });
});
