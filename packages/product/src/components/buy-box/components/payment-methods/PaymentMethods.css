.product_payments_container {
  display: flex;
  flex-direction: column;
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size--2');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-heavier');
}

.product_payments__message {
  display: flex;
  justify-content: space-between;
  color: theme('colors.color-type-copy');
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size--2');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-heavier');
  line-height: 100%;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
  vertical-align: middle;
  white-space: nowrap;
}

.product_payments__caption {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.utk-spacing-3xs');
  font-family: theme('fontFamily.brand-base') !important;
  font-size: theme('fontSize.font-size--2') !important;
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-heavier') !important;
}

.pp-learn-more-link {
  background: none;
  border: none;
  cursor: pointer;
}

.ap-learn-more-link {
  background: none;
  border: none;
  cursor: pointer;
}
