import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import MVGPaymentMethodsClient from '../MVGPaymentMethodsClient';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider/index';

jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: jest.fn(() => ({
    localize: jest.fn(),
  })),
}));

jest.mock('../../../../../providers/mvg-buybox-provider/index', () => ({
  useMVGBuyBoxStore: jest.fn(),
}));

jest.mock('zustand/shallow', () => ({
  useShallow: jest.fn(selector => selector),
}));

jest.mock('../../../../../legacy/src/app/helpers/script-helper', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(url => ({
    url: url,
    scriptAlreadyExists: jest.fn().mockReturnValue(false),
    addScriptToPage: jest.fn(attrs => {
      if (attrs.onload) {
        setTimeout(() => {
          window.presentAfterpay = jest.fn().mockReturnValue({
            modal: {
              modalOpenElement: '.ap-learn-more-link',
              open: jest.fn(),
            },
          });
          attrs.onload();
        }, 100);
      }
      return true;
    }),
  })),
}));

describe('MVGPaymentMethodsClient', () => {
  beforeEach(() => {
    useLocalize.mockReturnValue({
      localize: jest.fn(key => {
        switch (key) {
          case 'pdp.recommendedProduct.price':
            return '$40';
          case 'pdp.afterpay.afterpayEligibleContent':
            return '4 interest-free payments of $40 with';
          case 'pdp.afterpay.afterpayNotEligibleAboveMinThresholdContent':
            return 'available for orders above $40';
          default:
            return key;
        }
      }),
    });

    useMVGBuyBoxStore.mockImplementation(selector => {
      const state = {
        selectedCustomerChoice: {
          price: {
            max_effective_price: 100,
          },
        },
      };
      return selector(state);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete window.presentAfterpay;
  });

  const renderComponent = props => {
    return render(
      <MVGPaymentMethodsClient
        locale='en-US'
        displayName='Test Product'
        isGiftCard={false}
        customFeatures={{
          apEnabled: true,
          apHide: false,
          apThreshold: 50,
          ppEnabled: true,
          ppThreshold: 50,
        }}
        price={{
          currencySymbol: '$',
          localizedCurrencySymbol: '$',
        }}
        {...props}
      />
    );
  };

  it('should render the component with Afterpay and PayPal enabled', async () => {
    renderComponent({
      customFeatures: {
        apEnabled: true,
        apHide: false,
        apThreshold: 50,
        ppEnabled: true,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.getByTestId('product-payments-container')).toBeInTheDocument();
      expect(screen.getByText('Afterpay')).toBeInTheDocument();
      expect(screen.getByText('PayPal')).toBeInTheDocument();
    });
  });

  it('should render the correct message when price is above threshold', async () => {
    renderComponent({
      customFeatures: {
        apEnabled: true,
        apHide: false,
        apThreshold: 50,
        ppEnabled: true,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.getByText(/4 interest-free payments of \$40 with/)).toBeInTheDocument();
    });
  });

  it('should render the correct message when price is below threshold', async () => {
    useMVGBuyBoxStore.mockImplementation(selector => {
      const state = {
        selectedCustomerChoice: {
          price: {
            max_effective_price: 40,
          },
        },
      };
      return selector(state);
    });

    renderComponent({
      customFeatures: {
        apEnabled: true,
        apHide: false,
        apThreshold: 50,
        ppEnabled: true,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.getByText(/available for orders above \$40/)).toBeInTheDocument();
    });
  });

  it('should not render Afterpay or PayPal if both are disabled', async () => {
    renderComponent({
      customFeatures: {
        apEnabled: false,
        apHide: false,
        apThreshold: 50,
        ppEnabled: false,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.queryByText('Afterpay')).not.toBeInTheDocument();
      expect(screen.queryByText('PayPal')).not.toBeInTheDocument();
    });
  });

  it('should render only Afterpay if PayPal is disabled', async () => {
    renderComponent({
      customFeatures: {
        apEnabled: true,
        apHide: false,
        apThreshold: 50,
        ppEnabled: false,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.getByText('Afterpay')).toBeInTheDocument();
      expect(screen.queryByText('PayPal')).not.toBeInTheDocument();
    });
  });

  it('should render only PayPal if Afterpay is disabled', async () => {
    renderComponent({
      customFeatures: {
        apEnabled: false,
        apHide: false,
        apThreshold: 50,
        ppEnabled: true,
        ppThreshold: 50,
      },
    });

    await waitFor(() => {
      expect(screen.getByText('PayPal')).toBeInTheDocument();
      expect(screen.queryByText('Afterpay')).not.toBeInTheDocument();
    });
  });
});
