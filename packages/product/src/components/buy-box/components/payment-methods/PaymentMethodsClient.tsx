'use client';

import React, { useMemo, useEffect, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import type { ProductData } from '@pdp/types/product-data/style-level';
import ScriptHelper, { ScriptAttrs } from '../../../../legacy/src/app/helpers/script-helper';
import { useBuyBoxStore } from '../../../../providers/buybox-provider';
import { InfoIcon } from './info-icon';
import { afterpayJS, afterpayBaseConfig } from './afterpay-helper';

const scriptHelper = new ScriptHelper(afterpayJS);

type AfterpayInstance = {
  modal?: {
    modalOpenElement: string;
    open: () => void;
  };
};

const WrappedInfoIcon = ({ onClick, className }: { className: string; onClick: () => void }) => (
  <button
    className={className}
    onClick={onClick}
    onKeyDown={e => {
      if (e.key === 'Enter' || e.key === ' ') {
        onClick();
      }
    }}
    tabIndex={0}
    aria-label='Info'
  >
    <InfoIcon />
  </button>
);

const PaymentMethods = ({
  customFeatures,
  productData,
  locale,
  displayName,
}: {
  brandName: string;
  customFeatures: {
    apCategories: string | string[];
    apEnabled: string;
    apHide: boolean;
    apThreshold: string;
    ppEnabled: boolean;
    ppThreshold: string;
  };
  displayName: string;
  locale: Locale;
  market: string;
  productData: ProductData;
}) => {
  const [afterpayInstance, setAfterpayInstance] = useState({} as unknown as AfterpayInstance);
  const { priceAdapter, currencySymbol, price, isGiftCard } = productData;
  const { localize } = useLocalize();
  const [apScriptLoaded, setApScriptLoaded] = useState(false);
  const { selectedColor } = useBuyBoxStore(state => state);
  const selectedColorPrice = useMemo(() => selectedColor.price.currentMaxPrice, [selectedColor]);
  const selectedColorPriceCurrencySymbol = useMemo(() => selectedColor.price.currencySymbol, [selectedColor]);

  const afterpayConfig = afterpayBaseConfig({
    brandName: displayName,
    currencySymbol,
    locale,
    price: selectedColorPrice,
  });
  const afterpayOnLoad = () => {
    if (window?.presentAfterpay) {
      setApScriptLoaded(true);
      const afterpayInstanceTemp = new window.presentAfterpay(afterpayConfig);
      afterpayInstanceTemp.modal.modalOpenElement = '.ap-learn-more-link';
      setAfterpayInstance(afterpayInstanceTemp);
    }
  };
  /* eslint-disable */
  useEffect(() => {
    const scriptExists = scriptHelper.scriptAlreadyExists();
    if (!scriptExists) {
      scriptHelper.addScriptToPage({
        async: true,
        onload: afterpayOnLoad,
      } as {} as ScriptAttrs);
    }
  }, []);
  /* eslint-enable */
  const currentPrice = selectedColorPrice;
  const pricePerInstallment = (currentPrice / 4).toFixed(2);

  const localizedPrice = localize('pdp.recommendedProduct.price', {
    currencySymbol:
      selectedColorPriceCurrencySymbol || currencySymbol || priceAdapter.currencySymbol || price?.localizedCurrencySymbol || price?.currencySymbol,
    price: pricePerInstallment,
  });

  const thresholdLocalizedPrice = localize('pdp.recommendedProduct.price', {
    currencySymbol:
      selectedColorPriceCurrencySymbol || currencySymbol || priceAdapter.currencySymbol || price?.localizedCurrencySymbol || price?.currencySymbol,
    price: customFeatures.apThreshold || customFeatures.ppThreshold,
  });

  const afterpayKey = isGiftCard ? 'pdp.afterpay.giftCardAfterpayEligibleContent' : 'pdp.afterpay.afterpayEligibleContent';

  const messageAboveThreshold = localize(afterpayKey, {
    installments: localizedPrice,
  });

  const messageBelowThreshold = localize('pdp.afterpay.afterpayNotEligibleAboveMinThresholdContent', { minPrice: thresholdLocalizedPrice });

  return (
    <>
      {apScriptLoaded && (
        <div className='product_payments_container' data-testid='product-payments-container'>
          {currentPrice > (customFeatures.apThreshold || customFeatures.ppThreshold) ? (
            <div className='product_payments__message'>
              {customFeatures.ppEnabled && !customFeatures.apEnabled && (
                <div className='product_payments__caption'>
                  <div className='product_payments__threshold'>{messageAboveThreshold}</div>
                  <div className='product_payments__paypal'>PayPal</div>
                  <div className='pp-div'>
                    <button
                      aria-label='PayPal learn more'
                      className='pp-learn-more-link'
                      data-pp-amount={currentPrice}
                      data-pp-messagesmodal
                      data-pp-offer='GPL'
                      id='paypal-message-text'
                      onClick={e => {
                        e.preventDefault();
                      }}
                      tabIndex={0}
                    >
                      <InfoIcon />
                    </button>
                  </div>
                </div>
              )}
              {customFeatures.apEnabled && !customFeatures.ppEnabled && (
                <div className='product_payments__caption'>
                  <div className='product_payments__threshold'>{messageAboveThreshold}</div>
                  <>
                    <div className='product_payments__afterpay'>Afterpay</div>
                    <WrappedInfoIcon className='ap-learn-more-link' onClick={() => afterpayInstance?.modal?.open()} />
                  </>
                </div>
              )}
              {customFeatures.apEnabled && customFeatures.ppEnabled && (
                <div className='product_payments__caption'>
                  <div className='product_payments__threshold'>{messageAboveThreshold}</div>
                  <>
                    <div className='product_payments__paypal'>PayPal</div>
                    <div className='pp-div'>
                      <button
                        aria-label='PayPal learn more'
                        className='pp-learn-more-link'
                        data-pp-amount={currentPrice}
                        data-pp-messagesmodal
                        data-pp-offer='GPL'
                        id='paypal-message-text'
                        onClick={e => {
                          e.preventDefault();
                        }}
                        tabIndex={0}
                      >
                        <InfoIcon />
                      </button>
                    </div>
                  </>
                  <>
                    <span className='or-span'> or </span>
                    <>
                      <div className='product_payments__afterpay'>Afterpay</div>
                      <WrappedInfoIcon className='ap-learn-more-link' onClick={() => afterpayInstance?.modal?.open()} />
                    </>
                  </>
                </div>
              )}
            </div>
          ) : (
            <div className='product_payments__message'>
              {customFeatures.ppEnabled && !customFeatures.apEnabled && (
                <div className='product_payments__caption'>
                  <>
                    <div className='product_payments__paypal'>PayPal</div>
                    <div className='pp-div'>
                      <button
                        aria-label='PayPal learn more'
                        className='pp-learn-more-link'
                        data-pp-amount={currentPrice}
                        data-pp-messagesmodal
                        data-pp-offer='GPL'
                        id='paypal-message-text'
                        onClick={e => {
                          e.preventDefault();
                        }}
                        tabIndex={0}
                      >
                        <InfoIcon />
                      </button>
                    </div>
                    {messageBelowThreshold}
                  </>
                </div>
              )}
              {customFeatures.apEnabled && !customFeatures.ppEnabled && (
                <div className='product_payments__caption'>
                  <>
                    <div className='product_payments__afterpay'>Afterpay</div>
                    <WrappedInfoIcon className='ap-learn-more-link' onClick={() => afterpayInstance?.modal?.open()} />
                  </>
                  {messageBelowThreshold}
                </div>
              )}
              {customFeatures.apEnabled && customFeatures.ppEnabled && (
                <div className='product_payments__caption'>
                  <>
                    <div className='product_payments__paypal'>PayPal</div>
                    <div className='pp-div'>
                      <button
                        aria-label='PayPal learn more'
                        className='pp-learn-more-link'
                        data-pp-amount={currentPrice}
                        data-pp-messagesmodal
                        data-pp-offer='GPL'
                        id='paypal-message-text'
                        onClick={e => {
                          e.preventDefault();
                        }}
                        tabIndex={0}
                      >
                        <InfoIcon />
                      </button>
                    </div>
                  </>
                  <>
                    <span className='and-span'> and </span>
                    <div className='product_payments__afterpay'>Afterpay</div>
                    <WrappedInfoIcon className='ap-learn-more-link' onClick={() => afterpayInstance?.modal?.open()} />
                  </>
                  {messageBelowThreshold}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default PaymentMethods;
