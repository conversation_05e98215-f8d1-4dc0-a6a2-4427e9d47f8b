import pino from 'pino';
import { cacheableCapiDataV3Promise, cacheableFeaturesConfigMVG } from '../../../../pages/getReWrittenData';
import { getPageContextData } from '../../../../pages/getPageState';
import { CAPIV3TransformedData } from '../../../../pages/services/capi-aggregation-service';
import MVGPaymentMethodsClient from './MVGPaymentMethodsClient';

export const MVGPaymentMethods = async ({ requestParamString, pdpLogger }: { pdpLogger: pino.BaseLogger; requestParamString: string }) => {
  const { locale, displayName } = getPageContextData();

  const [featuresConfig, capiData] = await Promise.all([
    cacheableFeaturesConfigMVG(requestParamString, pdpLogger),
    cacheableCapiDataV3Promise(requestParamString, pdpLogger) as Promise<CAPIV3TransformedData>,
  ]);

  const { paymentMethods } = featuresConfig;
  const { selectedStyle } = capiData;

  const {
    afterPay: { apThreshold, apCategories, apHide, afterpayFeatureFlag },
    payPal: { paypalFeatureFlag, ppPaypal, ppThreshold },
  } = paymentMethods;

  const ppEnabled = paypalFeatureFlag && ppPaypal;
  const apEnabled =
    afterpayFeatureFlag &&
    !selectedStyle?.gift_card &&
    !apHide &&
    (apCategories === 'ALL' || (apCategories as string[]).includes(selectedStyle?.primary_category_id as string));

  const customFeatures = {
    apEnabled,
    apThreshold,
    apHide,
    ppEnabled,
    ppThreshold,
  };

  if ((!customFeatures.ppEnabled && !customFeatures.apEnabled) || selectedStyle?.isDropship) {
    return <></>;
  }

  return <MVGPaymentMethodsClient isGiftCard={selectedStyle?.gift_card ?? false} customFeatures={customFeatures} locale={locale} displayName={displayName} />;
};
