export const InfoIcon = () => (
  <svg height='14' width='14' viewBox='0 -3 14 17' xmlns='http://www.w3.org/2000/svg'>
    <g fill='none' fillRule='evenodd' transform='translate(1.2, -2)'>
      <circle cx='6' cy='6' r='6' stroke='#333' />
      <path
        d='M5.979 3.979a.66.66 0 00.459-.162.542.542 0 00.18-.423.556.556 0 00-.18-.428.65.65 0 00-.459-.166.65.65 0 00-.459.167.556.556 0 00-.18.427c0 .174.06.315.18.423a.66.66 0 00.459.162zm.513 5.193V4.753H5.457v4.419h1.035z'
        fill='#333'
        fillRule='nonzero'
      />
    </g>
  </svg>
);
