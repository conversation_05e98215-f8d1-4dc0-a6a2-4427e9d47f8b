'use client';

import React, { useMemo, useEffect, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useShallow } from 'zustand/shallow';
import ScriptHelper, { ScriptAttrs } from '../../../../legacy/src/app/helpers/script-helper';
import { useMVGBuyBoxStore } from '../../../../providers/mvg-buybox-provider/index';
import { InfoIcon } from './info-icon';
import { afterpayJS, afterpayBaseConfig } from './afterpay-helper';

const scriptHelper = new ScriptHelper(afterpayJS);

type AfterpayInstance = {
  modal?: {
    modalOpenElement: string;
    open: () => void;
  };
};

const WrappedInfoIcon = ({ onClick, className }: { className: string; onClick: () => void }) => (
  <button
    className={className}
    onClick={onClick}
    onKeyDown={e => {
      if (e.key === 'Enter' || e.key === ' ') {
        onClick();
      }
    }}
    tabIndex={0}
    aria-label='Info'
  >
    <InfoIcon />
  </button>
);

const PaymentMethodButton = ({
  label,
  className,
  onClick,
  icon,
}: {
  className: string;
  icon: React.ReactNode;
  label: string;
  onClick: (e: React.MouseEvent) => void;
}) => (
  <div className={className}>
    <button aria-label={`${label} learn more`} className='pp-learn-more-link' onClick={onClick} tabIndex={0}>
      {icon}
    </button>
  </div>
);

const MVGPaymentMethodsClient = ({
  locale,
  displayName,
  isGiftCard,
  customFeatures,
  price,
  priceAdapter,
}: {
  customFeatures: {
    apEnabled: boolean;
    apHide: boolean;
    apThreshold: number;
    ppEnabled: boolean;
    ppThreshold: number;
  };
  displayName: string;
  isGiftCard: boolean;
  locale: Locale;
  price?: {
    currencySymbol: string;
    localizedCurrencySymbol: string;
  };
  priceAdapter?: {
    currencySymbol: string;
  };
}) => {
  const { localize } = useLocalize();
  const currencySymbol = localize('pdp.currencySymbol');

  const { selectedCustomerChoice } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedCustomerChoice: state.selectedCustomerChoice,
    }))
  );

  const selectedColor = {
    price: {
      currentMaxPrice: selectedCustomerChoice?.price.max_effective_price as number,
      currencySymbol: currencySymbol,
    },
  };

  const [afterpayInstance, setAfterpayInstance] = useState({} as unknown as AfterpayInstance);
  const [apScriptLoaded, setApScriptLoaded] = useState(false);
  const selectedColorPrice = useMemo(() => selectedColor.price.currentMaxPrice, [selectedColor]);
  const selectedColorPriceCurrencySymbol = useMemo(() => selectedColor.price.currencySymbol, [selectedColor]);

  const afterpayConfig = afterpayBaseConfig({
    brandName: displayName,
    currencySymbol,
    locale,
    price: selectedColorPrice,
  });

  const afterpayOnLoad = () => {
    if (window?.presentAfterpay) {
      setApScriptLoaded(true);
      const afterpayInstanceTemp = new window.presentAfterpay(afterpayConfig);
      afterpayInstanceTemp.modal.modalOpenElement = '.ap-learn-more-link';
      setAfterpayInstance(afterpayInstanceTemp);
    }
  };
  useEffect(() => {
    const scriptExists = scriptHelper.scriptAlreadyExists();
    if (!scriptExists) {
      scriptHelper.addScriptToPage({
        async: true,
        onload: afterpayOnLoad,
      } as unknown as ScriptAttrs);
    }
  }, [afterpayOnLoad]);

  const currentPrice = selectedColorPrice;
  const pricePerInstallment = (currentPrice / 4).toFixed(2);

  const localizedPrice = localize('pdp.recommendedProduct.price', {
    currencySymbol: (selectedColorPriceCurrencySymbol ||
      currencySymbol ||
      priceAdapter?.currencySymbol ||
      price?.localizedCurrencySymbol ||
      price?.currencySymbol) as string,
    price: pricePerInstallment,
  });

  const thresholdLocalizedPrice = localize('pdp.recommendedProduct.price', {
    currencySymbol: (selectedColorPriceCurrencySymbol ||
      currencySymbol ||
      priceAdapter?.currencySymbol ||
      price?.localizedCurrencySymbol ||
      price?.currencySymbol) as string,
    price: customFeatures.apThreshold || customFeatures.ppThreshold,
  });

  const afterpayKey = isGiftCard ? 'pdp.afterpay.giftCardAfterpayEligibleContent' : 'pdp.afterpay.afterpayEligibleContent';

  const messageAboveThreshold = localize(afterpayKey, {
    installments: localizedPrice,
  });

  const messageBelowThreshold = localize('pdp.afterpay.afterpayNotEligibleAboveMinThresholdContent', {
    minPrice: thresholdLocalizedPrice,
  });

  const renderPayPalSection = () => (
    <>
      <div className='product_payments__paypal'>PayPal</div>
      <PaymentMethodButton label='PayPal' className='pp-div' onClick={(e: React.MouseEvent) => e.preventDefault()} icon={<InfoIcon />} />
    </>
  );

  const renderAfterpaySection = () => (
    <>
      <div className='product_payments__afterpay'>Afterpay</div>
      <WrappedInfoIcon className='ap-learn-more-link' onClick={() => afterpayInstance?.modal?.open()} />
    </>
  );

  const renderPaymentMethodsAboveThreshold = () => {
    if (customFeatures.ppEnabled && !customFeatures.apEnabled) {
      return (
        <div className='product_payments__caption'>
          {messageAboveThreshold}
          {renderPayPalSection()}
        </div>
      );
    }

    if (customFeatures.apEnabled && !customFeatures.ppEnabled) {
      return (
        <div className='product_payments__caption'>
          {messageAboveThreshold}
          {renderAfterpaySection()}
        </div>
      );
    }

    if (customFeatures.apEnabled && customFeatures.ppEnabled) {
      return (
        <div className='product_payments__caption'>
          {messageAboveThreshold}
          {renderPayPalSection()}
          <span className='or-span'> or </span>
          {renderAfterpaySection()}
        </div>
      );
    }

    return null;
  };

  const renderPaymentMethodsBelowThreshold = () => {
    if (customFeatures.ppEnabled && !customFeatures.apEnabled) {
      return (
        <div className='product_payments__caption'>
          {renderPayPalSection()}
          {messageBelowThreshold}
        </div>
      );
    }

    if (customFeatures.apEnabled && !customFeatures.ppEnabled) {
      return (
        <div className='product_payments__caption'>
          {renderAfterpaySection()}
          {messageBelowThreshold}
        </div>
      );
    }

    if (customFeatures.apEnabled && customFeatures.ppEnabled) {
      return (
        <div className='product_payments__caption'>
          {renderPayPalSection()}
          <span className='and-span'> and </span>
          {renderAfterpaySection()}
          {messageBelowThreshold}
        </div>
      );
    }

    return null;
  };

  return (
    <>
      {apScriptLoaded && (
        <div className='product_payments_container' data-testid='product-payments-container'>
          {currentPrice > (customFeatures.apThreshold || customFeatures.ppThreshold) ? (
            <div className='product_payments__message'>{renderPaymentMethodsAboveThreshold()}</div>
          ) : (
            <div className='product_payments__message'>{renderPaymentMethodsBelowThreshold()}</div>
          )}
        </div>
      )}
    </>
  );
};

export default MVGPaymentMethodsClient;
