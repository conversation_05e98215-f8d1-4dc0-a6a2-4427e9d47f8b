import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { getPageContext } from '@ecom-next/utils/server';
import { cacheableCapiDataPromise, cacheableEnabledFeaturesPromise, getUrlParamsString } from '../../../../pages/getReWrittenData';
import PaymentMethodsClient from './PaymentMethodsClient';

const AFTERPAY_FEATURE_NAME = 'pdp-afterpay';
const PAYPAL_FEATURE_NAME = 'pdp-paypal';

export const PaymentMethods = async ({ searchParams }: { searchParams: PageParams['searchParams'] }) => {
  const { brand, market, locale, displayName } = getPageContext();
  const requestParamString = getUrlParamsString(searchParams);
  const { productData } = await cacheableCapiDataPromise(requestParamString);
  const { primaryCategoryId, isGiftCard, isDropShip } = productData;
  const { enabledFeatures, featureVariables } = await cacheableEnabledFeaturesPromise(searchParams);

  const afterpayFeatureFlag = enabledFeatures[AFTERPAY_FEATURE_NAME] ?? false;
  const paypalFeatureFlag = enabledFeatures[PAYPAL_FEATURE_NAME] ?? false;

  const apFeatureVariables = featureVariables[AFTERPAY_FEATURE_NAME] ?? false;
  const ppFeatureVariables = featureVariables[PAYPAL_FEATURE_NAME] ?? false;

  const apThreshold = apFeatureVariables[`threshold-${market}-${brand}`];
  const apCategories = apFeatureVariables[`categories-${market}-${brand}`];
  const apHide = apFeatureVariables[`hide-${market}-${brand}`];
  const ppThreshold = ppFeatureVariables[`threshold-${market}-${brand}`];
  const ppPaypal = ppFeatureVariables[`paypal-${market}-${brand}`];
  const ppEnabled = paypalFeatureFlag && ppPaypal;
  const apEnabled = afterpayFeatureFlag && !isGiftCard && !apHide && (apCategories === 'ALL' || apCategories.includes(primaryCategoryId));

  const customFeatures = {
    apEnabled,
    apThreshold,
    apCategories,
    apHide,
    ppEnabled,
    ppThreshold,
  };

  if ((!ppEnabled && !apEnabled) || isDropShip) {
    return <></>;
  }

  return (
    <PaymentMethodsClient
      customFeatures={customFeatures}
      productData={productData}
      brandName={brand}
      locale={locale}
      market={market}
      displayName={displayName}
    />
  );
};
