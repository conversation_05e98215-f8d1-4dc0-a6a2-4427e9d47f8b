// @ts-nocheck
'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import React from 'react';
import type { ProductData } from '@pdp/types/product-data/style-level';
import { useDropShipData } from '../../../../hooks/use-dropship/use-dropship-data';

export const ExcludedFromPromotions = ({ productData }: { productData: ProductData }): JSX.Element => {
  const { localize } = useLocalize();
  const excludedFromPromotionsCopy = localize('pdp.excludedFromPromotions');
  const { excludedFromPromotions, isDropShip } = productData;
  const isDropShipEnabled = useDropShipData(isDropShip);
  return (
    <>
      {isDropShipEnabled && excludedFromPromotions && (
        <p className='excluded-from-promotions' data-testid='pdp-excluded-from-promotions'>
          {excludedFromPromotionsCopy}
        </p>
      )}
    </>
  );
};
