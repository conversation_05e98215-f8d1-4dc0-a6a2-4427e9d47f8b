'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import React from 'react';
import { useShallow } from 'zustand/shallow';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';

export const MvgExcludedFromPromotionsClient = ({ isDropShipEnabled }: { isDropShipEnabled: boolean }): JSX.Element => {
  const { localize } = useLocalize();
  const excludedFromPromotionsCopy = localize('pdp.excludedFromPromotions');
  const { selectedStyle } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedStyle: state.selectedStyle,
    }))
  );

  if (!isDropShipEnabled || (selectedStyle && selectedStyle.excluded_from_promotion === false)) {
    return <></>;
  }

  return (
    <p className='excluded-from-promotions' data-testid='pdp-excluded-from-promotions'>
      {excludedFromPromotionsCopy}
    </p>
  );
};
