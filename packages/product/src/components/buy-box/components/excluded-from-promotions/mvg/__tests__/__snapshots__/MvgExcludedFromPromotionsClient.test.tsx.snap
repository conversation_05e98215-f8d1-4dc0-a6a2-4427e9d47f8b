// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MvgExcludedFromPromotionsClient when \`isDropShipEnabled\` is false should NOT render the excluded from promotions message 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`MvgExcludedFromPromotionsClient when \`isDropShipEnabled\` is false should render the excluded from promotions message 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`MvgExcludedFromPromotionsClient when \`isDropShipEnabled\` is true should NOT render the excluded from promotions message 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`MvgExcludedFromPromotionsClient when \`isDropShipEnabled\` is true should render the excluded from promotions message 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="excluded-from-promotions"
      data-testid="pdp-excluded-from-promotions"
    >
      Excluded from Promotions
    </p>
  </div>
</div>
`;
