import { adaptedDataMock } from '../../../../../../pages/services/capi-aggregation-service/v3/__tests__/adaptedDataMock';
import { wrapMvgInTestApp } from '../../../../../../test-utils/appWrapper';
import { MvgExcludedFromPromotionsClient } from '../MvgExcludedFromPromotionsClient';
import { AdaptedStyle } from '../../../../../../pages/services/capi-aggregation-service';

describe('MvgExcludedFromPromotionsClient', () => {
  describe('when `isDropShipEnabled` is true', () => {
    it('should render the excluded from promotions message', () => {
      const { getByTestId, container } = wrapMvgInTestApp(<MvgExcludedFromPromotionsClient isDropShipEnabled />, {
        productData: {
          ...adaptedDataMock,
          selectedStyle: {
            ...adaptedDataMock.selectedStyle,
            excluded_from_promotion: true,
          } as AdaptedStyle,
        },
      });
      const excludedFromPromotionsMessage = getByTestId('pdp-excluded-from-promotions');
      expect(excludedFromPromotionsMessage).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
    it('should NOT render the excluded from promotions message', () => {
      const { queryByTestId, container } = wrapMvgInTestApp(<MvgExcludedFromPromotionsClient isDropShipEnabled />);
      const excludedFromPromotionsMessage = queryByTestId('pdp-excluded-from-promotions');
      expect(excludedFromPromotionsMessage).toBeNull();
      expect(container).toMatchSnapshot();
    });
  });
  describe('when `isDropShipEnabled` is false', () => {
    it('should render the excluded from promotions message', () => {
      const { queryByTestId, container } = wrapMvgInTestApp(<MvgExcludedFromPromotionsClient isDropShipEnabled={false} />);
      const excludedFromPromotionsMessage = queryByTestId('pdp-excluded-from-promotions');
      expect(excludedFromPromotionsMessage).toBeNull();
      expect(container).toMatchSnapshot();
    });
    it('should NOT render the excluded from promotions message', () => {
      const { queryByTestId, container } = wrapMvgInTestApp(<MvgExcludedFromPromotionsClient isDropShipEnabled={false} />, {
        productData: {
          ...adaptedDataMock,
          selectedStyle: {
            ...adaptedDataMock.selectedStyle,
            excluded_from_promotion: true,
          } as AdaptedStyle,
        },
      });
      const excludedFromPromotionsMessage = queryByTestId('pdp-excluded-from-promotions');
      expect(excludedFromPromotionsMessage).toBeNull();
      expect(container).toMatchSnapshot();
    });
  });
});
