'use client';

import { useCallback, useEffect, useState } from 'react';
import { useColorAvailabilityQuery } from '@product-page/hooks/use-availability';
import { adaptStyleInventoryResponse, InventoryStyleRaw } from '@product-page/capi-utils';
import { ProductAvailability } from '@pdp/types/product-data/product-availability';
import injectPasBopisData from '@product-page/pages/state-builder/pas-mvg-availability';
import { useBrandAgnosticFeatureFlag } from '@product-page/hooks/use-feature-flag';
import { AdaptedVariant } from '@product-page/pages/services/capi-aggregation-service/v3/types';
import { useShallow } from 'zustand/shallow';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';
import { FulfillmentWrapperType } from './types';

export const useMvgSelectionAvailability = ({
  selectedMultiVariantData,
  market,
  oidcUrl,
  brand,
  pid,
  storeId,
  bopisActive,
}: FulfillmentWrapperType & { bopisActive?: boolean; selectedMultiVariantData: AdaptedVariant; storeId?: string }) => {
  const {
    updateDeliveryLocationId,
    updateSelectedVariantAndDisplayedDimensions,
    updateBopisState,
    updateVariants,
    selectedStyle,
    selectedSku,
    variants,
    selectedCustomerChoice,
  } = useMVGBuyBoxStore(
    useShallow(state => ({
      updateDeliveryLocationId: state.updateDeliveryLocationId,
      updateSelectedVariantAndDisplayedDimensions: state.updateSelectedVariantAndDisplayedDimensions,
      updateBopisState: state.updateBopisState,
      updateVariants: state.updateVariants,
      selectedStyle: state.selectedStyle,
      selectedSku: state.selectedSku,
      variants: state.variants,
      selectedCustomerChoice: state.selectedCustomerChoice,
    }))
  );

  const [availabilityLoaded, setAvailabilityLoaded] = useState(false);

  const oosConsideredBopisFlag = useBrandAgnosticFeatureFlag('pdp-oos-considered-bopis');

  const colorAvailabilityQuery = useColorAvailabilityQuery({ brand, clientId: 'product-page', market, oidcUrl });
  const updateBopisInventory = useCallback(
    (data: ProductAvailability) => {
      const result = injectPasBopisData({
        apiData: data,
        selectedCustomerChoice,
        selectedSku,
        selectedMultiVariantData,
        updateVariant: oosConsideredBopisFlag,
        variants,
        selectedStyle: selectedStyle?.style_id,
      });
      if (result) {
        updateVariants(result.variants, variants);
        updateSelectedVariantAndDisplayedDimensions(result.selectedCustomerChoice, result.selectedMultiVariantData);
      }
      setAvailabilityLoaded(true);
    },
    [selectedCustomerChoice, selectedSku, selectedMultiVariantData.style_id]
  );

  const callInventory = useCallback(
    (selectedStoreId: string) => {
      colorAvailabilityQuery(selectedStoreId, selectedStyle?.primary_style_id || '')
        .then(data => {
          const adaptedData = adaptStyleInventoryResponse(data as InventoryStyleRaw);
          updateBopisInventory(adaptedData as ProductAvailability);
        })
        .catch(e => {
          setAvailabilityLoaded(true);
          // eslint-disable-next-line no-console
          console.error('colorAvailabilityQuery error: ', e);
        });
    },
    [colorAvailabilityQuery, pid, updateBopisInventory]
  );

  useEffect(() => {
    if (storeId) {
      setAvailabilityLoaded(false);
      callInventory(storeId);
    } else {
      setAvailabilityLoaded(true);
    }
  }, [storeId, selectedStyle?.primary_style_id]);

  useEffect(() => {
    if (availabilityLoaded) {
      updateBopisState();
    }
  }, [selectedSku, selectedCustomerChoice, availabilityLoaded]);

  useEffect(() => {
    bopisActive && storeId ? updateDeliveryLocationId(storeId) : updateDeliveryLocationId('');
  }, [storeId, bopisActive]);

  return availabilityLoaded;
};
