'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { SelectorTile as TileSelectorComponent } from '@ecom-next/core/fabric/selector-tile';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePersonalization } from '@pdp/src/app/hooks/use-personalization';
import { ReportEasyEnrollLinkClickType } from '@product-page/components/product-page/collaborators/pdp-reporter';
import { usePDPReporter } from '@product-page/providers/pdp-reporter-provider';
import { isFullyQualifiedSize } from '@product-page/providers/buybox-provider/helpers/isFullyQualifiedSize';
import { useBopisData } from '@pdp/src/app/hooks/use-bopis-data';
import { useBuyBoxStore } from '@product-page/providers/buybox-provider';
import { ProductStore } from '@ecom-next/core/components/migration/bopis/adapters/store-list';
import { Brands } from '@ecom-next/core/react-stitch';
import { ProductSku } from '../dimension-group/types';
import { FulfillmentClientProps, FulfillmentWrapperType } from './collaborators/types';
import { useSelectionAvailability } from './collaborators/use-selection-availability';
import { DoneParamsType, StoreModalClient } from './StorageModalClient';
import { BackorderMessage } from './BackorderMessage';

const getAvailabilityClass = (status: boolean, isSelected: boolean) => {
  return `availability-status ${isSelected ? (status ? 'in-stock-msg' : 'out-stock-msg') : ''}`;
};

const FulfillmentComponent = ({
  url: targetUrl,
  translations: { freeForRewardsMember, freeShipping, inStock, inStore, join, or: orString, outOfStock, signIn, changeStore, selectStore, bopisOutOfStock },
  isShippingAvailable,
  bopisAvailable,
  isValidSizeSelection,
  personalizationContextData,
  reporter,
  locale,
  market,
  bopisData,
  brand,
  availabilityLoaded = true,
  postalCode,
  children,
  storeId,
  contentType,
  env,
  selectedSize,
  alert,
  handleUserFeedback,
  setTemporaryStore,
  onClose,
}: FulfillmentClientProps) => {
  const userIsNotSignedIn = !personalizationContextData?.userContext?.isLoggedInUser || personalizationContextData?.userContext?.isLoggedInUser === 'false';
  const targetUrlWithScapedCharacter = targetUrl?.replace('?', '\\?');
  const linkSignIn = (label: string, reportClickCustomParam: ReportEasyEnrollLinkClickType, className?: string) => (
    <a
      href={`/my-account/sign-in?targetURL=${targetUrlWithScapedCharacter}`}
      onClick={() => reporter?.reportEasyEnrollLinkClick(reportClickCustomParam)}
      rel='noopener noreferrer'
      target='_blank'
      className={className}
    >
      {label}
    </a>
  );

  const buyBoxSignInEasyEnrollType = { easy_enroll_type: 'Product : BuyBox : SignIn' };
  const buyBoxJoinEasyEnrollType = { easy_enroll_type: 'Product : BuyBox : Join' };

  const signInLink = linkSignIn(signIn, buyBoxSignInEasyEnrollType);
  const joinLink = linkSignIn(join, buyBoxJoinEasyEnrollType, 'fulfillment-join-text');

  const signOrJoinLink = useMemo(
    () => (
      <>
        <span className='with-underline'> {signInLink} </span>
        <span> {orString} </span>
        <span className='with-underline'> {joinLink} </span>
      </>
    ),
    [signInLink, orString, joinLink]
  );

  const loyaltyEnrollShippingMessage = () => {
    if (userIsNotSignedIn) {
      return (
        <div>
          <div className='fulfillment-rewards-members' data-testid='pdp-loyalty-rewards-members'>{`${freeForRewardsMember} `}</div>
          <span className='fulfillment-link-sign-in-join' data-testid='pdp-sign-or-join-links'>
            {signOrJoinLink}
          </span>
        </div>
      );
    }
    return <div className='fulfillment-rewards-members'>{freeForRewardsMember}</div>;
  };

  const {
    selectedStore: store,
    active: bopisActive,
    setActive,
    setPostalCode: bopisSetPostalCode,
    setStores: bopisSetStores,
    setSelectedStore: bopisSetSelectedStore,
    setEnabled,
  } = bopisData;

  const [selectedStore, setSelectedStore] = useState<ProductStore | null>(null);

  useEffect(() => {
    if (store) {
      setSelectedStore(store);
    }
  }, [store]);

  const [isOpenStoreChange, setIsOpenStoreChange] = useState(false);
  const isBackOrder = selectedSize?.inventoryStatusId === 4;

  const handleOpenModal = () => {
    setIsOpenStoreChange(true);

    if (selectedStore) {
      setTemporaryStore(selectedStore);
    }
  };

  const handleClick = (isBopis: boolean) => {
    if (alert) {
      handleUserFeedback();
    }

    if (isBopis) {
      if (isValidSizeSelection) {
        if (selectedStore) {
          setActive(isBopis);
        } else {
          handleOpenModal();
          return;
        }
      } else {
        handleUserFeedback({ mappedFeedback: 'fulfillment', kind: 'negative', customData: { id: 'bopis' } });
      }
    } else {
      setActive(isBopis);
    }
  };

  const handleClickSelectStore = () => {
    if (alert) {
      handleUserFeedback();
    }

    if (isValidSizeSelection) {
      handleOpenModal();
    } else {
      handleUserFeedback({ mappedFeedback: 'fulfillment', kind: 'negative', customData: { id: 'bopis' } });
    }
  };

  const handleDoneToggle = useCallback(
    ({ zipCode, selectedStore: newSelectedStore, stores }: DoneParamsType) => {
      if (zipCode !== '') {
        bopisSetPostalCode(zipCode);
      }

      if (stores.length > 0) {
        bopisSetStores(stores);
      }

      if (newSelectedStore?.storeId !== selectedStore?.storeId) {
        bopisSetSelectedStore(newSelectedStore);
        setEnabled(true);
      }

      if (newSelectedStore) {
        setActive(true);
      }
    },
    [setEnabled, setActive, bopisSetSelectedStore, bopisSetPostalCode, bopisSetStores]
  );

  const isShippingSelected = !bopisActive && availabilityLoaded;
  const isBopisSelected = !!(isValidSizeSelection && bopisActive && selectedStore);
  let selectedStoreName = selectedStore?.storeName || '';
  const isGap = (brandName: string) => brandName === Brands.Gap || brandName === Brands.GapFactoryStore;

  if (isGap(brand) && selectedStoreName) {
    selectedStoreName = selectedStoreName.toLowerCase();
  }

  return (
    <div className='pdp_fulfillment-container flex w-full justify-between gap-x-2' data-testid='fulfillment-container'>
      <TileSelectorComponent
        checked={isShippingSelected}
        data-testid='pdp-fulfillment-shipping'
        data-selected={isShippingSelected}
        id='shipping'
        group='group1'
        disabled={!availabilityLoaded}
        singleSelect
        className='w-full'
        onClick={() => handleClick(false)}
        onChange={() => {}}
      >
        <div className='flex h-full flex-col justify-between gap-4'>
          <div>
            <div className='fulfillment-title'>{freeShipping}</div>
            {loyaltyEnrollShippingMessage()}
          </div>
          {isValidSizeSelection && isBackOrder ? (
            <div className='backorder-message' data-testid='backorder-message'>
              <BackorderMessage date={selectedSize?.backOrderDate} />
            </div>
          ) : (
            <div
              className={`${getAvailabilityClass(isShippingAvailable, !bopisActive)} fulfillment-store-sentence-case-text`}
              data-testid='shipping-inventory-status'
            >
              {isValidSizeSelection && (isShippingAvailable ? inStock : outOfStock)}
            </div>
          )}
        </div>
      </TileSelectorComponent>

      <TileSelectorComponent
        checked={isBopisSelected}
        disabled={!availabilityLoaded}
        id='pickup'
        group='group1'
        singleSelect
        className='w-full'
        data-testid='pdp-fulfillment-pickup'
        data-selected={isBopisSelected}
        onClick={() => handleClick(true)}
        onChange={() => {}}
      >
        <div className='flex h-full flex-col justify-between gap-4'>
          <div>
            <div className='fulfillment-title'>{inStore}</div>
            <div className='leading-[10px]'>
              {selectedStore && (
                <div className='store-name' data-testid='store-name'>
                  {selectedStoreName}
                </div>
              )}
              <button
                className='fulfillment-link fulfillment-store-sentence-case-text'
                onClick={handleClickSelectStore}
                data-testid='change-store-modal-link'
                disabled={!availabilityLoaded}
              >
                {selectedStore ? changeStore : selectStore}
              </button>
            </div>
          </div>

          <div className={`${getAvailabilityClass(bopisAvailable, !!bopisActive)} fulfillment-store-sentence-case-text`} data-testid='pickup-inventory-status'>
            {availabilityLoaded && isValidSizeSelection && selectedStore && (bopisAvailable ? inStock : bopisOutOfStock)}
          </div>
        </div>
      </TileSelectorComponent>
      <StoreModalClient
        locale={locale}
        market={market}
        brand={brand}
        postalCode={postalCode}
        storeId={storeId}
        setSelectedStore={setTemporaryStore}
        setIsOpenStoreChange={setIsOpenStoreChange}
        isOpenStoreChange={isOpenStoreChange}
        onDone={handleDoneToggle}
        contentType={contentType}
        selectedSize={selectedSize}
        env={env}
        onClose={onClose}
      >
        {children}
      </StoreModalClient>
    </div>
  );
};

export const FulfillmentClient = React.memo(FulfillmentComponent);

const FulfillmentWrapper = (
  props: Pick<FulfillmentClientProps, 'url' | 'locale' | 'market' | 'brand' | 'contentType' | 'env'> & FulfillmentWrapperType & { children: React.ReactNode }
) => {
  const { localize } = useLocalize();
  const freeShipping = props?.isHuiRampBlockersFix ? localize('pdp.freeShipping.title') : localize('pdp.fulfillmentMethod.freeShipping');
  const inStore = localize('pdp.fulfillmentMethod.insStore');
  const inStock = localize('changeStoreModal.inStock');
  const outOfStock = localize('changeStoreModal.outOfStock');
  const bopisOutOfStock = localize('changeStoreModal.bopisOutOfStock');
  const freeForRewardsMember = localize('pdp.fulfillmentMethod.rewardsMembers');
  const firstLetter = freeForRewardsMember[0].toLocaleLowerCase();
  const signIn = localize('pdp.fulfillmentMethod.rewardsSignIn');
  const join = localize('pdp.fulfillmentMethod.rewardsLinkJoin');
  const or = localize('pdp.conjunction.or');
  const changeStore = localize('changeStoreModal.openModalButton');
  const selectStore = localize('changeStoreModal.selectStore');

  const isShippingAvailable = useBuyBoxStore(state => state.isShippingAvailable);

  const selectedSize = useBuyBoxStore(state => state.selectedSize);
  const bopisAvailable = useBuyBoxStore(state => state.bopisAvailable);
  const handleUserFeedback = useBuyBoxStore(state => state.handleUserFeedback);
  const alert = useBuyBoxStore(state => state.alert);

  const selectedVariant = useBuyBoxStore(state => state.selectedVariant);
  const personalizationContextData = usePersonalization();
  const reporter = usePDPReporter() as FulfillmentClientProps['reporter'];

  const isValidSizeSelection = isFullyQualifiedSize(selectedVariant.dimensions);

  const bopisData = useBopisData();

  const { selectedStore, active: bopisActive, postalCode } = bopisData;

  const [temporaryStore, setTemporaryStore] = useState<typeof selectedStore>(selectedStore);

  const availabilityLoaded = useSelectionAvailability({
    selectedVariant,
    market: props.market,
    oidcUrl: props.oidcUrl,
    brand: props.brand,
    pid: props.pid,
    storeId: temporaryStore?.storeId || selectedStore?.storeId,
    bopisActive,
  });

  const resetInitialData = useCallback(() => {
    setTemporaryStore(selectedStore);
  }, [selectedStore]);

  const data = {
    translations: {
      freeShipping,
      inStore,
      inStock,
      outOfStock,
      or,
      signIn,
      join,
      freeForRewardsMember: `${firstLetter}${freeForRewardsMember.slice(1)}.`,
      changeStore,
      selectStore,
      bopisOutOfStock,
    },
    isShippingAvailable,
    bopisAvailable,
    selectedSize: selectedSize as ProductSku,
    personalizationContextData,
    reporter,
    isValidSizeSelection,
    bopisData,
    availabilityLoaded,
    locale: props.locale,
    market: props.market,
    brand: props.brand,
    postalCode: postalCode ? postalCode : '',
    storeId: selectedStore?.storeId ? selectedStore?.storeId : '',
    setTemporaryStore,
    children: props.children,
    contentType: props.contentType,
    env: props.env,
    alert,
    handleUserFeedback,
    onClose: resetInitialData,
  };

  return <FulfillmentClient {...props} {...data} />;
};

export default FulfillmentWrapper;
