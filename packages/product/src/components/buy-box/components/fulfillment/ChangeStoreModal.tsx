'use client';

import { useStoreFetch } from '@ecom-next/core/legacy/bopis';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Modal } from '@ecom-next/core/components/fabric/modal';

import { useUpdateEffect } from '@ecom-next/core/legacy/hooks';
import type { Dispatch, RefObject, SetStateAction } from 'react';
import { forwardRef, Fragment, useImperativeHandle, useMemo, useRef, useState } from 'react';
import type { InventorySkuApi } from '@pdp/packages/chas-utils';
import { adaptSkuInventoryResponse } from '@pdp/packages/chas-utils';
import HiddenHeading from '@pdp/packages/styles/utility/hidden-heading'; // NOSONAR
import { useSkuAvailabilityQuery } from '@pdp/packages/use-availability';
import { FixedFooter } from '@pdp/packages/change-store-modal/components/fixed-footer';
import { InStockFilter } from '@pdp/packages/change-store-modal/components/in-stock-filter';
import { getPickupTypesOptions } from '@pdp/packages/change-store-modal/components/pickup-type-filter';
import { StoreList } from '@pdp/packages/change-store-modal/components/store-list';
import { ZipCodeInput } from '@pdp/packages/change-store-modal/components/zip-code';
import type { EnvironmentConfig } from '@pdp/packages/change-store-modal/config/application';
import { getOIDCUrl } from '@pdp/packages/change-store-modal/helpers/get-urls';
import { getPropTranslations } from '@pdp/packages/change-store-modal/helpers/getPropTranslations';
import * as AppStateProviderHolder from '@ecom-next/sitewide/app-state-provider';

import type { ChangeStoreModalPropsType, ChangeStoreModalRefType, StoreAvailability, TranslationMapType } from '@pdp/packages/change-store-modal/types';
import { changeStoreModalStyles } from './styles/changeStorageModalStyle';

const filterCuberside = (stores: ProductStore[]) => Array.isArray(stores) && stores.filter(store => store?.activeFeatureToggles?.curbsidePickup);
const filterInStore = (stores: ProductStore[]) => Array.isArray(stores) && stores.filter(store => store?.activeFeatureToggles?.inStorePickup);

interface ChangeStorageModalType extends ChangeStoreModalPropsType {
  brandName: AbbrBrand;
  contentType: string;
  env: string;
  isOpen: boolean;
  market: string;
  ref?: RefObject<ChangeStoreModalRefType>;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}

// eslint-disable-next-line react/display-name
const ChangeStoreModal = forwardRef<ChangeStoreModalRefType, ChangeStorageModalType>((props, ref): JSX.Element => {
  const {
    children = null,
    LASConfig,
    onClose = () => {},
    onDone = () => {},
    onOpen = () => {},
    selectedStoreId,
    sku,
    translation = {} as TranslationMapType,
    zipCode,
    useStorage = false,
    onSelectStore = () => {},
    clientId,
    featureConfig = {
      isInStockFilterSquare: false,
      isSimpleStoreCard: false,
    },
    isOpen,
    env,
    brandName,
    market,
    contentType,
    setIsOpen,
  } = props;
  const storeAvailabilityOutOfStock = 'OUT_STOCK';
  const storeAvailabilityNotAvailable = 'NOT_AVAILABLE';
  const { localize } = useLocalize();
  const inStockMessage = localize(getPropTranslations('changeStoreModal.inStockFilter', translation));

  //env br us ecom stage
  const lasBrand = LASConfig.query?.brand as AbbrBrand;
  const lasMarket = LASConfig.query?.market as string;
  const defaultConfig = AppStateProviderHolder.useAppState().appConfig;
  const environmentConfig = AppStateProviderHolder.useAppState().appConfig;
  // @ts-ignore
  const finalConfig = { ...defaultConfig, ...environmentConfig } as EnvironmentConfig;
  const addToBagUrls = getOIDCUrl(finalConfig, env, brandName, market as Market, contentType);
  const hasChildren = !!children;

  // constants
  const LAS_ZIP_CODE_ERROR_MESSAGE = 'zipCode could not be found';

  const { getStores } = useStoreFetch({
    brand: lasBrand ?? brandName,
    market: lasMarket ?? market.toUpperCase(),
    options: {
      bopisCurbsideEnabled: true,
      domain: addToBagUrls.oidcUrl,
      storageEnabled: useStorage,
      ...LASConfig.query, // size & radius
    },
  });
  const skuAvailabilityQuery = useSkuAvailabilityQuery({
    brand: lasBrand ?? brandName,
    clientId,
    market,
    oidcUrl: addToBagUrls.oidcUrl,
  });
  // states
  const [postalCode, setPostalCode] = useState<string>(zipCode);
  const [outOfStockFiltered, setOutOfStockFilter] = useState<boolean>(false);
  const pickupFilterOptions = useMemo(() => getPickupTypesOptions(translation, localize), []); // [all types, cuberside, inStore]
  const [pickupSelected, _] = useState<string>(pickupFilterOptions[0]);
  const [expendedStoreCard, setExpendedStoreCard] = useState<string>('');
  const [stores, setStores] = useState<ProductStore[]>([]);
  const [selectedStore, setSelectedStore] = useState<ProductStore>();
  const [storesAvailability, setStoresAvailability] = useState<StoreAvailability[]>([]);
  const [errorCopyText, setErrorCopyText] = useState<string>('');
  const [tooltip, setTooltip] = useState<boolean>(false);
  const [fetchStatus, setFetchStatus] = useState<string>('complete');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // ref
  const storesInited = useRef(false);
  const prevPasSkuId = useRef('');
  const prevZipCode = useRef('');

  const initZip = useRef<string>(zipCode);
  const initSelectedStore = useRef<ProductStore | undefined>();
  const initStores = useRef<ProductStore[]>();

  const toggleTooltipClass = (): void => {
    setTooltip(!tooltip);
  };

  // i18n
  const closeModalText = localize(getPropTranslations('changeStoreModal.closeModal', translation));
  const modalTitle = localize(getPropTranslations('pdp.changeStoreModal.title', translation));
  const doneButtonText = localize(getPropTranslations('changeStoreModal.doneButton', translation));
  const zipCodeNotFoundMessage = localize(getPropTranslations('changeStoreModal.zipCodeNotFound', translation));
  const changeStoreUnavailableMessage = localize(getPropTranslations('changeStoreModal.changeStoreUnavailable', translation));
  const findStoreHeader = localize(getPropTranslations('changeStoreModal.findAStore', translation));
  const orderPickUpMessage = localize(getPropTranslations('changeStoreModal.orderPickupTime', translation));
  useImperativeHandle(
    ref,
    () => ({
      openModal() {
        setIsOpen(true);
      },
    }),
    []
  );

  const filterStores = useMemo(() => {
    const outOfStockStores = outOfStockFiltered
      ? stores.filter((store: ProductStore) => {
          return storesAvailability.find(
            (item: StoreAvailability) =>
              store.storeId === item.storeId &&
              item.inventoryStatusId !== storeAvailabilityOutOfStock &&
              item.inventoryStatusId !== storeAvailabilityNotAvailable
          );
        })
      : stores;

    const filterByPickupOption = {
      [pickupFilterOptions[0]]: () => outOfStockStores,
      [pickupFilterOptions[1]]: filterCuberside,
      [pickupFilterOptions[2]]: filterInStore,
    };
    const filteredStores = filterByPickupOption[pickupSelected](outOfStockStores);
    // @ts-ignore
    return filteredStores?.slice(0, 10);
  }, [outOfStockFiltered, pickupSelected, storesAvailability, stores]);

  useUpdateEffect(() => {
    if (fetchStatus === 'error') {
      const shouldDisplayZipCodeErrorMsg = errorMessage && errorMessage.includes(LAS_ZIP_CODE_ERROR_MESSAGE);

      if (shouldDisplayZipCodeErrorMsg) {
        setErrorCopyText(zipCodeNotFoundMessage);
      } else {
        setErrorCopyText(changeStoreUnavailableMessage);
      }
    } else {
      setErrorCopyText('');
    }
  }, [fetchStatus, errorMessage]);

  useUpdateEffect(() => {
    if (isOpen) {
      if (stores.length > 0 && selectedStore && selectedStoreId !== selectedStore.storeId) {
        const sStore = stores.find(store => store.storeId === selectedStoreId);
        setSelectedStore(sStore);
      }
    }

    if (isOpen && !storesInited.current) {
      setFetchStatus('loading');
      getStores(postalCode, !useStorage).then(({ postalCode: lasPostalCode, stores: lasStores, fetchStatus: lasFetchStatus, errorMessage }) => {
        if (lasPostalCode !== postalCode) {
          setPostalCode(lasPostalCode);
          prevZipCode.current = lasPostalCode;
        }
        storesInited.current = true;
        initZip.current = postalCode;
        initStores.current = lasStores;
        if (selectedStoreId) {
          initSelectedStore.current =
            lasStores.length > 0 && lasStores.find(store => store.storeId === selectedStoreId)
              ? lasStores.find(store => store.storeId === selectedStoreId)
              : undefined;
        }

        setFetchStatus(lasFetchStatus);
        setStores(lasStores);
        if (errorMessage) {
          setErrorMessage(errorMessage);
        }
      });
    }
  }, [isOpen]);

  useUpdateEffect(() => {
    if (storesInited.current) {
      setFetchStatus('loading');
      getStores(postalCode, true).then(({ stores: lasStores, fetchStatus: lasFetchStatus, errorMessage }) => {
        setFetchStatus(lasFetchStatus);
        setStores(lasStores);
        if (selectedStoreId && !initSelectedStore.current) {
          initSelectedStore.current =
            lasStores.length > 0 && lasStores.find(store => store.storeId === selectedStoreId)
              ? lasStores.find(store => store.storeId === selectedStoreId)
              : undefined;
        }
        if (errorMessage) {
          setErrorMessage(errorMessage);
        }
      });
    }
  }, [postalCode]);

  useUpdateEffect(() => {
    if (zipCode !== postalCode) {
      setPostalCode(zipCode);
      prevZipCode.current = zipCode;
    }
  }, [zipCode]);

  useUpdateEffect(() => {
    if (stores && selectedStoreId) {
      setSelectedStore(stores.find(store => store.storeId === selectedStoreId));
    }

    if (!selectedStoreId) {
      setSelectedStore(undefined);
    }
  }, [selectedStoreId]);

  useUpdateEffect(() => {
    if (isOpen && typeof onOpen === 'function') {
      onOpen();
    }
  }, [isOpen]);

  useUpdateEffect(() => {
    const storeIds = stores?.map((store: ProductStore) => store.storeId);
    if (storeIds && storeIds.length > 0 && isOpen) {
      // to disable useEffect type alarm
      const getAvailability = async (storeIds: string[]) => {
        const availability = (await skuAvailabilityQuery(storeIds, sku)) as InventorySkuApi;
        const adaptedAvailability = adaptSkuInventoryResponse(availability as InventorySkuApi);
        if (adaptedAvailability && Array.isArray(adaptedAvailability) && adaptedAvailability.length) {
          setStoresAvailability(adaptedAvailability as StoreAvailability[]);
        }
      };
      getAvailability(storeIds);
      prevPasSkuId.current = sku;
    }
  }, [sku, stores, isOpen]);

  useUpdateEffect(() => {
    if (stores && stores.length > 0 && !selectedStore) {
      setSelectedStore(selectedStoreId ? stores.find(store => store.storeId === selectedStoreId) : undefined);
    }
  }, [stores]);

  const closeModal = () => {
    setOutOfStockFilter(false);
    setExpendedStoreCard('');
    setErrorCopyText('');
    setFetchStatus('complete');
    setIsOpen(false);
  };

  const closeButtonClickHandler = () => {
    setPostalCode(initZip.current);
    prevZipCode.current = initZip.current;
    setSelectedStore(initSelectedStore.current);
    setStores(initStores.current || []);
    if (typeof onClose === 'function') {
      onClose();
    }
    closeModal();
  };

  const doneButtonClickHandler = () => {
    // if user have a selected store and user changed selected store in modal.
    initZip.current = postalCode;
    if (selectedStore?.storeId && selectedStore.storeId !== selectedStoreId) {
      initSelectedStore.current = selectedStore;
      initStores.current = stores;
      if (typeof onDone === 'function') {
        onDone({
          selectedStore,
          stores,
          zipCode: postalCode,
        });
      }
    } else {
      setPostalCode(initZip.current);
      prevZipCode.current = initZip.current;
      setSelectedStore(initSelectedStore.current);
      setStores(initStores.current!);
      if (typeof onDone === 'function') {
        onDone({
          selectedStore: selectedStore!,
          stores,
          zipCode: postalCode,
        });
      }
    }
    closeModal();
  };

  const footerMessageByAvailability = useMemo(() => {
    const hasAvailability =
      Array.isArray(filterStores) &&
      filterStores.some(store => {
        const availability = storesAvailability.find(item => item?.storeId === store?.storeId)?.availability;
        return availability === 'IN_STOCK' || availability === 'LOW_INVENTORY';
      });

    return hasAvailability ? orderPickUpMessage : '';
  }, [filterStores]);
  const { contentWrapperStyles, errorCopyStyles, visuallyHideStyles, doneButtonWrapperStyles, doneButtonStyles } = changeStoreModalStyles(hasChildren)();

  const changeStoreContent = (
    <div className={`${contentWrapperStyles()} data-testid='change-store-modal-content' grid w-full grid-cols-1 lg:w-auto lg:grid-cols-2`}>
      {hasChildren ? <div className='panel-details'>{children}</div> : null}
      <div className='panel-location'>
        <HiddenHeading headingLevel={3}>{findStoreHeader}</HiddenHeading>
        <ZipCodeInput
          onZipChange={zip => {
            if (zip.length > 0 && zip !== prevZipCode.current) {
              setPostalCode(zip);
              prevZipCode.current = zip;
            }
          }}
          zipcode={postalCode}
        />
        <InStockFilter isSquare={featureConfig.isInStockFilterSquare} label={inStockMessage} onInStockFilterChanged={checked => setOutOfStockFilter(checked)} />
        {fetchStatus === 'complete' && postalCode !== '' ? (
          <Fragment>
            <StoreList
              expendedStoreCard={expendedStoreCard}
              selectedStoreId={selectedStore ? selectedStore.storeId : ''}
              setExpendedStoreCard={setExpendedStoreCard}
              setSelectedStore={store => {
                setSelectedStore(store);
                onSelectStore(store);
              }}
              stores={filterStores}
              storesAvailability={storesAvailability}
              toggleTooltipClass={toggleTooltipClass}
              tooltip={tooltip}
            />
            {filterStores &&
              filterStores.map((store: ProductStore) => {
                const { storeId, storeName } = store;
                const selected = storeId === selectedStore?.storeId;
                return selected ? (
                  <span key={store.storeId} aria-live='polite' className={visuallyHideStyles()}>
                    {localize(getPropTranslations('changeStoreModal.selectedStore', translation), { storeName })}
                  </span>
                ) : null;
              })}
          </Fragment>
        ) : (
          <div className={errorCopyStyles()}>{errorCopyText}</div>
        )}
        {featureConfig.isFixedFooter ? (
          <FixedFooter doneButtonLabel={doneButtonText} message={footerMessageByAvailability} onDoneClick={doneButtonClickHandler} />
        ) : (
          <div className={doneButtonWrapperStyles()}>
            <button className={doneButtonStyles()} id='done-button' onClick={doneButtonClickHandler} type='button'>
              <h3>{doneButtonText}</h3>
            </button>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      closeButtonAriaLabel={closeModalText}
      headerAlignment={'left'}
      isOpen={isOpen}
      callbackFn={closeButtonClickHandler}
      headerContent={modalTitle}
      disablePortal={false}
      className='changeStoreModalWrapper'
    >
      {changeStoreContent}
    </Modal>
  );
});

export default ChangeStoreModal;
