'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import useModalIframeState from '../../modal-iframe/hooks/use-modal-iframe-state';
import { ModalIframe } from '../../modal-iframe/ModalIframe';

export function MvgSizeGuideModalWrapper(props: { isCentered: boolean; isFocusable?: boolean; url: string }): JSX.Element {
  const { localize } = useLocalize();
  const sizeGuideLocalization = localize('pdp.sizeGuide');

  const { url, isFocusable = true } = props;
  const { modalState, openModal, closeModal } = useModalIframeState({
    className: 'iframeSizeGuide',
    id: 'iframe-size-guide',
    src: url,
    title: sizeGuideLocalization,
  });

  const { modalTitle, modalOpen, modalIframeAttributes } = modalState;

  return (
    <>
      <button className='size-guide-button-text' data-testid='size-guide-button' onClick={() => openModal()} tabIndex={isFocusable ? 0 : -1} type='button'>
        {sizeGuideLocalization}
      </button>
      <ModalIframe iframeAttributes={modalIframeAttributes} isOpen={modalOpen} onClose={closeModal} title={modalTitle} />
    </>
  );
}
