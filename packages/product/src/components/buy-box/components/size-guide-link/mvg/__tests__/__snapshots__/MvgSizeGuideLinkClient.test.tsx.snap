// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MvgSizeGuideLinkClient when sizeGuideUrl exists should render correctly for at 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <button
      class="size-guide-button-text"
      data-testid="size-guide-button"
      tabindex="0"
      type="button"
    >
      Size Guide
    </button>
  </div>
</div>
`;

exports[`MvgSizeGuideLinkClient when sizeGuideUrl exists should render correctly for br 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <button
      class="size-guide-button-text"
      data-testid="size-guide-button"
      tabindex="0"
      type="button"
    >
      Size Guide
    </button>
  </div>
</div>
`;

exports[`MvgSizeGuideLinkClient when sizeGuideUrl exists should render correctly for gap 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <button
      class="size-guide-button-text"
      data-testid="size-guide-button"
      tabindex="0"
      type="button"
    >
      Size Guide
    </button>
  </div>
</div>
`;

exports[`MvgSizeGuideLinkClient when sizeGuideUrl exists should render correctly for on 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <button
      class="size-guide-button-text"
      data-testid="size-guide-button"
      tabindex="0"
      type="button"
    >
      Size Guide
    </button>
  </div>
</div>
`;

exports[`MvgSizeGuideLinkClient when the sizeGuideUrl is an empty string should not render 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;
