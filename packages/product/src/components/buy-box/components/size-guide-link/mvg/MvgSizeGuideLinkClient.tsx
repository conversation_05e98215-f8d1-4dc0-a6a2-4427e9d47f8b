'use client';

import { useShallow } from 'zustand/shallow';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';
import MvgSizeGuideButtonText from './MvgSizeGuideButton';

export const MvgSizeGuideLinkClient = () => {
  const { selectedStyle } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedStyle: state.selectedStyle,
    }))
  );

  return <MvgSizeGuideButtonText url={selectedStyle?.sizeGuideUrl ?? ''} />;
};
