'use client';

import React from 'react';
import getPreviewDate from '../../../../../util/getPreviewDate';
import { MvgSizeGuideModalWrapper } from './MvgSizeGuideModal';

type MvgSizeGuideButtonText = {
  isCentered?: boolean;
  isFocusable?: boolean;
  url: string;
};

const MvgSizeGuideButtonText = ({ url, isCentered = true, isFocusable = true }: MvgSizeGuideButtonText): JSX.Element | null => {
  if (!url) {
    return null;
  }
  const previewDate = getPreviewDate();
  const sizeGuideUrl = previewDate ? `${url}&previewDate=${previewDate}` : url;

  return <MvgSizeGuideModalWrapper isCentered={isCentered} isFocusable={isFocusable} url={sizeGuideUrl} />;
};

export default MvgSizeGuideButtonText;
