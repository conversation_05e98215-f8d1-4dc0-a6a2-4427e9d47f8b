import { Brands } from '@ecom-next/utils/server';
import { MvgSizeGuideLinkClient } from '../MvgSizeGuideLinkClient';
import { wrapMvgInTestApp } from '../../../../../../test-utils/appWrapper';
import { atMock } from '../../../../../../pages/services/capi-aggregation-service/v3/__fixtures__/atMock';
import { CapiV3AggregationServiceRaw } from '../../../../../../pages/services/capi-aggregation-service';

describe('MvgSizeGuideLinkClient', () => {
  describe('when sizeGuideUrl exists', () => {
    test.each([{ brand: Brands.Athleta }, { brand: Brands.OldNavy }, { brand: Brands.BananaRepublic }, { brand: Brands.Gap }])(
      'should render correctly for $brand',
      ({ brand }) => {
        const { container } = wrapMvgInTestApp(<MvgSizeGuideLinkClient />, {
          productData: { ...atMock, brand: brand, selectedStyle: { sizeGuideUrl: 'url' } } as unknown as CapiV3AggregationServiceRaw,
        });
        expect(container).toMatchSnapshot();
      }
    );
  });

  describe('when the sizeGuideUrl is an empty string', () => {
    test('should not render', () => {
      const { container } = wrapMvgInTestApp(<MvgSizeGuideLinkClient />, {
        productData: {
          ...atMock,
          selectedStyle: { sizeGuideUrl: '' },
        } as unknown as CapiV3AggregationServiceRaw,
      });
      expect(container).toMatchSnapshot();
    });
  });
});
