import React from 'react';
import { render, screen } from '@testing-library/react';
import MvgSizeGuideButtonText from '../MvgSizeGuideButton';
import getPreviewDate from '../../../../../../util/getPreviewDate';
import { MvgSizeGuideModalWrapper } from '../MvgSizeGuideModal';

jest.mock('../../../../../../util/getPreviewDate', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('../MvgSizeGuideModal', () => ({
  MvgSizeGuideModalWrapper: jest.fn().mockImplementation(props => <div data-testid='mock-modal-wrapper' data-props={JSON.stringify(props)} />),
}));

describe('MvgSizeGuideButtonText', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return null when url is not provided', () => {
    const { container } = render(<MvgSizeGuideButtonText url='' />);
    expect(container.firstChild).toBeNull();
  });

  it('should render MvgSizeGuideModalWrapper with correct props when url is provided without previewDate', () => {
    (getPreviewDate as jest.Mock).mockReturnValue(null);

    const testUrl = 'https://test.com/size-guide';
    render(<MvgSizeGuideButtonText url={testUrl} />);

    expect(MvgSizeGuideModalWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        isCentered: true,
        isFocusable: true,
        url: testUrl,
      }),
      expect.anything()
    );

    const modalWrapper = screen.getByTestId('mock-modal-wrapper');
    expect(modalWrapper).toBeInTheDocument();

    const passedProps = JSON.parse(modalWrapper.getAttribute('data-props') || '{}');
    expect(passedProps.url).toBe(testUrl);
    expect(passedProps.isCentered).toBe(true);
    expect(passedProps.isFocusable).toBe(true);
  });

  it('should render MvgSizeGuideModalWrapper with previewDate appended to url when available', () => {
    const mockPreviewDate = '2023-01-01';
    (getPreviewDate as jest.Mock).mockReturnValue(mockPreviewDate);

    const testUrl = 'https://test.com/size-guide';
    render(<MvgSizeGuideButtonText url={testUrl} />);

    expect(MvgSizeGuideModalWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        url: `${testUrl}&previewDate=${mockPreviewDate}`,
      }),
      expect.anything()
    );

    const modalWrapper = screen.getByTestId('mock-modal-wrapper');
    expect(modalWrapper).toBeInTheDocument();

    const passedProps = JSON.parse(modalWrapper.getAttribute('data-props') || '{}');
    expect(passedProps.url).toBe(`${testUrl}&previewDate=${mockPreviewDate}`);
  });

  it('should pass custom isCentered and isFocusable props to MvgSizeGuideModalWrapper', () => {
    (getPreviewDate as jest.Mock).mockReturnValue(null);

    const testUrl = 'https://test.com/size-guide';
    render(<MvgSizeGuideButtonText url={testUrl} isCentered={false} isFocusable={false} />);

    expect(MvgSizeGuideModalWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        isCentered: false,
        isFocusable: false,
        url: testUrl,
      }),
      expect.anything()
    );

    const modalWrapper = screen.getByTestId('mock-modal-wrapper');
    expect(modalWrapper).toBeInTheDocument();

    const passedProps = JSON.parse(modalWrapper.getAttribute('data-props') || '{}');
    expect(passedProps.isCentered).toBe(false);
    expect(passedProps.isFocusable).toBe(false);
  });
});
