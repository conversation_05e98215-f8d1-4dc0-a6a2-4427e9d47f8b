import { screen, fireEvent } from '@testing-library/react';
import { MvgSizeGuideModalWrapper } from '../MvgSizeGuideModal';
import { wrapMvgInTestApp } from '../../../../../../test-utils/appWrapper';
import { atMock } from '../../../../../../pages/services/capi-aggregation-service/v3/__fixtures__/atMock';
import { CapiV3AggregationServiceRaw } from '../../../../../../pages/services/capi-aggregation-service';

describe('MvgSizeGuideModalWrapper', () => {
  it('should render the size guide button and open the modal on click', () => {
    const url = 'https://test.com/size-guide';

    wrapMvgInTestApp(<MvgSizeGuideModalWrapper isCentered={true} url={url} />, {
      productData: { ...atMock } as unknown as CapiV3AggregationServiceRaw,
    });

    const button = screen.getByTestId('size-guide-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Size Guide');

    fireEvent.click(button);

    const iframe = screen.getByTitle('Size Guide');
    expect(iframe).toBeInTheDocument();
  });
});
