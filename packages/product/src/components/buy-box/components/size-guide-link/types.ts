export type SizeGuideProps = {
  brandName: string;
  enabledFeatures: EnabledFeatures;
  featureVariables: FeatureVariables;
  isDropShip: boolean;
  market: string;
  sizeGuideUrl: string;
};

type FeatureVariable = {
  name: string;
};

export type FeatureFlag = {
  featureVariables?: Record<string, FeatureVariable>;
  isBrandAgnostic: boolean;
  key: string;
};

export const FeatureFlags: Record<string, FeatureFlag> = {
  dropship: {
    featureVariables: {
      showBOPIS: { name: 'showBOPIS' },
      showDrapr: { name: 'showDrapr' },
      showFindMine: { name: 'showFindMine' },
      showOtherPaymentMethods: { name: 'showOtherPaymentMethods' },
      showSizeGuide: { name: 'showSizeGuide' },
      showTrueFit: { name: 'showTrueFit' },
    },
    isBrandAgnostic: false,
    key: 'pdp-dropship',
  },
};
