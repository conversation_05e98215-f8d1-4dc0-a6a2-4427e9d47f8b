'use client';

import React from 'react';
import getPreviewDate from '../../../../util/getPreviewDate';
import { SizeGuideModalWrapper } from './sizeGuideModal';

type SizeGuideButtonProps = {
  isCentered?: boolean;
  isFocusable?: boolean;
  url: string;
};

const SizeGuideButtonText = ({ url, isCentered = true, isFocusable = true }: SizeGuideButtonProps): JSX.Element | null => {
  if (!url) {
    return null;
  }
  const previewDate = getPreviewDate();
  const sizeGuideUrl = previewDate ? `${url}&previewDate=${previewDate}` : url;

  return <SizeGuideModalWrapper isCentered={isCentered} isFocusable={isFocusable} url={sizeGuideUrl} />;
};

export default SizeGuideButtonText;
