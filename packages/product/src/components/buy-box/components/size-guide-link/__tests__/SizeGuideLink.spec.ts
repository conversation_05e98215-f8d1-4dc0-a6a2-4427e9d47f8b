import { renderHook } from '@testing-library/react-hooks';
import { useDropShipData } from '../helpers/SizeGuideLink';

describe('useDropShipData', () => {
  const featureVariables = {
    'dropship-BR-GAP': { showSizeGuide: false },
    'dropship-BR-OLDNAVY': { showSizeGuide: true },
  };
  const enabledFeatures = {
    'dropship-BR-GAP': true,
    'dropship-BR-OLDNAVY': false,
  };

  it('should return showSizeGuide as true if there are no featureVariables', () => {
    const { result } = renderHook(() => useDropShipData(true, undefined, 'GAP', enabledFeatures, 'BR'));
    expect(result.current.showFeaturesForDropShip.showSizeGuide).toBe(true);
  });

  it('should return showSizeGuide according to featureVariable when dropship is enabled', () => {
    const { result } = renderHook(() => useDropShipData(true, featureVariables, 'GAP', enabledFeatures, 'BR'));
    expect(result.current.showFeaturesForDropShip.showSizeGuide).toBe(true);
  });

  it('should return showSizeGuide as true when dropship is not enabled', () => {
    const { result } = renderHook(() => useDropShipData(false, featureVariables, 'GAP', enabledFeatures, 'BR'));
    expect(result.current.showFeaturesForDropShip.showSizeGuide).toBe(true);
  });

  it('should return showSizeGuide as true if there are no dropShipFeatureVariables', () => {
    const { result } = renderHook(() => useDropShipData(true, {}, 'GAP', enabledFeatures, 'BR'));
    expect(result.current.showFeaturesForDropShip.showSizeGuide).toBe(true);
  });
});
