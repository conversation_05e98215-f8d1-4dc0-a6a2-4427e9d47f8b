import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SizeGuideModalWrapper } from '../sizeGuideModal';

jest.mock('../../../../../../../sitewide/src/providers/localization/index', () => ({
  useLocalize: () => ({ localize: (key: string) => (key === 'pdp.sizeGuide' ? 'Size Guide' : key) }),
}));

const openModalMock = jest.fn();
const closeModalMock = jest.fn();

jest.mock('../../modal-iframe/hooks/use-modal-iframe-state', () => () => ({
  modalState: {
    modalTitle: 'Size Guide',
    modalOpen: false,
    modalIframeAttributes: { src: 'https://example.com', title: 'Size Guide', id: 'iframe-size-guide', className: 'iframeSizeGuide' },
  },
  openModal: openModalMock,
  closeModal: closeModalMock,
}));

describe('SizeGuideModalWrapper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the button with the correct text', () => {
    render(<SizeGuideModalWrapper isCentered={true} url='https://example.com' />);
    expect(screen.getByTestId('size-guide-button')).toHaveTextContent('Size Guide');
  });

  it('should call openModal when the button is clicked', () => {
    render(<SizeGuideModalWrapper isCentered={true} url='https://example.com' />);
    fireEvent.click(screen.getByTestId('size-guide-button'));
    expect(openModalMock).toHaveBeenCalled();
  });

  it('should pass the props correctly to ModalIframe', () => {
    render(<SizeGuideModalWrapper isCentered={true} url='https://example.com' />);
    expect(screen.getByTestId('size-guide-button')).toBeInTheDocument();
  });

  it('should set tabIndex to -1 when isFocusable is false', () => {
    render(<SizeGuideModalWrapper isCentered={true} url='https://example.com' isFocusable={false} />);
    expect(screen.getByTestId('size-guide-button')).toHaveAttribute('tabindex', '-1');
  });
});
