import React from 'react';
import { render, screen } from '@testing-library/react';
import SizeGuideButtonText from '../sizeGuideButton';
import getPreviewDate from '../../../../../util/getPreviewDate';
import { SizeGuideModalWrapper } from '../sizeGuideModal';

jest.mock('../../../../../util/getPreviewDate');
jest.mock('../sizeGuideModal', () => ({
  SizeGuideModalWrapper: jest.fn(() => <div data-testid='size-guide-modal-wrapper' />),
}));

describe('SizeGuideButtonText', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render SizeGuideModalWrapper with the correct URL when previewDate is available', () => {
    (getPreviewDate as jest.Mock).mockReturnValue('2025-03-24');
    const url = 'https://example.com/size-guide';

    render(<SizeGuideButtonText url={url} />);

    expect(SizeGuideModalWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        url: `${url}&previewDate=2025-03-24`,
      }),
      {}
    );
    expect(screen.getByTestId('size-guide-modal-wrapper')).toBeInTheDocument();
  });

  it('should render SizeGuideModalWrapper with the correct URL when previewDate is not available', () => {
    (getPreviewDate as jest.Mock).mockReturnValue(null);
    const url = 'https://example.com/size-guide';

    render(<SizeGuideButtonText url={url} />);

    expect(SizeGuideModalWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        url,
      }),
      {}
    );
    expect(screen.getByTestId('size-guide-modal-wrapper')).toBeInTheDocument();
  });

  it('should not render SizeGuideModalWrapper when URL is not provided', () => {
    render(<SizeGuideButtonText url='' />);

    expect(SizeGuideModalWrapper).not.toHaveBeenCalled();
    expect(screen.queryByTestId('size-guide-modal-wrapper')).not.toBeInTheDocument();
  });
});
