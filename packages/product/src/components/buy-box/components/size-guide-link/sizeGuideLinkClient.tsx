'use client';

import { SizeGuideProps } from './types';
import { useDropShipData } from './helpers/SizeGuideLink';
import SizeGuideButtonText from './sizeGuideButton';

export const SizeGuideLink = ({ sizeGuideUrl, isDropShip, featureVariables, enabledFeatures, brandName, market }: SizeGuideProps) => {
  const {
    showFeaturesForDropShip: { showSizeGuide },
  } = useDropShipData(isDropShip, featureVariables, brandName, enabledFeatures, market);
  return showSizeGuide ? <SizeGuideButtonText url={sizeGuideUrl} /> : null;
};
