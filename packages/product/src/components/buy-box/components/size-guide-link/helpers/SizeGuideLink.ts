import { useCallback, useMemo } from 'react';
import { FeatureFlags, FeatureFlag } from '../types';

export const useDropShipData = (
  isDropShip: boolean,
  featureVariables: FeatureVariables,
  brandName: string,
  enabledFeatures: EnabledFeatures,
  market: string
) => {
  const dropShipEnabled = useFeature(FeatureFlags.dropship, enabledFeatures, brandName, market);
  const featureKey = useFeatureKey(FeatureFlags.dropship, brandName, market);
  const isDropShipEnabled = dropShipEnabled && isDropShip;
  const dropShipFeatureVariables = featureVariables ? featureVariables[featureKey] : undefined;

  const showFeature = useCallback(
    (featureVariable: boolean): boolean => {
      if (!dropShipFeatureVariables) {
        return true;
      }
      return isDropShipEnabled ? featureVariable : true;
    },
    [dropShipFeatureVariables, isDropShipEnabled]
  );

  return {
    showFeaturesForDropShip: {
      showSizeGuide: showFeature(dropShipFeatureVariables?.showSizeGuide),
    },
  };
};

function useFeatureKey(feature: FeatureFlag, brandName: string, market: string): string {
  const { key, isBrandAgnostic } = feature;
  return isBrandAgnostic ? key : `${key}-${market}-${brandName}`;
}

function useFeature(feature: FeatureFlag, enabledFeatures: EnabledFeatures, brandName: string, market: string): boolean {
  const featureKey = useFeatureKey(feature, brandName, market);
  return useMemo(() => !!enabledFeatures[featureKey], [enabledFeatures, featureKey]);
}
