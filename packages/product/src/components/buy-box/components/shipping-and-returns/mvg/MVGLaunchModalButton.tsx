'use client';

import React, { useState, ReactElement, CSSProperties, useCallback } from 'react';
import { createPortal } from 'react-dom';
import classnames from 'classnames';
import { Modal, ModalProps } from '@ecom-next/core/fabric/modal';

type MVGLaunchModalButtonProps = {
  'aria-labelledby'?: string;
  ariaLabelText?: string;
  children: (p: Record<string, unknown>) => ReactElement | null;
  classes?: Array<string>;
  id?: string;
  modalContainer?: Element;
  modalProps: Omit<ModalProps, 'isOpen' | 'onClose' | 'children'>;
  onClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent> | React.KeyboardEvent<HTMLDivElement>) => void;
  style?: CSSProperties;
  text: ReactElement;
};

export const MVGLaunchModalButton = ({
  'aria-labelledby': ariaLabelledById,
  ariaLabelText,
  children,
  classes = [],
  id,
  modalContainer,
  modalProps,
  onClick,
  style,
  text,
}: MVGLaunchModalButtonProps) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const activeStyle = classnames('launch-modal', classes);

  const toggleModal = useCallback(() => {
    setIsModalOpen(prev => !prev);
  }, []);

  const handleClickAndKeyPress = useCallback(
    (event: React.MouseEvent<HTMLDivElement, MouseEvent> | React.KeyboardEvent<HTMLDivElement>) => {
      if (
        event.type === 'keydown' &&
        (event as React.KeyboardEvent<HTMLDivElement>).key !== 'Enter' &&
        (event as React.KeyboardEvent<HTMLDivElement>).key !== ' '
      )
        return;

      setIsModalOpen(true);
      onClick?.(event);
    },
    [onClick]
  );

  const handleModalClose = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  if (text) {
    const modal = (
      <Modal isOpen={isModalOpen} callbackFn={handleModalClose} {...modalProps}>
        {children({ toggleModal })}
      </Modal>
    );
    return (
      <>
        {modalContainer ? createPortal(modal, modalContainer) : modal}
        <div
          aria-label={ariaLabelText || `${text} will launch a dialog window`}
          aria-labelledby={ariaLabelledById}
          className={activeStyle}
          id={id}
          onClick={handleClickAndKeyPress}
          onKeyDown={handleClickAndKeyPress}
          tabIndex={0}
          style={style}
          role='button'
        >
          {text}
        </div>
      </>
    );
  }
  return null;
};
