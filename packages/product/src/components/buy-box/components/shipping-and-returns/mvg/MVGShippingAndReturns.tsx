'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ChevronUpIcon } from '@ecom-next/core/components/fabric/icons';
import { MVGLaunchModalButton } from './MVGLaunchModalButton';

export function MVGShippingAndReturns({
  shippingAndReturnsLink,
  shippingAndReturnsTitle,
}: Readonly<{
  shippingAndReturnsLink: string;
  shippingAndReturnsTitle: string;
}>): JSX.Element | null {
  const { localize } = useLocalize();
  const title = localize(shippingAndReturnsTitle);

  return (
    <div data-testid='pdp-shipping-returns'>
      <MVGLaunchModalButton
        key={shippingAndReturnsTitle}
        modalProps={{
          closeButtonAriaLabel: localize('pdp.closeModal.altText'),
          hasSkinnyHeader: true,
        }}
        ariaLabelText={title}
        text={
          <div className='flex items-center justify-between'>
            {title}
            <ChevronUpIcon className='toggle-icon chevron rotate-90 fill-black' />
          </div>
        }
      >
        {() => <iframe className='iframe-returns-information' id='iframe' src={shippingAndReturnsLink} title={localize(shippingAndReturnsTitle)} />}
      </MVGLaunchModalButton>
    </div>
  );
}
