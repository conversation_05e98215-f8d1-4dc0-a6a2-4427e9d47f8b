import { Brands } from '@ecom-next/utils/server';
import { MVGShippingAndReturns } from '../MVGShippingAndReturns';
import { wrapMvgInTestApp } from '../../../../../../test-utils/appWrapper';
import { atMock } from '../../../../../../pages/services/capi-aggregation-service/v3/__fixtures__/atMock';
import { CapiV3AggregationServiceRaw } from '../../../../../../pages/services/capi-aggregation-service';

describe('MVGShippingAndReturns', () => {
  const props = {
    shippingAndReturnsLink: 'link',
    shippingAndReturnsTitle: 'label',
  };

  describe('when rendered', () => {
    test.each([{ brand: Brands.Athleta }, { brand: Brands.OldNavy }, { brand: Brands.BananaRepublic }, { brand: Brands.Gap }])(
      'should render correctly',
      ({ brand }) => {
        const { container } = wrapMvgInTestApp(<MVGShippingAndReturns {...props} />, {
          productData: { ...atMock, brand: brand } as unknown as CapiV3AggregationServiceRaw,
        });
        expect(container).toMatchSnapshot();
      }
    );
  });
});
