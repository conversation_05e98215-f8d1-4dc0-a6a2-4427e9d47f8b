.shipping-returns {
  .shipping-returns__link {
    font-family: theme('fontFamily.brand-base');
    font-size: theme('fontSize.font-size-b-m-font-size');
    line-height: theme('lineHeight.font-size-b-m-line-height');
    letter-spacing: theme('letterSpacing.font-tracking-b-regular-letter-spacing');
    color: theme('colors.color-font-link-critical');
    text-decoration: underline;
    font-style: normal;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
  }
}

.iframe-returns-information {
  @media (min-width: 64rem) {
    min-height: 34.688rem;
    min-width: 34.5rem;
  }

  @media (min-width: 48rem) and (max-width: 63.938rem) {
    min-height: 32.625rem;
    min-width: 28.25rem;
  }

  @media (max-width: 47.938rem) {
    min-height: 90vh;
    width: 88vw;
  }
}
