'use client';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ChevronUpIcon } from '@ecom-next/core/components/fabric/icons';
import { MVGLaunchModalButton } from './mvg/MVGLaunchModalButton';

export function ShippingAndReturns({
  shippingAndReturnsLink,
  shippingAndReturnsTitle,
}: Readonly<{
  shippingAndReturnsLink: string;
  shippingAndReturnsTitle: string;
}>): JSX.Element | null {
  const { localize } = useLocalize();

  return (
    <div data-testid='pdp-shipping-returns'>
      <MVGLaunchModalButton
        key={shippingAndReturnsTitle}
        modalProps={{
          closeButtonAriaLabel: localize('pdp.closeModal.altText'),
          hasSkinnyHeader: true,
        }}
        text={
          <div className='flex items-center justify-between'>
            {localize(shippingAndReturnsTitle)}
            <ChevronUpIcon className='toggle-icon chevron rotate-90 fill-black' />
          </div>
        }
      >
        {() => <iframe className='iframe-returns-information' id='iframe' src={shippingAndReturnsLink} title={localize(shippingAndReturnsTitle)} />}
      </MVGLaunchModalButton>
    </div>
  );
}
