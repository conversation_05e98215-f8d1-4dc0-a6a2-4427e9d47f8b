.dropship-shipping-and-returns {
  display: flex;
  padding: var(--spacing-200) 0;
  flex-direction: column;
  align-items: flex-start;
  width: 22.375rem;

  & p {
    align-self: stretch;
    margin-bottom: theme('padding.padding-inline-2x-small');
    display: flex;
    flex-direction: column;
  }

  .dropship-shipping-and-returns__vendor-name {
    color: theme('colors.color-font-default');
    font-family: theme('fontFamily.brand-base');
    font-size: theme('fontSize.font-size--2');
    font-style: normal;
    font-weight: theme('fontWeight.font-weight-base-heavier');
    line-height: normal;
    letter-spacing: theme('letterSpacing.font-letter-spacing-base');
    display: flex;
    flex-direction: row;
    gap: theme('gap.utk-spacing-2xs');
    align-items: center;

    .dropship-shipping-and-returns-vendor-text {
      text-transform: capitalize;
    }
  }

  .dropship-shipping-and-returns__flags {
    color: theme('colors.color-font-default');
    font-family: theme('fontFamily.brand-base');
    font-size: theme('fontSize.font-size--2');
    font-style: normal;
    font-weight: theme('fontWeight.font-weight-base-heavier');
    line-height: normal;
    letter-spacing: theme('letterSpacing.font-letter-spacing-base');
    text-transform: lowercase;
    &::first-letter {
      text-transform: uppercase;
    }
  }

  .dropship-shipping-and-returns__info-icon-btn {
    width: 0.875rem;
    height: 0.875rem;
  }
}
.iframe-returns-information {
  @media (min-width: 768px) {
    min-height: 200px;
    height: 65vh;
  }
}
