'use client';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import React, { useCallback, useState } from 'react';

import type { ProductData } from '@pdp/types/product-data/style-level';
import { Modal } from '@ecom-next/core/legacy/modal';
import DropshipFlagsContainer from '../dropship-flags-component';
import { textTransform } from './util';

type DropshipShippingAndReturnsProps = { productData: ProductData };
type InformationModaltypes = {
  closeModal: () => void;
  showModal: boolean;
};

const InfoIcon = () => (
  <svg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14' fill='none'>
    <circle cx='7.00039' cy='7.00039' r='5.1' stroke='black' />
    <path d='M7.55957 4.19922H6.43957V5.04628H7.55957V4.19922ZM7.55957 5.74432H6.43957V9.79922H7.55957V5.74432Z' fill='black' />
  </svg>
);

const InformationModal = (props: InformationModaltypes): JSX.Element => {
  const url = '/Asset_Archive/AllBrands/dropship/sellerInfo.html';
  const { closeModal, showModal } = props;
  const { localize } = useLocalize();

  return (
    <Modal closeButtonAriaLabel={localize('pdp.closeModal.altText')} id='informationModal' isOpen={showModal} noHeader onClose={closeModal}>
      <iframe
        className='iframe-returns-information'
        data-testid='shippingInfo-modal'
        id='informationModalIframe'
        src={url}
        title={localize('pdp.shipping.iframe.title')}
      />
    </Modal>
  );
};

export const DropshipShippingAndReturns = ({ productData }: DropshipShippingAndReturnsProps): JSX.Element => {
  const { vendorName } = productData;
  const [showModal, setShowModal] = useState(false);
  const { localize } = useLocalize();
  const copyText = localize('pdp.dropship.shippedByVendor');
  const dropshipText = textTransform(copyText);

  const openModal = useCallback(() => setShowModal(true), []);
  const closeModal = useCallback(() => setShowModal(false), []);

  return (
    <div data-testid='dropship-shipping-and-returns' className='dropship-shipping-and-returns'>
      <div className='dropship-shipping-and-returns__vendor-name'>
        <div>
          <span>{dropshipText}</span>
          <span className='dropship-shipping-and-returns-vendor-text'>{vendorName}</span>
        </div>
        <button
          className='dropship-shipping-and-returns__info-icon-btn'
          data-testid='dropship-shipping-and-returns__info-icon-btn'
          onClick={openModal}
          onKeyDown={openModal}
          tabIndex={0}
        >
          <InfoIcon data-testid='info-icon' />
        </button>
      </div>
      <DropshipFlagsContainer className='dropship-shipping-and-returns__flags' />
      <InformationModal closeModal={closeModal} showModal={showModal} />
    </div>
  );
};
