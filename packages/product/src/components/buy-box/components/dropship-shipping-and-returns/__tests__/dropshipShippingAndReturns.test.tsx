import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DropshipShippingAndReturns } from '..';
import { capiGap } from '../../../../../pages/__fixtures__/capiGap';
import { wrapInTestApp } from '../../../../../test-utils/appWrapper';

describe('<DropshipShippingAndReturns />', () => {
  it('matches the snapshot', async () => {
    const { container } = wrapInTestApp(<DropshipShippingAndReturns productData={capiGap} />);
    await waitFor(() => {
      expect(screen.getByTestId('dropship-shipping-and-returns')).toBeInTheDocument();
    });
    expect(container).toMatchSnapshot();
  });
  it('renders the component', async () => {
    const user = userEvent.setup();
    wrapInTestApp(<DropshipShippingAndReturns productData={capiGap} />);

    expect(screen.queryByTestId('shippingInfo-modal')).not.toBeInTheDocument();

    await user.click(screen.getByTestId('dropship-shipping-and-returns__info-icon-btn'));

    expect(screen.getByTestId('shippingInfo-modal')).toBeInTheDocument();
  });

  it('renders the dropship text', async () => {
    wrapInTestApp(<DropshipShippingAndReturns productData={capiGap} />);
    expect(screen.queryByText(`${capiGap.vendorName}`)).toBeInTheDocument();
  });
});
