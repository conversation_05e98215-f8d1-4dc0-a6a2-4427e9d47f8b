// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DropshipShippingAndReturns /> matches the snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="dropship-shipping-and-returns"
      data-testid="dropship-shipping-and-returns"
    >
      <div
        class="dropship-shipping-and-returns__vendor-name"
      >
        <div>
          <span>
            Sold & shipped by 
          </span>
          <span
            class="dropship-shipping-and-returns-vendor-text"
          >
            Little Words Project
          </span>
        </div>
        <button
          class="dropship-shipping-and-returns__info-icon-btn"
          data-testid="dropship-shipping-and-returns__info-icon-btn"
          tabindex="0"
        >
          <svg
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="7.00039"
              cy="7.00039"
              r="5.1"
              stroke="black"
            />
            <path
              d="M7.55957 4.19922H6.43957V5.04628H7.55957V4.19922ZM7.55957 5.74432H6.43957V9.79922H7.55957V5.74432Z"
              fill="black"
            />
          </svg>
        </button>
      </div>
      <ul
        data-testid="dropshipFlagsContainer"
      >
        <li
          class="dropship-shipping-and-returns__flags"
        >
          Return by Mail Only.
        </li>
      </ul>
    </div>
  </div>
</div>
`;
