/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { fireEvent, render, screen, act } from '@ecom-next/core/test/test-helper';
import { BuyBoxClient } from '../BuyBoxClient';

describe('<BuyBoxClient />', () => {
  let mockGetBoundingClientRect: jest.Mock;
  let originalScrollY: any;
  let originalInnerHeight: any;
  let currentScrollY = 0;
  const windowHeight = 800;
  const elementInitialTop = 100;
  const elementHeight = 400;

  beforeAll(() => {
    originalScrollY = Object.getOwnPropertyDescriptor(window, 'scrollY');
    originalInnerHeight = Object.getOwnPropertyDescriptor(window, 'innerHeight');

    Object.defineProperty(window, 'scrollY', {
      configurable: true,
      get: () => currentScrollY,
    });
    Object.defineProperty(window, 'innerHeight', {
      configurable: true,
      get: () => windowHeight,
    });
  });

  afterAll(() => {
    if (originalScrollY) Object.defineProperty(window, 'scrollY', originalScrollY);
    if (originalInnerHeight) Object.defineProperty(window, 'innerHeight', originalInnerHeight);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    currentScrollY = 0;

    mockGetBoundingClientRect = jest.fn().mockImplementation(() => {
      const buyBoxElement = screen.queryByTestId('buy-box-wrapper-id');
      let currentStyledTop = elementInitialTop;
      if (buyBoxElement && buyBoxElement.style.top) {
        currentStyledTop = parseFloat(buyBoxElement.style.top);
      } else {
        currentStyledTop = elementInitialTop;
      }

      return {
        top: currentStyledTop,
        left: 0,
        bottom: currentStyledTop + elementHeight,
        right: 100,
        height: elementHeight,
        width: 50,
        x: 0,
        y: currentStyledTop,
        toJSON: () => ({}),
      };
    });
    Element.prototype.getBoundingClientRect = mockGetBoundingClientRect;
  });

  describe('Smart scrolling event listener', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

    describe('Desktop', () => {
      it('should add listeners and set initial style top correctly when isDesktop is true (br brand)', () => {
        currentScrollY = 50; // Page loaded with some scroll
        render(
          <BuyBoxClient isDesktop={true} brandName='br'>
            <div></div>
          </BuyBoxClient>
        );
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial call: prevScroll = 50, currentScroll = 50. Delta = 0.
        // sizes.viewportTopBoundary = 160, sizes.footerOffset = 66
        // elementBounds.top = 100 (from mock)
        // viewportHeight = 800 - 66 = 734
        // desiredViewportTop = 100 - 0 = 100
        // elementOverflow = 400 - 734 = -334
        // newStyleTop = max(100, -(-334)) = 334
        // newStyleTop = min(334, 160) = 160
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' });
        expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function));
        expect(addEventListenerSpy).toHaveBeenCalledWith('pageshow', expect.any(Function));
      });

      it('should set initial style top correctly (gap brand)', () => {
        currentScrollY = 20;
        render(
          <BuyBoxClient isDesktop={true} brandName='gap'>
            <div></div>
          </BuyBoxClient>
        );
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial call: prevScroll = 20, currentScroll = 20. Delta = 0.
        // sizes.viewportTopBoundary = 16, sizes.footerOffset = 16
        // elementBounds.top = 100 (from mock)
        // viewportHeight = 800 - 16 = 784
        // desiredViewportTop = 100 - 0 = 100
        // elementOverflow = 400 - 784 = -384
        // newStyleTop = max(100, -(-384)) = 384
        // newStyleTop = min(384, 16) = 16
        expect(buyBoxWrapper).toHaveStyle({ top: '16px' });
      });

      it('should update style top on scroll', () => {
        render(
          <BuyBoxClient isDesktop={true} brandName='br'>
            <div></div>
          </BuyBoxClient>
        );
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial top: 160px, prevScroll.current = 0
        // Update mock to reflect current top position for next calculation
        mockGetBoundingClientRect.mockReturnValue({
          top: 160,
          height: elementHeight, // Use global elementHeight (400) for this scroll
          bottom: 160 + elementHeight,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });

        act(() => {
          currentScrollY = 100; // Scroll down by 100
          fireEvent.scroll(window);
        });
        // prevScroll = 0, currentScroll = 100. Delta = 100.
        // elementBounds.top = 160 (current style.top)
        // desiredViewportTop = 160 - 100 = 60
        // elementOverflow = 400 - (800 - 66) = -334
        // newStyleTop = max(60, 334) = 334
        // newStyleTop = min(334, 160) = 160. Stays pinned.
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' });

        // Scroll further down, enough to push content up
        mockGetBoundingClientRect.mockReturnValue({
          top: 160,
          height: 1000,
          bottom: 160 + 1000,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });
        act(() => {
          currentScrollY = 300; // prevScroll was 100 (after first scroll test)
          fireEvent.scroll(window);
        });
        // prevScroll = 100, currentScroll = 300. Delta = 200.
        // elementBounds.top = 160
        // elementBounds.height = 1000
        // desiredViewportTop = 160 - 200 = -40
        // elementOverflow = 1000 - 734 = 266. -elementOverflow = -266
        // newStyleTop = max(-40, -266) = -40
        // newStyleTop = min(-40, 160) = -40
        expect(buyBoxWrapper).toHaveStyle({ top: '-40px' });
      });

      it('should recalculate position on page refresh or navigation event', () => {
        currentScrollY = 100; // Initial scroll
        render(
          <BuyBoxClient isDesktop={true} brandName='br'>
            <div></div>
          </BuyBoxClient>
        );
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial top: 160px, prevScroll.current = 100

        // Simulate state change before refresh
        currentScrollY = 200; // Scrolled more, or restored at different position
        // Update mock to reflect current top position for next calculation
        mockGetBoundingClientRect.mockReturnValue({
          top: 160, // Current style.top from initial load
          height: elementHeight,
          bottom: 160 + elementHeight,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });

        act(() => {
          // Simulate refresh event
          const event = new PageTransitionEvent('pageshow', { persisted: true });
          fireEvent(window, event);
        });

        // In refresh: buyBoxPrevScroll.current = window.scrollY (200)
        // smartScrolling called: prevScroll = 200, currentScroll = 200. Delta = 0.
        // elementBounds.top = 160 (from mock)
        // desiredViewportTop = 160 - 0 = 160
        // elementOverflow = 400 - (800 - 66) = -334
        // newStyleTop = max(160, -(-334)) = 334
        // newStyleTop = min(334, 160) = 160
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' });
      });
    });

    describe('Mobile', () => {
      it('should not addEventListener for smartScrolling when isDesktop is false', () => {
        render(
          <BuyBoxClient isDesktop={false} brandName='br'>
            <div></div>
          </BuyBoxClient>
        );

        fireEvent.scroll(window, { target: { scrollY: 200 } });

        expect(addEventListenerSpy).not.toHaveBeenCalledWith('scroll', expect.any(Function));
        expect(addEventListenerSpy).not.toHaveBeenCalledWith('pageshow', expect.any(Function));
      });
    });

    it('should remove smart scrolling and pageshow event listeners on unmount', () => {
      const wrapper = render(
        <BuyBoxClient isDesktop={true} brandName='br'>
          <div></div>
        </BuyBoxClient>
      );

      const scrollListener = addEventListenerSpy.mock.calls.find((call: string[]) => call[0] === 'scroll')?.[1];
      const refreshListener = addEventListenerSpy.mock.calls.find((call: string[]) => call[0] === 'pageshow')?.[1];

      wrapper.unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', scrollListener);
      expect(removeEventListenerSpy).toHaveBeenCalledWith('pageshow', refreshListener);
    });
  });
});
