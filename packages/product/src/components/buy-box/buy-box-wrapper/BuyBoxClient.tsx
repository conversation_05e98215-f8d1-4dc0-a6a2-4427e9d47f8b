'use client';

import React, { useEffect, useRef, useCallback } from 'react';

export const BuyBoxClient = ({ children, isDesktop, brandName }: { brandName: string; children: React.ReactNode; isDesktop: boolean }) => {
  const buyBoxRef = useRef<HTMLDivElement>(null);
  const buyBoxPrevScroll = useRef<number>(0);

  const isBr = brandName === 'br' || brandName === 'brfs';
  const sizes = {
    viewportTopBoundary: isBr ? 160 : 16,
    footerOffset: isBr ? 66 : 16,
  };

  const smartScrolling = useCallback(() => {
    const targetElement = buyBoxRef.current;

    if (targetElement) {
      const viewportTopBoundary = sizes.viewportTopBoundary;
      const footerOffset = sizes.footerOffset;
      const elementBounds = targetElement.getBoundingClientRect();
      const viewportHeight = window.innerHeight - footerOffset;
      const currentScrollY = window.scrollY;
      const previousScrollY = buyBoxPrevScroll.current ?? 0;
      const scrollYDelta = currentScrollY - previousScrollY;
      const desiredViewportTop = elementBounds.top - scrollYDelta;
      const elementOverflow = elementBounds.height - viewportHeight;

      let newStyleTop = Math.max(desiredViewportTop, -elementOverflow);
      newStyleTop = Math.min(newStyleTop, viewportTopBoundary);

      if (!isNaN(newStyleTop)) {
        targetElement.style.top = `${newStyleTop}px`;
      }

      buyBoxPrevScroll.current = currentScrollY;
    }
  }, [sizes.footerOffset, sizes.viewportTopBoundary]);

  useEffect(() => {
    const handlePageRefresh = (event: PageTransitionEvent) => {
      if (event.persisted && isDesktop) {
        buyBoxPrevScroll.current = window.scrollY;
        smartScrolling();
      }
    };

    if (isDesktop) {
      buyBoxPrevScroll.current = window.scrollY;
      smartScrolling();
      window.addEventListener('scroll', smartScrolling);
      window.addEventListener('pageshow', handlePageRefresh);
    }
    return () => {
      window.removeEventListener('scroll', smartScrolling);
      window.removeEventListener('pageshow', handlePageRefresh);
    };
  }, [isDesktop, smartScrolling]);

  return (
    <div className='buy-box_wrapper w-100 flex flex-col items-start p-4 ' data-testid='buy-box-wrapper'>
      <div id='buy-box-wrapper-id' data-testid='buy-box-wrapper-id' className='buy-box-wrapper sticky w-full' ref={buyBoxRef}>
        {children}
      </div>
    </div>
  );
};
