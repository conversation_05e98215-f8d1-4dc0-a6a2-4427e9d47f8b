.pdp-margin-bottom-components {
  margin-bottom: theme('gap.spacing-l');
}

.pdp-margin-top-components {
  margin-top: theme('gap.spacing-l');
}

.pdp-margin-bottom-s-components {
  margin-bottom: theme('gap.spacing-s');
}

.pdp-margin-top-s-components {
  margin-top: theme('gap.utk-spacing-s');
}

.buy-box_wrapper {
  min-width: auto;
  background-color: theme('colors.white');
  position: relative;
  width: 358px;
}

.pdp-buy-box-size-selector-container {
  display: flex;
  gap: theme('gap.utk-spacing-xl');
  flex-direction: column;
}
