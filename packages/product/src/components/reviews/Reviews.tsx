import React from 'react';
import type { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { Brands, getPageContext } from '@ecom-next/utils/server';
import {
  cacheableCapiDataPromise,
  cacheableEnabledFeaturesPromise,
  cacheableFeaturesConfig,
  cacheableReviewRatingsPromise,
  getUrlParamsString,
} from '../../pages/getReWrittenData';
import getAppConfig from '../../pages/state-builder/appConfig';
import getTemplateFactory, { Templates } from '../../server-functions/template-factory';
import { StarRatings } from '../buy-box/components/star-ratings/StarRatings';
import { ReviewsContainerClient } from './ReviewsContainerClient';
import { ReviewSummaryKeys } from './ReviewsContainerClient/components/ReviewSummary';

const abSegMap = {
  reviewSummary: '202',
  reviewSummaryModel: '203',
  gapBuyboxRedesign2024: '214',
  brRedesign2024: '191',
};

const nonRegularPDP = [Templates.HYBRID, Templates.LPO, Templates.OOS, Templates.SPECIALCOLLABOOS, Templates.SPECIALOOS];

export const Reviews = async ({ searchParams }: { searchParams: PageParams['searchParams'] }) => {
  const { brand, market, locale } = getPageContext();
  const requestParamString = getUrlParamsString(searchParams);
  const forceReviewForm = searchParams?.write_review === 'true';
  const { powerReviewsConfig } = getAppConfig();
  const { productData, features } = await cacheableCapiDataPromise(requestParamString);
  const { enabledFeatures, featureVariables, abSeg } = await cacheableEnabledFeaturesPromise(searchParams);
  const { reviewRatings } = await cacheableReviewRatingsPromise(requestParamString);
  const powerReviewsEnabled = enabledFeatures['pdp-power-reviews'] ?? false;
  const shouldLoadReviewsScript = enabledFeatures['pdp-mfe-load-power-reviews'] ?? false;
  const powerReviewsFeatureVariables = featureVariables['pdp-power-reviews'];
  const shouldHidePhotoGallery = powerReviewsFeatureVariables[`hidden-photogallery-${market}-${brand}`];
  const { reviews } = await cacheableFeaturesConfig(searchParams);
  const { isReviewsDrawerEnabled } = reviews;
  const forceReviewFormInRegularPR = forceReviewForm && !isReviewsDrawerEnabled;
  const hasRatings = reviewRatings?.rating_count > 0;
  // br redesign
  const isBrWhiteBackground = enabledFeatures['br-white-background'] ?? false;
  const isBrRedesign2024Ph2 = enabledFeatures['br-redesign-2024-ph2'] ?? false;
  const isBrfsRedesign2024Ph2 = enabledFeatures['redesign-2024-ph2'] ?? false;
  const segment = abSeg?.[`${brand + abSegMap.brRedesign2024}`] || false;
  const isBrRedesign2024Ph2Enabled = isBrRedesign2024Ph2 && segment === 'a';
  const isBrfsRedesign2024Ph2Enabled = isBrfsRedesign2024Ph2 && (market !== 'us' || segment === 'a');
  // gap redesign
  const isGapBuyboxRedesign2024 = enabledFeatures['gap-buybox-2024'];
  const redesignSegment = abSeg?.[`${brand + abSegMap.gapBuyboxRedesign2024}`] || '';
  const isSegmentA = redesignSegment === 'a' || redesignSegment === 'b';
  const isGapBrands = brand === Brands.Gap || brand === Brands.GapFactoryStore;
  // review summary
  const isFeatureFlagEnabled = enabledFeatures[`pdp-ai-reviews-${market}-${brand}`];
  const placementSegment = abSeg?.[`${brand + abSegMap.reviewSummary}`] || '';
  const modelSegment = abSeg?.[`${brand + abSegMap.reviewSummaryModel}`] || '';
  const aiVariables = featureVariables[`pdp-ai-reviews-${market}-${brand}`] as Record<string, string>;
  const test1 = aiVariables?.test1;
  const test2 = aiVariables?.test2;
  const defaultKey = aiVariables?.default;
  const supportedCategories = aiVariables?.supportedCategories;
  const reviewSummaries = productData?.reviewSummaries ?? null;
  const primaryCategoryId = productData?.primaryCategoryId;
  const mockSummary = searchParams?.mockSummary;
  const isEnabledPrimaryCategory =
    !supportedCategories || supportedCategories?.split(',').includes(primaryCategoryId) || supportedCategories?.toLowerCase() === 'all';
  const enabled = isFeatureFlagEnabled && isEnabledPrimaryCategory && (placementSegment === 'a' || placementSegment === 'b');
  let model;
  switch (modelSegment) {
    case 'a':
      model = test1;
      break;
    case 'b':
      model = test2;
      break;
    default:
      model = defaultKey || 'default';
  }
  // v2 fulfillment
  const hasFulfillmentV2Flag = enabledFeatures['pdp-fulfillment-selection-v2'] ?? false;
  const fulfillmentV2features = featureVariables['pdp-fulfillment-selection-v2'];
  const variable = `${brand}-${market}`;
  const hasVariable = variable in (fulfillmentV2features || {});
  const hasFulfillmentV2Feature = hasVariable ? fulfillmentV2features[variable] : false;
  const isFulfillmentV2Enabled = hasFulfillmentV2Flag && hasFulfillmentV2Feature;

  const reviewSummary = {
    display: (enabled && !!reviewSummaries && !!reviewSummaries[model]) || !!mockSummary,
    enabled,
    model: model as ReviewSummaryKeys,
    modelNameForTracking: (reviewSummaries?.[model]?.modelId as string) ?? '',
    placement: placementSegment === 'a' ? 'default' : 'alternate',
  };

  const customFeatures = {
    isBrWhiteBackground,
    isBrBrfsRedesign: isBrRedesign2024Ph2Enabled || isBrfsRedesign2024Ph2Enabled,
    isAtRedesign: enabledFeatures['at-redesign-2023'] ?? false,
    isAtRedesign2024: enabledFeatures['at-redesign-2024'] ?? false,
    isGapBuyBoxRedesign2024: isGapBrands && isGapBuyboxRedesign2024 && isSegmentA,
    isReviewsSearch: enabledFeatures[`pdp-review-search-${market}-${brand}`] ?? false,
    pdpReviewVoting: enabledFeatures[`pdp-review-voting-${market}-${brand}`] ?? false,
    isFulfillmentV2Enabled: isFulfillmentV2Enabled,
    isReviewsDrawerEnabled: isReviewsDrawerEnabled,
    shouldLoadReviewsScript: shouldLoadReviewsScript,
    shouldHidePhotoGallery: shouldHidePhotoGallery,
  };

  const starRatingsComponent = <StarRatings brandName={brand} ratingSize='large' requestParamString={requestParamString} searchParams={searchParams} />;
  const template = getTemplateFactory(features);
  const shouldShowDrawer = !nonRegularPDP.includes(template) && isReviewsDrawerEnabled;

  if (!powerReviewsEnabled) {
    return <></>;
  }

  return (
    <ReviewsContainerClient
      forceLaunchReviewForm={forceReviewForm || forceReviewFormInRegularPR}
      locale={locale}
      template={template}
      reviewSummary={reviewSummary}
      customFeatures={customFeatures}
      hasRatings={hasRatings}
      brand={brand}
      shouldShowDrawer={shouldShowDrawer}
      powerReviewsConfig={powerReviewsConfig}
      styleId={productData.styleId}
      reviewSummaries={reviewSummaries}
      starRatingsComponent={starRatingsComponent}
    />
  );
};
