'use client';

import { ReactNode, useContext } from 'react';
import { ReviewsDrawerContext } from '../../../components/reviews/ReviewsDrawer/reviews-drawer-provider';
import { DrawerWrapper } from '../../../legacy/packages/drawer-wrapper';

type ReviewsDrawerProps = {
  children: ReactNode;
};

export const ReviewsDrawer = ({ children }: ReviewsDrawerProps) => {
  const { setReviewsDrawerIsOpen, isReviewsDrawerOpen } = useContext(ReviewsDrawerContext);

  const onClose = (): void => {
    setReviewsDrawerIsOpen(false);
  };

  return (
    <DrawerWrapper isOpen={isReviewsDrawerOpen} onClose={onClose} setInformationView={setReviewsDrawerIsOpen} title='' className='pdp-reviews-drawer'>
      <div>{children}</div>
    </DrawerWrapper>
  );
};
