.pdp-reviews-drawer.drawer-wrapper {
  border-radius: 8px 0px 0px 8px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 2px 0px;
  display: flex;
  position: fixed;
  align-self: flex-start;
  flex-direction: column;
  box-sizing: border-box;
  transition-property: transform;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  right: 0px;
  height: 100%;
  width: 408px !important;
  transform: none;
}

.pdp-reviews-drawer.drawer-wrapper .drawer-content {
  padding: 0 !important;
  background: rgb(255, 255, 255) !important;
  overflow: hidden auto !important;
  border-radius: 8px 0px 0px 8px;
}

.pdp-reviews-drawer.drawer-wrapper .drawer-header {
  height: 0 !important;
  padding: 0 !important;
  border-bottom: none !important;
}
