'use client';

import { ReactNode, useContext } from 'react';
import { DrawerWrapper } from '../../../legacy/packages/drawer-wrapper';
import { ReviewsDrawerContext } from './reviews-drawer-provider/MVGReviewsDrawerProvider';

type ReviewsDrawerProps = {
  children: ReactNode;
};

export const MVGReviewsDrawer = ({ children }: ReviewsDrawerProps) => {
  const { setReviewsDrawerIsOpen, isReviewsDrawerOpen } = useContext(ReviewsDrawerContext);

  const onClose = (): void => {
    setReviewsDrawerIsOpen(false);
  };

  return (
    <DrawerWrapper isOpen={isReviewsDrawerOpen} onClose={onClose} setInformationView={setReviewsDrawerIsOpen} title='' className='pdp-reviews-drawer'>
      <div>{children}</div>
    </DrawerWrapper>
  );
};
