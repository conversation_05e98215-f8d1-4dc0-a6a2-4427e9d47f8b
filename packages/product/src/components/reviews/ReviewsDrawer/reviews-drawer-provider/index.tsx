import type { Dispatch, PropsWithChildren, SetStateAction } from 'react';
import React, { createContext, useState } from 'react';
import { usePDPReporter } from '../../../../providers/pdp-reporter-provider';

type ReviewsDrawerType = {
  forceReviewForm: boolean;
  isReviewsDrawerOpen: boolean;
  setForceReviewForm: (forceReviewForm: boolean) => void;
  setReviewsDrawerIsOpen: Dispatch<SetStateAction<boolean>>;
};

export const ReviewsDrawerContext = createContext<ReviewsDrawerType>({
  forceReviewForm: false,
  isReviewsDrawerOpen: false,
  setForceReviewForm: () => {},
  setReviewsDrawerIsOpen: () => {},
});

export const ReviewsDrawerProvider = ({ children }: PropsWithChildren): React.JSX.Element => {
  const [isReviewsDrawerOpen, setIsReviewsDrawerOpen] = useState(false);
  const [forceReviewForm, setForceReviewForm] = useState(false);
  const reporter = usePDPReporter();

  const handleReviewsDrawerState = (isOpen: boolean, id?: string) => {
    setIsReviewsDrawerOpen(isOpen);
    if (isOpen && id) {
      const rantingsClickedFromBuybox = id === 'pdp-pr-star-ratings-buybox';
      reporter?.reportReviewRatingsClick(rantingsClickedFromBuybox);
    }
  };

  const contextValue = React.useMemo(
    () => ({
      forceReviewForm,
      isReviewsDrawerOpen,
      setForceReviewForm,
      setReviewsDrawerIsOpen: handleReviewsDrawerState as Dispatch<SetStateAction<boolean>>,
    }),
    [forceReviewForm, isReviewsDrawerOpen, handleReviewsDrawerState]
  );

  return <ReviewsDrawerContext.Provider value={contextValue}>{children}</ReviewsDrawerContext.Provider>;
};
