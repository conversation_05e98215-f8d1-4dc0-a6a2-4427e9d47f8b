'use client';
import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { ProductImage } from '@ecom-next/core/fabric/product-image';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';
import { getLocalizedPrice } from '@product-page/capi-utils/response-adapter/helper/price/get-localized-price';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ProductRecommendation } from '@ecom-next/sitewide/aiRecommendationsService/types';
import classNames from 'classnames';
import { isBRandBRFS } from '../../util/evaluate-brand';
import CarouselNavigation from './CarouselNavigation';
import { formatReviewCount } from './utils/formatReviewCount';
import { stringToTitleCase } from './utils/stringToTitleCase';
import { RecsCarouselConfigByViewport, recsCarouselConfigByViewport } from './utils/recsCarouselConfigByViewport';
import { QuickAdd } from './quick-add/QuickAdd';

interface ProductCarouselProps {
  headline: string;
  id?: string;
  index?: number;
  isQuickAddEnabled?: boolean;
  pageType?: string;
  products: ProductRecommendation[];
  showImage?: boolean;
  showMarketingFlag?: boolean;
  showName?: boolean;
  showPercentageOff?: boolean;
  showPrice?: boolean;
  showRating?: boolean;
}

export default function ProductCarousel({
  headline,
  products,
  showImage = true,
  showName = true,
  showPrice = true,
  showMarketingFlag = true,
  showRating = true,
  isQuickAddEnabled = false,
  showPercentageOff = false,
  pageType = 'PRODUCT',
  id = '',
  index = 0,
}: ProductCarouselProps) {
  const { isDesktop = false, locale, brand } = usePageContext();
  const [configByViewport, setConfigByViewport] = useState<RecsCarouselConfigByViewport>(recsCarouselConfigByViewport(window?.innerWidth, brand));
  const [batchPoliteMessage, setBatchPoliteMessage] = useState('');

  const { localize } = useLocalize();
  const { localizePrice } = getLocalizedPrice(locale);
  const slidesToScroll = isDesktop ? configByViewport.slidesToScroll : 1;
  const duration = 25; // default duration used by useEmblaCarousel

  useEffect(() => {
    const handleResize = () => {
      setConfigByViewport(recsCarouselConfigByViewport(window.innerWidth, brand));
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'start',
    containScroll: 'trimSnaps',
    slidesToScroll,
    duration,
  });

  const [prevDisabled, setPrevDisabled] = useState(true);
  const [nextDisabled, setNextDisabled] = useState(true);

  const focusOnSlide = useCallback(() => {
    const currentIndex = emblaApi?.selectedScrollSnap() ?? 0;
    const slide = emblaApi?.slideNodes()[currentIndex * slidesToScroll] as HTMLElement;
    const focusable = slide?.querySelector('a[href]') as HTMLElement;
    focusable?.focus();
  }, [emblaApi, slidesToScroll]);

  const scrollPrev = useCallback(() => {
    if (!prevDisabled) {
      emblaApi?.scrollPrev();
      setTimeout(focusOnSlide, duration);
    }
  }, [emblaApi, prevDisabled, focusOnSlide]);

  const scrollNext = useCallback(() => {
    if (!nextDisabled) {
      emblaApi?.scrollNext();
      setTimeout(focusOnSlide, duration);
    }
  }, [emblaApi, nextDisabled, focusOnSlide]);

  useEffect(() => {
    if (!emblaApi) return;
    const updateButtons = () => {
      setPrevDisabled(!emblaApi.canScrollPrev());
      setNextDisabled(!emblaApi.canScrollNext());
    };

    const announceBatchScroll = () => {
      const currentIndex = emblaApi.selectedScrollSnap();
      const newBatch = Math.ceil(currentIndex / slidesToScroll) + 1;
      setBatchPoliteMessage(`Recommended products batch ${newBatch} displayed.`);
    };

    emblaApi.on('select', () => {
      updateButtons();
      announceBatchScroll();
    });

    updateButtons();
  }, [emblaApi, products, slidesToScroll]);

  const shouldCenterTitle = () => brand === 'on' || brand === 'br' || brand === 'brfs' || (brand === 'at' && isDesktop);

  return (
    <div
      id={id}
      data-test-id={`recs-carousel-${index}`}
      className='recs-section recs-carousel relative mx-auto bg-white'
      role='region'
      aria-label={stringToTitleCase(headline)}
    >
      <div className='sr-only' aria-live='polite' aria-atomic='true'>
        {batchPoliteMessage}
      </div>
      <div
        className={`recs-title-wrapper relative mb-2 flex w-full ${isBRandBRFS(brand) ? 'items-start' : 'items-end'} ${shouldCenterTitle() ? 'justify-center' : 'justify-start'}`}
      >
        <h2 className='recs-carousel-title'>{brand === 'gap' || brand === 'gapfs' ? headline : stringToTitleCase(headline)}</h2>
        {isDesktop && products.length > configByViewport.slidesToScroll && pageType !== 'ADDTOCART' && (
          <CarouselNavigation
            scrollPrev={scrollPrev}
            scrollNext={scrollNext}
            prevDisabled={prevDisabled}
            nextDisabled={nextDisabled}
            aria-label='Product Recommendations Carousel navigation'
          />
        )}
      </div>

      <div ref={emblaRef} className='product-recommendation-wrapper overflow-hidden' role='region' aria-roledescription='Product Recommendations Carousel'>
        <div className='flex gap-3'>
          {products.map((product: ProductRecommendation, productIndex) => {
            const { CurrentPrice, DetailURL, ID, ImageURL, isSalePrice, MarketingFlag, OriginalPrice, Percentage, ProductName, Rating, ReviewCount } = product;
            const shouldShowPercentageOff = isSalePrice && showPercentageOff && Percentage;

            const percentageOffText = Percentage
              ? localize('pdp.price.percentageOff', {
                  value: Percentage,
                })
              : '';

            const currentPriceClass = classNames({
              'recs-carousel-product-card--price-sale': isSalePrice,
              'recs-carousel-product-card--price-normal': !isSalePrice,
              'w-full': shouldShowPercentageOff,
            });

            return (
              <div
                key={ID}
                className='recs-carousel-product-card--container'
                style={{ width: `${configByViewport.itemWidth}px` }}
                role='group'
                aria-label={`${ProductName}. Product ${productIndex + 1} of ${products.length}`}
              >
                <div className='recs-carousel-product-card--image'>
                  {showImage && (
                    <a href={DetailURL} tabIndex={-1}>
                      <ProductImage className='!p-0' id={CurrentPrice} width={`100%`} imageUrl={ImageURL} height={`auto`} imageAltText={ProductName} />
                    </a>
                  )}
                  <QuickAdd id={ID} productName={ProductName} rating={Rating} reviewCount={ReviewCount} isQuickAddEnabled={isQuickAddEnabled} />
                </div>
                <div className='recs-carousel-product-card--details'>
                  {showName && (
                    <a href={DetailURL}>
                      <div className='recs-carousel-product-card--name'>{ProductName}</div>
                    </a>
                  )}
                  {showPrice && (
                    <div className='recs-carousel-product-card--price'>
                      {isSalePrice && <span className='recs-carousel-product-card--price-strike'>{localizePrice(Number(OriginalPrice))}</span>}
                      {shouldShowPercentageOff && <span className='recs-carousel-product-card--price-percentage-off'>{percentageOffText}</span>}
                      <span className={currentPriceClass} style={{ flexBasis: shouldShowPercentageOff ? '100%' : 'auto' }}>
                        {localizePrice(Number(CurrentPrice))}
                      </span>
                    </div>
                  )}
                  {showMarketingFlag && MarketingFlag && <span className='recs-carousel-product-card--marketing-flag'>{MarketingFlag}</span>}

                  {showRating && Rating && (
                    <a href={`${DetailURL}`}>
                      <StarRatings
                        ratingValue={Number(Rating)}
                        showRatingValue={false}
                        postText={formatReviewCount(ReviewCount)}
                        showUnderlineForRatingValue={false}
                        ratingSize='small'
                      />
                    </a>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
