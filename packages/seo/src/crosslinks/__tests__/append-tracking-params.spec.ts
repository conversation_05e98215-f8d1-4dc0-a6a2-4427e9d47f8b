import { appendTrackingParams } from '../append-tracking-params';

describe('appendTrackingParams', () => {
  const url = new URL('https://www.gap.com/men/jeans?cid=123');
  const divisionName = 'some-division-name';
  const pageName = 'some-page-name';
  const pageType = 'category';
  const environmentAwareBrandSite = 'https://www.stage.gaptechol.com';

  it('appends cl param to the url', () => {
    const res = appendTrackingParams({ url, divisionName, pageName, pageType, environmentAwareBrandSite });
    expect(res).toContain('cl=true');
  });

  it('appends nav param to the url', () => {
    const res = appendTrackingParams({ url, divisionName, pageName, pageType, environmentAwareBrandSite });
    expect(res).toContain(`nav=smartlink:${divisionName}::${pageName}`);
  });

  it('persists existing params on the url', () => {
    const res = appendTrackingParams({ url, divisionName, pageName, pageType, environmentAwareBrandSite });
    expect(res).toContain('cid=123');
  });

  it('returns a fully qualified URL', () => {
    const res = appendTrackingParams({ url, divisionName, pageName, pageType, environmentAwareBrandSite });
    expect(new URL(res)).toBeInstanceOf(URL);
  });

  it('should have the information with the correct encoding', () => {
    const diviosionNameNeedsEncoding = 'jewelry & accessories';
    const pageNameNeedsEncoding = 'some&page name';
    const res = appendTrackingParams({ url, divisionName: diviosionNameNeedsEncoding, pageName: pageNameNeedsEncoding, pageType, environmentAwareBrandSite });
    expect(res).toContain('nav=smartlink:jewelry%20%26%20accessories::some%26page%20name');
  });

  it('ignores empty division name when building params', () => {
    const res = appendTrackingParams({ url, pageName, pageType, environmentAwareBrandSite });
    expect(res).toContain('nav=smartlink:::some-page-name');
  });

  it('ignores empty page name when building params', () => {
    const res = appendTrackingParams({ url, divisionName, pageType, environmentAwareBrandSite });
    expect(res).toContain('nav=smartlink:some-division-name::');
  });

  it('includes both category and subcategory page names for subcategory pages', () => {
    const categoryName = 'some-category-name';
    const res = appendTrackingParams({ categoryName, divisionName, pageName, pageType: 'subcategory', url, environmentAwareBrandSite });
    expect(res).toContain('nav=smartlink:some-division-name::some-category-name:some-page-name');
  });

  it('should use environmentAwareBrandSite', () => {
    const categoryName = 'some-category-name';
    const res = appendTrackingParams({ categoryName, divisionName, pageName, pageType: 'subcategory', url, environmentAwareBrandSite });
    expect(res).toContain('https://www.stage.gaptechol.com');
  });
});
