import transformCrosslinkApiResponse, { RequestType } from '../transform-crosslink-api-response';
import { getTargetEnv } from '../get-target-env';
import { Environment } from '../../../../sitewide/src/components/universal-bar/sister-brand-links/types';

const environmentAwareUrlsEcom = require('./data-fixtures/environment-aware-urls-ecom.json');
const environmentAwareUrlsApp = require('./data-fixtures/environment-aware-urls-app.json');
const environmentAwareUrlsWip = require('./data-fixtures/environment-aware-urls-wip.json');
const mockData = require('./data-fixtures/mockCategoryData.json');
const decodeProductData = require('./data-fixtures/decode-am-product-crosslink-response.json');
const defectProductData = require('./data-fixtures/defect-am-product-crosslink-response.json');
const mockProductData = require('./data-fixtures/mock-am-product-crosslink-response.json');

jest.mock('../get-target-env', () => ({
  getTargetEnv: jest.fn(),
}));

describe('transformCrosslinkApiResponse', () => {
  const divisionName = 'some-division';
  const pageName = 'some-page';
  const brand = 'at';
  const market = 'ca';
  const requestType = 'ecom' as RequestType;

  beforeEach(() => {
    jest.mocked(getTargetEnv).mockReturnValue('prod' as Environment);
  });

  describe('category page', () => {
    const args = {
      data: mockData.mockCategoryData,
      divisionName,
      pageName,
      pageType: 'category',
      brand,
      market,
      requestType,
    };

    it('returns an object which contains data and error fields', () => {
      const result = transformCrosslinkApiResponse(args);

      expect(result).toHaveProperty('crosslinks');
      expect(result).toHaveProperty('error');
    });

    it('transforms Adeptmind response to a format that the TagLinkGroup can consume', () => {
      const { crosslinks } = transformCrosslinkApiResponse(args);

      expect(Array.isArray(crosslinks)).toBe(true);
      crosslinks.forEach(crossLink => {
        expect(crossLink).toHaveProperty('name');
        expect(crossLink).toHaveProperty('link');
        expect(crossLink).toHaveProperty('ariaLabel');
      });
    });

    it('returns same amount of urls contained in the Adeptmind response, as well as an empty object for error when there are no data issues', () => {
      const { crosslinks, error } = transformCrosslinkApiResponse(args);

      expect(crosslinks.length).toEqual(mockData.mockCategoryData.length);
      expect(error).toEqual([]);
    });

    it('returns an error message when Adeptmind data is in the incorrect format', () => {
      const responseForEmptyDataIssues = [{ log: 'Invalid data format' }];
      const result = transformCrosslinkApiResponse({ data: mockProductData, divisionName, pageName, pageType: 'category', brand, market, requestType });

      expect(result.error).toEqual(responseForEmptyDataIssues);
    });

    it('returns an appropriate error message for inconsistencies in Adeptmind data', () => {
      const defectiveArgs = {
        data: mockData.defectCategoryData,
        divisionName,
        pageName,
        pageType: 'category',
        brand,
        market,
        requestType,
      };
      const expectedDataError = [
        {
          log: 'Missing title',
          url: 'https://oldnavy.gap.com/shop/example-page-1',
        },
        { log: 'Missing url', title: 'Example Page 2' },
        { log: 'Missing both title and url' },
      ];

      const result = transformCrosslinkApiResponse(defectiveArgs);
      expect(result.error).toEqual(expect.arrayContaining(expectedDataError));
    });

    it('returns a decoded anchor text if there is any special character', () => {
      const decodeArgs = {
        data: mockData.decodeCategoryData,
        divisionName,
        pageName,
        pageType: 'category',
        brand,
        market,
        requestType,
      };
      const expectedDecodedTitle = 'Pain | Relief™';
      const transformedResponse = transformCrosslinkApiResponse(decodeArgs);

      expect(transformedResponse.crosslinks[0].name).toEqual(expectedDecodedTitle);
    });

    it('returns urls with cl and nav tracking params', () => {
      const { crosslinks } = transformCrosslinkApiResponse(args);

      crosslinks.forEach(crosslink => {
        expect(crosslink.link).toContain('cl=true');
        expect(crosslink.link).toContain('nav=smartlink:some-division::some-page');
      });
    });
  });

  describe('product page', () => {
    const args = {
      data: mockProductData,
      divisionName,
      pageName,
      pageType: 'product',
      brand,
      market,
      requestType,
    };

    it('returns an object which contains data and error fields', () => {
      const result = transformCrosslinkApiResponse(args);

      expect(result).toHaveProperty('crosslinks');
      expect(result).toHaveProperty('error');
    });

    it('transforms Adeptmind response to a format that the TagLinkGroup can consume', () => {
      const { crosslinks } = transformCrosslinkApiResponse(args);

      expect(Array.isArray(crosslinks)).toBe(true);
      crosslinks.forEach(crossLink => {
        expect(crossLink).toHaveProperty('name');
        expect(crossLink).toHaveProperty('link');
        expect(crossLink).toHaveProperty('ariaLabel');
      });
    });

    it('returns same amount of urls contained in the Adeptmind response, as well as an empty object for error when there are no data issues', () => {
      const { crosslinks, error } = transformCrosslinkApiResponse(args);

      expect(crosslinks.length).toEqual(mockData.mockCategoryData.length);
      expect(error).toEqual([]);
    });

    it('returns an error message when Adeptmind data is in the incorrect format', () => {
      const responseForEmptyDataIssues = [{ log: 'Invalid data format' }];
      const result = transformCrosslinkApiResponse({
        data: mockData.mockCategoryData,
        divisionName,
        pageName,
        pageType: 'product',
        brand,
        market,
        requestType,
      });

      expect(result.error).toEqual(responseForEmptyDataIssues);
    });

    it('returns an appropriate error message for no data in Adeptmind data', () => {
      defectProductData[''] = '';
      const defectiveArgs = {
        data: defectProductData,
        divisionName,
        pageName,
        pageType: 'product',
        brand,
        market,
        requestType,
      };
      const expectedDataError = [{ log: 'Missing both title and url' }];

      const result = transformCrosslinkApiResponse(defectiveArgs);
      expect(result.error).toEqual(expect.arrayContaining(expectedDataError));
    });

    it('returns a decoded anchor text if there is any special character', () => {
      const decodeArgs = {
        data: decodeProductData,
        divisionName,
        pageName,
        pageType: 'product',
        brand,
        market,
        requestType,
      };
      const expectedDecodedTitle = 'Pain | Relief™';
      const transformedResponse = transformCrosslinkApiResponse(decodeArgs);

      expect(transformedResponse.crosslinks[0].name).toEqual(expectedDecodedTitle);
    });

    it('returns urls with cl and nav tracking params', () => {
      const { crosslinks } = transformCrosslinkApiResponse(args);

      crosslinks.forEach(crosslink => {
        expect(crosslink.link).toContain('cl=true');
        expect(crosslink.link).toContain('nav=smartlink:some-division::some-page');
      });
    });

    it('uses category data transform logic if similarProductsApiEnabled', () => {
      const result = transformCrosslinkApiResponse({ ...args, data: mockData.mockCategoryData, similarProductsApiEnabled: true });

      expect(result).toHaveProperty('crosslinks');
      expect(result).toHaveProperty('error');
      expect(result.crosslinks.length).toBeGreaterThan(0);
      expect(result.error).toEqual([]);
    });

    describe('environment aware brand urls', () => {
      it.each(environmentAwareUrlsEcom)(
        'should use correct environment aware url when brand is $brandToTest, market is $marketToTest, environment is $environment',
        ({ brandToTest, marketToTest, environment, expectedUrl }) => {
          jest.mocked(getTargetEnv).mockReturnValue(environment as Environment);
          const { crosslinks } = transformCrosslinkApiResponse({ ...args, brand: brandToTest, market: marketToTest, requestType });

          const result = crosslinks.every(({ link }) => {
            return link.startsWith(expectedUrl);
          });

          expect(result).toBe(true);
        }
      );

      it.each(environmentAwareUrlsWip)(
        'should use correct environment aware url when brand is $brandToTest, market is $marketToTest, environment is $environment, requestType is wip',
        ({ brandToTest, marketToTest, environment, expectedUrl }) => {
          jest.mocked(getTargetEnv).mockReturnValue(environment as Environment);
          const { crosslinks } = transformCrosslinkApiResponse({ ...args, brand: brandToTest, market: marketToTest, requestType: 'wip' });

          const result = crosslinks.every(({ link }) => {
            return link.startsWith(expectedUrl);
          });

          expect(result).toBe(true);
        }
      );

      it.each(environmentAwareUrlsApp)(
        'should use correct environment aware url when brand is $brandToTest, market is $marketToTest, environment is $environment, requestType is app',
        ({ brandToTest, marketToTest, environment, expectedUrl }) => {
          jest.mocked(getTargetEnv).mockReturnValue(environment as Environment);
          const { crosslinks } = transformCrosslinkApiResponse({ ...args, brand: brandToTest, market: marketToTest, requestType: 'app' });

          const result = crosslinks.every(({ link }) => {
            return link.startsWith(expectedUrl);
          });

          expect(result).toBe(true);
        }
      );
    });
  });
});
