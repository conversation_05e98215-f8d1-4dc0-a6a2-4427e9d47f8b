# SEO configs

This package handles configurations for all SEO services and applications. The function returns a normalized static path to the specified applications' config folder that can then be used to configure the application.

## Set Up

### Install

```js
npm i --save @ecom-next/seo-helpers/configs
```

### Basic Usage

```js
const configPath = require('@ecom-next/seo-helpers/configs')('seo-worker');
```
