// @ts-nocheck
/**
 * This function returns a boolean to determine is an input is empty or not.
 * @param {any} input
 * @returns {boolean}
 */
const isEmpty = input => !input
    || (input.constructor === Object && Object.keys(input).length === 0)
    || input.length === 0;

/**
 * This function takes an array input and chunks it into sizeable elements.
 * @param {Array} arr
 * @param {number} chunkSize
 * @returns {Array[]} an array of arrays where each element's max length is the chunkSize
 */
const chunkArray = (arr, chunkSize) => {
  const arrCopy = arr.slice();
  const finalArr = [];
  while (arrCopy.length > chunkSize) {
    const keys = arrCopy.splice(0, chunkSize);
    finalArr.push(keys);
  }
  finalArr.push(arrCopy);
  return finalArr;
};

/**
 * This function validates the storage type and returns a formatted version of it.
 * @param {string} storageType
 * @returns {string}
 */
const validateStorageType = (storageType) => {
  if (!storageType || typeof storageType !== 'string') {
    throw new TypeError(`${storageType} is not a valid storage type for this library.`);
  }

  return storageType.toLowerCase();
};

/**
 * This method recieves the input message and formats it.
 * @param {*} response
 * @param {string} message
 * @returns {{success: boolean, response: *, message: string}}
 */
const respondWithSuccess = (response, message = '') => ({ success: true, response, message });

// eslint-disable-next-line valid-jsdoc
/**
 * This method generates our error response string.
 * @param {string} msg
 * @param {{status: string, code: string, message: string}} err
 * @returns {string}
 */
const generateErrorResponse = (msg, err) => {
  const customMsg = msg ? `${msg}.` : '';
  const errCode = err?.details?.errorCode || '';
  const errMsg = err?.message || '';

  return `${customMsg} ${errCode} ${errMsg}`.trim();
};

/**
 * This method logs the failure response for azure methods
 * @param {string} logLevel detail the log level. ex: error
 * @param {string} failureMessage
 * @param {any} error
 * @returns {{success: boolean, response: string}}
 */
const respondWithFailure = (logLevel, failureMessage, error) => {
  const failureResponse = generateErrorResponse(failureMessage, error);

  return {
    success: false,
    response: failureResponse
  };
};

/**
 * This method logs the
 * @param {any} error
 * @param {string} errorMessage
 * @returns {{success: boolean, response: string, error: any}}
 */
const respondWithS3Failure = (error, errorMessage) => ({
  success: false,
  error,
  response: errorMessage
});

module.exports = {
  isEmpty,
  chunkArray,
  validateStorageType,
  respondWithSuccess,
  generateErrorResponse,
  respondWithFailure,
  respondWithS3Failure
};
