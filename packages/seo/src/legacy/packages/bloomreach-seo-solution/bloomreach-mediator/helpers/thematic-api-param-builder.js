// @ts-nocheck
/* eslint-disable camelcase */
const { services } = require('@ecom-next/seo-helpers/configs').default;
const { efqQueryBuilder } = require('./efq-query-builder');
const { getDomainKeyByBrand } = require('./get-domain-key-by-brand');

var fl = [
 "brand",
 "defaultColorMarketingMessage",
 "description",
 "is_live",
 "maxSalePrice",
 "minSalePrice",
 "pid",
 "price",
 "price_range",
 "price_type",
 "pristine_images",
 "promotions",
 "sale_price",
 "sale_price_range",
 "score",
 "sku_color",
 "sku_color_group",
 "sku_sizes",
 "sku_swatch_images",
 "sku_thumb_images",
 "styleMarketingMessage",
 "style_color_id",
 "swatch_image_attribute",
 "thumb_image",
 "title",
 "url",
 "webProductType",
 "zImage"
].join(',');

const buildThematicApiParams = ({
  _br_uid_2,
  brand,
  debug,
  efqQueryArray,
  facetQueryArray,
  referrer,
  requestId,
  rows,
  start,
  theme,
  url,
}) => {
  const thematicQueryParams = new URLSearchParams({
    '_br_uid_2': _br_uid_2 || 'test',
    'account_id': services?.bloomreach?.auth[brand]?.accountId,
    'auth_key': '',
    'debug': debug || false,
    'domain_key': getDomainKeyByBrand(brand),
    fl,
    'q': theme,
    'ref_url': referrer || '',
    'request_id': requestId || 0,
    'request_type': 'thematic',
    'rows': rows || 100,
    'search_type': 'keyword',
    'start': start || 0,
    'stats.field': 'sale_price',
    'url': url || 'invalid_url',
  });

  if (facetQueryArray?.length > 0) {
    facetQueryArray.forEach((fq) => thematicQueryParams.append('fq', fq));
  };

  if (efqQueryArray?.length > 0) {
    efqQueryBuilder({ efq: efqQueryArray })
      .forEach((filter) => thematicQueryParams.append('efq', filter));
  };

  return thematicQueryParams;
};

module.exports = { buildThematicApiParams };
