// @ts-nocheck
const { expect } = require('chai');
const { transformWidgetApiReponse } = require('../transform-widget-api-response');
const brWidgetResponse = require('../../../data-fixtures/mock-br-widget-response.json');
const defectWidgetResponse = require('../../../data-fixtures/defect-br-widget-response.json');
const decodeWidgetResponse = require('../../../data-fixtures/decode-br-widget-response.json');

describe('transformWidgetApiReponse', () => {
  const divisionName = 'some-division';
  const pageName = 'some-page';
  const args = { brData: brWidgetResponse, divisionName, pageName };

  it('returns an object which contains data and error fields', () => {
    expect(transformWidgetApiReponse(args)).to.be.an('object').that.contains.keys(['crosslinks', 'error', 'globalVariables']);
  });

  it('transforms bloomreach response to a format that the TagLinkGroup can consume', () => {
    const { crosslinks } = transformWidgetApiReponse(args);

    expect(crosslinks).to.be.an('array');
    crosslinks.forEach((crossLink) => {
      expect(crossLink).to.be.an('object').with.keys(['name', 'link', 'ariaLabel']);
    });
  });

  it('returns same amount of urls contained in the bloomreach response, as well as an empty object for error when there are no data issues', () => {
    const { 'related-category': relatedCategory, 'related-item': relatedItems } = brWidgetResponse;
    const expectedNumOfUrls = [...relatedCategory, ...relatedItems].length;

    expect(transformWidgetApiReponse(args).crosslinks.length).to.eql(expectedNumOfUrls);
    expect(transformWidgetApiReponse(args).error.length).to.eql(0);
  });

  it('returns the global variables contained in the bloomreach response', () => {
    const { globalVariables } = transformWidgetApiReponse(args);
    expect(Object.keys(globalVariables).length).to.eql(4);
    expect(globalVariables).to.be.an('object').with.keys(['br_iuid', 'br_related_rid', 'br_related_rid_tag', 'br_iuid_tag']);
  });


  it('returns an error message when there is no data to transform', () => {
    const responseForEmptyDataIssues = [{ log: 'The bloomreach data is empty!' }];

    expect(transformWidgetApiReponse('').error).to.eql(responseForEmptyDataIssues);
    expect(transformWidgetApiReponse({}).error).to.eql(responseForEmptyDataIssues);
    expect(transformWidgetApiReponse({ divisionName, pageName }).error).to.eql(responseForEmptyDataIssues);
  });

  it('returns an appropriate error message for inconsistencies in bloomreach data', () => {
    const defectiveArgs = { brData: defectWidgetResponse, divisionName, pageName };
    const expectedDataError = [
      { log: 'Missing url', title: 'Christmas Trees' },
      { log: 'Missing title', url: '/sweet-smiles-cherry-sours-7-oz-bag.html' },
      { log: 'Missing both title and url' },
    ];

    expect(transformWidgetApiReponse(defectiveArgs).error).to.deep.include.members(expectedDataError);
  });

  it('returns a decoded anchor text if there is any special character', () => {
    const decodeArgs = { brData: decodeWidgetResponse, divisionName, pageName };
    const expectedDecodedTitle = 'Pain | Relief™';
    const transformedResponse = transformWidgetApiReponse(decodeArgs);

    expect(transformedResponse.crosslinks[0].name).to.eql(expectedDecodedTitle);
  });

  it('returns urls with cl and nav tracking params', () => {
    const { crosslinks } = transformWidgetApiReponse(args);
    crosslinks.forEach((crosslink) => {
      expect(crosslink.link).to.include('cl=true');
      expect(crosslink.link).to.include('nav=smartlink:some-division::some-page');
    });
  });
});
