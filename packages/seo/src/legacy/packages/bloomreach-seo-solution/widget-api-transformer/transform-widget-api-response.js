// @ts-nocheck
/* eslint-disable camelcase */
const { appendTrackingParams } = require('./append-tracking-params');
const he = require('he');

const transformWidgetApiReponse = ({ brData, divisionName, pageName }) => {
  if (!brData || Object.keys(brData).length === 0) return { crosslinks: [], error: [{ log: 'The bloomreach data is empty!' }] };

  const crosslinks = [];
  const error = [];

  const { 'related-category': relatedCategory, 'related-item': relatedItems, br_iuid, br_related_rid, br_related_rid_tag } = brData;
  const globalVariables = {
    br_iuid,
    br_related_rid,
    br_related_rid_tag,
    br_iuid_tag: `<script type="text/javascript">var br_iuid = "${br_iuid}";</script>`
  };

  [...relatedCategory, ...relatedItems].forEach((widgetElement) => {
    const title = widgetElement.anchor || widgetElement.title;
    const { url } = widgetElement;
    const cleanTitle = title ? he.decode(title) : '';

    if (!title && url) error.push({ log: 'Missing title', url });
    if (title && !url) error.push({ log: 'Missing url', title });
    if (!title && !url) error.push({ log: 'Missing both title and url' });

    if (cleanTitle && url) {
      crosslinks.push({
        name: cleanTitle,
        link: appendTrackingParams(url, divisionName, pageName),
        ariaLabel: cleanTitle,
      });
    }
  });

  return { crosslinks, error, globalVariables };
};

module.exports = { transformWidgetApiReponse };
