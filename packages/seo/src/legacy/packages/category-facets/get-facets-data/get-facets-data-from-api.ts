// @ts-nocheck
import { requestProductSearchV2Api } from './product-search-v2-client';
import { transformFacetsData } from '../helpers/transform-facets-data';
import { ErrorLog, FacetsNames, RequestArgs, TransformedFacets } from '../types';

const getFacetsDataFromApi = (
  { brand, market, cid, locale, styleId, host, mode, previewDate }:RequestArgs,
  facetsToTransform: FacetsNames[]
): Promise<TransformedFacets> => {
  return requestProductSearchV2Api({ market, brand, locale, cid, styleId, host, mode, previewDate })
    .then(apiRes => ({
      transformedFacetsData: transformFacetsData(apiRes, facetsToTransform),
      apiRes
    }))
    .catch(err => {
      const errObj: ErrorLog = {
        log: 'Error getting facets data from API.',
        type: 'Facets API Request',
        brand,
        market,
        locale,
        cid,
        styleId,
        error: err.stack || JSON.stringify(err)
      };
  
      return Promise.reject(errObj);
    });
}

export { getFacetsDataFromApi }
