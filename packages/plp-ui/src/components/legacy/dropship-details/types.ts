// @ts-nocheck
'use client'

export interface DropshipData {
  isExcludedFromPromotion?: string;
  isShowSellerName: boolean;
  isFreeShipping: string;
  isLowestPriceCC: boolean;
  vendorName?: string;
  vendorId?: string;
}

export interface DropshipProps {
  dropshipData: DropshipData;
}
export interface ShippedByData {
  isFreeShipping?: string;
  vendorName?: string;
}

export interface PromotionsData {
  isExcludedFromPromotion: string;
}

export interface ShippedByProps {
  shippedByData: ShippedByData;
}

export interface PromotionsProps {
  promotionsData: PromotionsData;
}
