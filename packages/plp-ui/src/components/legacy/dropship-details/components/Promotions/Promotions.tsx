// @ts-nocheck
'use client'

import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { PromotionsProps } from '../../types';
import Container from '../ShippedBySection/Container/Container';
import { DROPSHIP_EXCLUDED_FROM_PROMOTIONS } from '../../translation-tokens';

const Promotions = ({ promotionsData }: PromotionsProps) => {
  const { localize } = useLocalize();
  const { isExcludedFromPromotion } = promotionsData;
  const promotionsText = () => {
    if (isExcludedFromPromotion === 'true') {
      return `${localize(DROPSHIP_EXCLUDED_FROM_PROMOTIONS)}`;
    }
    return '';
  };
  return (
    <div data-testid="promotions">
      <Container>{promotionsText()}</Container>
    </div>
  );
};

export default Promotions;
