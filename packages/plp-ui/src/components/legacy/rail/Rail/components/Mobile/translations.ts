// @ts-nocheck
'use client'

const DASHBOARD_FILTER = 'dashboard.filter';
const AND_SORT_TEXT = 'dashboard.and_sort'
const MOBILE_FACETS_WRAPPER_DONE_TEXT = 'mobile_facets_wrapper.done_text';
const FILTERS_ARIA_LABEL = 'facet_bar.product_filters_aria_label';
const CLOSE_ARIA_LABEL = 'facet_bar.facet_tag_aria_label';

export {
  DASHBOARD_FILTER,
  AND_SORT_TEXT,
  MOBILE_FACETS_WRAPPER_DONE_TEXT,
  FILTERS_ARIA_LABEL,
  CLOSE_ARIA_LABEL,
};
