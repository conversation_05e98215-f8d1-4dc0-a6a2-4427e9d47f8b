// @ts-nocheck
import { Brands } from "@ecom-next/core/legacy/utility";

import { Theme } from "@ecom-next/core/react-stitch";
import { getRedesignStickyRailStyles } from "./sticky-rail-styles";

jest.mock("@ecom-next/core/react-stitch");
jest.mock("@ecom-next/sitewide/app-state-provider");

describe("getRedesignStickyRailStyles", () => {
  const theme = {
    color: {
      bk: "black",
    },
  };

  it.each(["desktop", "mobile"])(
    "When br redesign slice 2 is enabled and breakpoint is %s and isStuck is false",
    (breakpoint) => {
      const isStuck = false;
      const hasMarketingDataEBB = true;
      const isBRWhiteBackground = true;
      const isBRRedesignSlice2 = true;
      const result = getRedesignStickyRailStyles(
        theme as Theme,
        breakpoint,
        isStuck,
        hasMarketingDataEBB,
        isBRWhiteBackground,
        isBRRedesignSlice2
      );
      expect(result).toMatchSnapshot();
    }
  );

  it.each(["desktop", "mobile"])(
    "When br redesign slice 2 is enabled and breakpoint is %s and isStuck is true",
    (breakpoint) => {
      const isStuck = true;
      const hasMarketingDataEBB = true;
      const isBRWhiteBackground = true;
      const isBRRedesignSlice2 = true;
      const result = getRedesignStickyRailStyles(
        theme as Theme,
        breakpoint,
        isStuck,
        hasMarketingDataEBB,
        isBRWhiteBackground,
        isBRRedesignSlice2
      );
      expect(result).toMatchSnapshot();
    }
  );

  it.each(["desktop", "mobile"])(
    "When br redesign slice 2 is enabled and breakpoint is %s and hasMarketingDataEBB is false",
    (breakpoint) => {
      const isStuck = false;
      const hasMarketingDataEBB = false;
      const isBRWhiteBackground = true;
      const isBRRedesignSlice2 = true;
      const result = getRedesignStickyRailStyles(
        theme as Theme,
        breakpoint,
        isStuck,
        hasMarketingDataEBB,
        isBRWhiteBackground,
        isBRRedesignSlice2
      );
      expect(result).toMatchSnapshot();
    }
  );

  it.each([true, false])(
    "When isBRWhiteBackground is false and isBRRedesignSlice2 is %s",
    (isBRRedesignSlice2) => {
      const isStuck = true;
      const hasMarketingDataEBB = true;
      const breakpoint = "desktop";
      const isBRWhiteBackground = false;
      const result = getRedesignStickyRailStyles(
        theme as Theme,
        breakpoint,
        isStuck,
        hasMarketingDataEBB,
        isBRWhiteBackground,
        isBRRedesignSlice2
      );
      expect(result).toMatchSnapshot();
    }
  );
});
