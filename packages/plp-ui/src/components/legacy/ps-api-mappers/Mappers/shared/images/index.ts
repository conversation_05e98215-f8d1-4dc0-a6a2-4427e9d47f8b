// @ts-nocheck
'use client'

import { ProductImageData, ProductImages } from "@ecom-next/plp-ui/legacy/ps-api-response-types";

const buildKeyValueList = (
  data: ProductImageData[]
): Record<string, string> => {
  let keyValueList: Record<string, string> = {};
  data.forEach((image) => {
    keyValueList = { ...keyValueList, [image.type]: image.path };
  });
  return keyValueList;
};

const createImagesObj = (
  fieldMap: Record<string, string>,
  imageList: Record<string, string>
) => {
  let images: Record<string, string> = {};
  Object.keys(fieldMap).forEach((key: string) => {
    const value = imageList[key];
    const newKey = fieldMap[key];
    images = { ...images, [newKey]: value };
  });
  return images;
};

export const productImagesAdapter = (data: ProductImageData[]): ProductImages => {
  const avImagesFieldMap: Record<string, string> = {
    AV1: 'av1QuicklookImagePath',
    AV2: 'av2QuicklookImagePath',
    AV3: 'av3QuicklookImagePath',
    AV4: 'av4QuicklookImagePath',
    AV5: 'av5QuicklookImagePath',
    AV6: 'av6QuicklookImagePath',
    AV9: 'av9QuicklookImagePath',
    P01: 'p1QuicklookImagePath',
  };
  const zoomImagesFieldMap: Record<string, string> = {
    Z: 'p01ZoomImagePath',
    AV1_Z: 'av1ZoomImagePath',
  };
  const pristineImagesFieldMap: Record<string, string> = {
    PRST_IMG: 'pristine1ImagePath',
  };
  const otherImagesFieldMap: Record<string, string> = {
    VI_ONESITE: 'categoryLargeImage',
    QL: 'quicklookImage',
    S: 'colorSwatchImage',
  };

  const originalImageList = buildKeyValueList(data);
  const avImages = createImagesObj(avImagesFieldMap, originalImageList);
  const zoomImages = createImagesObj(zoomImagesFieldMap, originalImageList);
  const pristineImages = createImagesObj(
    pristineImagesFieldMap,
    originalImageList
  );
  const otherImages = createImagesObj(otherImagesFieldMap, originalImageList);

  return {
    avImages,
    zoomImages,
    pristineImages,
    otherImages,
  } as ProductImages;
};

export default productImagesAdapter;
