// @ts-nocheck
'use client'

export type FacetType = 'simple' | 'complex' | 'range';

export type SimpleFacetOption = {
  id?: string;
  name?: string;
  isActive?: 'true';
  value?: string;
  applied?: boolean;
};

export type Size = {
  description: string;
  dimId: string;
  id: string;
  name: string;
  searchFacetOptionId: string;
  selected: boolean;
  sfcId: string;
  sfcName: string;
  variantName: string;
};

export type StyleGroups = {
  name?: string;
  sizes?: Size[];
};

export type SizeVariants = {
  name?: string;
  selected?: boolean;
  styleGroups?: StyleGroups[];
};

export type MinMaxRange = {
  min?: number;
  max?: number;
};

export type Facet = {
  displayName?: string;
  facetDisplay?: string;
  facetLayout?: string;
  hidden?: boolean;
  id?: string;
  isActive?: 'true';
  name?: string;
  order?: number;
  selectionType?: string;
  type?: FacetType;
};

export type SimpleFacet = Facet & {
  searchFacetId?: string;
  options?: SimpleFacetOption[];
};

export type RangeFacet = Facet & {
  appliedRange?: unknown;
  isOpen?: boolean;
  isSelected?: boolean;
  maxOptions?: string[];
  maxValue?: number;
  minOptions?: string[];
  minValue?: number;
  range?: MinMaxRange;
  searchFacetOptionId?: string;
  shouldShow?: boolean;
  value?: MinMaxRange;
};

export type SizeFacet = Facet & {
  sizeVariants?: SizeVariants[];
};

export type ComplexFacet = SizeFacet;

export type AllFacets = SimpleFacet | ComplexFacet | RangeFacet;

const SELECTION_TYPE = {
  SINGLE_SELECT: 'single-select',
  MULTI_SELECT: 'multi-select',
  SIZE: 'size',
};

export const defaultConfig = {
  facetDisplay: 'checkbox',
  type: 'simple',
  facetLayout: 'list',
  selectionType: SELECTION_TYPE.MULTI_SELECT,
};

const department: SimpleFacet = {
  displayName: 'Department',
  type: 'simple',
  facetDisplay: 'radio',
  facetLayout: 'list',
  selectionType: SELECTION_TYPE.SINGLE_SELECT,
  order: 1,
};

const style: SimpleFacet = {
  displayName: 'Category',
  type: 'simple',
  facetDisplay: 'checkbox',
  facetLayout: 'list',
  selectionType: SELECTION_TYPE.MULTI_SELECT,
  order: 2,
};

const size: ComplexFacet = {
  displayName: 'Size',
  type: 'complex',
  selectionType: SELECTION_TYPE.SIZE,
  order: 3,
};

const color: SimpleFacet = {
  displayName: 'Color',
  type: 'simple',
  facetDisplay: 'swatch',
  facetLayout: 'grid',
  selectionType: SELECTION_TYPE.MULTI_SELECT,
  order: 4,
};

const price: RangeFacet = {
  displayName: 'Price',
  type: 'range',
  selectionType: SELECTION_TYPE.SINGLE_SELECT,
  order: 5,
};

const reviewScore: SimpleFacet = {
  facetDisplay: 'ratings',
  facetLayout: 'list',
  type: 'simple',
  selectionType: SELECTION_TYPE.SINGLE_SELECT,
  order: 6,
};

const config: Record<string, SimpleFacet | RangeFacet | ComplexFacet> = {
  color,
  department,
  style,
  price,
  size,
  reviewScore,
};

export default config;
