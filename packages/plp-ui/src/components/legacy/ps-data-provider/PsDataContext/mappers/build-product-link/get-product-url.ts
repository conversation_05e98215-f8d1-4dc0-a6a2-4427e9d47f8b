// @ts-nocheck
'use client'

import { AdaptedProduct, GridParams } from "@ecom-next/plp-ui/legacy/ps-api-response-types";
import buildProductLink from "./index";
import { getHashParameter, getLocationSearch } from "@ecom-next/core/url-helper";

export const getProductUrl = (
  product: AdaptedProduct,
  cid?: string,
  grid?: GridParams
) => {
  const productLink = buildProductLink({
    pid: product.productID,
    additionalQueryParams: product?.additionalQueryParams,
    cid,
    pcid: new URLSearchParams(window.location.search).get("cid") as string,
    vid: product.vid,
    grid,
    nav: new URLSearchParams(getLocationSearch()).get("nav"),
    storeId: getHashParameter("storeId")
  });

  return !product.outOfStock ? productLink : undefined;
};
