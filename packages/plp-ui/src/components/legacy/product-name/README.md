# `ProductName`

## Overview

This package contains the category version of the `ProductName` component, which renders a Product Name for Product Card.

### Component Tree

- ProductName
  - index
  - CardName

## Technical Notes

### Props

| Name        | Type                                           | Default |
| ----------- | ---------------------------------------------- | ------- |
| clickable   | `boolean`                                      | -       |
| name        | `string`                                       | -       |
| link        | `string`                                       | -       |
| onClick     | `() => void \| undefined`                      | -       |
| breakPoints | `{ isMedium: boolean; isSmall: boolean; ... }` | -       |
| brand       | `string`                                       | -       |

### Usage

```
 <ProductName {...props} />
```

## Styles

All styles for this component live in [CardName.ts](https://github.gapinc.com/ecomfrontend/category-page/blob/main/packages/product-name/CardName.ts).

### Developing locally

- [Developing Packages Locally](https://github.gapinc.com/ecomfrontend/category-page/wiki/Developing-Packages-Locally)

## Storybook

You can see the Storybook from `ProductName` [here](https://category-page-storybook-main.apps.cfcommerce.dev.azeus.gaptech.com/?path=%2Fstory%2Fpackages-product-card-name--default&brand=gap).

## Breaking Changes

To view information regarding BREAKING CHANGES, please view the [MIGRATION.md file](https://github.gapinc.com/ecomfrontend/category-page/blob/main/packages/product-name/MIGRATION.md).
