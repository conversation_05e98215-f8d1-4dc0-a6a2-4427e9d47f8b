// @ts-nocheck
'use client'

import React from 'react';
import CardName from './CardName';
import { ProductCardNameProps } from './types';
import { useProductName } from './hooks/useProductName';
import { useBrRedesign2023, usePLPGapRedesign2024 } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useGridButtons } from '@ecom-next/plp-ui/legacy/grid-buttons';

const brRedesign2023LinkStyles = {
  minWidth: '0',
};

export const ProductCardName = (props: ProductCardNameProps) => {
  const { productId, breakPoints, brand } = props
  const { isSuccess, ...contextData } = useProductName(productId as string);
  const useContext = Object.keys(contextData).length > 0;
  const { name, clickable, link, onClick } = useContext ? contextData : props;
  const isBRRedesign2023Enabled = useBrRedesign2023();
  const { isAlternateGrid } = useGridButtons();
  const gapRedesign2024Enabled = usePLPGapRedesign2024()
  const isMobile = !breakPoints?.isXLarge;

  const productNameForBRRedesign = (
    <CardName
      breakPoints={breakPoints}
      dangerouslySetInnerHTML={{ __html: name as string }}
      isBRRedesign2023Enabled={isBRRedesign2023Enabled}
      isAltGrid={isAlternateGrid}
    />
  );

  const productName = (
    <CardName
      gapRedesign2024Enabled={gapRedesign2024Enabled}
      isMobile={isMobile}
      dangerouslySetInnerHTML={{ __html: name as string }}
    />
  );

  if (clickable) {
    return (
      <a href={link} onClick={onClick} css={isBRRedesign2023Enabled ? brRedesign2023LinkStyles: {}}>
        {brand === 'br' || brand === 'brfs'
          ? productNameForBRRedesign
          : productName}
      </a>
    );
  }

  return productName;
};
