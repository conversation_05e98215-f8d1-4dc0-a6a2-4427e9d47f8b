// @ts-nocheck
'use client'

import {
  CSSObject,
  Theme,
  forBrands,
  styled,
} from "@ecom-next/core/react-stitch";

interface BreakPoints {
  isSmall: boolean;
  isLarge: boolean;
  isMedium: boolean;
  isXLarge: boolean;
}

interface CardNameProps {
  bolder?: boolean;
  breakPoints?: BreakPoints;
  isBRRedesign2023Enabled?: boolean;
  isAltGrid?: boolean;
  gapRedesign2024Enabled?: boolean,
  isMobile?: boolean;
}

const gapStyles = (gapRedesign2024Enabled: boolean = false, isMobile: boolean = false): CSSObject => ({
  overflow: gapRedesign2024Enabled ? 'hidden' : '',
  fontSize: gapRedesign2024Enabled ? isMobile ? "14px" : "16px" : "0.8rem",
  textOverflow: gapRedesign2024Enabled ? 'ellipsis' : '',
  lineHeight: gapRedesign2024Enabled ? isMobile ? "20px" : "22px" : 1.125,
  letterSpacing: gapRedesign2024Enabled ? isMobile ? "0.28px" : "0.32px" : "0.5px",
  marginTop: gapRedesign2024Enabled ? isMobile ? "1rem" : "1.25rem" : "",
});

const oldNavyStyles = (): CSSObject => ({
  fontSize: "0.8rem",
});

const getBrFontStyle = (breakPoints?: BreakPoints): CSSObject => ({
  ...((breakPoints?.isSmall || breakPoints?.isMedium) && {
    fontSize: "0.6875rem",
    lineHeight: "0.75rem",
  }),
  ...(breakPoints?.isLarge && {
    fontSize: "0.8125rem",
    lineHeight: "0.8125rem",
  }),
  ...(breakPoints?.isXLarge && {
    fontSize: "0.9375rem",
    lineHeight: "1rem",
  }),
  fontWeight: 350,
  letterSpacing: "0.5px",
});

const getBrFontColor = (
  theme: Theme,
  isBRRedesign2023Enabled?: boolean
): CSSObject => ({
  color: isBRRedesign2023Enabled ? theme.color.b1 : theme.color.wh,
});

const getBRRedesign2023Styles = (
  theme: Theme,
  breakPoints: BreakPoints | undefined,
  isAltGrid: boolean = false
): CSSObject => {
  const getBreakPoints = breakPoints?.isLarge && !breakPoints.isXLarge;
  const isNextLine = getBreakPoints || isAltGrid;
  return {
    color: theme.color.b1,
    fontSize: "0.75rem",
    ...((breakPoints?.isSmall ? isAltGrid : !isNextLine) && { marginRight: "24px" }),
    ...(breakPoints?.isSmall && { marginBottom: "4px" }),
  };
};

const bananaRepublicStyles = (
  theme: Theme,
  breakPoints?: BreakPoints,
  isBRRedesign2023Enabled?: boolean,
  isAltGrid?: boolean
): CSSObject => ({
  ...theme.brandFontAlt,
  fontSize: "0.8125rem",
  color: theme.color.b1,
  textTransform: "uppercase",
  fontFamily: theme.brandFont.fontFamily,
  overflow: "hidden",
  textOverflow: "ellipsis",
  ...(isBRRedesign2023Enabled
    ? getBrFontColor(theme, isBRRedesign2023Enabled)
    : getBrFontColor(theme)),
  ...getBrFontStyle(breakPoints),
  ...(isBRRedesign2023Enabled &&
    getBRRedesign2023Styles(theme, breakPoints, isAltGrid)),
});

const athletaRedesignStyles = (theme: Theme) => ({
  fontSize: "14px",
  letterSpacing: "0px",
  color: theme.color.bk,
  fontWeight: "300",
  lineHeight: "15.4px",
  paddingTop: "4px",
  paddingBottom: "4px",
});

const defaultStyles = (): CSSObject => ({
  fontSize: "1rem",
  lineHeight: 1.125,
  letterSpacing: "0.5px",
});

const brandStyles = (
  theme: Theme,
  breakPoints: BreakPoints | undefined,
  isBRRedesign2023Enabled?: boolean,
  isAltGrid?: boolean,
  gapRedesign2024Enabled: boolean = false,
  isMobile: boolean = false
): CSSObject =>
  forBrands(theme, {
    gap: gapStyles(gapRedesign2024Enabled, isMobile),
    gapfs: gapStyles(gapRedesign2024Enabled, isMobile),
    br: () =>
      bananaRepublicStyles(
        theme,
        breakPoints,
        isBRRedesign2023Enabled,
        isAltGrid
      ),
    brfs: () =>
      bananaRepublicStyles(theme, breakPoints, isBRRedesign2023Enabled),
    on: oldNavyStyles,
    at: athletaRedesignStyles(theme),
    default: defaultStyles,
  }) as CSSObject;

const CardName = styled.div<CardNameProps>(
  ({
    theme,
    breakPoints,
    isBRRedesign2023Enabled = true,
    isAltGrid = false,
    bolder = false,
    gapRedesign2024Enabled,
    isMobile,
  }) => ({
    ...theme.brandFont,
    color: gapRedesign2024Enabled ? theme.color.bk : theme.color.g2,
    lineHeight: 1.4,
    textTransform: "none",
    fontWeight: bolder ? "bold" : gapRedesign2024Enabled ? 500 : "normal",
    ...brandStyles(
      theme,
      breakPoints,
      isBRRedesign2023Enabled,
      isAltGrid,
      gapRedesign2024Enabled,
      isMobile
    ),
  })
);

export default CardName;
