// @ts-nocheck
'use client'

import { usePsDataContext, ProductNameState } from "@ecom-next/plp-ui/legacy/ps-data-provider";

export const useProductName = (
  productId: string
): ProductNameState & { isSuccess: boolean } => {
  const { state } = usePsDataContext();
  const isSuccess = state?.isSuccess ?? false;
  const contextData = state?.data?.productInfos[productId]?.productName;

  return { ...contextData, isSuccess };
};

