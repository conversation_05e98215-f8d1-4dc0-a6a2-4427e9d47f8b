// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-hnc6ik"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-hnc6ik"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 and alt grid enabled state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders br redesign 2023 enabled state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-f4zzky"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-f4zzky"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-16fzz5u"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-16fzz5u"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-16fzz5u"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-16fzz5u"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-101x1th"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point Medium Bolder state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-101x1th"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-7c2hms"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-7c2hms"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-7c2hms"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-7c2hms"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point large state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1qzx1rb"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders break point xLarge state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled desktop state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1tt204t"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-guxafx"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 disabled mobile state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1r1wcud"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1pizajl"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1pizajl"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-1pi6ubu"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1pi6ubu"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1pi6ubu"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1pi6ubu"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-19mpl4o"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled desktop state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-19mpl4o"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="css-1pizajl"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1pizajl"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1t58csd"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="css-1mahkh1"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1mahkh1"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="css-1mahkh1"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-1mahkh1"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="css-19mpl4o"
  />
</DocumentFragment>
`;

exports[`<CardName/> > renders gap redesign 2024 enabled mobile state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="css-19mpl4o"
  />
</DocumentFragment>
`;
