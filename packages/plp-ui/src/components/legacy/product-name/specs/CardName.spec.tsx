// @ts-nocheck
import { snapshotTests, act } from "test-utils";
import Card<PERSON><PERSON> from '../CardName';

describe('<CardName/>', () => {
  // @ts-ignore
  snapshotTests(CardName, [
    [
      'break point large',
      {
        breakPoints: {
          isSmall: false,
          isMedium: false,
          isLarge: true,
          isXLarge: false,
        },
      },
    ],
    [
      'break point Medium Bolder',
      {
        bolder: true,
        breakPoints: {
          isSmall: false,
          isMedium: true,
          isLarge: false,
          isXLarge: true,
        },
      },
    ],
    [
      'break point xLarge',
      {
        breakPoints: {
          isSmall: false,
          isMedium: false,
          isLarge: false,
          isXLarge: true,
        },
      },
    ],
    [
      'br redesign 2023 enabled',
      {
        breakPoints: {
          isSmall: false,
          isMedium: false,
          isLarge: false,
          isXLarge: true,
        },
        isBRRedesign2023Enabled: true,
      },
    ],
    [
      'gap redesign 2024 enabled mobile',
      {
        isMobile: true,
        gapRedesign2024Enabled: true,
      },
    ],
    [
      'gap redesign 2024 enabled desktop',
      {
        isMobile: false,
        gapRedesign2024Enabled: true,
      },
    ],
    [
      'gap redesign 2024 disabled mobile',
      {
        isMobile: true,
        gapRedesign2024Enabled: false,
      },
    ],
    [
      'gap redesign 2024 disabled desktop',
      {
        isMobile: false,
        gapRedesign2024Enabled: false,
      },
    ],
    [
      'br redesign 2023 and alt grid enabled',
      {
        breakPoints: {
          isSmall: false,
          isMedium: false,
          isLarge: false,
          isXLarge: true,
        },
        isBRRedesign2023Enabled: true,
        isAltGrid: true,
      },
    ],
  ]);
});
