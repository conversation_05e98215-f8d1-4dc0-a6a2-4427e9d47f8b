// @ts-nocheck
import { mount, act } from "test-utils";
import { ProductCardName } from '../product-name';

import { ProductCardNameProps } from '../types';
import { PLPStateProvider } from '@ecom-next/plp-ui/legacy/plp-state-provider';

jest.mock('@ecom-next/plp-ui/legacy/plp-experiments', () => ({
  useBrRedesign2023: jest.fn(),
  usePLPGapRedesign2024: jest.fn(),
  GridToggle: {},
}));

const renderProductCardName = (props: ProductCardNameProps) => {
  return mount(
    <PLPStateProvider abSeg={{}}>
      <ProductCardName {...props} />
    </PLPStateProvider>
  );
};
describe('<ProductCardName/>', () => {
  it('should not render a link anchor if link or onClick is not defined', () => {
    const dummyParams = {
      clickable: false,
      name: 'Dummy product',
      breakPoints: {
        isMedium: false,
        isLarge: false,
        isSmall: true,
        isXLarge: false,
      },
      brand: 'gap',
    };
    const wrapper = renderProductCardName(dummyParams);
    expect(wrapper.find('a').length).toBe(0);
  });
  it('should render content on a link anchor if has link and onClick', () => {
    const dummyParams = {
      clickable: true,
      name: 'Dummy product',
      link: 'https://some/product/link',
      onClick: () => {},
      breakPoints: {
        isMedium: false,
        isLarge: true,
        isSmall: false,
        isXLarge: false,
      },
      brand: 'gap',
    };

    const wrapper = renderProductCardName(dummyParams);
    expect(wrapper.find('a').length).toBe(1);
  });
  it('should render cardName with br resdesign styles', () => {
    const dummyParams = {
      clickable: true,
      name: 'Dummy product',
      link: 'https://some/product/link',
      onClick: () => {},
      breakPoints: {
        isMedium: false,
        isLarge: false,
        isSmall: false,
        isXLarge: true,
      },
      brand: 'br',
    };

    const wrapper = renderProductCardName(dummyParams);
    expect(wrapper.find('div').length).toBe(1);
  });

  it('should render cardName with br resdesign styles for large viewport', () => {
    const dummyParams = {
      clickable: true,
      name: 'Dummy product',
      link: 'https://some/product/link',
      onClick: () => {},
      breakPoints: {
        isMedium: false,
        isSmall: false,
        isLarge: true,
        isXLarge: false,
      },
      brand: 'br',
    };

    const wrapper = renderProductCardName(dummyParams);
    expect(wrapper.find('div').length).toBe(1);
  });

  it('should render cardName with br resdesign styles for small viewport', () => {
    const dummyParams = {
      clickable: true,
      name: 'Dummy product',
      link: 'https://some/product/link',
      onClick: () => {},
      breakPoints: {
        isMedium: false,
        isSmall: true,
        isLarge: false,
        isXLarge: false,
      },
      brand: 'br',
    };
    const wrapper = renderProductCardName(dummyParams);
    expect(wrapper.find('div').length).toBe(1);
  });
});
