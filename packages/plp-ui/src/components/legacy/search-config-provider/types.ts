// @ts-nocheck
'use client'

import React from "react";
import {
  AUTOSUGGEST_BLOOMREACH_FLAG,
  SEARCH_BLOOMREACH_FLAG,
  BLOOMREACH_ENGINE_ID,
  PDS_ENGINE_ID,
  SEARCH_PRODUCT_SEARCH_FLAG,
  PRODUCT_SEARCH_ID,
  AUTOSUGGEST_PRODUCT_SEARCH_FLAG,
  NO_OP_ENGINE,
} from "./constants";

export type EnabledFeatures = {
  [AUTOSUGGEST_BLOOMREACH_FLAG]?: typeof AUTOSUGGEST_BLOOMREACH_FLAG | boolean;
  [SEARCH_BLOOMREACH_FLAG]?: typeof SEARCH_BLOOMREACH_FLAG | boolean;
  [BLOOMREACH_ENGINE_ID]?: typeof BLOOMREACH_ENGINE_ID | boolean;
  [PDS_ENGINE_ID]?: typeof PDS_ENGINE_ID | boolean;
  [SEARCH_PRODUCT_SEARCH_FLAG]?: typeof SEARCH_PRODUCT_SEARCH_FLAG | boolean;
  [PRODUCT_SEARCH_ID]?: typeof PRODUCT_SEARCH_ID | boolean;
  [AUTOSUGGEST_PRODUCT_SEARCH_FLAG]?:
    | typeof AUTOSUGGEST_PRODUCT_SEARCH_FLAG
    | boolean;
};

type EngineIDs =
  | typeof BLOOMREACH_ENGINE_ID
  | typeof PDS_ENGINE_ID
  | typeof BLOOMREACH_ENGINE_ID
  | typeof PRODUCT_SEARCH_ID;

export type SingleEngineEndpointConfig = {
  autosuggest?:
    | {
        apikey?: string;
        engineName: EngineIDs;
        url: string;
      }
    | typeof NO_OP_ENGINE;
  search?:
    | {
        apikey?: string;
        engineName: EngineIDs;
        url: string;
      }
    | typeof NO_OP_ENGINE;
};

type Engines = "BloomReach" | "ProductSearch" | "PDSSearch";

export type EngineEndpointConfiguration = Partial<
  Record<Engines, SingleEngineEndpointConfig>
>;

export type SearchConfigProps = {
  children: React.ReactNode;
  enabledFeatures: EnabledFeatures;
  engineEndpointConfiguration: EngineEndpointConfiguration;
  isSearchPsApiEnabled?: boolean;
};
