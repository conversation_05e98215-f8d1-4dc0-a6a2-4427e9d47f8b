// @ts-nocheck
'use client'

import { Facet } from '../types';

export const colorFacet: Facet = {
  displayName: 'Color',
  name: 'color',
  type: 'simple',
  options: [
    {
      id: '1022',
      name: 'Green',
      value: 'green',
      isActive: 'true',
    },
    {
      id: '1021',
      name: 'Blue',
      value: 'blue',
      isActive: 'true',
    },
    {
      id: '1040',
      name: 'Beige',
      value: 'beige',
      isActive: 'true',
    },
    {
      id: '1023',
      name: '<PERSON>',
      value: 'brown',
      isActive: 'true',
    },
    {
      id: '1018',
      name: 'Gray',
      value: 'gray',
      isActive: 'true',
    },
    {
      id: '1017',
      name: 'Black',
      value: 'black',
      isActive: 'true',
    },
    {
      id: '1029',
      name: 'White',
      value: 'white',
      isActive: 'true',
    },
  ],
  facetLayout: 'grid',
  facetDisplay: 'swatch',
};

export const departmentFacet: Facet = {
  displayName: 'Department',
  name: 'department',
  type: 'simple',
  options: [
    {
      id: '16',
      name: 'boys',
      value: 'boys',
      isActive: 'true',
    },
    {
      id: '48',
      name: 'girls',
      value: 'girls',
      isActive: 'true',
    },
  ],
  facetLayout: 'list',
  facetDisplay: 'radio',
};

export const styleFacet: Facet = {
  displayName: 'Category',
  name: 'style',
  type: 'simple',
  options: [
    {
      id: '1050838',
      name: 'skinny',
      value: 'skinny',
      isActive: 'true',
      applied: true,
    },
    {
      id: '1050839',
      name: 'slim',
      value: 'slim',
      isActive: 'true',
      applied: true,
    },
    {
      id: '1050840',
      name: 'straight',
      value: 'straight',
      isActive: 'true',
    },
    {
      id: '1098463',
      name: 'slim straight',
      value: 'slim straight',
      isActive: 'true',
    },
    {
      id: '1076087',
      name: 'athletic',
      value: 'athletic',
      isActive: 'true',
      applied: false,
    },
    {
      id: '1050842',
      name: 'standard',
      value: 'standard',
      isActive: 'true',
      applied: true,
    },
    {
      id: '1050841',
      name: 'boot',
      value: 'boot',
      isActive: 'true',
    },
    {
      id: '1050843',
      name: 'relaxed',
      value: 'relaxed',
      isActive: 'true',
    },
    {
      id: '1107837',
      name: 'Selvedge',
      value: 'Selvedge',
      isActive: 'true',
      applied: false,
    },
    {
      id: '1105102',
      name: 'Wearlight',
      value: 'Wearlight',
      isActive: 'true',
    },
  ],
  facetLayout: 'list',
  facetDisplay: 'checkbox',
};

export const priceFacet: Facet = {
  displayName: 'Price',
  searchFacetId: 'price',
  name: 'price',
  type: 'range',
  range: {
    min: 10,
    max: 100,
  },
  minOptions: ['10'],
  maxOptions: ['100'],
};

const facets: Facet[] = [styleFacet, colorFacet, priceFacet, departmentFacet];

export default facets;
