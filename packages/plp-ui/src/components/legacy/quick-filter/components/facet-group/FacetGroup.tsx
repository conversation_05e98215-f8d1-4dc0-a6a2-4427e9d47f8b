// @ts-nocheck
'use client'

import React from 'react';
import { ScrollMenu, VisibilityContext } from 'react-horizontal-scrolling-menu';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Facet, Option, SizeOption } from '../../types';
import { FacetGroupItem } from './components/FacetGroupItem/FacetGroupItem';
import { <PERSON><PERSON>Container, <PERSON>s } from './styles';
import {
  ARIA_PREV_BUTTON_TEXT,
  ARIA_NEXT_BUTTON_TEXT,
} from '../../localization-tokens';

export interface FacetGroupProps {
  facets: Facet[] | undefined;
  isMobile: boolean;
  selectedFacet: Facet | undefined;
  appliedOptions: (Option | SizeOption)[];
  handleFacetTabClick: (facet: Facet) => void;
}

function Arrow({
  children,
  disabled,
  onClick,
  ariaLabel,
  className,
}: {
  children: React.ReactNode;
  disabled: boolean;
  onClick: VoidFunction;
  ariaLabel: string;
  className: string;
}) {

  return (
    <Arrows
      aria-label={ariaLabel}
      className={className}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </Arrows>
  );
}

function LeftArrow() {
  const { localize } = useLocalize();
  const { isFirstItemVisible, scrollPrev } =
    React.useContext(VisibilityContext);

  return (
    <Arrow
      ariaLabel={localize(ARIA_PREV_BUTTON_TEXT)}
      className="left-shadow"
      disabled={isFirstItemVisible}
      onClick={() => scrollPrev()}
    >
      <svg
        fill="none"
        height="17"
        viewBox="0 0 10 17"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clipRule="evenodd"
          d="M8.06937 0.5L0.514648 8.5L8.06937 16.5L9.48588 15L3.34767 8.5L9.48588 2L8.06937 0.5Z"
          fill="#003764"
          fillRule="evenodd"
        />
      </svg>
    </Arrow>
  );
}

function RightArrow() {
  const { localize } = useLocalize();
  const { isLastItemVisible, scrollNext } = React.useContext(VisibilityContext);

  return (
    <Arrow
      ariaLabel={localize(ARIA_NEXT_BUTTON_TEXT)}
      className="right-shadow"
      disabled={isLastItemVisible}
      onClick={() => scrollNext()}
    >
      <svg
        fill="none"
        height="17"
        viewBox="0 0 10 17"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clipRule="evenodd"
          d="M1.93063 0.5L9.48535 8.5L1.93063 16.5L0.514124 15L6.65233 8.5L0.514124 2L1.93063 0.5Z"
          fill="#003764"
          fillRule="evenodd"
        />
      </svg>
    </Arrow>
  );
}

type ScrollVisibilityApiType = React.ContextType<typeof VisibilityContext>;

const FacetGroup = ({
  facets,
  isMobile,
  selectedFacet,
  handleFacetTabClick,
  appliedOptions,
}: FacetGroupProps) => {
  const handleItemClick =
    (facet: Facet) =>
    ({ getItemById, scrollToItem }: ScrollVisibilityApiType) => {
      // NOTE: for center items
      scrollToItem(getItemById(facet.name), 'smooth', 'center', 'nearest');
      handleFacetTabClick(facet);
    };

  return (
    <ScrollContainer isMobile={isMobile}>
      {facets && facets.length > 0 && (
        <ScrollMenu
          LeftArrow={isMobile ? <></> : LeftArrow}
          RightArrow={isMobile ? <></> : RightArrow}
        >
          {facets.map((facet) => (
            <FacetGroupItem
              key={facet.name}
              active={selectedFacet?.name === facet.name}
              appliedOptions={appliedOptions}
              facet={facet}
              itemId={facet.name}
              onClick={handleItemClick(facet)}
              selected={selectedFacet?.name === facet.name}
            />
          ))}
        </ScrollMenu>
      )}
    </ScrollContainer>
  );
};

export default FacetGroup;
