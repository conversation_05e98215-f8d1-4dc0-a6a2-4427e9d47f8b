// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Styled -> <ClearFilters /> > should match with snapshot desktop 1`] = `
"<Styled(div)>
  <Insertion cache={{...}} serialized={{...}} isStringTag={true} />
  <div className="css-mrvkvj" />
</Styled(div)>"
`;

exports[`Styled -> <ClearFilters /> > should match with snapshot mobile 1`] = `
"<Styled(div)>
  <Insertion cache={{...}} serialized={{...}} isStringTag={true} />
  <div className="css-mrvkvj" />
</Styled(div)>"
`;
