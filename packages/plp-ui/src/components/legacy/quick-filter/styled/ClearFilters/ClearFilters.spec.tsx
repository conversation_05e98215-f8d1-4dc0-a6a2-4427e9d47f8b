// @ts-nocheck
import React from 'react';
import { mount, act } from "test-utils";
import ClearFilters from '.';


const mountClearFiltersWithProviders = ({
  // @ts-ignore
  breakpoint,
  ...props
}) =>
  mount(
    // @ts-ignore
    <ClearFilters {...props} />,
    {
      breakpoint,
    }
  );

beforeEach(() => {
  jest.clearAllMocks();
});

describe('Styled -> <ClearFilters />', () => {
  it('should match with snapshot desktop', () => {
    const wrapper = mountClearFiltersWithProviders({ breakpoint: 'x-large' });
    expect(wrapper.debug()).toMatchSnapshot();
  });

  it('should match with snapshot mobile', () => {
    const wrapper = mountClearFiltersWithProviders({ breakpoint: 'small' });
    expect(wrapper.debug()).toMatchSnapshot();
  });
});
