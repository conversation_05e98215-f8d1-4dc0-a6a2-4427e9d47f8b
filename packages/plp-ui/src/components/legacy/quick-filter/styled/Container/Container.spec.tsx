// @ts-nocheck
import React from 'react';
import { mount, act } from "test-utils";
import Container from '.';

describe('Styled -> <Container />', () => {
  it('should match with snapshot mobile', () => {
    const wrapper = mount(<Container isMobile />);
    expect(wrapper.debug()).toMatchSnapshot();
  });

  it('should match with snapshot desktop', () => {
    const wrapper = mount(<Container />);
    expect(wrapper.debug()).toMatchSnapshot();
  });
});
