// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Styled -> <Container /> > should match with snapshot desktop 1`] = `
"<Styled(div)>
  <Insertion cache={{...}} serialized={{...}} isStringTag={true} />
  <div className="css-ts0rve" />
</Styled(div)>"
`;

exports[`Styled -> <Container /> > should match with snapshot mobile 1`] = `
"<Styled(div) isMobile={true}>
  <Insertion cache={{...}} serialized={{...}} isStringTag={true} />
  <div className="css-1ilv9mw" />
</Styled(div)>"
`;
