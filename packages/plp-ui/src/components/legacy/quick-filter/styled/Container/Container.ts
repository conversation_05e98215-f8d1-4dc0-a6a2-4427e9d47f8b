// @ts-nocheck
'use client'

import { styled } from "@ecom-next/core/react-stitch";

interface ContainerProps {
  isMobile?: boolean;
}

const Container = styled.div<ContainerProps>(({ isMobile = false }) => ({
  backgroundColor: 'white',
  width: '100%',
  ...(isMobile
    ? {
        maxWidth: '100vw',
      }
    : {
        height: '100%',
        minHeight: '90px',
        marginBottom: '17px',
        padding: '0px 1rem',
      }),
}));

export default Container;
