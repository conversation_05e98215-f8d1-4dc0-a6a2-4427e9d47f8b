// @ts-nocheck
'use client'

import {
  XLARGE,
  LARGE,
  MEDIUM,
  SMALL,
} from "@ecom-next/core/breakpoint-provider";
import { Theme } from "@emotion/react";

export type GridData = {
  sizes?: ColumnsConfig;
  type?: string;
  [key: string]: any; // eslint-disable-line @typescript-eslint/no-explicit-any
};

export type ColumnsConfig = {
  [XLARGE]?: number;
  [LARGE]?: number;
  [MEDIUM]?: number;
  [SMALL]?: number;
  [key: number]: number;
};

export type DefaultedColumnsConfig = {
  [XLARGE]: number;
  [LARGE]: number;
  [MEDIUM]: number;
  [SMALL]: number;
  [key: number]: number;
};

export type Contents = {
  default: React.FC<GridData>;
  [key: string]: React.FC<GridData>;
};

export interface GridProps {
  data?: Array<GridData>;
  contents: Contents;
  index: number;
  columns?: ColumnsConfig;
  spacing?: number;
  theme: Theme;
  isAlternateGrid?: boolean;
  gridSize?: string;
}
