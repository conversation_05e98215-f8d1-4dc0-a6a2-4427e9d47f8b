// @ts-nocheck
'use client'

import React from "react";
import { ProductGridIdState, usePsDataContext } from "@ecom-next/plp-ui/legacy/ps-data-provider";

export const useGridContext = (): {
  productsGridIds: ProductGridIdState[];
  isSuccess: boolean;
} => {
  const context = usePsDataContext();
  const isSuccess = context?.state?.isSuccess ?? false;

  const { productsGridIds } = context?.state?.data ?? [];
  return {
    productsGridIds,
    isSuccess,
  };
};
