// @ts-nocheck
import React from "react";
import { renderHook, act } from "test-utils";
import { PsDataProvider } from "@ecom-next/plp-ui/legacy/ps-data-provider";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HttpResponse, http } from "msw";
import { setupServer } from "msw/node";
import { useGridContext } from ".";
import productsV2Json from "../../../../../ps-data-provider/src/PsDataContext/fixtures/products-v2.json";

import { AppStateProvider } from "@ecom-next/sitewide/app-state-provider";
import { Brands } from "@ecom-next/core/legacy/utility";
import { usePLPState } from "@ecom-next/plp-ui/legacy/plp-state-provider";

const fakeUrlSuccess = "http://fakeurl.com";
const fakeUrlError = "http://fakeurlerror.com";

// TODO: these types are going to be replaced
type Body = any;
type Response = any;

const restHandlers = [
  http.get<Body, Response>(
    `${fakeUrlSuccess}/commerce/search/products/v2/cc`,
    async (_req, res, context) => {
      return HttpResponse.json(productsV2Json, {status: 200});
    }
  ),
  http.get<Body, Response>(
    `${fakeUrlError}/commerce/search/products/v2/cc`,
    async (_req, res, context) => {
      return HttpResponse.json({}, {status: 500});
    }
  ),
];
jest.mock("@ecom-next/plp-ui/legacy/plp-state-provider");
const server = setupServer(...restHandlers);

beforeAll(() =>{
  server.listen({ onUnhandledRequest: "error" });
  (usePLPState as Mock).mockReturnValue({
   abSeg: {}
  });
});

afterAll(() => {
  server.close();
  server.resetHandlers();
});

const client = new QueryClient({
  logger: {
    log: console.log,
    warn: console.warn,
    // ✅ no more errors on the console for tests
    error: process.env.NODE_ENV === "test" ? () => {} : console.error,
  },
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  //@ts-ignore
  (<AppStateProvider value={{
    brandName: Brands.Gap,
    locale: 'en_US',
    market: 'us'}}>
    <QueryClientProvider client={client}>
      <PsDataProvider url={fakeUrlSuccess} cid={""}>{children}</PsDataProvider>
    </QueryClientProvider>
  </AppStateProvider>)
);

describe("useGridContext", () => {
  it(
    "should return the grid data from context if ps context returns with data",
    async () => {
      const { result, waitFor } = renderHook(() => useGridContext(), {
        wrapper,
      });

      await waitFor(() => { if (!result?.current?.isSuccess) throw new Error("Error fetching data"); }, { timeout: 7000 });

      // TODO: will handle these jest issues soon
      // eslint-disable-next-line jest/no-standalone-expect
      expect(result.current).toMatchSnapshot();
    },
    7000
  );
});
