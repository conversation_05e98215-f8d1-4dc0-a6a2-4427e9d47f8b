// @ts-nocheck
'use client'

import { colorSortFn, departmentSortFn, styleSortFn } from './facet-sort-map';

const CURRENT_COLOR_NAME = 'color_facet';
const CURRENT_SIZES_NAME = 'sizes';
const SKU_COLOR_GROUP = 'sku_color_group';
const SKU_SIZES = 'sku_sizes';

export const getDefaultFacetNameMap = options => ({
  color: options.isSkuSizesEnabled ? SKU_COLOR_GROUP : CURRENT_COLOR_NAME,
  department: 'department',
  style: 'style',
  price: 'sale_price',
  size: options.isSkuSizesEnabled ? SKU_SIZES : CURRENT_SIZES_NAME,
  rise: 'rise',
  length: 'length',
  sustainability: 'sustainability',
  'performance / technology': 'perf_tech',
  collection: 'collection',
  fit: 'fit',
  wash: 'wash',
  activity: 'activity',
  occasion: 'occasion',
  'leg shape': 'leg_shape',
  'warmth rating': 'warmth_rating',
  'fabric material': 'fabric',
  collar: 'collar',
  'sleeve length': 'sleeve_length',
  features: 'details',
  'support type': 'support_type',
  brand: 'brand',
  neckline: 'neckline',
  'pregnancy stage': 'pregnancy_stage',
  'panel type': 'panel_type',
  'package quantity': 'package_quantity',
});

export const DEFAULT_FACET_BUILDER = {
  simple: 'simpleFacetBuilder',
  range: 'rangeFacetBuilder',
  size: 'sizeFacetBuilder',
};

export const getDefaultCoreFacetConfigurationOptionsMap = (options) => {
  const colorNameAttribute = options.isSkuSizesEnabled ? SKU_COLOR_GROUP : CURRENT_COLOR_NAME;
  const sizeNameAttribute = options.isSkuSizesEnabled ? SKU_SIZES : CURRENT_SIZES_NAME;
  return {
    department: {
      name: 'Department',
      builder: 'simple',
      optionIndex: 0,
      sort: departmentSortFn,
    },
    style: {
      name: 'Style',
      builder: 'simple',
      optionIndex: 1,
      sort: styleSortFn,
      showOnlyIf: 'department',
    },
    [sizeNameAttribute]: {
      name: 'Size',
      builder: 'size',
      optionIndex: 2,
      showOnlyIf: 'department',
    },
    [colorNameAttribute]: {
      name: 'Color',
      builder: 'simple',
      optionIndex: 3,
      sort: colorSortFn,
    },
    price: {
      name: 'Price',
      builder: 'range',
      optionIndex: 4,
    },
  };
};

export const DEFAULT_FLEX_FACET_CONFIGURATION_OPTIONS_MAP = {
  rise: {
    name: 'Rise',
    builder: 'simple',
  },
  length: {
    name: 'Length',
    builder: 'simple',
  },
  sustainability: {
    name: 'Sustainability',
    builder: 'simple',
  },
  perf_tech: {
    name: 'Performance / Technology',
    builder: 'simple',
  },
  collection: {
    name: 'Collection',
    builder: 'simple',
  },
  fit: {
    name: 'Fit',
    builder: 'simple',
  },
  wash: {
    name: 'Wash',
    builder: 'simple',
  },
  activity: {
    name: 'Activity',
    builder: 'simple',
  },
  occasion: {
    name: 'Occasion',
    builder: 'simple',
  },
  leg_shape: {
    name: 'Leg Shape',
    builder: 'simple',
  },
  warmth_rating: {
    name: 'Warmth Rating',
    builder: 'simple',
  },
  fabric: {
    name: 'Fabric Material',
    builder: 'simple',
  },
  collar: {
    name: 'Collar',
    builder: 'simple',
  },
  sleeve_length: {
    name: 'Sleeve Length',
    builder: 'simple',
  },
  details: {
    name: 'Features',
    builder: 'simple',
  },
  support_type: {
    name: 'Support Type',
    builder: 'simple',
  },
  brand: {
    name: 'Brand',
    builder: 'simple',
  },
  neckline: {
    name: 'Neckline',
    builder: 'simple',
  },
  pregnancy_stage: {
    name: 'Pregnancy Stage',
    builder: 'simple',
  },
  panel_type: {
    name: 'Panel Type',
    builder: 'simple',
  },
  package_quantity: {
    name: 'Package Quantity',
    builder: 'simple',
  },
};
