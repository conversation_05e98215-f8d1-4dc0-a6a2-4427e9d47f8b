// @ts-nocheck
'use client'

import React from "react";
import { ScrollButton } from '../styles'

type Props = {
    children: React.ReactNode;
}

export const EmbeddedScroll = ({ children }: Props): JSX.Element => {
    const topFunction = () => { window.scroll({ top: 0, left: 0, behavior: 'smooth' }) }
    return (
        <ScrollButton data-testid="embedded-scroll" onClick={() => topFunction()}>
            {children}
        </ScrollButton>
    )
}
