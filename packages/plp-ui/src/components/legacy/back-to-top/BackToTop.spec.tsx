// @ts-nocheck
import { fireEvent, act } from 'test-utils';
  import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { BackToTop } from './BackToTop';
import { mount } from "test-utils";
import { EmotionJSX } from '@emotion/react/types/jsx-namespace';

const mountWithProviders = (
    children: EmotionJSX.Element
) =>
    mount(
        <BreakpointContext.Provider
            value={{
                smallerThan: () => true,
                greaterOrEqualTo: () => true,
                minWidth: () => true,
                maxWidth: () => true,
                size: 'large',
                orientation: '',
                media: '',
            }}
        >
            {[children]}
        </BreakpointContext.Provider>
    );

describe('BackToTop', () => {
    it('should handle scroll and render the BackToTop component when scroll position is greater than 0', () => {
        const component = mountWithProviders(<BackToTop />);
        Object.defineProperty(window, 'pageYOffset', { value: 100, writable: true });
        fireEvent.scroll(window);
        expect(component.find(BackToTop)).toHaveLength(1);
    });
});
