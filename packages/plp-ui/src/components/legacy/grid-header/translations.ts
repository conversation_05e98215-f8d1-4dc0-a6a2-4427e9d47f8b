// @ts-nocheck
'use client'

export const ALL_FILTERS_TEXT_DEFAULT = 'grid_header.default.all_filters_text';
export const ALL_FILTERS_TEXT_AT = 'grid_header.at.all_filters_text';
export const ALL_FILTERS_TEXT_BR = 'grid_header.br.all_filters_text';
export const ITEM_COUNT_TEXT = 'left_rail.item_count_text';
export const ITEM_COUNT_ARIA_LABEL = 'left_rail.item_count_aria_label';
export const CLEAR_FILTER_BUTTON = 'left_rail_lhf.clear_filter_button';
export const GRID_HEADER_FACET_DRAWER_SEE_RESULTS_TEXT = 'grid_header.facet_drawer.see_results_text'
export const CLOSE_ARIA_LABEL = 'facet_bar.facet_tag_aria_label';
export const FACET_REFERENCE_FACET_LABEL_DEPARTMENT = 'facet_reference.facet_label_department'
export const FACET_REFERENCE_FACET_LABEL_STYLE = 'facet_reference.facet_label_style';
export const FACET_REFERENCE_FACET_LABEL_COLOR = 'facet_reference.facet_label_color';
export const FACET_REFERENCE_FACET_LABEL_PRICE = 'facet_reference.facet_label_price';
export const FACET_REFERENCE_FACET_LABEL_SIZE = 'facet_reference.facet_label_size';
export const FACET_REFERENCE_FACET_LABEL_RATING = 'facet_reference.facet_label_rating';
