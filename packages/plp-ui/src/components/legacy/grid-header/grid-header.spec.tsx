// @ts-nocheck
import React from 'react';
import { screen, waitFor, render, RenderOptions } from 'test-utils';
import { GridHeader } from './grid-header';
import { PLPStateProvider } from '@ecom-next/plp-ui/legacy/plp-state-provider';
import mockData from './fixtures/mock-data.json';
import { StitchStyleProvider, Brands } from '@ecom-next/core/react-stitch';
import en_US from 'translations/translations-cron/translation-data/i18n/en_US.json';

import { BopisDataType } from '@ecom-next/core/legacy/bopis';
import { DataLayer } from '@ecom-next/core/legacy/app-state-provider/types';
import { BopisBarToggleAreaProps } from '../../bopis-bar/src/types';
import { XLARGE } from '@ecom-next/core/breakpoint-provider';
import { SizeModelToggleProps } from '@ecom-next/plp-ui/legacy/size-model-toggle';
import { sizeModelToggleFeatureVariables } from './fixtures/mock-feature-variables';
import { useCategoryFacetName } from '@ecom-next/plp-ui/legacy/facet-bar';
import { FeatureVariablesType, Provider as FeatureFlagProvider } from '@ecom-next/core/legacy/feature-flags';
import { GridToggle, usePLPGapRedesign2024 } from '@ecom-next/plp-ui/legacy/plp-experiments';
import GridButtons, { GridButtonsProps, useGridButtons } from '../../grid-buttons/src';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';

jest.mock('@ecom-next/plp-ui/legacy/plp-experiments');
jest.mock('@ecom-next/plp-ui/legacy/facet-bar');

jest.mock('@ecom-next/plp-ui/legacy/grid-buttons', async () => {
  const originalModule = await jest.importActual('@ecom-next/plp-ui/legacy/grid-buttons');
  return {
    ...originalModule,
    useGridButtons: jest.fn(),
    GridButtons: originalModule.GridButtons,
  };
});

const mockedUseGridButtons = useGridButtons as Mock;
const mockedPLPGapRedesign2024 = usePLPGapRedesign2024 as Mock;
const useCategoryFacetNameMock = useCategoryFacetName as Mock;

const renderGridButtons = (props?: GridButtonsProps, options?: RenderOptions) =>
  render(<GridButtons />, {
    ...options,
  });
describe('Grid Header', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });
  const renderGridHeader = (brand: Brands = Brands.Gap, bopisArgs?: BopisBarToggleAreaProps, isOnlyOneDepartment: boolean = false) =>
    render(
      <PLPStateProvider abSeg={{}}>
        <StitchStyleProvider brand={brand}>
          <FeatureFlagsContext.Provider value={{ enabledFeatures: { 'gap-redesign-2024': true } }}>
            <GridHeader
              facets={mockData.facets}
              appliedFacets={mockData.appliedFacets}
              bopisArgs={bopisArgs}
              isSortByRatingsEnabled={false}
              isOnlyOneDepartment={isOnlyOneDepartment}
            />
          </FeatureFlagsContext.Provider>
        </StitchStyleProvider>
      </PLPStateProvider>,
      { localization: en_US, breakpoint: XLARGE }
    );

  it('should render grid header correctly', async () => {
    const { getByTestId } = await renderGridHeader();
    expect(await getByTestId('grid-header-container')).toBeInTheDocument();
  });

  it.each([Brands.Gap, Brands.GapFactoryStore, Brands.OldNavy, Brands.Athleta])(
    'should render the all filters button first with the correct text for brand - %s on desktop',
    async brandName => {
      const isBrandAt = brandName === 'at';
      const expectedText = isBrandAt ? 'Filters (3)' : 'All Filters (3)';
      const { findByText } = await renderGridHeader(brandName);
      // add a baseElement in the destructuring assignment above to debug what was rendered
      // debug(baseElement)
      expect(await findByText(expectedText)).toBeInTheDocument();
    }
  );

  it.each([Brands.Gap, Brands.GapFactoryStore, Brands.OldNavy, Brands.Athleta])('should render filter icon for brand %s', async brandName => {
    const { getByTestId } = await renderGridHeader(brandName);
    expect(await getByTestId('iconFilter')).toBeInTheDocument();
  });

  it.each([Brands.Gap])('should open facet drawer on click of filter button %s', async brandName => {
    const { getByTestId } = renderGridHeader(brandName);
    screen.getAllByRole('button')[0].click();
    await waitFor(async () => {
      expect(getByTestId('plp__filters-drawer')).toBeInTheDocument();
    });
  });

  it.skip('should render the bopis in the grid header given bopisArgs properties', async () => {
    const bopisData: BopisDataType = {
      bopisInitialed: false,
      enabled: false,
      setActive: () => {},
      setBopisData: () => {},
      setEnabled: () => {},
      setSelectedStore: () => {},
      setStores: () => {},
      setPostalCode: () => {},
      getStores: () => Promise.resolve({ data: {} }),
      fetchStatus: 'complete',
      resetFetchStatus: () => {},
    };
    const dataLayer: DataLayer = {
      add: () => {},
      build: () => Promise.resolve({ business_unit_id: 123 }),
      builderNames: () => '',
      isTealiumReady: () => {},
      link: () => {},
      linkWithPageProps: () => {},
      view: () => Promise.resolve(),
    };

    const bopisArgs: BopisBarToggleAreaProps = {
      position: '',
      abbrNameForTealium: 'abc',
      bopisData: bopisData,
      businessUnitId: 123,
      categoryName: '',
      datalayer: dataLayer,
      isToggleButtonActive: true,
      pageType: '',
      publish: () => {},
      selectedNodes: [],
      isBopisHidden: false,
      enabled: true,
    };

    const { getByTestId } = renderGridHeader(Brands.Gap, bopisArgs);
    const allFiltersButton = screen.getByText('All Filters');
    allFiltersButton.click();
    await waitFor(async () => {
      expect(getByTestId('bopis-bar')).toBeInTheDocument();
    });
  });

  it('should supress department facet when response has only one department option', async () => {
    const mockResponse = [
      {
        isActive: 'true',
        searchFacetId: 'department',
        searchFacetName: 'Department',
        facetDisplay: 'radio',
        type: 'simple',
        facetLayout: 'list',
        selectionType: 'single-select',
        displayName: 'Department',
        order: 1,
        options: [
          {
            id: 'Women',
            value: 'Women',
            name: 'Women',
            isActive: 'true',
            applied: false,
          },
        ],
        name: 'department',
      },
    ];
    render(
      <PLPStateProvider abSeg={{}}>
        <StitchStyleProvider brand={Brands.Gap}>
          <GridHeader facets={mockResponse} appliedFacets={mockResponse} bopisArgs={undefined} isSortByRatingsEnabled={false} isOnlyOneDepartment={true} />
        </StitchStyleProvider>
      </PLPStateProvider>,
      { localization: en_US }
    );
    waitFor(async () => {
      expect(screen.queryByText('department')).not.toBeInTheDocument();
    });
  });

  it('should render SizeModelToggle in the Grid Header when the feature flag cat-model-toggle is set', async () => {
    const mockSizeModelToggleArgs: SizeModelToggleProps = {
      brand: Brands.Gap,
      isMixedGridEnabled: true,
      publish: () => {},
      isMobile: false,
      enabled: true,
    };
    render(
      <FeatureFlagProvider
        value={{
          enabledFeatures: {},
          featureVariables: sizeModelToggleFeatureVariables as unknown as FeatureVariablesType,
        }}
      >
        <PLPStateProvider abSeg={{ gap44: 'a' }}>
          <StitchStyleProvider brand={Brands.Gap}>
            <GridHeader
              facets={mockData.facets}
              appliedFacets={mockData.appliedFacets}
              bopisArgs={undefined}
              sizeModelToggleArgs={mockSizeModelToggleArgs}
              isSortByRatingsEnabled={false}
              isOnlyOneDepartment={false}
            />
          </StitchStyleProvider>
        </PLPStateProvider>
        ,
      </FeatureFlagProvider>,
      { localization: en_US, breakpoint: XLARGE }
    );

    expect(screen.queryAllByTestId('size-model-toggle')[0]).toBeInTheDocument();
  });

  it('should render Grid Button in the Grid Header when plpGridToggleExperiment is enabled', async () => {
    mockedUseGridButtons.mockReturnValue(GridToggle.Four);
    renderGridHeader(Brands.Gap);
    renderGridButtons();
    expect(screen.getByTestId('grid-toggle-container')).toBeInTheDocument();
  });

  it('should not render Grid Button in the Grid Header when plpGridToggleExperiment is disabled', async () => {
    mockedUseGridButtons.mockReturnValue(GridToggle.Off);
    renderGridHeader(Brands.Gap);
    await waitFor(async () => {
      expect(screen.queryByTestId('grid-toggle-container')).not.toBeInTheDocument();
    });
  });

  it.skip('should persist Grid Toggle selection after selecting any filter', async () => {
    mockedUseGridButtons.mockReturnValue(GridToggle.Four);
    renderGridHeader(Brands.Gap);
    const departmentFilterButton = screen.getByText('department');
    departmentFilterButton.click();
    await waitFor(async () => {
      const filterOption = screen.getByText('women');
      filterOption.click();
    });
    await waitFor(async () => {
      const closeButton = screen.getAllByRole('button')[0];
      closeButton.click();
      expect(screen.getByText(/4 Across/i)).toBeInTheDocument();
    });
  });

  it.skip('should persist Grid Toggle selection after sort', async () => {
    mockedUseGridButtons.mockReturnValue(GridToggle.Five);
    renderGridHeader(Brands.Gap);
    const sortDropdown = screen.getByText('sort by');
    sortDropdown.click();
    await waitFor(async () => {
      const sortOption = screen.getByText('price: low–high');
      sortOption.click();
    });
    await waitFor(async () => {
      expect(screen.getByText(/5 Across/i)).toBeInTheDocument();
    });
  });

  it('should show category facet name as shop by style when useCategoryFacetName name returns non null value', async () => {
    useCategoryFacetNameMock.mockReturnValue('shop by team');
    const { findByText } = await renderGridHeader();
    expect(await findByText('shop by team')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'shop by team' })).toHaveStyle('text-transform: none');
  });
  it('should show category facet name as category when useCategoryFacetName name returns  null value', async () => {
    useCategoryFacetNameMock.mockReturnValue(null);
    const { findByText } = await renderGridHeader();
    expect(await findByText('category')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'category' })).toHaveStyle('text-transform: capitalize');
  });

  describe('GAP Redesign 2024', () => {
    it('should apply 500 font weight when GAP Redesign 2024 is enabled', () => {
      mockedPLPGapRedesign2024.mockReturnValue(true);
      renderGridHeader(Brands.Gap);
      expect(screen.getByRole('button', { name: 'department' })).toHaveStyle('font-weight: 500');
    });

    it('should apply 400 font weight when GAP Redesign 2024 is disabled', () => {
      mockedPLPGapRedesign2024.mockReturnValue(false);
      renderGridHeader(Brands.Gap);
      expect(screen.getByRole('button', { name: 'department' })).toHaveStyle('font-weight: 400');
    });
  });
});
