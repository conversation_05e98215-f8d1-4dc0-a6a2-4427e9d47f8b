// @ts-nocheck
import { redirectTopSearchTerm } from "../redirectTopSearchTerms";
import { divisionsPreferences } from "../divisionsPreferences";
import { Brands } from "../types";
import {
  enableFeaturesMock,
  engineConfigMock,
  searchServiceMock,
  personalizationContextMock,
} from "../fixtures/mocks";
import { redirectSearch } from "../redirectSearch";
import { addPageAction } from "@ecom-next/core/reporting";
import { NEW_RELIC_PAGE_ACTION } from "../constants";
import { PersonalizationContextData } from "@ecom-next/core/legacy/personalization-provider/types/types";

jest.mock("../redirectSearch");

jest.mock("@ecom-next/core/reporting");

describe("redirectTopSearchTerm", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const cases: [string, string, string, string, boolean][] = [
    ["", "Disney", "", "b", false],
    ["", "Disney", "Girls", "a", true],
    ["", "Disney", "Girls", "b", false],
    ["", "Disney", "Girls", "a", false],
    ["Girls", "Disney", "Girls", "b", true],
  ];

  test.each(cases)(
    "should call redirectSearch with string %p when searchTerm is %p, departmentNameMock is %p, experiment is %p and feature flag search-bloomreach is %p",
    async (
      departmentName,
      searchTerm,
      departmentNameMock,
      experiment,
      searchBloomreach
    ) => {
      const abSeg = { gap120: experiment };
      enableFeaturesMock["search-bloomreach"] = searchBloomreach;

      jest
        .spyOn(divisionsPreferences, "getMatchingDepartment")
        .mockImplementation(() => Promise.resolve(departmentNameMock));

      await redirectTopSearchTerm(
        searchTerm,
        personalizationContextMock as PersonalizationContextData,
        "gap" as Brands,
        enableFeaturesMock,
        "US",
        "en_US",
        engineConfigMock,
        abSeg,
        searchServiceMock
      );

      expect(redirectSearch).toHaveBeenCalledWith(
        searchTerm,
        false,
        departmentName,
        false,
        true
      );
      expect(addPageAction).toHaveBeenCalledWith(
        NEW_RELIC_PAGE_ACTION.TOPSEARCHTERMS.DIVISION_PREFERENCE,
        {
          departmentName,
        }
      );
    }
  );
});
