// @ts-nocheck
'use client'

const data = [
  {
    parents: ["5058"],
    type: "division",
    id: "36707",
    name: "gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36707",
    hasSubDivision: false,
    brandCode: "1",
  },
  {
    parents: ["5058"],
    type: "division",
    id: "36710",
    name: "bananarepublic.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36710",
    hasSubDivision: false,
    brandCode: "2",
  },
  {
    parents: ["5058"],
    type: "division",
    id: "94810",
    name: "Athleta.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=94810",
    hasSubDivision: false,
    brandCode: "10",
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1086624",
    name: "New + Now",
    children: [
      {
        parents: ["1086624", "5058"],
        type: "trimheader",
        id: "1139272",
        name: "Shop New Arrivals",
        children: [
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139377",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139377",
            customUrl: "/browse/category.do?cid=8792",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139378",
            name: "Maternity",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139378",
            customUrl: "/browse/category.do?cid=11437",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139379",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139379",
            customUrl: "/browse/category.do?cid=11900",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1158136",
            name: "Teen Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1158136",
            customUrl: "/browse/category.do?cid=1171903",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1161957",
            name: "Teen Guys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161957",
            customUrl: "/browse/category.do?cid=1171904",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139380",
            name: "Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139380",
            customUrl: "/browse/category.do?cid=63895#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139382",
            name: "Boys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139382",
            customUrl: "/browse/category.do?cid=63896#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139383",
            name: "Toddler Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139383",
            customUrl: "/browse/category.do?cid=63863#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139384",
            name: "Toddler Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139384",
            customUrl:
              "/browse/category.do?cid=1016138#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139386",
            name: "Baby Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139386",
            customUrl: "/browse/category.do?cid=14249#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1139387",
            name: "Baby Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139387",
            customUrl: "/browse/category.do?cid=95575#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1086624", "5058"],
        type: "header",
        id: "1164542",
        name: "Featured Shops",
        children: [
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1189735",
            name: "The New Campaign",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1189735",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1165853",
            name: "Generation Good",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1165853",
            hasSubDivision: false,
          },
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1173955",
            name: "The Matching Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1173955",
            customUrl: "/browse/category.do?cid=1070833",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1086624", "5058"],
        type: "trimheader",
        id: "1185064",
        name: "Gift Cards",
        children: [
          {
            parents: ["1086624", "5058"],
            type: "category",
            id: "1185065",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185065",
            customUrl: "/customerService/info.do?cid=2116",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1086624",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1077403",
    name: "Jeans",
    children: [
      {
        parents: ["1077403", "5058"],
        type: "header",
        id: "1135249",
        name: "Jeans",
        children: [
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139367",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139367",
            customUrl: "/browse/category.do?cid=5664",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139368",
            name: "Maternity",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139368",
            customUrl: "/browse/category.do?cid=6013",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139369",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139369",
            customUrl: "/browse/category.do?cid=6998",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139370",
            name: "Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139370",
            customUrl: "/browse/category.do?cid=6276",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139371",
            name: "Boys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139371",
            customUrl: "/browse/category.do?cid=6189",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139372",
            name: "Toddler Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139372",
            customUrl: "/browse/category.do?cid=6427",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139373",
            name: "Toddler Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139373",
            customUrl: "/browse/category.do?cid=6359",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139374",
            name: "Baby Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139374",
            customUrl:
              "/browse/category.do?cid=7191#department=166&style=69650",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139375",
            name: "Baby Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139375",
            customUrl:
              "/browse/category.do?cid=95684#department=166&style=1027403",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1077403", "5058"],
        type: "header",
        id: "1137310",
        name: "Denim Jackets, Shirts, Etc.",
        children: [
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1137312",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1137312",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1137350",
            name: "Maternity",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1137350",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1137363",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1137363",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1137821",
            name: "Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1137821",
            customUrl: "/browse/category.do?cid=1137821#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1137822",
            name: "Boys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1137822",
            customUrl: "/browse/category.do?cid=1137822#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1138074",
            name: "Toddler Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1138074",
            customUrl:
              "/browse/category.do?cid=1138074#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1138071",
            name: "Toddler Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1138071",
            customUrl:
              "/browse/category.do?cid=1138071#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1138072",
            name: "Baby Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1138072",
            customUrl:
              "/browse/category.do?cid=1138072#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1138073",
            name: "Baby Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1138073",
            customUrl:
              "/browse/category.do?cid=1138073#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1077403", "5058"],
        type: "trimheader",
        id: "1137320",
        name: "Features",
        children: [
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1168751",
            name: "Made in the USA Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1168751",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1135422",
            name: "Women's Denim Guide",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1135422",
            customUrl: "/browse/info.do?cid=1137373",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139110",
            name: "Men's Denim Guide ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139110",
            customUrl: "/browse/info.do?cid=1137372",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139376",
            name: "Baby's First Denim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139376",
            customUrl: "/browse/category.do?cid=1133690",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1071687",
            name: "Gap Icons for All",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1071687",
            customUrl: "/browse/category.do?cid=1071687&showDF=true",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1077403", "5058"],
        type: "trimheader",
        id: "1137321",
        name: "Featured Shops",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1077403", "5058"],
        type: "header",
        id: "1137322",
        name: "The 1969 Premium Collection",
        children: [
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139111",
            name: "Women",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1139111",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1139094",
            name: "Men",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1139094",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1086406",
            name: "Denim Jackets, Shirts, Etc.",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1086406",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1077403", "5058"],
        type: "header",
        id: "1089958",
        name: "Featured Shops",
        children: [
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1115591",
            name: "Exclusion setup_W and Mat except GapBody",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1115591",
            hasSubDivision: false,
          },
          {
            parents: ["1077403", "5058"],
            type: "category",
            id: "1185090",
            name: "Gift Cards",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1185090",
            customUrl: "/customerService/info.do?cid=2116",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1077403",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "5643",
    name: "Women",
    children: [
      {
        parents: ["5643", "5058"],
        type: "sub-division",
        id: "5646",
        name: "Womens",
        children: [
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1164545",
            name: "Just Arrived",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "8792",
                name: "New Arrivals",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=8792",
                customUrl:
                  "/browse/category.do?cid=8792#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1189728",
                name: "The New Campaign",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189728",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1189708",
                name: "The Linen Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189708",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1188847",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188847",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1187870",
                name: "Artist Series Tee Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1187870",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1042481",
            name: "Categories",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1127938",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127938",
                customUrl:
                  "/browse/category.do?cid=1127938#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "5664",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5664",
                customUrl:
                  "/browse/category.do?cid=5664#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "13658",
                name: "Dresses ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=13658",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1152367",
                name: "Jumpsuits & Rompers",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1152367",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "17076",
                name: "T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=17076",
                customUrl:
                  "/browse/category.do?cid=17076#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "34608",
                name: "Shirts & Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=34608",
                customUrl:
                  "/browse/category.do?cid=34608#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1167927",
                name: "Hoodies",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1167927",
                customUrl:
                  "/browse/category.do?cid=1167927#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1041168",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1041168",
                customUrl:
                  "/browse/category.do?cid=1041168#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "5745",
                name: "Sweaters ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5745",
                customUrl:
                  "/browse/category.do?cid=5745#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "5736",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5736",
                customUrl:
                  "/browse/category.do?cid=5736#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1011761",
                name: "Pants & Leggings",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1011761",
                customUrl:
                  "/browse/category.do?cid=1011761#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1169932",
                name: "Joggers",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1169932",
                customUrl:
                  "/browse/category.do?cid=1169932#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1188517",
                name: "Denim Shorts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188517",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1041308",
                name: "Shorts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1041308",
                customUrl:
                  "/browse/category.do?cid=1041308#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1082574",
                name: "Skirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1082574",
                customUrl:
                  "/browse/category.do?cid=1082574#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1027291",
                name: "Swim ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027291",
                customUrl:
                  "/browse/category.do?cid=1027291#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "35300",
                name: "Shoes & Accessories ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=35300",
                customUrl:
                  "/browse/category.do?cid=35300#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1157839",
                name: "Face Masks",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1157839",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=5643",
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "header",
            id: "1131696",
            name: "Activewear",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1172898",
                name: "GapFit for Life",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1172898",
                customUrl: "/browse/info.do?cid=1171233",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1117374",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1117374",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1131377",
                name: "GapFit Shop by Activity",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1131377",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "83221",
                name: "Sports Bras ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=83221",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1006976",
                name: "Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1006976",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "83072",
                name: "Sweatshirts & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=83072",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1005439",
                name: "Leggings & Joggers",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1005439",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1187905",
                name: "Dresses & One-Pieces",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1187905",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1056307",
                name: "Shorts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1056307",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1187581",
                name: "Accessories",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1187581",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "header",
            id: "5903",
            name: "GapBody",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1140272",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1140272",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1173681",
                name: "Shades of You",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1173681",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "34524",
                name: "Bras",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=34524",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1015387",
                name: "Undies",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1015387",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "29504",
                name: "Pajamas & Loungewear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=29504",
                customUrl:
                  "/browse/category.do?cid=29504#pageId=0&department=136",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1131698",
            name: "More Sizes",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1026609",
                name: "The Petite Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1026609",
                customUrl:
                  "/browse/category.do?cid=1026609#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1026616",
                name: "The Tall Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1026616",
                customUrl:
                  "/browse/category.do?cid=1026616#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1131699",
                name: "Maternity",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1131699",
                customUrl: "/browse/category.do?cid=1127956",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1131702",
            name: "Featured Shops",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1147561",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1147561",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1165327",
                name: "The Matching Sets Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1165327",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1052025",
                name: "The Logo Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1052025",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1157520",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1157520",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1158115",
                name: "Teen Collection",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1158115",
                customUrl: "/browse/category.do?cid=1127146",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1122595",
            name: "Deals",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "sale",
                id: "65179",
                name: "Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65179",
                customUrl:
                  "/browse/category.do?cid=65179#pageId=0&department=136",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "sale",
                id: "1015684",
                name: "Final Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1015684",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1072170",
                name: "Washwell™ Denim",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1072170",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "header",
            id: "1047701",
            name: "Featured Shops",
            children: [],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1131695",
            name: "Activewear",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1088544",
                name: "Bags",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1088544",
                customUrl:
                  "/browse/category.do?cid=1088544#pageId=0&department=136",
                hasSubDivision: false,
              },
            ],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "trimheader",
            id: "1131697",
            name: "Love by Gap",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1069608",
                name: "DK TEST CATEGORY",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1069608",
                hasSubDivision: false,
              },
            ],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5646", "5643", "5058"],
            type: "header",
            id: "1131700",
            name: "Sale",
            children: [
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1138955",
                name: "Basic ReShoot Candidates",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1138955",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1173387",
                name: "Spring Flow",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1173387",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1185067",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185067",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1191163",
                name: "DPG Testing - Core",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1191163",
                hasSubDivision: false,
              },
              {
                parents: ["5646", "5643", "5058"],
                type: "category",
                id: "1191164",
                name: "DPG Testing - Content",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1191164",
                hasSubDivision: false,
              },
            ],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=5646",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5643",
    hasSubDivision: true,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "5997",
    name: "GapMaternity",
    children: [
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1164546",
        name: "Just Arrived",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "11437",
            name: "New Arrivals ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=11437",
            customUrl: "/browse/category.do?cid=11437#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188848",
            name: "Celebrate Spring Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188848",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188850",
            name: "Spring Break Shop ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188850",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1186391",
        name: "Gifts",
        children: [],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1042513",
        name: "Categories",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1127956",
            name: "Shop All Styles",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127956",
            customUrl:
              "/browse/category.do?cid=1127956#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "6013",
            name: "Jeans ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6013",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1008565",
            name: "Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1008565",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1154239",
            name: "Jumpsuits & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1154239",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "6049",
            name: "T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6049",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1014424",
            name: "Shirts & Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1014424",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1019160",
            name: "Sweaters & Sweatshirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1019160",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1016449",
            name: "Outerwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1016449",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1014425",
            name: "Pants & Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1014425",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1025764",
            name: "Shorts & Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1025764",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1050384",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1050384",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1103148",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1103148",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1088548",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1088548",
            customUrl:
              "/browse/category.do?cid=1088548#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/division.do?cid=5997",
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1188906",
        name: "Nursing",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1023948",
            name: "Shop All Nursing",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1023948",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188667",
            name: "Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188667",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188658",
            name: "Nursing Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188658",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188666",
            name: "Sleepwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188666",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188665",
            name: "Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188665",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1188668",
            name: "Nursing Friendly",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188668",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "header",
        id: "1014415",
        name: "GapBody",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1021874",
            name: "Pajamas & Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1021874",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1020009",
            name: "Bras, Undies & More",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1020009",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1149538",
        name: "Featured Shops ",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1189685",
            name: "Responsibly-Made Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189685",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1175142",
            name: "Shop By Trimester",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1175142",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1173195",
            name: "Mom & Baby Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1173195",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1175678",
            name: "The Matching Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1175678",
            customUrl: "/browse/category.do?cid=1070833",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1113090",
            name: "The Essentials",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1113090",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1020890",
            name: "Starter Styles",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1020890",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1038114",
            name: "Shop by Panel",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1038114",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1141270",
            name: "Baby's First Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141270",
            customUrl: "/browse/category.do?cid=1027221",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "trimheader",
        id: "1122765",
        name: "Deals",
        children: [
          {
            parents: ["5997", "5058"],
            type: "sale",
            id: "65302",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=65302",
            customUrl: "/browse/category.do?cid=65302#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "header",
        id: "1017083",
        name: "Sale",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1165612",
            name: "Maternity wip",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1165612",
            customUrl:
              "/browse/category.do?cid=1165612#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5997", "5058"],
        type: "header",
        id: "1189426",
        name: "Nursing",
        children: [
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1177650",
            name: "April Divisional WIP Environment",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1177650",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1177704",
            name: "May Divisional WIP Environment",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1177704",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1177712",
            name: "June Divisional WIP Environment",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1177712",
            hasSubDivision: false,
          },
          {
            parents: ["5997", "5058"],
            type: "category",
            id: "1185069",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185069",
            customUrl: "/customerService/info.do?cid=2116",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5997",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "5063",
    name: "Men",
    children: [
      {
        parents: ["5063", "5058"],
        type: "sub-division",
        id: "5065",
        name: "Men",
        children: [
          {
            parents: ["5065", "5063", "5058"],
            type: "trimheader",
            id: "1164547",
            name: "Just Arrived",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "11900",
                name: "New Arrivals",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=11900",
                customUrl:
                  "/browse/category.do?cid=11900#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1189736",
                name: "The New Campaign",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189736",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1192106",
                name: "The Linen Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1192106",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1188624",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1188624",
                customUrl:
                  "/browse/category.do?cid=1188624#pageId=0&department=75",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "trimheader",
            id: "1042515",
            name: "Categories",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1127944",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127944",
                customUrl:
                  "/browse/category.do?cid=1127944#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "6998",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6998",
                customUrl:
                  "/browse/category.do?cid=6998#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5225",
                name: "T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5225",
                customUrl:
                  "/browse/category.do?cid=5225#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "83056",
                name: "Polos",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=83056",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "15043",
                name: "Shirts ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=15043",
                customUrl:
                  "/browse/category.do?cid=15043#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1167929",
                name: "Hoodies",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1167929",
                customUrl:
                  "/browse/category.do?cid=1167929#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1066503",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1066503",
                customUrl:
                  "/browse/category.do?cid=1066503#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5180",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5180",
                customUrl:
                  "/browse/category.do?cid=5180#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5168",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5168",
                customUrl:
                  "/browse/category.do?cid=5168#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "80799",
                name: "Pants ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=80799",
                customUrl:
                  "/browse/category.do?cid=80799#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "8655",
                name: "Joggers",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=8655",
                customUrl:
                  "/browse/category.do?cid=8655#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5156",
                name: "Shorts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5156",
                customUrl:
                  "/browse/category.do?cid=5156#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1106658",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1106658",
                customUrl:
                  "/browse/category.do?cid=1106658#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1120779",
                name: "Activewear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1120779",
                customUrl:
                  "/browse/category.do?cid=1120779#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5270",
                name: "Pajamas & Loungewear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5270",
                customUrl:
                  "/browse/category.do?cid=5270#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "11523",
                name: "Underwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=11523",
                customUrl:
                  "/browse/category.do?cid=11523#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "5300",
                name: "Accessories",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=5300",
                customUrl:
                  "/browse/category.do?cid=5300#pageId=0&department=75",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=5063",
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "trimheader",
            id: "1191605",
            name: "Activewear",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1191619",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1191619",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1191618",
                name: "Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1191618",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1191620",
                name: "Bottoms",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1191620",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1191622",
                name: "Outerwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1191622",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1191621",
                name: "Accessories",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1191621",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "trimheader",
            id: "1149531",
            name: "Featured Shops",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1176483",
                name: "Spring Break Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1176483",
                customUrl:
                  "/browse/category.do?cid=1176483#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1149858",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1149858",
                customUrl:
                  "/browse/category.do?cid=1149858#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1097611",
                name: "The Logo Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1097611",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1174504",
                name: "Activewear",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1174504",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1164092",
                name: "The Gap Collective",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1164092",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1175679",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175679",
                customUrl: "/browse/category.do?cid=1070833",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1174877",
                name: "Teen Collection",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1174877",
                customUrl: "/browse/category.do?cid=1138967",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "header",
            id: "1076121",
            name: "More Sizes",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1035086",
                name: "The Slim Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1035086",
                customUrl:
                  "/browse/category.do?cid=1035086#pageId=0&department=75",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1026756",
                name: "The Tall Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1026756",
                customUrl:
                  "/browse/category.do?cid=1026756#pageId=0&department=75",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "trimheader",
            id: "1122755",
            name: "Deals",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "sale",
                id: "65289",
                name: "Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65289",
                customUrl:
                  "/browse/category.do?cid=65289#pageId=0&department=75",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["5065", "5063", "5058"],
            type: "header",
            id: "37931",
            name: "Sale",
            children: [
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1185485",
                name: "HOL21 TESTING",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185485",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1180787",
                name: "FALL 21 TESTING",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1180787",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1180788",
                name: "SUMMER 21 TESTING",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1180788",
                hasSubDivision: false,
              },
              {
                parents: ["5065", "5063", "5058"],
                type: "category",
                id: "1185071",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185071",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
            ],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=5065",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5063",
    hasSubDivision: true,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1159071",
    name: "Teen",
    children: [
      {
        parents: ["1159071", "5058"],
        type: "header",
        id: "1161736",
        name: "Teen Girls Categories",
        children: [
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1184523",
            name: "Shop All Teen",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184523",
            customUrl: "/browse/category.do?cid=1184523#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1127146",
            name: "Shop All Teen",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127146",
            customUrl: "/browse/category.do?cid=1127146#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1171903",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1171903",
            customUrl: "/browse/category.do?cid=1171903#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1159073",
            name: "Tops & T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159073",
            customUrl: "/browse/category.do?cid=1159073#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1159074",
            name: "Sweatshirts & Outerwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159074",
            customUrl: "/browse/category.do?cid=1159074#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1171905",
            name: "Dresses & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1171905",
            customUrl: "/browse/category.do?cid=1171905#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1172017",
            name: "Leggings & Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1172017",
            customUrl: "/browse/category.do?cid=1172017#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1159075",
            name: "Shorts & Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159075",
            customUrl: "/browse/category.do?cid=1159075#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1159076",
            name: "Jeans ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159076",
            customUrl: "/browse/category.do?cid=1159076#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1177076",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177076",
            customUrl: "/browse/category.do?cid=1177076#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1177077",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177077",
            customUrl: "/browse/category.do?cid=1177077#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1182044",
            name: "Pajamas ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182044",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/division.do?cid=1182426",
        hasSubDivision: false,
      },
      {
        parents: ["1159071", "5058"],
        type: "header",
        id: "1161737",
        name: "Teen Guys Categories",
        children: [
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1184524",
            name: "Shop All Teen",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184524",
            customUrl: "/browse/category.do?cid=1184524#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1138967",
            name: "Shop All Teen",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1138967",
            customUrl: "/browse/category.do?cid=1138967#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1171904",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1171904",
            customUrl: "/browse/category.do?cid=1171904#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1161314",
            name: "Tops & T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161314",
            customUrl: "/browse/category.do?cid=1161314#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1161311",
            name: "Sweatshirts & Outerwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161311",
            customUrl: "/browse/category.do?cid=1161311#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1172009",
            name: "Pants & Joggers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1172009",
            customUrl: "/browse/category.do?cid=1172009#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1171909",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1171909",
            customUrl: "/browse/category.do?cid=1171909#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1161313",
            name: "Jeans ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161313",
            customUrl: "/browse/category.do?cid=1161313#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1177079",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177079",
            customUrl: "/browse/category.do?cid=1177079#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1184747",
            name: "Pajamas ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1184747",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/division.do?cid=1182426",
        hasSubDivision: false,
      },
      {
        parents: ["1159071", "5058"],
        type: "trimheader",
        id: "1188312",
        name: "Featured Shops",
        children: [
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1188313",
            name: "Spring Break Shop ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188313",
            customUrl: "/browse/category.do?cid=1188313",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1188671",
            name: "Matching Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188671",
            customUrl: "/browse/category.do?cid=1188671",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1191010",
            name: "The Tee Remix",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1191010",
            customUrl: "/browse/category.do?cid=1191010",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1159071", "5058"],
        type: "trimheader",
        id: "1177960",
        name: "Deals",
        children: [
          {
            parents: ["1159071", "5058"],
            type: "sale",
            id: "1177961",
            name: "Teen Girls Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177961",
            customUrl: "/browse/category.do?cid=1177961#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "sale",
            id: "1177962",
            name: "Teen Guys Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177962",
            customUrl: "/browse/category.do?cid=1177962#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1159071", "5058"],
            type: "category",
            id: "1185074",
            name: "Gift Cards",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1185074",
            customUrl: "/customerService/info.do?cid=2116",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1159071",
    customUrl: "/browse/division.do?cid=1182426",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "6170",
    name: "GapKids",
    children: [
      {
        parents: ["6170", "5058"],
        type: "sub-division",
        id: "6256",
        name: "Girls",
        children: [
          {
            parents: ["6256", "6170", "5058"],
            type: "trimheader",
            id: "1164548",
            name: "Just Arrived",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "63895",
                name: "New Arrivals ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=63895",
                customUrl:
                  "/browse/category.do?cid=63895#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1159116",
                name: "Shop All Teen",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1159116",
                customUrl: "/browse/category.do?cid=1127146",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1141742",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1141742",
                customUrl:
                  "/browse/category.do?cid=1141742#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1027839",
                name: "Spring Break Shop ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027839",
                customUrl:
                  "/browse/category.do?cid=1027839#pageId=0&department=48",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6256", "6170", "5058"],
            type: "trimheader",
            id: "1042516",
            name: "Categories",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1127946",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127946",
                customUrl:
                  "/browse/category.do?cid=1127946#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "6276",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6276",
                customUrl:
                  "/browse/category.do?cid=6276#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "6300",
                name: "Dresses",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6300",
                customUrl:
                  "/browse/category.do?cid=6300#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1141739",
                name: "Rompers & Jumpsuits",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1141739",
                customUrl:
                  "/browse/category.do?cid=1141739#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "14417",
                name: "Tops & T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=14417",
                customUrl:
                  "/browse/category.do?cid=14417#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1122942",
                name: "Graphic T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1122942",
                customUrl:
                  "/browse/category.do?cid=1122942#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1153699",
                name: "Shirts & Polos",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1153699",
                customUrl:
                  "/browse/category.do?cid=1153699#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1056270",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1056270",
                customUrl:
                  "/browse/category.do?cid=1056270#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1161843",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1161843",
                customUrl:
                  "/browse/category.do?cid=1161843#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "6303",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6303",
                customUrl:
                  "/browse/category.do?cid=6303#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "13148",
                name: "Leggings & Pants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=13148",
                customUrl:
                  "/browse/category.do?cid=13148#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "14403",
                name: "Shorts & Skirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=14403",
                customUrl:
                  "/browse/category.do?cid=14403#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1051487",
                name: "GapFit & Active",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1051487",
                customUrl:
                  "/browse/category.do?cid=1051487#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1075777",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1075777",
                customUrl:
                  "/browse/category.do?cid=1075777#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "6323",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6323",
                customUrl:
                  "/browse/category.do?cid=6323#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1061822",
                name: "School Uniforms",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1061822",
                customUrl:
                  "/browse/category.do?cid=1061822#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1188764",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188764",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1189408",
                name: "Matching Sets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189408",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1107336",
                name: "Socks & Underwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1107336",
                customUrl:
                  "/browse/category.do?cid=1107336#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1082288",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1082288",
                customUrl:
                  "/browse/category.do?cid=1082288#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "56233",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=56233",
                customUrl:
                  "/browse/category.do?cid=56233#pageId=0&department=48",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137865",
            hasSubDivision: false,
          },
          {
            parents: ["6256", "6170", "5058"],
            type: "trimheader",
            id: "1056088",
            name: "Featured Shops",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1192773",
                name: "Gap x Disney Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1192773",
                customUrl: "/browse/category.do?cid=1192757",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1129256",
                name: "The Character Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1129256",
                customUrl:
                  "/browse/category.do?cid=1129256#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1120913",
                name: "The Logo Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1120913",
                customUrl:
                  "/browse/category.do?cid=1120913#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1149860",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1149860",
                customUrl:
                  "/browse/category.do?cid=1149860#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1151802",
                name: "Back to School Shop",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1151802",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1180997",
                name: "School Uniforms",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1180997",
                customUrl:
                  "/browse/category.do?cid=1061822#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1189459",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189459",
                customUrl: "/browse/category.do?cid=1070833",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1130369",
                name: "Flippy & Interactive",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1130369",
                customUrl:
                  "/browse/category.do?cid=1130369#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1164090",
                name: "The Gap Collective",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1164090",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6256", "6170", "5058"],
            type: "header",
            id: "6258",
            name: "More Sizes",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "98252",
                name: "Shop by Size",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=98252",
                customUrl:
                  "/browse/category.do?cid=98252#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1158116",
                name: "Teen Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1158116",
                customUrl: "/browse/category.do?cid=1127146",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "82760",
                name: "Slim Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=82760",
                customUrl:
                  "/browse/category.do?cid=82760#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "82743",
                name: "Plus Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=82743",
                customUrl:
                  "/browse/category.do?cid=82743#pageId=0&department=48",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=6170",
            hasSubDivision: false,
          },
          {
            parents: ["6256", "6170", "5058"],
            type: "trimheader",
            id: "1122748",
            name: "Deals",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "sale",
                id: "65194",
                name: "Girls Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65194",
                customUrl:
                  "/browse/category.do?cid=65194#pageId=0&department=48",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1175611",
                name: "category w/facet override",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1175611",
                customUrl:
                  "/browse/category.do?cid=1175611#pageId=0&department=48",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6256", "6170", "5058"],
            type: "header",
            id: "1101222",
            name: "Sale",
            children: [
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1152095",
                name: "CDA ONLY",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1152095",
                hasSubDivision: false,
              },
              {
                parents: ["6256", "6170", "5058"],
                type: "category",
                id: "1185076",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185076",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
            ],
            hidden: true,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=6256",
        hasSubDivision: false,
      },
      {
        parents: ["6170", "5058"],
        type: "sub-division",
        id: "6172",
        name: "Boys",
        children: [
          {
            parents: ["6172", "6170", "5058"],
            type: "trimheader",
            id: "1164549",
            name: "Just Arrived",
            children: [
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "63896",
                name: "New Arrivals ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=63896",
                customUrl:
                  "/browse/category.do?cid=63896#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1174878",
                name: "Shop All Teen",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1174878",
                customUrl: "/browse/category.do?cid=1138967",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1136774",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1136774",
                customUrl:
                  "/browse/category.do?cid=1136774#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1027210",
                name: "Spring Break Shop ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027210",
                customUrl:
                  "/browse/category.do?cid=1027210#pageId=0&department=16",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6172", "6170", "5058"],
            type: "trimheader",
            id: "1042518",
            name: "Categories",
            children: [
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1127945",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127945",
                customUrl:
                  "/browse/category.do?cid=1127945#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "6189",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6189",
                customUrl:
                  "/browse/category.do?cid=6189#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1122119",
                name: "Graphic T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1122119",
                customUrl:
                  "/browse/category.do?cid=1122119#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1070923",
                name: "T-Shirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1070923",
                customUrl:
                  "/browse/category.do?cid=1070923#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "6197",
                name: "Shirts & Polos",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6197",
                customUrl:
                  "/browse/category.do?cid=6197#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1117991",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1117991",
                customUrl:
                  "/browse/category.do?cid=1117991#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1175613",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175613",
                customUrl:
                  "/browse/category.do?cid=1175613#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "6205",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6205",
                customUrl:
                  "/browse/category.do?cid=6205#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1085428",
                name: "Joggers & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1085428",
                customUrl:
                  "/browse/category.do?cid=1085428#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "6187",
                name: "Pants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6187",
                customUrl:
                  "/browse/category.do?cid=6187#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "6191",
                name: "Shorts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6191",
                customUrl:
                  "/browse/category.do?cid=6191#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1050851",
                name: "GapFit & Active",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1050851",
                customUrl:
                  "/browse/category.do?cid=1050851#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1075793",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1075793",
                customUrl:
                  "/browse/category.do?cid=1075793#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "9470",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=9470",
                customUrl:
                  "/browse/category.do?cid=9470#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1060990",
                name: "School Uniforms",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1060990",
                customUrl:
                  "/browse/category.do?cid=1060990#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1188761",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188761",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1107335",
                name: "Socks & Underwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1107335",
                customUrl:
                  "/browse/category.do?cid=1107335#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1082280",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1082280",
                customUrl:
                  "/browse/category.do?cid=1082280#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "96875",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=96875",
                customUrl:
                  "/browse/category.do?cid=96875#pageId=0&department=16",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137867",
            hasSubDivision: false,
          },
          {
            parents: ["6172", "6170", "5058"],
            type: "trimheader",
            id: "1056087",
            name: "Featured Shops",
            children: [
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1192772",
                name: "Gap x Disney Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1192772",
                customUrl: "/browse/category.do?cid=1192757",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1128444",
                name: "The Character Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1128444",
                customUrl:
                  "/browse/category.do?cid=1128444#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1120330",
                name: "The Logo Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1120330",
                customUrl:
                  "/browse/category.do?cid=1120330#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1149862",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1149862",
                customUrl:
                  "/browse/category.do?cid=1149862#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1180998",
                name: "School Uniforms",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1180998",
                customUrl: "/browse/category.do?cid=1060990",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1175680",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175680",
                customUrl: "/browse/category.do?cid=1070833",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1161316",
                name: "Gap | National Geographic ",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1161316",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1164091",
                name: "The Gap Collective",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1164091",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6172", "6170", "5058"],
            type: "header",
            id: "6174",
            name: "More Sizes",
            children: [
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "98255",
                name: "Shop by Size",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=98255",
                customUrl:
                  "/browse/category.do?cid=98255#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1161297",
                name: "Teen Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1161297",
                customUrl: "/browse/category.do?cid=1138967",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "82731",
                name: "Slim Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=82731",
                customUrl:
                  "/browse/category.do?cid=82731#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1050027",
                name: "Plus  Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1050027",
                customUrl:
                  "/browse/category.do?cid=1050027#pageId=0&department=16",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=6170",
            hasSubDivision: false,
          },
          {
            parents: ["6172", "6170", "5058"],
            type: "trimheader",
            id: "1122747",
            name: "Deals",
            children: [
              {
                parents: ["6172", "6170", "5058"],
                type: "sale",
                id: "65217",
                name: "Boys Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65217",
                customUrl:
                  "/browse/category.do?cid=65217#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1151807",
                name: "CFO TEST",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1151807",
                customUrl:
                  "/browse/category.do?cid=1151807#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1153702",
                name: "Uniform autopop test",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1153702",
                customUrl:
                  "/browse/category.do?cid=1153702#pageId=0&department=16",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1177088",
                name: "Gen.Good All Divs",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1177088",
                hasSubDivision: false,
              },
              {
                parents: ["6172", "6170", "5058"],
                type: "category",
                id: "1185080",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185080",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=6172",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=6170",
    hasSubDivision: true,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "6344",
    name: "BabyGap",
    children: [
      {
        parents: ["6344", "5058"],
        type: "sub-division",
        id: "6413",
        name: "Toddler",
        children: [
          {
            parents: ["6413", "6344", "5058"],
            type: "trimheader",
            id: "1164550",
            name: "Just Arrived",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "63863",
                name: "New Arrivals for Her ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=63863",
                customUrl:
                  "/browse/category.do?cid=63863#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016138",
                name: "New Arrivals for Him ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016138",
                customUrl:
                  "/browse/category.do?cid=1016138#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1144190",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1144190",
                customUrl:
                  "/browse/category.do?cid=1144190#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "92915",
                name: "Spring Break Shop ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=92915",
                customUrl:
                  "/browse/category.do?cid=92915#pageId=0&department=165",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6413", "6344", "5058"],
            type: "header",
            id: "1016135",
            name: "Toddler Girl 12m to 5y",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1127952",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127952",
                customUrl:
                  "/browse/category.do?cid=1127952#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1059829",
                name: "Mix & Match Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1059829",
                customUrl:
                  "/browse/category.do?cid=1059829#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "6427",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6427",
                customUrl:
                  "/browse/category.do?cid=6427#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "6436",
                name: "Dresses",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6436",
                customUrl:
                  "/browse/category.do?cid=6436#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1145378",
                name: "Rompers & Jumpsuits",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1145378",
                customUrl:
                  "/browse/category.do?cid=1145378#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "6444",
                name: "T-Shirts & Graphics",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6444",
                customUrl:
                  "/browse/category.do?cid=6444#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1132758",
                name: "Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1132758",
                customUrl:
                  "/browse/category.do?cid=1132758#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "17846",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=17846",
                customUrl:
                  "/browse/category.do?cid=17846#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1175977",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175977",
                customUrl:
                  "/browse/category.do?cid=1175977#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "8770",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=8770",
                customUrl:
                  "/browse/category.do?cid=8770#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "12378",
                name: "Leggings & Pants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=12378",
                customUrl:
                  "/browse/category.do?cid=12378#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1121815",
                name: "Shorts & Skirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1121815",
                customUrl:
                  "/browse/category.do?cid=1121815#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1167042",
                name: "Activewear",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1167042",
                customUrl:
                  "/browse/category.do?cid=1167042#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "76918",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=76918",
                customUrl:
                  "/browse/category.do?cid=76918#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1072981",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1072981",
                customUrl:
                  "/browse/category.do?cid=1072981#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1188469",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188469",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "6465",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6465",
                customUrl:
                  "/browse/category.do?cid=6465#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1084375",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1084375",
                customUrl:
                  "/browse/category.do?cid=1084375#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1033898",
                name: "Socks & Underwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1033898",
                customUrl:
                  "/browse/category.do?cid=1033898#pageId=0&department=165",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137868",
            hasSubDivision: false,
          },
          {
            parents: ["6413", "6344", "5058"],
            type: "header",
            id: "1016083",
            name: "Toddler Boy 12m to 5y",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1127955",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127955",
                customUrl:
                  "/browse/category.do?cid=1127955#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1059828",
                name: "Mix & Match Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1059828",
                customUrl:
                  "/browse/category.do?cid=1059828#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "6359",
                name: "Jeans ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6359",
                customUrl:
                  "/browse/category.do?cid=6359#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1129848",
                name: "Graphic T-Shirts",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1129848",
                customUrl:
                  "/browse/category.do?cid=1129848#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016096",
                name: "T-Shirts & Graphics",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016096",
                customUrl:
                  "/browse/category.do?cid=1016096#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016169",
                name: "Shirts & Polos",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016169",
                customUrl:
                  "/browse/category.do?cid=1016169#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016107",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016107",
                customUrl:
                  "/browse/category.do?cid=1016107#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1175972",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175972",
                customUrl:
                  "/browse/category.do?cid=1175972#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016108",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016108",
                customUrl:
                  "/browse/category.do?cid=1016108#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016106",
                name: "Pants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016106",
                customUrl:
                  "/browse/category.do?cid=1016106#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1121839",
                name: "Shorts ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1121839",
                customUrl:
                  "/browse/category.do?cid=1121839#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1146501",
                name: "Activewear",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1146501",
                customUrl:
                  "/browse/category.do?cid=1146501#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016109",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016109",
                customUrl:
                  "/browse/category.do?cid=1016109#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1072982",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1072982",
                customUrl:
                  "/browse/category.do?cid=1072982#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1188470",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1188470",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1016110",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1016110",
                customUrl:
                  "/browse/category.do?cid=1016110#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1084376",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1084376",
                customUrl:
                  "/browse/category.do?cid=1084376#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1034157",
                name: "Socks & Underwear",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1034157",
                customUrl:
                  "/browse/category.do?cid=1034157#pageId=0&department=166",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137868",
            hasSubDivision: false,
          },
          {
            parents: ["6413", "6344", "5058"],
            type: "trimheader",
            id: "1067853",
            name: "Shop By Size",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "98248",
                name: "Her Shop by Size 12m to 5y",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=98248",
                customUrl:
                  "/browse/category.do?cid=98248#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "98250",
                name: "His Shop by Size 12m to 5y",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=98250",
                customUrl:
                  "/browse/category.do?cid=98250#pageId=0&department=166",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6413", "6344", "5058"],
            type: "trimheader",
            id: "1149845",
            name: "Featured Shops",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1192771",
                name: "Gap x Disney Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1192771",
                customUrl: "/browse/category.do?cid=1192757",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1132757",
                name: "The Character Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1132757",
                customUrl:
                  "/browse/category.do?cid=1132757#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1181268",
                name: "Back to Pre-School Shop",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1181268",
                customUrl:
                  "/browse/category.do?cid=1181268#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1161772",
                name: "Gap | National Geographic ",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1161772",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1122148",
                name: "The Logo Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1122148",
                customUrl:
                  "/browse/category.do?cid=1122148#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1164089",
                name: "The Gap Collective",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1164089",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1149863",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1149863",
                customUrl:
                  "/browse/category.do?cid=1149863#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1175685",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175685",
                customUrl: "/browse/category.do?cid=1070833",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6413", "6344", "5058"],
            type: "trimheader",
            id: "1048209",
            name: "Deals",
            children: [
              {
                parents: ["6413", "6344", "5058"],
                type: "sale",
                id: "65263",
                name: "Toddler Girl Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65263",
                customUrl:
                  "/browse/category.do?cid=65263#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "sale",
                id: "65236",
                name: "Toddler Boy Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65236",
                customUrl:
                  "/browse/category.do?cid=65236#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1152885",
                name: "New Arrivals",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1152885",
                hasSubDivision: false,
              },
              {
                parents: ["6413", "6344", "5058"],
                type: "category",
                id: "1185083",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185083",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=6413",
        hasSubDivision: false,
      },
      {
        parents: ["6344", "5058"],
        type: "sub-division",
        id: "6487",
        name: "Baby",
        children: [
          {
            parents: ["6487", "6344", "5058"],
            type: "trimheader",
            id: "1164551",
            name: "Just Arrived",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "14249",
                name: "New Arrivals for Her ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=14249",
                customUrl:
                  "/browse/category.do?cid=14249#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "95575",
                name: "New Arrivals for Him ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=95575",
                customUrl:
                  "/browse/category.do?cid=95575#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1151844",
                name: "Celebrate Spring Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1151844",
                customUrl:
                  "/browse/category.do?cid=1151844#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1150300",
                name: "Spring Break Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1150300",
                customUrl:
                  "/browse/category.do?cid=1150300#pageId=0&department=165",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "header",
            id: "95461",
            name: "Baby Girl 0 to 24m",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1127947",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127947",
                customUrl:
                  "/browse/category.do?cid=1127947#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1123993",
                name: "Mix & Match Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1123993",
                customUrl:
                  "/browse/category.do?cid=1123993#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1098333",
                name: "Sets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1098333",
                customUrl:
                  "/browse/category.do?cid=1098333#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1027203",
                name: "One-pieces",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027203",
                customUrl:
                  "/browse/category.do?cid=1027203#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "6437",
                name: "Dresses & Rompers",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=6437",
                customUrl:
                  "/browse/category.do?cid=6437#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "7189",
                name: "Bodysuits & Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=7189",
                customUrl:
                  "/browse/category.do?cid=7189#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1028587",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1028587",
                customUrl:
                  "/browse/category.do?cid=1028587#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1175810",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175810",
                customUrl:
                  "/browse/category.do?cid=1175810#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1108608",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1108608",
                customUrl:
                  "/browse/category.do?cid=1108608#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "7191",
                name: "Jeans, Pants & Leggings",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=7191",
                customUrl:
                  "/browse/category.do?cid=7191#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1102200",
                name: "Shorts & Skirts",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1102200",
                customUrl:
                  "/browse/category.do?cid=1102200#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "76748",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=76748",
                customUrl:
                  "/browse/category.do?cid=76748#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1120931",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1120931",
                customUrl:
                  "/browse/category.do?cid=1120931#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1146519",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1146519",
                customUrl:
                  "/browse/category.do?cid=1146519#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "7206",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=7206",
                customUrl:
                  "/browse/category.do?cid=7206#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1086661",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1086661",
                customUrl:
                  "/browse/category.do?cid=1086661#pageId=0&department=165",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137869",
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "header",
            id: "95574",
            name: "Baby Boy 0 to 24m",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1127948",
                name: "Shop All Styles",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1127948",
                customUrl:
                  "/browse/category.do?cid=1127948#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1123992",
                name: "Mix & Match Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1123992",
                customUrl:
                  "/browse/category.do?cid=1123992#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1098335",
                name: "Sets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1098335",
                customUrl:
                  "/browse/category.do?cid=1098335#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1027202",
                name: "One-pieces",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027202",
                customUrl:
                  "/browse/category.do?cid=1027202#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "95598",
                name: "Bodysuits & Tops",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=95598",
                customUrl:
                  "/browse/category.do?cid=95598#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1028588",
                name: "Sweatshirts & Sweatpants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1028588",
                customUrl:
                  "/browse/category.do?cid=1028588#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1175813",
                name: "Sweaters",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175813",
                customUrl:
                  "/browse/category.do?cid=1175813#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1114529",
                name: "Outerwear & Jackets",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1114529",
                customUrl:
                  "/browse/category.do?cid=1114529#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "95684",
                name: "Jeans & Pants",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=95684",
                customUrl:
                  "/browse/category.do?cid=95684#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1102201",
                name: "Shorts ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1102201",
                customUrl:
                  "/browse/category.do?cid=1102201#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "95697",
                name: "Pajamas ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=95697",
                customUrl:
                  "/browse/category.do?cid=95697#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1120932",
                name: "Swim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1120932",
                customUrl:
                  "/browse/category.do?cid=1120932#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1146528",
                name: "Multi-Packs",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1146528",
                customUrl:
                  "/browse/category.do?cid=1146528#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "95693",
                name: "Shoes",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=95693",
                customUrl:
                  "/browse/category.do?cid=95693#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1086662",
                name: "Accessories & More",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1086662",
                customUrl:
                  "/browse/category.do?cid=1086662#pageId=0&department=166",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            customUrl: "/browse/division.do?cid=1137869",
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "trimheader",
            id: "1067854",
            name: "Shop by Size",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "94859",
                name: "Her Shop by Size 0 to 24m",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=94859",
                customUrl:
                  "/browse/category.do?cid=94859#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "94883",
                name: "His Shop by Size 0 to 24m",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=94883",
                customUrl:
                  "/browse/category.do?cid=94883#pageId=0&department=166",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "trimheader",
            id: "1164552",
            name: "Newborn",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1027221",
                name: "Newborn First Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1027221",
                customUrl:
                  "/browse/category.do?cid=1027221#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1123994",
                name: "The Neutral Shop ",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1123994",
                customUrl:
                  "/browse/category.do?cid=1123994#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1055119",
                name: "Gifts We Love",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1055119",
                customUrl:
                  "/browse/category.do?cid=1055119#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "92495",
                name: "Organics",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=92495",
                customUrl:
                  "/browse/category.do?cid=92495#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1123991",
                name: "Multi-Packs & Sets",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1123991",
                customUrl:
                  "/browse/category.do?cid=1123991#pageId=0&department=165",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "trimheader",
            id: "1149847",
            name: "Featured Shops",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1192769",
                name: "Gap x Disney Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1192769",
                customUrl: "/browse/category.do?cid=1192757",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1133697",
                name: "The Character Shop ",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1133697",
                customUrl:
                  "/browse/category.do?cid=1133697#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1189539",
                name: "Mom & Baby Favorites",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1189539",
                customUrl: "/browse/category.do?cid=1173195",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1133690",
                name: "My First Denim",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1133690",
                customUrl:
                  "/browse/category.do?cid=1133690#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1164088",
                name: "The Gap Collective",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1164088",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1123328",
                name: "The Logo Shop ",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1123328",
                customUrl:
                  "/browse/category.do?cid=1123328#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1149865",
                name: "Responsibly-Made Shop",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1149865",
                customUrl:
                  "/browse/category.do?cid=1149865#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1175686",
                name: "The Matching Shop",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=1175686",
                customUrl: "/browse/category.do?cid=1070833",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
          {
            parents: ["6487", "6344", "5058"],
            type: "trimheader",
            id: "1048187",
            name: "Deals",
            children: [
              {
                parents: ["6487", "6344", "5058"],
                type: "sale",
                id: "65208",
                name: "Baby Girl Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65208",
                customUrl:
                  "/browse/category.do?cid=65208#pageId=0&department=165",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "sale",
                id: "65261",
                name: "Baby Boy Sale",
                children: [],
                hidden: false,
                selected: false,
                link: "/browse/category.do?cid=65261",
                customUrl:
                  "/browse/category.do?cid=65261#pageId=0&department=166",
                hasSubDivision: false,
              },
              {
                parents: ["6487", "6344", "5058"],
                type: "category",
                id: "1185087",
                name: "Gift Cards",
                children: [],
                hidden: true,
                selected: false,
                link: "/browse/category.do?cid=1185087",
                customUrl: "/customerService/info.do?cid=2116",
                hasSubDivision: false,
              },
            ],
            hidden: false,
            selected: false,
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        link: "/browse/subDivision.do?cid=6487",
        hasSubDivision: false,
      },
      {
        parents: ["6344", "5058"],
        type: "sub-division",
        id: "1069459",
        name: "TEST area",
        children: [
          {
            type: "headerless-group",
          },
        ],
        hidden: true,
        selected: false,
        link: "/browse/subDivision.do?cid=1069459",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=6344",
    hasSubDivision: true,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1189451",
    name: "Matching",
    children: [
      {
        parents: ["1189451", "5058"],
        type: "trimheader",
        id: "1189452",
        name: "Featured Shops",
        children: [
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1070833",
            name: "The Matching Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1070833",
            hasSubDivision: false,
          },
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1173983",
            name: "Family Logo Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1173983",
            hasSubDivision: false,
          },
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1189461",
            name: "Big & Little Matching PJs",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189461",
            hasSubDivision: false,
          },
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1189071",
            name: "Artist Series Tee Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189071",
            hasSubDivision: false,
          },
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1191351",
            name: "Mother’s Day Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1191351",
            hasSubDivision: false,
          },
          {
            parents: ["1189451", "5058"],
            type: "category",
            id: "1192757",
            name: "Gap x Disney Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1192757",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1189451",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1188985",
    name: "Logo",
    children: [
      {
        parents: ["1188985", "5058"],
        type: "trimheader",
        id: "1188986",
        name: "Logo",
        children: [
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188993",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188993",
            customUrl: "/browse/category.do?cid=1173985",
            hasSubDivision: false,
          },
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188987",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188987",
            customUrl: "/browse/category.do?cid=1173987",
            hasSubDivision: false,
          },
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188994",
            name: "Teen",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188994",
            customUrl: "/browse/category.do?cid=1181464",
            hasSubDivision: false,
          },
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188995",
            name: "Kids",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188995",
            customUrl: "/browse/category.do?cid=1173989",
            hasSubDivision: false,
          },
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188996",
            name: "Toddler",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188996",
            customUrl: "/browse/category.do?cid=1173990",
            hasSubDivision: false,
          },
          {
            parents: ["1188985", "5058"],
            type: "category",
            id: "1188997",
            name: "Baby",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188997",
            customUrl: "/browse/category.do?cid=1173991",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1188985",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1164963",
    name: "Masks ",
    children: [
      {
        parents: ["1164963", "5058"],
        type: "trimheader",
        id: "1164964",
        name: "Masks For All",
        children: [
          {
            parents: ["1164963", "5058"],
            type: "category",
            id: "1164966",
            name: "Adult Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164966",
            customUrl: "/browse/category.do?cid=1157839",
            hasSubDivision: false,
          },
          {
            parents: ["1164963", "5058"],
            type: "category",
            id: "1164967",
            name: "Kids' Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164967",
            customUrl: "/browse/category.do?cid=1157843",
            hasSubDivision: false,
          },
          {
            parents: ["1164963", "5058"],
            type: "category",
            id: "1164965",
            name: "All Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164965",
            customUrl: "/browse/category.do?cid=1157839",
            hasSubDivision: false,
          },
          {
            parents: ["1164963", "5058"],
            type: "category",
            id: "1185093",
            name: "Gift Cards",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1185093",
            customUrl: "/customerService/info.do?cid=2116",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1164963",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1156863",
    name: "Sale",
    children: [
      {
        parents: ["1156863", "5058"],
        type: "trimheader",
        id: "1156864",
        name: "Shop Sale",
        children: [
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156865",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156865",
            customUrl: "/browse/category.do?cid=65179",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156867",
            name: "Maternity",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156867",
            customUrl: "/browse/category.do?cid=65302",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156869",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156869",
            customUrl: "/browse/category.do?cid=65289",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1180068",
            name: "Teen Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1180068",
            customUrl: "/browse/category.do?cid=1177961",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1180069",
            name: "Teen Guys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1180069",
            customUrl: "/browse/category.do?cid=1177962",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156871",
            name: "Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156871",
            customUrl: "/browse/category.do?cid=65194",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156873",
            name: "Boys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156873",
            customUrl: "/browse/category.do?cid=65217",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156874",
            name: "Toddler Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156874",
            customUrl: "/browse/category.do?cid=65263",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156875",
            name: "Toddler Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156875",
            customUrl: "/browse/category.do?cid=65236",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156877",
            name: "Baby Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156877",
            customUrl: "/browse/category.do?cid=65208",
            hasSubDivision: false,
          },
          {
            parents: ["1156863", "5058"],
            type: "category",
            id: "1156878",
            name: "Baby Boy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156878",
            customUrl: "/browse/category.do?cid=65261",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1156863",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1147558",
    name: "Gap for Good",
    children: [
      {
        parents: ["1147558", "5058"],
        type: "trimheader",
        id: "1150546",
        name: "Shop Responsibly-Made Styles",
        children: [
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176653",
            name: "Women",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176653",
            customUrl: "/browse/category.do?cid=1147561",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176654",
            name: "Men",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176654",
            customUrl: "/browse/category.do?cid=1149858",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1177205",
            name: "Teen Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177205",
            customUrl: "/browse/category.do?cid=1127146",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1177206",
            name: "Teen Guy",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177206",
            customUrl: "/browse/category.do?cid=1138967",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176657",
            name: "Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176657",
            customUrl: "/browse/category.do?cid=1149860",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176655",
            name: "Boys",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176655",
            customUrl: "/browse/category.do?cid=1149862",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176658",
            name: "Toddler",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176658",
            customUrl: "/browse/category.do?cid=1149863",
            hasSubDivision: false,
          },
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176659",
            name: "Baby",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176659",
            customUrl: "/browse/category.do?cid=1149865",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1147558", "5058"],
        type: "header",
        id: "1160688",
        name: "Featured Shops",
        children: [
          {
            parents: ["1147558", "5058"],
            type: "category",
            id: "1176818",
            name: "See How We Do Good",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176818",
            customUrl: "/browse/info.do?cid=1086537",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1147558",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1163044",
    name: "Seasonal Must Haves",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1163044",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "76739",
    name: "US ONLY Seasonal Promotion Exclusions ",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1166486",
            name: "Recurring Exclusions",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1166486",
            customUrl: "old 3PA, no jeans",
            hasSubDivision: false,
          },
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1019342",
            name: "In Season Requests ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1019342",
            customUrl: "old 3PA, no jeans",
            hasSubDivision: false,
          },
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1157455",
            name: "Denim Exclusions",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1157455",
            hasSubDivision: false,
          },
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1186449",
            name: "KTB Inc/Exc",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1186449",
            hasSubDivision: false,
          },
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1189276",
            name: "Teen Inc/Exc",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1189276",
            hasSubDivision: false,
          },
          {
            parents: ["76739", "5058"],
            type: "category",
            id: "1192852",
            name: "Shorts Exclusions",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1192852",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=76739",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1139452",
    name: "Promo Event Specific",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1139452",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1118140",
    name: "hillcity.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1118140",
    hasSubDivision: false,
    brandCode: "36",
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1182426",
    name: "Teen",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1182426",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1137865",
    name: "Girls",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1137865",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1137867",
    name: "Boys",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1137867",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "36709",
    name: "oldnavy.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36709",
    hasSubDivision: false,
    brandCode: "3",
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1137868",
    name: "Toddler",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1137868",
    hasSubDivision: false,
  },
  {
    parents: ["5058"],
    type: "division",
    id: "1137869",
    name: "Baby",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1137869",
    hasSubDivision: false,
  },
];

export default data;
