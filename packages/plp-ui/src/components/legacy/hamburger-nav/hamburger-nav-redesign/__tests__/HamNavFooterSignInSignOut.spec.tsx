// @ts-nocheck
import React from "react";
import { render, screen, RenderOptions, act } from "test-utils";
import { HamNavFooterSignInSignOut } from "../HamNavFooterSignInSignOut";
import { HamNavProvider } from "../HamNavContext";
import { HamNavProviderProps } from "../../utils/types";
import { EnabledFeatures } from "@ecom-next/plp-ui/legacy/experiments";

const localizeSignInKey = "global.signIn";
const localizeSignInValue = "Sign In";
const localizeSignOutKey = "global.signOut";
const localizeSignOutValue = "Sign Out";
type RenderHamNavProps = {
  hamNavState?: Partial<HamNavProviderProps>;
  enabledFeatures?: EnabledFeatures;
  options?: RenderOptions;
};

function renderFooterSignInSignOut(
  isLoggedInUser?: boolean,
  renderProps: RenderHamNavProps = {}
): void {
  const { hamNavState = {}, options } = renderProps;
  const {
    brandSiteData = {
      currentBrandCode: "",
      currentBrandSiteId: "",
      gidBrandSites: {},
    },
    ...hamNavProviderRest
  } = hamNavState;
  render(
    <HamNavProvider
      {...(hamNavProviderRest as HamNavProviderProps)}
      brandSiteData={brandSiteData}
    >
      <HamNavFooterSignInSignOut isLoggedInUser={isLoggedInUser} />{" "}
    </HamNavProvider>,
    {
      localization: {
        [localizeSignInKey]: localizeSignInValue,
        [localizeSignOutKey]: localizeSignOutValue,
      },
      appState: {
        brandSiteData: {
          gidBrandSites: {
            "34": {
              link: "https://www.stage.factory-gaptechol.com?ssiteID=GAPFS",
              unsecureUrl: "https://www.stage.factory-gaptechol.com",
              secureUrl: "https://secure.www.stage.factory-gaptechol.com",
              displayName: "gapfactory.com",
              brandDisplayName: "gapfactory.com",
              brandCode: "34",
              brandAbbr: "GAPFS",
            },
          },
          currentBrandCode: 34,
          currentBrandSiteId: "GAPFS",
        },
      },
      ...options,
    }
  );
}

describe("HamburgerNav - Hamburger nav footer sign in sing out", () => {
  describe("signed in", () => {
    it("should render Sign Out text", () => {
      renderFooterSignInSignOut(true);

      expect(screen.getByText(localizeSignOutValue)).toBeInTheDocument();
    });

    it("should contain sign out url", () => {
      const signOutUrl = "/my-account/sign-out";
      renderFooterSignInSignOut(true);

      expect(
        screen
          .getByRole("link", { name: localizeSignOutValue })
          .getAttribute("href")
      ).toContain(signOutUrl);
    });
  });

  describe("signed out", () => {
    it("should render Sign In text", () => {
      renderFooterSignInSignOut(false);

      expect(screen.getByText(localizeSignInValue)).toBeInTheDocument();
    });

    it("should contain sign out url", () => {
      const signInUrl = "/my-account/sign-in";
      renderFooterSignInSignOut(false);

      expect(
        screen
          .getByRole("link", { name: localizeSignInValue })
          .getAttribute("href")
      ).toContain(signInUrl);
    });
  });
});
