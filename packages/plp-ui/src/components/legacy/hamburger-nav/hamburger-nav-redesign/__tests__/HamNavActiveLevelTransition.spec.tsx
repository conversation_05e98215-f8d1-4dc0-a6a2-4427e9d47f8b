// @ts-nocheck
import React from "react";
import { render, screen, act } from "test-utils";
import HamNavActiveLevelTransition from "../HamNavActiveLevelTransition";

describe("HamNavActiveLevelTransition", () => {
  let animatedChild: HTMLElement;
  beforeAll(() => {
    render(
      <HamNavActiveLevelTransition
        activeLevel="blue"
        animationDirection="left"
        animationSpeed={0}
      >
        <div role="dialog" />
      </HamNavActiveLevelTransition>
    );
    animatedChild = screen.getByRole("dialog");
  });
  it("renders children", () => {
    expect(animatedChild).toBeInTheDocument();
  });
  it("renders wrapper with animation direction", () => {
    expect(animatedChild.parentElement?.parentElement?.className).toInclude(
      "anim-left"
    );
  });
  describe("when direciton changes", () => {
    beforeAll(() => {
      render(
        <HamNavActiveLevelTransition
          activeLevel="blue"
          animationDirection="right"
          animationSpeed={0}
        >
          <div role="dialog" />
        </HamNavActiveLevelTransition>
      );
      animatedChild = screen.getByRole("dialog");
    });
    it("renders children", () => {
      expect(animatedChild).toBeInTheDocument();
    });
    it("renders wrapper with animation direction", () => {
      expect(animatedChild.parentElement?.parentElement?.className).toInclude(
        "anim-right"
      );
    });
  });
});
