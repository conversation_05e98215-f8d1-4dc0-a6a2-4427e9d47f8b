// @ts-nocheck
import React from "react";
import { fireEvent, render, RenderResult, screen, waitFor, act } from "test-utils";
import { Brands } from "@ecom-next/core/react-stitch";
import { AppState, Market } from "@ecom-next/sitewide/app-state-provider";
import {
  HamNavActiveMenu,
  LOCALIZED_BACK_BUTTON_TEXT,
  LOCALIZED_SUBTITLE,
} from "../HamNavActiveMenu";
import {
  HamNavActiveMenuProps,
  HamburgerNavDataItem,
} from "../../utils/types/navigation";

const shopByDepartmentText = "Shop By Department";
const menuText = "Menu";
const backButtonLabel = "Back";

const activatedLevelsData: HamburgerNavDataItem[] = [
  {
    name: "Custom Level Two Title",
    cid: "0000",
    active: false,
    ancestor: false,
    type: "division",
    dataType: "division",
    children: [],
    customStyles: {},
  },
  {
    cid: "1111",
    name: "Custom Level Three Title",
    type: "category",
    dataType: "category",
    active: false,
    link: "samplelink",
    children: [],
  },
];

const renderNavActiveMenu = (
  props?: Partial<HamNavActiveMenuProps>
): RenderResult => {
  const hamNavActiveMenuProps: HamNavActiveMenuProps = {
    activatedLevels: [],
    animationDirection: "left",
    onBack: jest.fn(),
    ...props,
  };

  return render(<HamNavActiveMenu {...hamNavActiveMenuProps} />, {
    localization: {
      [LOCALIZED_SUBTITLE]: shopByDepartmentText,
      [LOCALIZED_BACK_BUTTON_TEXT]: backButtonLabel,
    },
  });
};

const renderNavActiveMenuNewColors = (
  market: Market,
  flag: boolean,
  brandName: string,
  props?: Partial<HamNavActiveMenuProps>
): RenderResult => {
  const hamNavActiveMenuProps: HamNavActiveMenuProps = {
    activatedLevels: [],
    animationDirection: "left",
    onBack: jest.fn(),
    ...props,
  };
  const brandTyped = brandName as Brands;

  return render(<HamNavActiveMenu {...hamNavActiveMenuProps} />, {
    localization: {
      [LOCALIZED_SUBTITLE]: shopByDepartmentText,
      [LOCALIZED_BACK_BUTTON_TEXT]: backButtonLabel,
    },
    appState: {
      brandName: brandTyped,
      market,
    } as AppState,
    enabledFeatures: {
      "find-gap-us-font-color-update": market === "us" ? flag : false,
      "find-gap-ca-font-color-update": market === "ca" ? flag : false,
    },
  });
};

const testTitleCases = [
  ["#2b2b2b", Brands.Gap],
  ["#003764", Brands.OldNavy],
  ["#000000", Brands.BananaRepublic],
  ["#333333", Brands.Athleta],
  ["#2B2B2B", Brands.GapFactoryStore],
  ["#000000", Brands.BananaRepublicFactoryStore],
];

const testSubtitleCases = [
  ["#2b2b2b", Brands.Gap],
  ["#2C84D1", Brands.OldNavy],
  ["#666666", Brands.BananaRepublic],
  ["#666666", Brands.Athleta],
  ["#2B2B2B", Brands.GapFactoryStore],
  ["#666666", Brands.BananaRepublicFactoryStore],
];

const testLocalizedTitleCases = [
  ["#2b2b2b", Brands.Gap],
  ["#2C84D1", Brands.OldNavy],
  ["#666666", Brands.BananaRepublic],
  ["#666666", Brands.Athleta],
  ["#2B2B2B", Brands.GapFactoryStore],
  ["#666666", Brands.BananaRepublicFactoryStore],
];

const testLocalizedSubtitleCases = [
  ["#2b2b2b", Brands.Gap],
  ["#003764", Brands.OldNavy],
  ["#000000", Brands.BananaRepublic],
  ["#333333", Brands.Athleta],
  ["#2B2B2B", Brands.GapFactoryStore],
  ["#000000", Brands.BananaRepublicFactoryStore],
];

describe("HamburgerNav - Hamburger Nav Active Menu", () => {
  describe("Title and Subtitle", () => {
    describe("When HamNav with Subcats is enabled", () => {
      it("should not render localized subtitle and title when title not provided and you're on the first level", async () => {
        renderNavActiveMenu({ isHamNavSubcatsEnabled: true });

        expect(
          screen.queryByText(shopByDepartmentText)
        ).not.toBeInTheDocument();
        expect(screen.queryByText(menuText)).not.toBeInTheDocument();
      });
      it("should only render the title when you're on the second level", () => {
        const title = "custom title";
        renderNavActiveMenu({ title, isHamNavSubcatsEnabled: true });

        expect(screen.getByText(title)).toBeInTheDocument();
        expect(screen.queryByText(menuText)).not.toBeInTheDocument();
      });
      it("should render the title and subtitle when you're on the third level", () => {
        const title = activatedLevelsData[1].name;
        const subtitle = activatedLevelsData[0].name;
        renderNavActiveMenu({
          title,
          isHamNavSubcatsEnabled: true,
          activatedLevels: activatedLevelsData,
        });

        expect(screen.getByText(title)).toBeInTheDocument();
        expect(screen.queryByText(subtitle)).toBeInTheDocument();
      });
    });

    it("should render localized subtitle and title when title not provided", () => {
      renderNavActiveMenu();

      expect(screen.getByText(shopByDepartmentText)).toBeInTheDocument();
      expect(screen.getByText(menuText)).toBeInTheDocument();
    });

    it("should render title and subtitle when title is provided", () => {
      const title = "custom title";
      renderNavActiveMenu({ title });

      expect(screen.getByText(title)).toBeInTheDocument();
      expect(screen.getByText(menuText)).toBeInTheDocument();
    });

    // TODO: will be re-enabled once there is a solution on handling screen reader reading
    // the headers (h1, h2) in the proper order. Currently, that is not the case given the requirements
    // in FND-1795.
    xit("should focus on heading when title prop changed on rerender", () => {
      const title = "custom title";
      const { rerender } = renderNavActiveMenu();

      rerender(
        <HamNavActiveMenu
          activatedLevels={[]}
          animationDirection="left"
          onBack={jest.fn()}
          title={title}
        />
      );

      const activeMenuHeader = screen.getByText(title).parentElement;

      expect(activeMenuHeader).toHaveFocus();
    });
  });

  describe("Back button", () => {
    it("shouldn't render back button when you're on the first level", async () => {
      renderNavActiveMenu();

      expect(screen.queryByLabelText(backButtonLabel)).not.toBeInTheDocument();
    });

    it("should render back button when you click on a level", async () => {
      renderNavActiveMenu({ title: "title" });

      expect(screen.getByLabelText(backButtonLabel)).toBeInTheDocument();
    });

    it("back button should call onBack function when clicked", async () => {
      const onBack = jest.fn(() => {});
      renderNavActiveMenu({ onBack, title: "title" });

      await act(async () => { 

       fireEvent.click(screen.getByTestId("hamburger-nav-redesign-back-button")); 

       })
      await waitFor(() => screen.queryByRole("dialog"));

      expect(onBack).toHaveBeenCalledTimes(1);
    });
  });

  describe("when market is US", () => {
    test.each(testTitleCases)(
      "When the feature flag is on, should use %s color for title when title is provided and brand is %s",
      (color, brandName) => {
        const title = "custom title";
        renderNavActiveMenuNewColors("us", true, brandName, { title });

        expect(screen.getByText(title)).toHaveStyleRule("color", color);
      }
    );

    test.each(testSubtitleCases)(
      "When the feature flag is on, should use %s color for subtitle when title is provided and brand is %s",
      (color, brandName) => {
        const title = "custom title";
        renderNavActiveMenuNewColors("us", true, brandName, { title });

        expect(screen.getByText(menuText)).toHaveStyleRule("color", color);
      }
    );

    test.each(testLocalizedTitleCases)(
      "When the feature flag is on, should use %s color for localized title when title not provided and brand is %s",
      (color, brandName) => {
        renderNavActiveMenuNewColors("us", true, brandName);

        expect(screen.getByText(shopByDepartmentText)).toHaveStyleRule(
          "color",
          color
        );
      }
    );

    test.each(testLocalizedSubtitleCases)(
      "When the feature flag is on, should use %s color for localized subtitle when title not provided and brand is %s",
      (color, brandName) => {
        renderNavActiveMenuNewColors("us", true, brandName);

        expect(screen.getByText(menuText)).toHaveStyleRule("color", color);
      }
    );
  });

  describe("when market is CA", () => {
    test.each(testTitleCases)(
      "When the feature flag is on, should use %s color for title when title is provided and brand is %s",
      (color, brandName) => {
        const title = "custom title";
        renderNavActiveMenuNewColors("ca", true, brandName, { title });

        expect(screen.getByText(title)).toHaveStyleRule("color", color);
      }
    );

    test.each(testSubtitleCases)(
      "When the feature flag is on, should use %s color for subtitle when title is provided and brand is %s",
      (color, brandName) => {
        const title = "custom title";
        renderNavActiveMenuNewColors("ca", true, brandName, { title });

        expect(screen.getByText(menuText)).toHaveStyleRule("color", color);
      }
    );

    test.each(testLocalizedTitleCases)(
      "When the feature flag is on, should use %s color for localized title when title not provided and brand is %s",
      (color, brandName) => {
        renderNavActiveMenuNewColors("ca", true, brandName);

        expect(screen.getByText(shopByDepartmentText)).toHaveStyleRule(
          "color",
          color
        );
      }
    );

    test.each(testLocalizedSubtitleCases)(
      "When the feature flag is on, should use %s color for localized subtitle when title not provided and brand is %s",
      (color, brandName) => {
        renderNavActiveMenuNewColors("ca", true, brandName);

        expect(screen.getByText(menuText)).toHaveStyleRule("color", color);
      }
    );
  });
});
