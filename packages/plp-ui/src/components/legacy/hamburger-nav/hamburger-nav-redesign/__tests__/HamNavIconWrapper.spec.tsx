// @ts-nocheck
import React from "react";
import { render, screen, RenderResult, act } from "test-utils";
import { HamNavIconWrapper } from "../HamNavIconWrapper";
import { Icon, IconPosition } from "../../utils/types";

const icon: Icon = {
  altText: "O.N.L.Y.",
  position: IconPosition.Left,
  src: "https://www.gap.com/Asset_Archive/AllBrands/assets/DEC/on-header-only.svg",
  styles: {},
};
const CHILD_TEXT = "child component";
const renderHamNavIconWrapper = (icon?: Icon): RenderResult => {
  return render(
    <HamNavIconWrapper icon={icon}>
      <div>{CHILD_TEXT}</div>
    </HamNavIconWrapper>
  );
};

describe("HamNavIconWrapper", () => {
  describe("When the icon is not provided", () => {
    it("should only render the children component", () => {
      renderHamNavIconWrapper();
      expect(screen.queryByRole("img")).not.toBeInTheDocument();
      expect(screen.getByText(CHILD_TEXT)).toBeInTheDocument();
    });
  });

  describe("When the icon is provided", () => {
    it("should render the icon and the children component", () => {
      renderHamNavIconWrapper(icon);
      expect(screen.getByRole("img")).toBeInTheDocument();
      expect(screen.getByText(CHILD_TEXT)).toBeInTheDocument();
    });

    it("should render icon on left position", () => {
      const { container } = renderHamNavIconWrapper(icon);
      expect(container).toMatchSnapshot();
    });

    it("should render icon on right position", () => {
      const { container } = renderHamNavIconWrapper({
        ...icon,
        position: IconPosition.Right,
      });
      expect(container).toMatchSnapshot();
    });

    it("should render icon on top position", () => {
      const { container } = renderHamNavIconWrapper({
        ...icon,
        position: IconPosition.Top,
      });
      expect(container).toMatchSnapshot();
    });
  });
});
