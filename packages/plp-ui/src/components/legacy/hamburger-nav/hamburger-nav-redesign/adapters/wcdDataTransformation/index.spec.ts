// @ts-nocheck
import { wcdDataTransformation } from ".";
import {
  CurrentNavigationRoute,
  HamburgerNavDivision,
  HamburgerNavSection,
} from "../../../utils/types/navigation";
import { WcdOrder } from "./types";
import hamburgerNavData from "../../fixtures/hamburgerNavData.json";
import {
  activeDivisions,
  expectedResult,
} from "../../fixtures/hamburgerNavWcdActiveDivisionsData";
import {
  divisionHeaders,
  expectedResultFromDivisionHeaders,
  expectedResultFromRenamedDivisionHeaders,
} from "../../fixtures/hamburgerNavWcdDivisionHeadersData";
import {
  categories,
  expectedResultFromCategories,
  expectedResultFromRenamedCategories,
} from "../../fixtures/hamburgerNavWcdCategoriesData";
import { Categories as Category } from "../../../utils/types";
import { hamburgerNavAdapter } from "..";

const getWcdDataTransformation = (wcdNavData: WcdOrder) =>
  wcdDataTransformation({
    WcdNavData: wcdNavData,
    hamburgerNavItems: hamburgerNavData as HamburgerNavDivision[],
  });

describe("sortWithWCDData", () => {
  describe("and WCD content is being sent over", () => {
    it("should return the divisions based on the activeDivisions order", () => {
      const orderedData = getWcdDataTransformation(activeDivisions);

      expect(orderedData).toEqual(expectedResult);
    });

    it("should return the division headers based on the divisionHeaders order", () => {
      const orderedData = getWcdDataTransformation(divisionHeaders);

      expect(orderedData).toEqual(expectedResultFromDivisionHeaders);
    });

    it("should return the categories based on the categories order", () => {
      const orderedData = getWcdDataTransformation(categories);

      expect(orderedData).toEqual(expectedResultFromCategories);
    });

    it("should rename the first division header to 'Lounge, Footwear & Intimates'", () => {
      const transformedData = getWcdDataTransformation(divisionHeaders);

      expect(transformedData).toEqual(expectedResultFromRenamedDivisionHeaders);
    });

    it("should rename the third category to 'Footwear'", () => {
      const originalData = getWcdDataTransformation(categories);
      const division = originalData[0] as HamburgerNavDivision;
      const headers = division.children as HamburgerNavSection[];
      const category = headers[0].children[2];

      category.name = "Footwear";

      expect(originalData).toEqual(expectedResultFromRenamedCategories);
    });

    it("update a division link", () => {
      const DIVISION_CID = "48546";
      const EXPECTED_LINK = "/updated-url-link";
      const transformedData = getWcdDataTransformation(activeDivisions);
      const updatedLinkDivision = transformedData.find(
        (item) => item.cid === DIVISION_CID
      ) as HamburgerNavDivision;
      expect(updatedLinkDivision).toHaveProperty("link", EXPECTED_LINK);
    });

    it("update a category link", () => {
      const DIVISION_CID = "5002";
      const DIVISION_HEADER_CID = "1182446";
      const CATEGORY_CID = "dummy5";
      const EXPECTED_CATEGORY_LINK = "/updated-category-link";

      const transformedData = getWcdDataTransformation(categories);

      const division = transformedData.find(
        (item) => item.cid === DIVISION_CID
      ) as HamburgerNavDivision;

      const divisionHeader = (division.children as HamburgerNavSection[]).find(
        (item) => item.cid === DIVISION_HEADER_CID
      ) as HamburgerNavSection;

      const updatedCategory = divisionHeader.children.find(
        (item) => item.cid === CATEGORY_CID
      ) as Category;

      expect(updatedCategory).toHaveProperty("link", EXPECTED_CATEGORY_LINK);
    });

    it("append tracking links after wcd transformation", () => {
      const WCDHamnavConfiguration = {
        activeDivisions: [
          {
            type: "category",
            cid: "48546",
            link: "/updated-from-wcd?cid=48546",
          },
        ],
      };

      const activeRoute: unknown = [];

      const transformedData = hamburgerNavAdapter(
        hamburgerNavData as HamburgerNavDivision[],
        activeRoute as CurrentNavigationRoute,
        WCDHamnavConfiguration
      );

      const updatedCategory = transformedData[0];
      const updatedCategoryWithTrackingLinks =
        "/updated-from-wcd?cid=48546&nav=hamnav%3A%3A%3AFactory%20Store";

      expect(updatedCategory).toHaveProperty(
        "link",
        updatedCategoryWithTrackingLinks
      );
    });
  });
});
