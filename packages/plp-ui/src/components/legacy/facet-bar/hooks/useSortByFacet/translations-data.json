{"core.currency": "{{currencyValue, currency.USD}}", "sortBy.desktop.placeholder": "sort by", "sortBy.desktop.price.options.featured": "featured", "sortBy.desktop.price.options.asc": "price: low–high", "sortBy.desktop.price.options.desc": "price: high–low", "sortBy.desktop.price.options.featured.caps": "Featured", "sortBy.desktop.price.options.asc.caps": "Price: Low–High", "sortBy.desktop.price.options.desc.caps": "Price: High–Low", "sortBy.desktop.reviewScore.options.desc": "Top Rated", "sortBy.desktop.new.options.desc": "New", "sortBy.desktop.bestsellers.options.desc": "Best Sellers", "sortBy.mobile.placeholder": "sort", "sortBy.mobile.placeholder.caps": "Sort", "sortBy.mobile.price.options.featured": "featured", "sortBy.mobile.price.options.asc": "price: low–high", "sortBy.mobile.price.options.desc": "price: high–low", "sortBy.mobile.price.options.featured.caps": "Featured", "sortBy.mobile.price.options.asc.caps": "Price: Low–High", "sortBy.mobile.price.options.desc.caps": "Price: High–Low", "sortBy.mobile.reviewScore.options.desc": "Top Rated", "sortBy.mobile.new.options.desc": "New", "sortBy.mobile.bestsellers.options.desc": "Best Sellers", "sortby.desktop.placeholder": "sort by", "sortby.desktop.options.featured": "featured", "sortby.desktop.options.low": "price: low–high", "sortby.desktop.options.high": "price: high–low", "sortby.mobile.options.featured": "featured", "sortby.mobile.options.low": "price: low–high", "sortby.mobile.options.high": "price: high–low", "paginator.of_text": "of", "paginator.page_text": "Page", "paginator.left_arrow_aria_label": "Previous Page", "paginator.right_arrow_aria_label": "Next Page", "left_rail.product_count_text": "{{count}} product", "left_rail.product_count_text_plural": "{{count}} products", "left_rail.product_count_aria_label_plural": "{{count}} items in the product grid", "left_rail.product_count_aria_label": "{{count}} item in the product grid", "left_rail.item_count_text": "{{count}} item", "left_rail.item_count_text_plural": "{{count}} items", "left_rail.item_count_text_br": "item ({{count}})", "left_rail.item_count_text_br_plural": "items ({{count}})", "left_rail.item_count_aria_label_plural": "{{count}} items in the product grid", "left_rail.item_count_aria_label": "{{count}} item in the product grid", "left_rail_lhf.filter_button": "FILTER {{selectedFacetsCountString}}", "left_rail_lhf.clear_filter_button": "Clear Filters", "product_grid_region.error_description": "We're having a technical problem on this page. Refresh the page and try your search again.", "product_grid_region.no_product_error_description": "There are currently no products that match your criteria. Please try again.", "top_rail_product_grid_region.error_description": "We're having a technical problem on this page. Refresh the page and try your search again.", "top_rail_product_grid_region.no_product_error_description": "There are currently no products that match your criteria. Please try again.", "price.current_price_range_aria_label": "Now {{minPrice}} to {{maxPrice}}", "price.now_current_price": "Now {{price}}", "price.percentage_off": "{{value}}% Off", "price.regular_price_aria_label": "Was {{price}}", "price.regular_price_range_aria_label": "Was {{minPrice}} to {{maxPrice}}", "facet_bar.accordion_header_aria_label_open": "{{facetLabel}} filter options, click to open the accordion", "facet_bar.accordion_header_aria_label_close": "{{facetLabel}} filter options, click to close the accordion", "facet_bar.clear_all_button": "clear all", "facet_bar.done_button": "done", "facet_bar.size_facet_one_size_variant": "one size", "facet_bar.modal_aria_label_close": "click to close filter dialog window", "facet_bar.product_filters_aria_label": "Product filters", "facet_bar.facet_tag_aria_label": "close", "facet_tabs.facet_tab_aria_label": "{{facetLabel}} filter options dropdown", "facet_tabs.item_count": "{{count}} item", "facet_tabs.item_count_plural": "{{count}} items", "facet_tabs.item_count_aria_label": "{{count}} item in the product grid", "facet_tabs.item_count_aria_label_plural": "{{count}} items in the product grid", "facet_tabs.mobile_facet_modal_trigger": "filter", "facet_tabs.mobile_facet_modal_trigger_aria_label": "filter will launch a dialog window", "facet_reference.facet_label_department": "department", "facet_reference.facet_label_style": "category", "facet_reference.facet_label_color": "color", "facet_reference.facet_label_price": "price", "facet_reference.facet_label_size": "size", "facet_reference.facet_label_rating": "rating", "price_facet.range_label": "Showing products between {{value.min, currency.USD}} and {{value.max, currency.USD}}", "cat.bopis.map_link_aria_label": "directions to {{store.storeName}} - {{store.storeDistance}} miles away, opens in a new tab", "cat.bopis.modal.close_button_aria_label": "close modal", "changeStoreModal.postCodeValidationError": "Please enter a 5-digit ZIP Code.", "changeStoreModal.noStoreMatch": "We couldn't find a store that matched your criteria. Please try again.", "changeStoreModal.pickUpUnavailable": "Pickup In-Store is unavailable. Please try again later.", "changeStoreModal.curbside.pickUpUnavailable": "C<PERSON><PERSON><PERSON> Pickup is unavailable. Please try again later.", "changeStoreModal.selectStoreCheckAvailability": "Select a store to check Pickup In-Store product availability.", "changeStoreModal.curbside.selectStoreCheckAvailability": "Select a store to check Curbside Pickup product availability.", "changeStoreModal.store.instore": "In-Store Pickup Only", "changeStoreModal.store.curbside": "Curbside Pickup Only", "changeStoreModal.store.instoreAndCurbside": "Curbside and In-Store Pickup", "changeStoreModal.bopisAndCurbside.selectStoreCheckAvailability": "Select a store to check product availability", "changeStoreModal.selectedButton": "SELECTED", "changeStoreModal.seeAvailability": "SEE AVAILABILITY", "changeStoreModal.searchByZipcode": "SEARCH BY ZIP CODE", "product-grid.reduced-price-flag": "Now", "bopis_facet.available": "Available for", "bopis_facet.pickup_in_store": "in-store pickup", "bopis_facet.pickup_curbside": "curbside pickup", "bopis_facet.pickup_in_store_and_curbside": "curbside & in-store pickup", "bopis_facet.unavailable_pickup_in_store": "Store unavailable for in-store shopping", "bopis_facet.unavailable_pickup_curbside": "Store unavailable for Curbside Pickup", "bopis_facet.available_pickup_in_store": "Find products that are available to pickup in-store", "bopis_facet.available_pickup_curbside": "Find products that are available to pickup.", "bopis_facet.select_store": "Press to choose a store", "mobile_facets_wrapper.clear_all_text": "Clear All", "mobile_facets_wrapper.done_text": "Done", "grid_header.facet_drawer.see_results_text": "See {{count}} Result", "grid_header.facet_drawer.see_results_text_plural": "See {{count}} Results", "bopis_bar.toggle_switch_label": "Pick-up In-store", "bopis_bar.toggle_enabled_text": "Pick-up In-store is enabled.", "bopis_bar.toggle_disabled_text": "Pick-up In-store is disabled.", "bopis_bar.toggle_switch_label_curbside": "Pick-up <PERSON><PERSON><PERSON><PERSON>", "bopis_bar.toggle_enabled_text_curbside": "Pick-up Curbside is enabled.", "bopis_bar.toggle_disabled_text_curbside": "Pick-up <PERSON><PERSON><PERSON><PERSON> is disabled.", "bopis_bar.bar_label_text": "IN-STORE PICKUP", "bopis_bar.bar_label_text_curbside": "CURBSIDE PICKUP", "bopis_bar.bar_label_text_in_store_and_curbside": "CURBSIDE & IN-STOR<PERSON> PICKUP", "bopis_bar.modal_change_store_text": "Change Store", "bopis_bar.modal_change_store_text_br_redesign": "Change", "left_hand_facet_size.size_facet_regular_variant": "Regular", "left_hand_facet_size.size_facet_one_size": "One Size", "left_hand_facet_size.size_facet_title": "size variant", "left_hand_facet_rating.rating_facet_all_reviewed": "All Reviewed Products", "left_hand_facet_rating.rating_facet_option": "{{starNumber}} out of 5 stars", "tag_link_group.header_label": "Related Items", "tag_link_group.related_categories_heading": "Related Categories", "tag_link_group.see_more_related_categories_aria_label": "See more (related categories)", "tag_link_group.see_less_related_categories_aria_label": "See less (related categories)", "tag_link_group.see_more_related_categories_text": "See more", "tag_link_group.see_less_related_categories_text": "See less", "metadata.subcategory.br.description": "Discover a spirit of adventure, shop {{subcategoryTitle}} at Banana Republic for thoughtfully designed classics, crafted from luxurious materials with an eye towards sustainability.", "metadata.subcategory.on.description": "Shop Old Navy for {{subcategoryTitle}}, find essential styles & fashion trends for the family at amazing prices.", "metadata.subcategory.gap.description": "Shop Gap for the {{subcategoryTitle}} you need. Our iconic pieces. Your personal style. Since 1969.", "metadata.subcategory.at.description": "Shop for {{subcategoryTitle}} at Athleta, a premium fitness & lifestyle brand that creates versatile performance apparel to inspire a community of active, confident women.", "metadata.subcategory.gapfs.description": "Shop Gap Factory for the {{subcategoryTitle}} you need. Our iconic pieces. Your personal style. Since 1969.", "metadata.subcategory.brfs.description": "Shop {{subcategoryTitle}} at Banana Republic Factory, designed for a life with no boundaries.", "marketing.recommendations.price.off": "off", "marketing.recommendations.price.now": "Now", "marketing.recommendations.price.was": "was", "inline-facet-tags.tag-close-button-aria-label": "remove filter", "product_breadcrumbs.product_breadcrumbs_aria_label": "breadcrumb", "left-nav.skip-to-products": "skip to products", "left-nav.skip-to": "Skip to", "left-nav.skip-to-product-filters": "Skip to product filters", "left-nav.skip-to-quick-filters": "Skip to quick filters", "left-nav.aria-label": "product links", "quickAdd.addToBag": "Add To Bag", "quickAdd.addToBagSuccess": "Added To Bag", "quickAdd.addToBagFailure": "Item Not Added", "quickAdd.error.sizeSelection": "Please select a {{unselectedDimensions}} before adding to bag.", "quickAdd.selectASize": "Select A Size", "quickAdd.modal-close-button-aria-label": "Close Quick Add", "quickAdd.toggle-open-button-aria-label": "Open Quick Add", "quickAdd.variantGroupLabel": "Variants", "quickAdd.error.general": "We're having a technical problem on this page. Refresh the page and try your selection again or find another product.", "quickAdd.colorLabel": "Color", "quickAdd.openModal": "Open Quick Add", "quickAdd.closeModal": "Close Quick Add", "quickAdd.error.itemOutOfStock": "We're sorry, this item is now out of stock. Try another selection.", "quickAdd.onBackOrderMessage": "On back order - est. shipping date {{date}}", "quickAdd.excludedFromPromotions": "Excluded from Promotions", "dashboard.filter": "Filter", "dashboard.filter_plural": "Filters", "dashboard.and_sort": "& Sort", "dashboard.model": "Model", "dashboard.sort_by": "Sort By", "model_toggle.size": "Size", "model_toggle.all_sizes": "All Sizes", "model_toggle.model": "Model", "model_toggle.size_model_small": "S", "model_toggle.size_model_medium": "M", "model_toggle.size_model_large": "L", "model_toggle.size_model_xlarge": "XL", "model_toggle.size_model_2xlarge": "2X", "model_toggle.br.size_model_medium": "32(M)", "model_toggle.br.size_model_large": "36(L)", "category_navigation.toggle_button.expanded": "Show Less", "category_navigation.toggle_button.collapsed": "Show All", "quick_filter.aria_prev_btn_text": "Previous Quick Filters Available", "quick_filter.aria_next_btn_text": "Next Quick Filters Available", "quick_filter.aria_quick_filter_text": "Quick Filters", "dropship_shipped_by_text": "Shipped by", "dropship_shipped_free_by_text": "Shipped free by", "dropship_excluded_from_promotions_text": "Excluded from promotions", "dropship_starts_from_text": "Starts from", "dropship_starting_at_text": "Starting at", "outOfStock_filter_text": "Show Out-Of-Stock items", "outOfStock_inline_facet_text": "Out-Of-Stock", "cms.carousel.previous": "Previous", "cms.carousel.next": "Next", "cms.carousel.previous_with_slide_count": "View previous of {{slidesLength}} slides", "cms.carousel.next_with_slide_count": "View next of {{slidesLength}} slides", "cms.pauseplay.play": "play", "cms.pauseplay.pause": "pause", "cms.promosticker.close": "Close", "inline_facet_tags.in_stock_facet_label": "in Stock", "inline_facet_tags.reviews_facet_label": "Reviews", "inline_facet_tags.clear_filters_button": "Clear Filters", "outOfStock.overlay": "Sold out", "grid_header.default.all_filters_text": "All Filters", "grid_header.at.all_filters_text": "Filters", "grid_header.br.all_filters_text": "All", "sortBy.facet.price.options.asc": "Price: Low To High", "sortBy.facet.price.options.desc": "Price: High To Low", "sortBy.facet.title": "Sort", "grid_toggle_dropdown.three_columns": "3 Across", "grid_toggle_dropdown.four_columns": "4 Across", "grid_toggle_dropdown.five_columns": "5 Across", "grid_toggle_dropdown.six_columns": "6 Across"}