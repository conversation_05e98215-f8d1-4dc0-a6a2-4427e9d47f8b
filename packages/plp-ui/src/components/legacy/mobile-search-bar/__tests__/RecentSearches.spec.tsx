// @ts-nocheck
import userEvent from "@testing-library/user-event";
import {
  act,
  fireEvent,
  waitFor,
  render,
  RenderOptions,
  screen,
} from "test-utils";
import { mocked } from "jest-mock";
import {
  ExperimentStatus,
  clientSideLocalStorage,
  redirectSearch,
  Brands,
  AbSeg,
} from "@ecom-next/plp-ui/legacy/utils";
import {
  clickOpenSearchButton,
  focusSearchBox,
  getSearchBox,
  getSearchButton,
  getSuggestion,
  inputSearchText,
  translations,
} from "../helpers";
import { filledRecentSearches } from "../__mocks__/recentSearches";
import { DataLayer } from "@ecom-next/core/legacy/app-state-provider/types";
import { renderMobileSearchBarButton } from "../helpers/renderMobileSearchBar";
import { AutoSuggestResponse } from "@ecom-next/plp-ui/legacy/autosuggest";
import { MockDepartmentSuggestions, MockProducts } from "../helpers/constants";

const datalayer = {
  linkWithPageProps: jest.fn(),
} as unknown as DataLayer;

const expectWrapperToHaveBackgroundColor = async (
  backgroundColor: string
): Promise<void> => {
  await waitFor(() =>
    expect(
      screen.getByTestId("mobile-suggestions-wrapper").firstChild
    ).toHaveStyle({
      backgroundColor,
    })
  );
};

jest.mock("@find/utils/redirectSearch");
jest.mock("@find/utils/clientSideStorage", () => ({
  clientSideLocalStorage: {
    setItem: jest.fn(),
    getItem: jest.fn(),
  },
}));
jest.mock("@ecom-next/plp-ui/legacy/search-service", () => ({
  createSearchService: jest.fn(() => ({
    autosuggest: () =>
      new Promise<AutoSuggestResponse>((resolve) =>
        resolve({
          suggestionsList: ["cat", "bat", "rat"],
          visualSearchProducts: MockProducts,
          departmentsList: MockDepartmentSuggestions,
        })
      ),
  })),
}));

const mockedGetItem = mocked(clientSideLocalStorage?.getItem);
const mockedSetItem = mocked(clientSideLocalStorage?.setItem);
const recentSearchesTestId = "recent-searches-mobile";
const ariaSelected = "aria-selected";

const expectSuggestion = async (
  suggestion: string,
  isActive: boolean
): Promise<void> => {
  expect(await getSuggestion(suggestion)).toHaveAttribute(
    ariaSelected,
    isActive.toString()
  );
};

const arrowKeyDown = async (): Promise<void> => {
  fireEvent.keyDown(await getSearchBox(), {
    key: "ArrowDown",
    code: "ArrowDown",
  });
};

const arrowKeyUp = async (): Promise<void> => {
  fireEvent.keyDown(await getSearchBox(), { key: "ArrowUp", code: "ArrowUp" });
};

const enterPress = (element: HTMLElement): void => {
  fireEvent.keyPress(element, { key: "Enter", code: 13, charCode: 13 });
};

const spacePress = (element: HTMLElement): void => {
  fireEvent.keyPress(element, { key: "(space)", code: 32, charCode: 32 });
};

describe("RecentSearches", () => {
  const renderMobileSearchBarWithRecentSearches = async (
    isExperimentActive: boolean,
    abSeg?: AbSeg,
    options?: RenderOptions
  ) => {
    const experimentStatus = isExperimentActive
      ? ExperimentStatus.Active
      : ExperimentStatus.Control;

    const abSegComp = abSeg || { gap132: experimentStatus };
    const option = {
      enabledFeatures: {
        "recent-searches-mobile": true,
      },
      appState: {
        datalayer,
      },
      breakpoint: 'small',
      ...options,
    };
    const renderOptions = {
      localization: translations,
      ...option,
    } as RenderOptions;
    render(renderMobileSearchBarButton(abSegComp), renderOptions);
    await act(() => clickOpenSearchButton());
  };

  describe("when the experiment is Active", () => {
    describe("when there are recent searches and no input text", () => {
      it("should render RecentSearches component", async () => {
        mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
        await renderMobileSearchBarWithRecentSearches(true);
        focusSearchBox();
        expect(screen.getByTestId(recentSearchesTestId)).toBeInTheDocument();
      });
      test.each`
        brand                                | featureEnabled | backgroundColor
        ${Brands.Gap}                        | ${true}        | ${"#FFFFFF"}
        ${Brands.OldNavy}                    | ${true}        | ${"#FFFFFF"}
        ${Brands.BananaRepublic}             | ${true}        | ${"#F6F4EB"}
        ${Brands.Athleta}                    | ${true}        | ${"#FFFFFF"}
        ${Brands.GapFactoryStore}            | ${true}        | ${"#FFFFFF"}
        ${Brands.BananaRepublicFactoryStore} | ${true}        | ${"#F6F4EB"}
        ${Brands.Gap}                        | ${false}       | ${"#FFFFFF"}
        ${Brands.OldNavy}                    | ${false}       | ${"#FFFFFF"}
        ${Brands.BananaRepublic}             | ${false}       | ${"#FFFFFF"}
        ${Brands.Athleta}                    | ${false}       | ${"#FFFFFF"}
        ${Brands.GapFactoryStore}            | ${false}       | ${"#FFFFFF"}
        ${Brands.BananaRepublicFactoryStore} | ${false}       | ${"#FFFFFF"}
      `(
        'should show $backgroundColor background color for $brand when "pdp-redesign-2022" feature flag is $featureEnabled',
        async ({ brand, featureEnabled, backgroundColor }) => {
          mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
          await renderMobileSearchBarWithRecentSearches(
            true,
            { [`${brand}132`]: ExperimentStatus.Active },
            {
              appState: {
                brandName: brand,
                datalayer,
              },
              enabledFeatures: {
                "pdp-redesign-2022": featureEnabled,
                "br-colors-2023": featureEnabled
              },
            }
          );
          focusSearchBox();
          await expectWrapperToHaveBackgroundColor(backgroundColor);
        }
      );
    });
    describe("when there are recent searches and text input", () => {
      it("should not render recentSearches component", async () => {
        mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
        await renderMobileSearchBarWithRecentSearches(true);
        inputSearchText();
        expect(
          await waitFor(() => screen.queryByTestId(recentSearchesTestId))
        ).not.toBeInTheDocument();
      });
    });
    describe("when user performs a search", () => {
      it("should set a new recent Search", async () => {
        await renderMobileSearchBarWithRecentSearches(true);
        inputSearchText();
        await act(async () => { 

         fireEvent.click(await getSearchButton()); 

         })
        expect(mockedSetItem).toHaveBeenCalled();
      });
    });
    describe("when user clicks a regular autosuggest item", () => {
      it("should set a new recent Search", async () => {
        await renderMobileSearchBarWithRecentSearches(true);
        inputSearchText();
        await act(async () => { 

         fireEvent.click(await getSuggestion("cat")); 

         })
        expect(mockedSetItem).toHaveBeenCalled();
      });
    });
  });
  describe("when the experiment is control", () => {
    it("should not render recentSearches component", async () => {
      mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
      await renderMobileSearchBarWithRecentSearches(false);
      expect(
        screen.queryByTestId(recentSearchesTestId)
      ).not.toBeInTheDocument();
    });
  });
  describe("when there are no recent searches or input text", () => {
    beforeEach(async () => {
      mockedGetItem?.mockReturnValue("");
      await renderMobileSearchBarWithRecentSearches(true);
    });
    it("should not render RecentSearches", async () => {
      inputSearchText();
      expect(
        await waitFor(() => screen.queryByTestId(recentSearchesTestId))
      ).not.toBeInTheDocument();
    });
    it("should not render Autosuggest", async () => {
      expect(
        await waitFor(() => screen.queryByTestId("autosuggest"))
      ).not.toBeInTheDocument();
    });
  });

  describe("when navigate with keyboard", () => {
    beforeEach(async () => {
      mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
      await renderMobileSearchBarWithRecentSearches(true);
      focusSearchBox();
    });

    it("should focus on clear all throught arrow key", async () => {
      await act(async () => arrowKeyUp());
      const element = screen.getByTestId("mobile-clear-all");
      expect(element).toHaveStyle({ fontSize: "14px" });
    });
    it("should focus on first recent search when press arrow down", async () => {
      await act(async () => arrowKeyDown());
      expectSuggestion("Search socks", true);
    });
    it("should be able to cycle between recent search elements", async () => {
      await act(async () => arrowKeyDown());
      await act(async () => arrowKeyDown());
      await act(async () => arrowKeyDown());
      await act(async () => arrowKeyDown());
      expectSuggestion("Search socks", true);
    });
    it("should have an associated keyPress event on recent search elements", async () => {
      focusSearchBox();
      await act(async () => arrowKeyDown());
      const element = screen.getByRole("option", {
        name: "Search socks",
        selected: true,
      });
      await act(async () => spacePress(element));
      expect(redirectSearch).toHaveBeenCalledWith(
        "socks",
        false,
        "",
        true,
        false
      );
    });

    it("should clear the suggestion selection when tabbing and opening recent searches dropdown again", async () => {
      await act(async () => arrowKeyDown());
      await act(async () => userEvent.tab());

      focusSearchBox();

      expectSuggestion("Search socks", false);
    });

    it("should have an associated keyPress event on clear all button", async () => {
      focusSearchBox();
      await act(async () => arrowKeyUp());
      const element = screen.getByTestId("mobile-clear-all");
      await act(async () => enterPress(element));
      expect(
        await waitFor(() => screen.queryByTestId(recentSearchesTestId))
      ).not.toBeInTheDocument();
    });
    it("should update aria-activedescendant", async () => {
      focusSearchBox();
      await act(async () => arrowKeyDown());
      const searchBox = await getSearchBox();
      expect(searchBox).toHaveAttribute(
        "aria-activedescendant",
        "suggestedOption0"
      );
      await act(async () => arrowKeyDown());
      expect(searchBox).toHaveAttribute(
        "aria-activedescendant",
        "suggestedOption1"
      );
    });
  });
});
