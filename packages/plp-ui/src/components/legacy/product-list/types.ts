// @ts-nocheck
'use client'

import { DataLayer } from "@ecom-next/core/legacy/app-state-provider/types";
import {
  XLARGE,
  LARGE,
  MEDIUM,
  SMALL,
} from "@ecom-next/core/breakpoint-provider";
import { GridToggle } from "@ecom-next/plp-ui/legacy/plp-experiments";
import { HighPriorityEvent } from "@ecom-next/plp-ui/legacy/product-image";
import React from "react";

export type ColumnsConfig = {
  [XLARGE]?: number;
  [LARGE]?: number;
  [MEDIUM]?: number;
  [SMALL]?: number;
  [key: number]: number;
};

export type ProductListProps = {
  columns?: ColumnsConfig;
  spacing?: number;
  onClick?: () => void;
  highPriorityEvents: Array<HighPriorityEvent>;
  errorHandler: (error: string | Error) => void;
  cid: string;
  url: string;
  datalayer?: DataLayer | undefined;
  abSeg: any;
  gridSize?: GridToggle;
};

export declare enum Brands {
  Athleta = "at",
  BananaRepublic = "br",
  BananaRepublicFactoryStore = "brfs",
  Gap = "gap",
  GapFactoryStore = "gapfs",
  OldNavy = "on",
}

export interface BrandInfoContextType {
  abbrBrand: Brands;
  market: string;
}
