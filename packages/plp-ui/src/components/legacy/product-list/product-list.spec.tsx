// @ts-nocheck
import React from "react";
import { fireEvent, render, RenderOptions, screen, act } from "test-utils";
import { QueryClientProvider } from "@tanstack/react-query";
import { HttpResponse, http } from "msw";
import { setupServer, SetupServer } from "msw/node";

import { ProductList } from "./product-list";
import { ProductListProps } from "./types";
import genderNeutralBlueTshirt from "shared-fixtures/cc/gender-neutral-blue-tshirt.json";
import translationsEnUS from "translations/translations-cron/translation-data/i18n/en_US.json";
import { client } from "./product-list";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";
import { GridToggle } from "@ecom-next/plp-ui/legacy/plp-experiments";

const fakeUrlSuccess = "http://fakeurl.com";

const restHandlers = [
  http.get(
    `${fakeUrlSuccess}/commerce/search/products/v2/cc`,
    async (_req, res, context) => {
      return HttpResponse.json(genderNeutralBlueTshirt, {status: 200});
    }
  ),
];

describe("ProductList", () => {
  let server: SetupServer;

  beforeAll(() => {
    server = setupServer(...restHandlers);
    server.listen({ onUnhandledRequest: "error" });
  });

  afterAll(() => server.close());

  // Reset handlers after each test `important for test isolation`
  afterEach(() => server.resetHandlers());

  const renderProductList = (props?: ProductListProps, options?: RenderOptions) =>
    render(
      <QueryClientProvider client={client}>
        <PLPStateProvider abSeg={{ at189: "a" }}>
          <ProductList
            {...props}
            highPriorityEvents={[]}
            cid="123456"
            url={fakeUrlSuccess}
            errorHandler={jest.fn()}
            abSeg={{}}
            gridSize={GridToggle.Off}
          />
        </PLPStateProvider>
      </QueryClientProvider>,
      {
        ...options,
        localization: translationsEnUS,
      }
    );

  it("should render product list correctly", async () => {
    renderProductList();

    expect(await screen.findByText(/kids gap logo graphic t-shirt/i)).toBeInTheDocument();
    expect(screen.getByText(/nasa graphic t-shirt/i)).toBeInTheDocument();
    expect(screen.getByText(/marvel graphic t-shirt/i)).toBeInTheDocument();
  });

  it("products list snapshot", async () => {
    const { container } = renderProductList();

    expect(container).toMatchSnapshot();
  });
});
