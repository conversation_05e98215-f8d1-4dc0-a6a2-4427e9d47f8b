// @ts-nocheck
'use client'

import React, { useContext } from "react";
import { PsDataProvider } from "@ecom-next/plp-ui/legacy/ps-data-provider";
import { ProductListProps } from "./types";
import { RailComponent } from "./components/rail-wrapper";
import { SortByComponent } from "./components/sortby-wrapper";
import { ErrorBoundary } from "@ecom-next/core/legacy/error-boundary";
import { styled } from "@ecom-next/core/react-stitch";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { InlineFacetTags } from "@ecom-next/plp-ui/legacy/inline-facet-tags";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import GridWrapper from "./components/grid-wrapper";
import { onFacetEngagement } from '@find/components/legacy/plp-data-layer/plpDataLayer';
import { PlpDataLayerProvider } from "@ecom-next/plp-ui/legacy/plp-data-layer";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";

export const client = new QueryClient({
  logger:
    process.env.NODE_ENV === "test"
      ? {
          log: console.log,
          warn: console.warn,
          error: console.error,
        }
      : undefined,
});

export const ProductList = (props: ProductListProps): JSX.Element => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const { errorHandler, url, cid, datalayer, abSeg } = props;

  const isDesktop = greaterOrEqualTo(XLARGE);
  const ContentContainer = styled.div(() => ({
    width: "100%",
    minHeight: "500px",
    maxWidth: "1280px",
    marginRight: "auto",
  }));
  const ProductListContainer = styled.div(() => ({
    display: isDesktop ? "flex" : "block",
    width: "100%",
    flexGrow: 1,
    "*": {
      boxSizing: "border-box",
    },
    paddingRight: "0.5rem",
  }));

  return (
    <ContentContainer data-testid="product-list">
      <ErrorBoundary componentName="Product List" errorLogger={errorHandler}>
        <QueryClientProvider client={client}>
          <PLPStateProvider abSeg={abSeg}>
            <PlpDataLayerProvider>
              <PsDataProvider
                url={url}
                cid={cid}
                onFacetEngagement={(facets: any) => {
                  onFacetEngagement(datalayer, { appliedFacets: facets });
                }}
              >
                <InlineFacetTags />
                {isDesktop ? <SortByComponent /> : ""}
                <ProductListContainer>
                  <RailComponent />

                  <GridWrapper {...props} />
                </ProductListContainer>
              </PsDataProvider>
            </PlpDataLayerProvider>
          </PLPStateProvider>
        </QueryClientProvider>
      </ErrorBoundary>
    </ContentContainer>
  );
};
