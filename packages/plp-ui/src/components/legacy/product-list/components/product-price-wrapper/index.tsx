// @ts-nocheck
'use client'

import React from 'react';
import { useProductPrice} from '../../hooks/useProductPrice';
import { Price } from '@ecom-next/core/legacy/product-price';
import { catPriceAdapter } from '@ecom-next/core/legacy/price-case';

type PriceWrapperProps = {
    productId: string;
  }

export const ProductPriceWrapper = ({productId}: PriceWrapperProps) => {
  const { isSuccess, ...contextData } = useProductPrice(productId);

  return (
    < Price
        adapter={catPriceAdapter}
        price={contextData?.adapter}
    />
)
};
