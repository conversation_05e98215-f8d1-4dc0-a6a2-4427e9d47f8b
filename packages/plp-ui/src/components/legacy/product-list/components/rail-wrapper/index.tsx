// @ts-nocheck
'use client'

/* eslint-disable react/no-unknown-property */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import React, { DataHTMLAttributes, useContext } from "react";
import { SitewideContextProvider } from "@ecom-next/core/legacy/sitewide";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { BrandInfoContext } from "@ecom-next/sitewide/brand-info-provider";
import { Rail } from "@ecom-next/plp-ui/legacy/rail";
import { FacetBar } from "@ecom-next/plp-ui/legacy/facet-bar";
import { useTheme } from "@ecom-next/core/react-stitch";
import { useLocalize } from "@ecom-next/sitewide/localization-provider";
import { SortBy, SortByValue } from "@ecom-next/plp-ui/legacy/sort-by";
import { useSortBy } from "@ecom-next/plp-ui/legacy/ps-data-provider";
import { ItemCount } from "@ecom-next/plp-ui/legacy/item-count";
import ClearButton from "../clear-button";
import {
  FilterDetailsStyles,
  DrawerMiddleSection,
  DrawerTopSection,
  FacetBarWrapper,
} from "./styled";

export const RailComponent = (): JSX.Element => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const { sortByValue, sortByOnChange } = useSortBy();
  const { brand } = useContext(BrandInfoContext);
  const theme = useTheme();
  const isDesktop = greaterOrEqualTo(XLARGE);

  return (
    <SitewideContextProvider>
      {isDesktop ? (
        <Rail hasLeftFacetedGrid={true} newDashboard={false}>
          {/* @ts-ignore */}
          <FilterDetailsStyles position="scrollable" theme={theme}>
            <ItemCount />
            <ClearButton />
          </FilterDetailsStyles>
          <div
            // @ts-ignore
            position="scrollable"
            style={{ height: "1000px" }}
          >
            <FacetBar position="scrollable" />
          </div>
        </Rail>
      ) : (
        <Rail newDashboard={false}>
          {/* @ts-ignore */}
          <DrawerTopSection position="drawer" theme={theme}>
            <ItemCount />
            <ClearButton />
          </DrawerTopSection>

          {/* @ts-ignore */}
          <DrawerMiddleSection position="drawer">
            <FacetBarWrapper>
              <FacetBar />
            </FacetBarWrapper>
          </DrawerMiddleSection>
          <div
            // @ts-ignore
            position="dashboard"
          >
            <ItemCount />
          </div>
          <div
            // @ts-ignore
            position="dashboard"
            style={{ maxHeight: "max-content" }}
          >
            <SortBy
              // TODO: Fix the type for sortByValue
              value={sortByValue as unknown as SortByValue}
              onChange={sortByOnChange}
              isSortByRatingsEnabled={false}
              isSortByNewEnabled={false}
              isSortByBestSellersEnabled={false}
              brand={brand}
            />
          </div>
        </Rail>
      )}
    </SitewideContextProvider>
  );
};
