// @ts-nocheck
'use client'

import { styled } from "@ecom-next/core/react-stitch";

export const DrawerTopSection = styled.div(({ theme }) => ({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "1rem",
    borderBottom: `1px solid ${theme.color.gray20}`,
  }));
  
export const DrawerMiddleSection = styled.section`
    width: 100%;
    overflow-y: scroll;

    div > button {
      padding-right: 20px !important;
  
      &:after {
        right: 0.5rem !important;
      }
    }
  `;
  
 export const FacetBarWrapper = styled.div`
    & button[class$='AccordionItem'] {
      padding:'1rem'};
    }
  `;
  
 export const FilterDetailsStyles = styled.div(({ theme }) => ({
    paddingTop: "1rem",
    paddingBottom: "1rem",
    backgroundColor: 'white',
    borderTop: `1px solid ${theme?.color?.gray20}`,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    position: "sticky",
    top: 0,
    zIndex: 150,
  }));
  