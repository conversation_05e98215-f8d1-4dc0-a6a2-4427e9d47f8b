// @ts-nocheck
'use client'

import React, { useContext } from "react";
import { useSortBy } from "@ecom-next/plp-ui/legacy/ps-data-provider";
import { BrandInfoContext } from "@ecom-next/sitewide/brand-info-provider";
import { SortBy, SortByValue } from "@ecom-next/plp-ui/legacy/sort-by";
import styled from "@emotion/styled";

const ProductOptionsContainer = styled.div(() => ({
  margin: "0 0.5rem",
  width: "100%",
  alignSelf: "flex-end",
  display: "flex",
  flex: "1 1 auto",
  justifyContent: "flex-end",
  ".sort-by-price-dropdown": {
    width: "auto",
    marginLeft: "1rem",
  },
}));

export const SortByComponent = (): JSX.Element => {
  const { brand } = useContext(BrandInfoContext);
  const { sortByValue, sortByOnChange } = useSortBy();

  return (
    <ProductOptionsContainer>
      <SortBy
        // TODO: Fix the type for sortByValue
        value={sortByValue as unknown as SortByValue}
        onChange={sortByOnChange}
        isSortByRatingsEnabled={false}
        isSortByNewEnabled={false}
        isSortByBestSellersEnabled={false}
        brand={brand}
      />
    </ProductOptionsContainer>
  );
};
