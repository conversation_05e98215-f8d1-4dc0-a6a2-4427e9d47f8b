// @ts-nocheck
'use client'

import React from 'react';
import ProductGridPlaceholder from '@ecom-next/core/legacy/product-grid-placeholder';
import ProductGridPlaceholderWrapper from './styled/ProductGridPlaceholderWrapper';

const getWindowWidth = () => {
  return (typeof window !== 'undefined') ? window?.innerWidth : 0;
};

const getNumberOfPlaceholders = (isBrRedesignEnabled: boolean) => {
  if (isBrRedesignEnabled && getWindowWidth() >= 768) {
    return 9;
  }
  return 8;
};

const GridPlaceholder = (): JSX.Element => {
  const isBrRedesignEnabled = false;

  const numberOfPlaceholders = getNumberOfPlaceholders(isBrRedesignEnabled);

  return (
    <ProductGridPlaceholderWrapper isBrRedesignEnabled={isBrRedesignEnabled}>
      <ProductGridPlaceholder
        numberOfPlaceholders={numberOfPlaceholders}
        showColorSwatchesPlaceholder={false}
        showStarRatingsPlaceholder={false}
      />
    </ProductGridPlaceholderWrapper>
  );
};

export default GridPlaceholder;
