// @ts-nocheck
'use client'

import React from "react";
import { useTheme } from "@emotion/react";
import Grid from "@ecom-next/plp-ui/legacy/grid";
import ProductImage from "@ecom-next/plp-ui/legacy/product-image";
import ProductCard from "@ecom-next/plp-ui/legacy/product-card";
import ProductCardName from "@ecom-next/plp-ui/legacy/product-name";
import ProductMarketingFlag from "@ecom-next/plp-ui/legacy/product-marketing-flag";
import { usePsDataContext } from "@ecom-next/plp-ui/legacy/ps-data-provider";
import { ProductPriceWrapper } from "../product-price-wrapper";
import { ProductListProps } from "../../types";
import GridPlaceholder from "../grid-placeholder";

const GridWrapper = ({
  cid,
  url,
  highPriorityEvents,
  errorHandler,
  datalayer,
  columns,
  spacing,
  ...props
}: ProductListProps): JSX.Element => {
  const theme = useTheme();
  const context = usePsDataContext();
  const hasPlaceholder = context.state.isLoading;

  return hasPlaceholder ? (
    <GridPlaceholder />
  ) : (
    <Grid
      index={0}
      contents={{
        default: ({ productId }) => {
          return (
            <ProductCard productInfo={{ productID: productId }}>
              <ProductImage
                id={productId}
                highPriorityEvents={highPriorityEvents}
                onClick={props.onClick}
              ></ProductImage>
              <ProductCardName productId={productId} clickable={false} />
              <ProductPriceWrapper productId={productId} />
              <ProductMarketingFlag productId={productId} />
            </ProductCard>
          );
        },
      }}
      theme={theme}
      columns={columns}
      spacing={spacing}
    />
  );
};

export default GridWrapper;
