// @ts-nocheck
'use client'

import React from "react";
import { useLocalize } from "@ecom-next/sitewide/localization-provider";
import { useFacets } from "@ecom-next/plp-ui/legacy/ps-data-provider";

import { ClearButtonProps } from "./types";
import StyledButton from "./styles";
import { CLEAR_FILTER_BUTTON } from "../localization-tokens";

const focusElement = (accessibleFocusSelector: string): void => {
  if (accessibleFocusSelector !== "") {
    const element = document.querySelector(
      accessibleFocusSelector
    ) as HTMLElement;
    element?.focus();
  }
};

const ClearButton = ({
  disabled,
  localizedButtonText,
  publish,
  accessibleFocusSelector = "",
}: ClearButtonProps): JSX.Element | null => {
  const { localize } = useLocalize();
  const { clearFacetOption, appliedFacetsCount } = useFacets();

  return appliedFacetsCount > 0 ? 
  (
    <StyledButton
      className="clear-button"
      disabled={disabled}
      onClick={(event) => {
        publish ? publish("clearAllFacets") : clearFacetOption?.();
        focusElement(accessibleFocusSelector);
        event.stopPropagation();
      }}
      tabIndex={0}
    >
      {localizedButtonText ?? localize(CLEAR_FILTER_BUTTON)}
    </StyledButton>
  ) : null;
};

export * from "./types";
export default ClearButton;
