// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > Athleta 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-5a86zu"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-5a86zu"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > Gap 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-1n33kkn"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-1n33kkn"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > OldNavy 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-145y9r4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for small breakpoint > renders default style state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-145y9r4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > Athleta 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-uo9c5d"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-uo9c5d"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-88rzd"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > Gap 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-8z0o4"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > OldNavy 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-1azcoic"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;

exports[`<ClearButton/> > snapshots > for x-large breakpoint > renders default style state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <button
    class="clear-button css-1azcoic"
    tabindex="0"
  >
    Clear All
  </button>
</DocumentFragment>
`;
