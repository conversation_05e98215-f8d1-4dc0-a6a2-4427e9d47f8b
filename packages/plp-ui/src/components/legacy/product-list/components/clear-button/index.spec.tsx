// @ts-nocheck
import React from "react";
import { snapshotsForBreakpoints, act } from "test-utils";
import {
  render,
  fireEvent,
  RenderResult,
  screen,
} from "test-utils";
import { Size, SMALL, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { Brands, StitchStyleProvider } from "@ecom-next/core/react-stitch";
import { useFacets } from "@ecom-next/plp-ui/legacy/ps-data-provider";

import { merge } from "lodash";
import ClearButton, { ClearButtonProps } from ".";

jest.mock("@ecom-next/plp-ui/legacy/ps-data-provider");
const clearFacetMock = jest.fn();
const mockedUseFacets = useFacets as Mock;

const publishMock = jest.fn();
const baseProps = {
  disabled: false,
  localizedButtonText: "Clear All",
  publish: publishMock,
};

const renderClearButton = (config?: {
  props?: ClearButtonProps;
  breakpoint?: Size;
}): RenderResult => {
  const { props = baseProps, breakpoint = SMALL } = config ?? {};
  return render(<ClearButton {...props} />, { breakpoint });
};

const renderWithEnableFeatures = (
  enabledFeatures: Record<string, boolean>,
  breakpoint: Size = SMALL
) =>
  render(
    <StitchStyleProvider
      brand={Brands.BananaRepublic}
      enabledFeatures={enabledFeatures}
    >
      <ClearButton {...baseProps} />
    </StitchStyleProvider>,
    { breakpoint }
  );

describe("<ClearButton/>", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("snapshots", () => {
    mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 1 });
    snapshotsForBreakpoints(
      [SMALL, XLARGE],
      // @ts-ignore
      ClearButton,
      [["default style", baseProps]],
      {}
    );
  });

  describe("render clear button", () => {
    it("should not render when there are no applied facets", () => {
      mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 0 });
      renderClearButton({ props: baseProps });
      expect(
        screen.queryByRole('button', { name: new RegExp(baseProps.localizedButtonText, 'i') })
      ).not.toBeInTheDocument();
    });
    it("should render when applied facets count > 0", () => {
      mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 1 });
      renderClearButton({ props: baseProps });
      expect(
        screen.getByRole('button', { name: new RegExp(baseProps.localizedButtonText, 'i') })
      ).toBeInTheDocument();
    });
  });

  describe("fonts for Breakpoints", () => {
    mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 1 });
    it("use correct font size at XLARGE breakpoint", () => {
      renderClearButton({
        breakpoint: XLARGE,
      });
      expect(screen.getByRole("button")).not.toHaveStyleRule(
        "font-size",
        "1.2rem"
      );
    });
    it("use correct font size when NOT XLARGE breakpoint", () => {
      renderClearButton({
        breakpoint: SMALL,
        props: baseProps,
      });
      expect(screen.getByRole("button")).toHaveStyleRule("font-size", "1.2rem");
    });

    it("use correct font size when NOT XLARGE breakpoint and BR redesign is enabled", () => {
      renderWithEnableFeatures({ "cat-br-redesign": true });
      expect(screen.getByRole("button")).toHaveStyleRule(
        "font-size",
        "0.75rem"
      );
    });
  });

  describe("onclick behavior", () => {
    mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 1 });
    it("publish callback is called", async () => {
      renderClearButton({ props: baseProps });
      const btn = screen.getByRole("button");
      await act(async () => { 

       await act(async () => { 

        fireEvent.click(btn); 

        }) 

       })
      expect(publishMock).toHaveBeenCalled();
    });
    it("clearFacetOption callback from hooks is called", async () => {
      renderClearButton({ props: {} });
      const btn = screen.getByRole("button");
      await act(async () => { 

       await act(async () => { 

        fireEvent.click(btn); 

        }) 

       })
      expect(clearFacetMock).toHaveBeenCalled();
    });
    it("document.querySelector is NOT called when accessibleFocusSelector prop is missing", async () => {
      document.querySelector = jest.fn();
      jest.spyOn(document, "querySelector").mockImplementation(() => null);
      renderClearButton({ props: baseProps });
      await act(async () => { 

       await act(async () => { 

        fireEvent.click(screen.getByRole("button")); 

        }) 

       })
      expect(document.querySelector).not.toHaveBeenCalled();
    });
    it("document.querySelector is not called using accessibleFocusSelector prop", async () => {
      const mockSelector = ".clear-button";
      const mockProps = merge({}, baseProps, {
        accessibleFocusSelector: mockSelector,
      });
      document.querySelector = jest.fn();
      jest.spyOn(document, "querySelector").mockImplementation(() => null);
      renderClearButton({ props: mockProps });
      await act(async () => { 

       await act(async () => { 

        fireEvent.click(screen.getByRole("button")); 

        }) 

       })
      expect(document.querySelector).toHaveBeenCalledWith(mockSelector);
    });
  });

  describe("props", () => {
    mockedUseFacets.mockReturnValue({ clearFacetOption: clearFacetMock, appliedFacetsCount: 1 });
    it("button has disabled attribute", () => {
      const mockProps = baseProps;
      mockProps.disabled = true;
      renderClearButton({ props: mockProps });
      expect(screen.getByRole("button")).toHaveAttribute("disabled");
    });
  });
});
