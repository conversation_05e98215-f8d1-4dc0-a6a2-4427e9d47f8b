// @ts-nocheck
'use client'

import {
  styled,
  css,
  forBrands,
  getFont,
  getFontWeight,
} from "@ecom-next/core/react-stitch";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { useContext } from "react";

const StyledButton = styled.button(({ theme }) => {
  const { minWidth } = useContext(BreakpointContext);

  const brandStyles = forBrands(theme, {
    gap: (theme) => css`
      ${getFont("primary")({ theme })}
      color: ${theme.color.b2};
      font-size: ${!minWidth(XLARGE) ? "1.2rem" : "0.8rem"};
      padding: 0;
    `,
    gapfs: (theme) => css`
      ${getFont("primary")({ theme })}
      color: ${theme.color.b2};
      font-size: 0.8rem;
      padding: 0;
    `,
    on: (theme) => css`
      ${getFont("secondary")({ theme })}
      color: ${theme.color.b1};
      font-size: ${!minWidth(XLARGE) ? "1.2rem" : "0.8rem"};
      padding: 1px 0 0 0;
    `,
    br: (theme) => css`
      ${getFont("primary")({ theme })}
      border: none;
      font-size: 0.75rem;
      line-height: 0.875rem;
      ${getFontWeight("demiBold")};
      text-transform: capitalize;
      padding: 0;
      letter-spacing: 0.3px;
      color: ${theme.color.bk};
    `,
    brfs: (theme) => css`
      ${getFont("primary")({ theme })}
      border: none;
      font-size: 0.75rem;
      line-height: 0.875rem;
      ${getFontWeight("demiBold")};
      text-transform: capitalize;
      padding: 0;
      letter-spacing: 0.3px;
      color: ${theme.color.bk};
    `,
    at: (theme) => css`
      ${getFont("secondary")({ theme })}
      color: ${theme.color.g1};
      letter-spacing: 1px;
      font-size: ${!minWidth(XLARGE) ? "1.2rem" : "0.8125rem"};
      padding: 0.25rem 0;
    `,
  });

  return css`
    ${theme.utilities.unbuttonize}
    display: inline-block;
    letter-spacing: 0.0625em;
    padding: 3px;
    width: auto;
    cursor: pointer;
    text-transform: uppercase;
    ${brandStyles}
  `;
});
export default StyledButton;
