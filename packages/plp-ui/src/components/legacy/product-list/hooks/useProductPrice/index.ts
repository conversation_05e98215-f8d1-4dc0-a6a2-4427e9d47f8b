// @ts-nocheck
'use client'

import { usePsDataContext, ProductPriceState } from "@ecom-next/plp-ui/legacy/ps-data-provider";

export const useProductPrice = (
  productId: string
): ProductPriceState & { isSuccess: boolean } => {
  const { state } = usePsDataContext();
  const isSuccess = state?.isSuccess ?? false;
  const contextData = state?.data?.productInfos[productId]?.productPrice;

  return { ...contextData, isSuccess };
};

