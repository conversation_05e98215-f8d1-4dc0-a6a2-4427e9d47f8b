// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<AutosuggestVisualSearchItem /> should apply product-price__no-strike  class when price type is R 1`] = `
.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 80px;
  position: relative;
}

.emotion-1 .product-card__image {
  max-width: none;
  display: block;
  height: 80px;
}

.emotion-2 {
  margin-bottom: 0.5rem;
}

.emotion-4 {
  margin-left: 8px;
}

.emotion-5 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.emotion-6 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__markdown,
.emotion-6 .product-price--markdown {
  color: #666;
}

.emotion-6 .product-price__highlight,
.emotion-6 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-6 .product-price__highlight priceHighlightFont,
.emotion-6 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__highlight--br,
.emotion-6 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-6 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-6 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-6 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .product-price__sale {
  color: #666;
}

.emotion-6 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <li
    aria-label="Using price_type 2"
    aria-selected="false"
    class="emotion-0"
    data-testid="visual-item"
    id="suggestedOption2"
    role="option"
  >
    <a
      data-testid="tappable-area"
      href="http://localhost/browse/product.do?pid=853061002&vid=1&autosuggest=true&searchText=searchText&position=0&results=1"
    >
      <div
        class="product-card emotion-1"
      >
        <div
          class="product-card__image-wrapper emotion-2"
          id="product853061002"
        >
          <a
            aria-hidden="false"
            class="emotion-0"
            href="http://localhost/browse/product.do?pid=853061002&vid=1&autosuggest=true&searchText=searchText&position=0&results=1"
            target="_self"
          >
            <img
              alt=""
              class="product-card__image"
              src="https://www.stage.gaptechol.com/webcontent/0028/848/058/cn28848058.jpg"
            />
          </a>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            Using price_type 2
          </div>
          <div
            class="product-card-price"
          >
            <div
              class="emotion-6"
            >
              <div>
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $79.65
                    </span>
                  </div>
                  <div
                    aria-label="price.now_current_price"
                    class="product-price__highlight"
                    role="text"
                  >
                    $50.00
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a>
  </li>
</div>
`;

exports[`<AutosuggestVisualSearchItem /> should apply product-price__strike  class when price type is M 1`] = `
<div>
  <li
    aria-label="Using price_type 1"
    aria-selected="false"
    class="emotion-0"
    data-testid="visual-item"
    id="suggestedOption2"
    role="option"
  />
</div>
`;
