// @ts-nocheck
'use client';

import { BrandedFont, CSSObject, Theme } from '@ecom-next/core/react-stitch';
import { FONT_COLOR_UPDATE_RGB } from '@ecom-next/plp-ui/legacy/utils';

export function getDepartmentSuggestionStyles(
  isDesktop: boolean,
  theme: Theme,
  brandName: string,
  brandFont: BrandedFont,
  isFontColorUpdateEnabled: boolean,
  isBRWhiteBackgroundEnabled: boolean
): CSSObject {
  const isBananaRepublic = brandName === 'br' || brandName === 'brfs';

  const fontColorUpdateStyles = {
    color: FONT_COLOR_UPDATE_RGB,
  };

  const spacingStyles: CSSObject = {
    margin: `0 ${theme.spacing.small}`,
    padding: `${theme.spacing.small} 0`,
  };

  const headerStyles: CSSObject = {
    ...brandFont,
    ...spacingStyles,
    paddingTop: 0,
    paddingBottom: 0,
    marginTop: '8px',
    marginBottom: '8px',
    fontSize: '12px',
    ...(isFontColorUpdateEnabled ? fontColorUpdateStyles : { color: theme.color.g2 }),
  };

  const departmentSuggestionsStyles: CSSObject = {
    paddingTop: 0,
    paddingBottom: 0,
    margin: `0 ${isDesktop ? theme.spacing.small : 0}  4px ${theme.spacing.small}`,
  };

  const bananaRepublicStyles: CSSObject = {
    textTransform: 'uppercase',
    fontWeight: 'bold',
    border: 'none',
    backgroundColor: isBRWhiteBackgroundEnabled ? theme.color.wh : '#f6f4eb',
  };

  const desktopButtonStyles: CSSObject = {
    margin: '0 0.5rem 0.5rem 0',
    padding: '0.375rem 0.75rem',
    minHeight: '2.375rem',
  };

  const mobileButtonStyles: CSSObject = {
    display: 'inline-flex !important',
    padding: '8px 16px',
    width: 'fit-content',
  };

  const activeStyles = [
    {
      "&[aria-selected='true'], &:hover, &:focus": {
        textDecoration: 'underline',
        background: 'transparent',
      },
    },
  ];

  const composableButtonStyles: CSSObject = {
    background: 'transparent',
    ...brandFont,
    border: `1px solid ${isFontColorUpdateEnabled ? fontColorUpdateStyles.color : 'black'}`,
    fontWeight: 'normal',
    fontSize: '1rem',
    maxHeight: '35px',
    textTransform: 'capitalize',
    ...(isBananaRepublic ? bananaRepublicStyles : {}),
    ...(isDesktop ? desktopButtonStyles : mobileButtonStyles),
    ...(isFontColorUpdateEnabled ? fontColorUpdateStyles : { color: '#000000' }),
  };

  const tagLinkAnchorStyles: CSSObject = {
    ...brandFont,
    backgroundColor: 'transparent',
    color: isFontColorUpdateEnabled ? fontColorUpdateStyles.color : 'black',
    fontWeight: 'normal',
    fontSize: '1rem',
    textTransform: 'capitalize',
    borderRadius: '0',
    padding: '12px 8px',
    maxHeight: '16px',
    border: `1px solid ${isFontColorUpdateEnabled ? fontColorUpdateStyles.color : 'black'}`,
    ...(isBananaRepublic ? bananaRepublicStyles : {}),
  };

  const tagLinkUnorderedListStyles: CSSObject = {
    marginTop: '0',
  };

  const tagLinkListItemStyles: CSSObject = {
    marginBottom: '0',
  };

  return {
    composableButtonStyles,
    activeStyles,
    headerStyles,
    departmentSuggestionsStyles,
    tagLinkAnchorStyles,
    tagLinkUnorderedListStyles,
    tagLinkListItemStyles,
  };
}
