// @ts-nocheck
'use client'

import {
  CSSObject,
  Theme,
  getFontWeight,
  forBrands,
  Brands,
} from "@ecom-next/core/react-stitch";

const bananaRepublicStyles = (theme: Theme): CSSObject => ({
  color: theme.color.inverse.b1,
  textTransform: 'capitalize',
  fontWeight: getFontWeight('book').fontWeight,
});

const defaultTextStyles = (theme: Theme): CSSObject => ({
  textTransform: 'capitalize',
  fontStyle: 'normal',
  fontFamily: theme.brandFont.fontFamily,
  lineHeight: 'normal',
  textAlign: 'left',
});

const getGapTextStyles = (theme: Theme): CSSObject => ({
  ...defaultTextStyles(theme),
  color: theme.color.g1,
  fontSize: '16px',
  fontWeight: '400',
});

const getOldNavyTextStyles = (theme: Theme): CSSObject => ({
  ...defaultTextStyles(theme),
  color: theme.color.g1,
  fontSize: '16px',
  fontWeight: '350',
});

const getAthletaTextStyles = (theme: Theme): CSSObject => ({
  ...defaultTextStyles(theme),
  color: theme.color.bk,
  fontSize: '1.125rem',
  fontWeight: '400',
  letterSpacing: '0px',
  lineHeight: 'normal',
  outline: '1px black',

});

const getPLPRedesignTextStyles = (theme: Theme): CSSObject => ({
  ...(forBrands(theme, {
    gap: getGapTextStyles(theme),
    gapfs: getGapTextStyles(theme),
    on: getOldNavyTextStyles(theme),
    at: getAthletaTextStyles(theme),
  }) as CSSObject),
});

const getDefaultTextStyles = (theme: Theme): CSSObject => ({
  borderBottom: 'none',
  color: theme.color.b1,
  fontSize: theme.brand === Brands.Athleta ? '1.125rem' : '0.9rem',
  ...getFontWeight('bold'),
  textAlign: 'right',
  textTransform: 'uppercase',
});

const getTextStyles = (theme: Theme, isPLPRedesignEnabled: boolean): CSSObject => ({
  ...(isPLPRedesignEnabled ? getPLPRedesignTextStyles(theme) : getDefaultTextStyles(theme)),
  ...(forBrands(theme, {
    br: () => bananaRepublicStyles(theme),
    brfs: () => bananaRepublicStyles(theme),
  }) as CSSObject),
});

export default getTextStyles;
