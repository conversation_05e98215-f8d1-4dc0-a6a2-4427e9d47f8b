// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<SortBy/> > BR Redesign enabled > calls on change in an option control with dynamic label for BR 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > BR Redesign enabled > calls on change in an option control with dynamic label for BRFS 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign desktop state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1uai644-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1uai644-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8efoy4-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders BR Redesign mobile state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-8efoy4-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="sort"
          >
            sort
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > BR Redesign enabled > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > machtes snapshots with custom text as placeholder on desktop 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > With custom options > machtes snapshots with custom text as placeholder on mobile 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > With custom options > machtes snapshots with custom values as options on desktop 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > With custom options > machtes snapshots with custom values as options on mobile 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders desktop state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          Custom Text
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1uai644-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1uai644-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8aojr1-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-1io460j-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8efoy4-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > renders mobile state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          Custom Text
        </span>
        <select
          class="css-8efoy4-NativeDropdown"
          id="sortBySelect"
        >
          <option
            disabled=""
            selected=""
            value="Custom Text"
          >
            Custom Text
          </option>
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="new"
            value="new"
          >
            new
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
          <option
            aria-label="reviewScore"
            value="reviewScore"
          >
            reviewScore
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > With custom options > sort by options should not be Caps when customPlaceholder is passed as prop 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on desktop > renders with featured applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with featured as default value 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with featured as default value without label 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with new as default value 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with new as default value without label 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with op rated as default value 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: high-low applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: high-low as default value 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: high-low as default value without label 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: low-high applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: low-high as default value 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with price: low-high as default value without label 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with top rated applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on desktop > renders with top rated as default value without label 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders separate label state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > renders with featured applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with price: high-low applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with price: low–high applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with top rated and new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with top rated and new applied 2`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > renders with top rated applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label and new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label and price: high-low applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label and top rated applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders separate label state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders with separate label and featured applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in english > without text-align-last support > renders with separate label and price: low–high applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders separate label state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > renders with featured applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with price: high-low applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with price: low–high applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with top rated and new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with top rated and new applied 2`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > renders with top rated applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1ay1clw-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-1sqo87v"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1gdbn7u-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-iukufy"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-139d6gm"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-ib6idt-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-xpyzbn"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders default state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <div
        class="css-1r8xaah-DropdownBase"
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          class="css-j30cd5"
          data-toggle="true"
          type="button"
        >
          sort by
        </button>
        <ul
          class="css-1eao3a1"
          role="listbox"
          tabindex="-1"
        />
        <select
          aria-hidden="true"
          hidden=""
          name=""
        />
      </div>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label and new applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label and price: high-low applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label and top rated applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > Athleta 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > Athleta in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-131w1ny"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-14tvtrc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > BananaRepublic 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > BananaRepublic in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > BananaRepublicFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-me1vbu"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-1su3j5u-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > Gap 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > Gap in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > GapFactoryStore 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > GapFactoryStore in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-u4zwsw"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-16kt1xc-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > OldNavy 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders separate label state correctly > OldNavy in crossBrand 1`] = `
LoadedCheerio {
  "0": <div
    class="css-et9d0y-SortBy"
  >
    <div>
      <label
        class="css-122aikv"
      >
        <span
          class="css-alie39"
        >
          sort
        </span>
        <select
          class="css-g3nddl-NativeDropdown"
          id="sortBySelect"
        >
          <option
            aria-label="featured"
            value="featured"
          >
            featured
          </option>
          <option
            aria-label="price: low–high"
            value="price: low–high"
          >
            price: low–high
          </option>
          <option
            aria-label="price: high–low"
            value="price: high–low"
          >
            price: high–low
          </option>
        </select>
      </label>
    </div>
  </div>,
  "_root": LoadedCheerio {
    "0": Document {
      "children": [
        <html>
          <head />
          <body />
        </html>,
      ],
      "endIndex": null,
      "next": null,
      "parent": null,
      "prev": null,
      "startIndex": null,
      "type": "root",
      "x-mode": "quirks",
    },
    "_root": [Circular],
    "length": 1,
    "options": {
      "decodeEntities": true,
      "xml": false,
    },
  },
  "length": 1,
  "options": {
    "decodeEntities": true,
    "xml": false,
  },
}
`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders with separate label and featured applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > on mobile > in french > without text-align-last support > renders with separate label and price: low–high applied 1`] = `ReactWrapper {}`;

exports[`<SortBy/> > with br-colors-2023 > renders proper styles when br-colors-2023 is true 1`] = `ReactWrapper {}`;
