// @ts-nocheck
'use client';

import { useContext, useEffect, useState } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { useTheme } from '@ecom-next/core/react-stitch';
import ToggleSwitch from './components/ToggleSwitch';
import GridToggleSwitch from './components/GridToggleSwitch';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GridButtonsProps } from './types';
import { GridContainer, GridToggleWrapper } from './styles';
import { getBreakpoints } from './components/get-breakpoints';
import { useIsBrWhiteBackground, usePLPGridToggle, GridToggle, useBrRedesignSlice2 } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Brands } from '@ecom-next/core/legacy/utility';
import GridToggleDropdown from './components/GridToggleDropdown';
import GridButtonPlaceholder from './components/GridButtonPlaceholder';

export const client = new QueryClient({
  logger:
    process.env.NODE_ENV === 'test'
      ? {
          log: console.log,
          warn: console.warn,
          error: console.error,
        }
      : undefined,
});

export const GridButtons = (props: GridButtonsProps): JSX.Element | null => {
  const breakpointQuery = useContext(BreakpointContext);
  const { brandName } = useAppState();
  const breakpoints = getBreakpoints(breakpointQuery);
  const { smallerThan } = useContext(BreakpointContext);
  const isBRWhiteBackground = useIsBrWhiteBackground();
  const isBRRedesignSlice2 = useBrRedesignSlice2();
  const plpGridToggleExperiment = usePLPGridToggle();
  const isValidGridBreakpoint: boolean = smallerThan(XLARGE);
  const theme = useTheme();
  const isBRBrand: boolean = brandName === Brands.BananaRepublic || brandName === Brands.BananaRepublicFactoryStore;
  const isValidBrand = brandName === Brands.Gap || brandName === Brands.Athleta || brandName === Brands.GapFactoryStore;
  const isPlpGridToggleExperimentEnabled = plpGridToggleExperiment !== GridToggle.Off;
  const shouldShowGridToggleSwitch = isValidGridBreakpoint && isPlpGridToggleExperimentEnabled;
  const shouldShowGridToggleDropdown: boolean = isPlpGridToggleExperimentEnabled && breakpoints.isXLarge;
  const isMobile: boolean = breakpoints.isSmall;
  const [isLoading, setLoading] = useState(true);
  const placeholderWidth = {
    DESKTOP: 132,
    MOBILE: 80,
  };
  useEffect(() => {
    setLoading(false);
  }, []);

  if (isLoading && isValidBrand && isPlpGridToggleExperimentEnabled) {
    return <GridButtonPlaceholder key='grid-button-placeholder' placeholderWidth={breakpoints.isXLarge ? placeholderWidth.DESKTOP : placeholderWidth.MOBILE} />;
  }

  if (shouldShowGridToggleDropdown && isValidBrand) {
    return (
      <GridToggleWrapper theme={theme} data-testid='grid-toggle-container' shouldShowGridToggleDropdown={shouldShowGridToggleDropdown}>
        <GridToggleDropdown getGridToggleDataLayer={props.getGridToggleDataLayer} />
      </GridToggleWrapper>
    );
  }

  if (shouldShowGridToggleSwitch && isValidBrand) {
    return (
      <GridToggleWrapper theme={theme} data-testid='grid-toggle-container'>
        <GridToggleSwitch getGridToggleDataLayer={props.getGridToggleDataLayer} />
      </GridToggleWrapper>
    );
  }

  if (isBRBrand) {
    return (
      <GridContainer
        theme={theme}
        isBRWhiteBackground={isBRWhiteBackground}
        isBRRedesignSlice2={isBRRedesignSlice2}
        data-testid='grid-buttons-container'
        isMobile={isMobile}
      >
        <QueryClientProvider client={client}>
          <ToggleSwitch breakPoints={breakpoints} getGridToggleDataLayer={props.getGridToggleDataLayer} />
        </QueryClientProvider>
      </GridContainer>
    );
  }

  return null;
};
