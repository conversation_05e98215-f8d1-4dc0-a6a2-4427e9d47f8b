// @ts-nocheck
'use client';

import { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { GridButtonsProviderProps, UseGridButtons } from './types';
import { BRGrid } from '../../types';
import { GridToggle, usePLPGridToggle, useBrDefaultMobileGrid, DefaultMobileGrid } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { XLARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { MOBILE_GRID_OPTIONS, DESKTOP_GRID_OPTIONS } from '../../utils/constants';
import { storeGridSelection, getGridOptionFromStorage } from '../../helpers/store-grid-selection';

export const GridButtonsContext = createContext<UseGridButtons>({
  onGridChange: (_option: string, _alternateGrid: boolean, _source: string) => {},
  grid: BRGrid.LargeMobile,
  isAlternateGrid: false,
  plpGridValue: GridToggle.One,
  isAlternatePLPGrid: false,
  plpDropdownGridValue: GridToggle.Four,
});

export function GridButtonsProvider({ children }: GridButtonsProviderProps) {
  const [isAlternateGrid, setIsAlternateGrid] = useState<boolean>(false);
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const isBrDefault2Grid = useBrDefaultMobileGrid(isMobile) === DefaultMobileGrid.Two;
  const [grid, setValue] = useState<string>(isBrDefault2Grid ? BRGrid.LargeMobile : BRGrid.SmallMobile);
  const plpGridToggleExperiment = usePLPGridToggle();
  const [plpGridValue, setGridValue] = useState<string>(GridToggle.One);
  const [plpDropdownGridValue, setPLPDropdownGridValue] = useState<string>(GridToggle.One);
  const [isAlternatePLPGrid, setIsPLPAlternateGrid] = useState<boolean>(false);

  useEffect(() => {
    const storedGridOption = getGridOptionFromStorage();
    if (storedGridOption) {
      if (MOBILE_GRID_OPTIONS.includes(storedGridOption)) {
        setGridValue(storedGridOption);
        const isAlternateGrid = storedGridOption === GridToggle.Two;
        setIsPLPAlternateGrid(isAlternateGrid);
      } else if (DESKTOP_GRID_OPTIONS.includes(storedGridOption)) {
        setPLPDropdownGridValue(storedGridOption);
      }
    } else {
      setGridValue(plpGridToggleExperiment);
      setPLPDropdownGridValue(plpGridToggleExperiment);
      if (isMobile) {
        setIsPLPAlternateGrid(plpGridToggleExperiment === GridToggle.Two);
      }
    }
  }, [plpGridToggleExperiment]);

  const onGridChange = useCallback((gridOption: string, alternateGrid: boolean, source: string) => {
    if (source === 'br') {
      setValue(gridOption);
      setIsAlternateGrid(alternateGrid);
    } else if (source === 'dropdown') {
      setPLPDropdownGridValue(gridOption);
      storeGridSelection(gridOption);
    } else {
      setGridValue(gridOption);
      setIsPLPAlternateGrid(alternateGrid);
      storeGridSelection(gridOption);
    }
  }, []);

  return (
    <GridButtonsContext.Provider
      value={{
        grid,
        onGridChange,
        isAlternateGrid,
        plpGridValue,
        isAlternatePLPGrid,
        plpDropdownGridValue,
      }}
    >
      {children}
    </GridButtonsContext.Provider>
  );
}

const useGridButtons = (): UseGridButtons => {
  const context = useContext(GridButtonsContext);
  if (!context) {
    throw new Error('useGridButtons must be used within an GridButtonsProvider');
  }
  return context;
};

export default useGridButtons;
