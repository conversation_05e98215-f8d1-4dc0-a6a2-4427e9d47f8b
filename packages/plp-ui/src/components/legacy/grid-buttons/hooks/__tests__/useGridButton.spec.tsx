import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import useGridButtons, { GridButtonsProvider } from '../useGridButtons/useGridButtons';
import { GridToggle, usePLPGridToggle } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { getGridOptionFromStorage } from '../../helpers/store-grid-selection';

jest.mock('@ecom-next/plp-ui/legacy/plp-experiments', () => ({
  __esModule: true,
  ...jest.requireActual('@ecom-next/plp-ui/legacy/plp-experiments'),
  usePLPGridToggle: jest.fn(),
  useBrDefaultMobileGrid: jest.fn(),
}));

jest.mock('../../helpers/store-grid-selection', () => ({
  storeGridSelection: jest.fn(),
  getGridOptionFromStorage: jest.fn(),
}));

const mockBreakpointContext = {
  smallerThan: () => true,
};

describe('useGridButtons', () => {
  const wrapper =
    () =>
    // eslint-disable-next-line react/display-name
    ({ children }: { children: JSX.Element }) => (
      <BreakpointContext.Provider value={mockBreakpointContext}>
        <GridButtonsProvider>{children}</GridButtonsProvider>
      </BreakpointContext.Provider>
    );

  describe('isAlternatePLPGrid', () => {
    describe('When there is no stored grid selection', () => {
      it('should return true if plpGridToggleExperiment is 2 Across', () => {
        (usePLPGridToggle as jest.Mock).mockReturnValue(GridToggle.Two);
        const {
          result: { current },
        } = renderHook(() => useGridButtons(), {
          wrapper: wrapper(),
        });

        expect(current.isAlternatePLPGrid).toBeTrue();
      });
      it('should return false if plpGridToggleExperiment is 1 Across', () => {
        (usePLPGridToggle as jest.Mock).mockReturnValue(GridToggle.One);
        const {
          result: { current },
        } = renderHook(() => useGridButtons(), {
          wrapper: wrapper(),
        });

        expect(current.isAlternatePLPGrid).toBeFalse();
      });
    });
    describe('When stored grid selection exists', () => {
      it('should return true if stored grid selection is 2 Across irrespective of experiment', () => {
        (usePLPGridToggle as jest.Mock).mockReturnValue(GridToggle.One);
        (getGridOptionFromStorage as jest.Mock).mockReturnValue(GridToggle.Two);
        const {
          result: { current },
        } = renderHook(() => useGridButtons(), {
          wrapper: wrapper(),
        });

        expect(current.isAlternatePLPGrid).toBeTrue();
      });
      it('should return false if stored grid selection is 1 Across irrespective of experiment', () => {
        (usePLPGridToggle as jest.Mock).mockReturnValue(GridToggle.Two);
        (getGridOptionFromStorage as jest.Mock).mockReturnValue(GridToggle.One);
        const {
          result: { current },
        } = renderHook(() => useGridButtons(), {
          wrapper: wrapper(),
        });

        expect(current.isAlternatePLPGrid).toBeFalse();
      });
    });
  });
});
