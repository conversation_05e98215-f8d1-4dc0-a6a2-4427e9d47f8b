// @ts-nocheck
import { render, act } from 'test-utils';
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import LargeGridIconMobile from './LargeGridIconMobile';

describe('<LargeGridIconMobile />', () => {
  it('can be rendered', () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <LargeGridIconMobile/>
      </StitchStyleProvider>
    );

    const LargeGridIconsMobile = getByTestId('large-grid-icon-mobile')
    expect(LargeGridIconsMobile).toBeInTheDocument();
  });
});
