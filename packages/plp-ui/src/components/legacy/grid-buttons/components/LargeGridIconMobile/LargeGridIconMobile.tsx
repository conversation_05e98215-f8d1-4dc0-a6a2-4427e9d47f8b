// @ts-nocheck
'use client'

/* eslint-disable react/no-unknown-property */
import { useTheme } from "@ecom-next/core/react-stitch";
import { LargeGridIconProps } from "../../types";

const LargeGridIconMobile = ({
  width = "40",
  height = "40",
  isActive,
  isHovering,
  onClick,
}: LargeGridIconProps) => {
  const theme = useTheme();
  const fillColor = isActive || isHovering ? theme.color.bk : theme.color.gray40;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      onClick={onClick}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.75"
        y="0.25"
        data-testid="large-grid-icon-mobile"
        width="40"
        height="40"
        stroke={theme.color.bk}
        strokeWidth="0"
      />
      <rect x="21.5" y="18" width="3" height="3.00012" fill={fillColor} />
      <rect x="16.5" y="18" width="3" height="3.00012" fill={fillColor} />
      <rect x="16.5" y="23" width="3" height="3.00012" fill={fillColor} />
      <rect x="21.5" y="23" width="3" height="3.00012" fill={fillColor} />
    </svg>
  );
};

export default LargeGridIconMobile;
