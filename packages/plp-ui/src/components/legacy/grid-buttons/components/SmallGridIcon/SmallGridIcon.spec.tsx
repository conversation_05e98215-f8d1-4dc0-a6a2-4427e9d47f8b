// @ts-nocheck
import { render, act } from 'test-utils';
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import SmallGridIcon from './SmallGridIcon';
import { Mock, vi } from 'vitest';

describe('<SmallGridIcon />', () => {
  it('can be rendered', () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <SmallGridIcon />
      </StitchStyleProvider>
    );

    const SmallGridIcons = getByTestId('small-grid-icon')
    expect(SmallGridIcons).toBeInTheDocument();
  });

  it('should render 3 column grid for BRFS', () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.BananaRepublicFactoryStore}>
        <SmallGridIcon />
      </StitchStyleProvider>
    );

    const SmallGridIcons = getByTestId('small-grid-icon')
    expect(SmallGridIcons).toBeInTheDocument();
  });
});
