// @ts-nocheck
'use client'

/* eslint-disable react/no-unknown-property */
import { useTheme } from "@ecom-next/core/react-stitch";
import { SmallGridIconProps } from "../../types";

const SmallGridIcon = ({
  width = "56px",
  height = "56px",
  isActive,
  isHovering,
  onClick,
}: SmallGridIconProps) => {
  const theme = useTheme();
  const fillColor = isActive || isHovering ? theme.color.bk : theme.color.gray40;
  return (
        <svg
            data-testid="small-grid-icon"
            width={width}
            height={height}
            onClick={onClick}
            viewBox="0 0 56 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
          <rect
              x="0.25"
              y="0.25"
              width="55.5"
              height="55.5"
              stroke={theme.color.bk}
              strokeWidth="0"
          />
          <rect x="23.5" y="25" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="27.5" y="25" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="31.5" y="25" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="23.5" y="29" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="27.5" y="29" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="31.5" y="29" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="23.5" y="33" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="27.5" y="33" width="2.5" height="2.5" fill={fillColor}/>
          <rect x="31.5" y="33" width="2.5" height="2.5" fill={fillColor}/>
        </svg>
  );
};

export default SmallGridIcon;
