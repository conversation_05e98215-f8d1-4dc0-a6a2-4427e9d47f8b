// @ts-nocheck
import { useContext } from 'react';
import { Dropdown } from '@ecom-next/core/legacy/dropdown';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { DropdownOptions, GridToggleDropdownProps } from './types';
import { Brands, useTheme } from '@ecom-next/core/react-stitch';
import { gridToggleCommonStyles } from './styles';
import { useGridButtons } from '../..';
import { GridToggle, usePLPGapRedesign2024 } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { GRID_TOGGLE_THREE_COLUMNS, GRID_TOGGLE_FOUR_COLUMNS, GRID_TOGGLE_FIVE_COLUMNS, GRID_TOGGLE_SIX_COLUMNS } from './localization-tokens';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';

const GridToggleDropdown = ({ getGridToggleDataLayer }: GridToggleDropdownProps): React.ReactElement => {
  const theme = useTheme();
  const { featureVariables = {}, enabledFeatures } = useContext(FeatureFlagsContext);
  const { onGridChange, plpDropdownGridValue } = useGridButtons();
  const { smallerThan } = useContext(BreakpointContext);
  const native = smallerThan(XLARGE);
  const { localize } = useLocalize();
  const { locale } = useAppState();
  const isPLPGapRedesign2024 = usePLPGapRedesign2024();
  const gapNewFont = 'gap-redesign-2024';
  const isGapNewFontEnabled = enabledFeatures?.[gapNewFont];
  const shouldChangeFontWeightForGapRedesign2024 = isPLPGapRedesign2024 && isGapNewFontEnabled;

  const mapExperimentValueToTranslation: Record<string, string> = {
    '3 Across': GRID_TOGGLE_THREE_COLUMNS,
    '4 Across': GRID_TOGGLE_FOUR_COLUMNS,
    '5 Across': GRID_TOGGLE_FIVE_COLUMNS,
    '6 Across': GRID_TOGGLE_SIX_COLUMNS,
  };

  const mapValueToTranslation = (value: string) => `${localize(mapExperimentValueToTranslation[value] || mapExperimentValueToTranslation['4 Across'])}`;

  const mapTranslationToValueFR: Record<string, string> = {
    '3 photos': GridToggle.Three,
    '4 photos': GridToggle.Four,
    '5 photos': GridToggle.Five,
    '6 photos': GridToggle.Six,
  };

  const handleDataLayer = (event_name?: string, grid_layout_type?: string) => {
    if (getGridToggleDataLayer) getGridToggleDataLayer(event_name, grid_layout_type);
  };

  /* fallback options */
  const options = [
    {
      value: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
    },
    {
      value: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
    },
    {
      value: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
    },
    {
      value: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
    },
  ];

  const translationFromStoredOption = options.find(option => option.value === mapValueToTranslation(plpDropdownGridValue))?.label;

  const gridOptionLabel = translationFromStoredOption;

  const mapToOptions: Record<string, DropdownOptions> = {
    '3': {
      value: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_THREE_COLUMNS)}`,
    },
    '4': {
      value: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_FOUR_COLUMNS)}`,
    },
    '5': {
      value: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_FIVE_COLUMNS)}`,
    },
    '6': {
      value: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
      ariaLabel: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
      label: `${localize(GRID_TOGGLE_SIX_COLUMNS)}`,
    },
  };

  /* maps the return of the feature variable to a valid option for the dropdown */
  const mapFeatureVariablesToOptions = (gridToggleFeatureVariables: string[]) => {
    const minNumbersPerRow = parseInt(gridToggleFeatureVariables[0]);
    const maxNumbersPerRow = parseInt(gridToggleFeatureVariables[1]);
    /* return to fallback in case of unexpected values */
    if (!minNumbersPerRow || !maxNumbersPerRow || minNumbersPerRow < 3 || maxNumbersPerRow > 6) {
      return options;
    }

    const featureVariablesArray = [];
    for (let i = minNumbersPerRow; i <= maxNumbersPerRow; i++) {
      featureVariablesArray.push(i.toString());
    }
    return featureVariablesArray.map((columns: string) => mapToOptions[columns]);
  };

  const getOptions = (abbrBrand: string, featureVariables: Record<string, Record<string, boolean | string>>): DropdownOptions[] | [] => {
    if (!featureVariables['plp-grid-toggle']) {
      return [];
    }

    const {
      ['plp-grid-toggle']: { gapGridToggle, atGridToggle },
    } = featureVariables;
    const gapGridToggleOptions = String(gapGridToggle).split('-');
    const atGridToggleOptions = String(atGridToggle).split('-');

    if (abbrBrand === Brands.Gap || abbrBrand === Brands.GapFactoryStore) {
      return mapFeatureVariablesToOptions(gapGridToggleOptions);
    }

    if (abbrBrand === Brands.Athleta) {
      return mapFeatureVariablesToOptions(atGridToggleOptions);
    }

    return options;
  };

  return (
    <Dropdown
      options={getOptions(theme.brand, featureVariables)}
      noPlaceholder
      onFocus={event => {
        if (event.target.ariaExpanded === 'false') {
          handleDataLayer('grid-layout-click', '');
        }
      }}
      onChange={option => {
        const optionValue = typeof option !== 'string' ? (option as { value: string }).value : option;
        onGridChange(`${locale === 'fr_CA' ? mapTranslationToValueFR[optionValue] : optionValue}`, false, 'dropdown');
        handleDataLayer('grid-layout-select', optionValue);
        return option;
      }}
      value={gridOptionLabel}
      styles={gridToggleCommonStyles(theme, shouldChangeFontWeightForGapRedesign2024)}
      native={native}
    />
  );
};

export default GridToggleDropdown;
