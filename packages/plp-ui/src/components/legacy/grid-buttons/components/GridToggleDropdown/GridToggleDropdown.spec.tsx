// @ts-nocheck
import { useTheme, forBrands } from "@ecom-next/core/react-stitch";
import { FeatureFlagsContext } from "@ecom-next/core/legacy/feature-flags";

import { render, screen, fireEvent, act } from "test-utils";
import { <PERSON><PERSON> } from "@ecom-next/core/react-stitch";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { useLocalize } from "@ecom-next/sitewide/localization-provider";
import { GridToggle, usePLPGapRedesign2024 } from "@ecom-next/plp-ui/legacy/plp-experiments";
import GridToggleDropdown from "./GridToggleDropdown";
import { GridButtonsContext } from "@ecom-next/plp-ui/legacy/grid-buttons";

jest.mock("@ecom-next/core/react-stitch");
jest.mock("@ecom-next/core/legacy/localization-provider");
const mockedOptions: Record<string, string> = {
  "grid_toggle_dropdown.three_columns": "3 Across",
  "grid_toggle_dropdown.four_columns": "4 Across",
  "grid_toggle_dropdown.five_columns": "5 Across",
  "grid_toggle_dropdown.six_columns": "6 Across",
};
jest.mock("@ecom-next/plp-ui/legacy/plp-experiments");
const mockDropdownOptionsTranslation = (key: string) => mockedOptions[key];
const onChangeSpy = jest.fn();
const gridButtonContextMock = {
  onGridChange: onChangeSpy,
  grid: "",
  isAlternateGrid: false,
  plpGridValue: "",
  isAlternatePLPGrid: false,
  plpDropdownGridValue: "",
};

const mockedPLPGapRedesign2024 = usePLPGapRedesign2024 as Mock;

describe("Grid Toggle Dropdown", () => {
  beforeEach(() => {
    (useTheme as Mock).mockReturnValue({
      brand: Brands.Gap,
      color: {},
      brandFont: {},
    });
    (forBrands as Mock).mockReturnValue({
      toggleIcon: "",
      arrow: "",
      fontSizeMenu: "",
      fontFamilyMenu: "",
      circleIcon: "",
      fontSizeOption: "",
      fontFamilyOption: "",
    });
    (useLocalize as Mock).mockReturnValue({
      localize: mockDropdownOptionsTranslation,
    });
  });
  const renderGridToggleDropdown = (
    experimentValue: string = GridToggle.Three,
    featureVariablesValue: string = "3-6",
    isPlpGridToggleInFeatureVariablesSchema = true,
    plpDropdownGridValue = ""
  ) =>
    render(
      <FeatureFlagsContext.Provider
        value={{
          enabledFeatures: {
            "plp-grid-toggle": true,
            "gap-redesign-2024": true,
          },
          featureVariables: {
            ...(isPlpGridToggleInFeatureVariablesSchema && {
              "plp-grid-toggle": {
                gapGridToggle: featureVariablesValue,
                atGridToggle: featureVariablesValue,
              },
            }),
          },
        }}
      >
        <BreakpointContext.Provider
          value={{
            size: XLARGE,
            orientation: "portrait",
            media: "(min-width: 1024px) and (min-aspect-ratio: 1/1)",
            minWidth: () => true,
            greaterOrEqualTo: () => true,
            maxWidth: () => false,
            smallerThan: () => false,
          }}
        >
          <GridButtonsContext.Provider
            value={{ ...gridButtonContextMock, plpDropdownGridValue }}
          >
            <GridToggleDropdown experimentValue={experimentValue} />
          </GridButtonsContext.Provider>
        </BreakpointContext.Provider>
      </FeatureFlagsContext.Provider>
    );

  const checkAllDropDownOptions = () => {
    expect(screen.getAllByRole("option")).toHaveLength(4);
    expect(
      screen.getByRole("option", { name: GridToggle.Three })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: GridToggle.Four })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: GridToggle.Five })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: GridToggle.Six })
    ).toBeInTheDocument();
  };

  it("should render all the options for GAP", async () => {
    renderGridToggleDropdown(GridToggle.Three, "3-6");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    checkAllDropDownOptions();
  });

  it("should not render 6 Across Option for Athleta", async () => {
    (useTheme as Mock).mockReturnValue({
      brand: Brands.Athleta,
      color: {},
      brandFont: {},
    });
    renderGridToggleDropdown(GridToggle.Three, "3-5");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(screen.getAllByRole("option")).toHaveLength(3);
    expect(
      screen.getByRole("option", { name: GridToggle.Three })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: GridToggle.Four })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("option", { name: GridToggle.Five })
    ).toBeInTheDocument();
    expect(
      screen.queryByRole("option", { name: GridToggle.Six })
    ).not.toBeInTheDocument();
  });

  it("should return fallback options if number of colums is greater than 6", async () => {
    renderGridToggleDropdown(GridToggle.Three, "3-50");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    checkAllDropDownOptions();
  });

  it("should return fallback options if number of colums is less than 3", async () => {
    renderGridToggleDropdown(GridToggle.Three, "2-6");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    checkAllDropDownOptions();
  });

  it("should return the fallback options if there are no feature variables in plp-grid-toggle", async () => {
    renderGridToggleDropdown(GridToggle.Three, undefined);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    checkAllDropDownOptions();
  });

  it("should return No options found if there are no plp-grid-toggle", async () => {
    renderGridToggleDropdown(GridToggle.Three, undefined, false);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(screen.getAllByRole("option")).toHaveLength(1);
    expect(
      screen.getByRole("option", { name: "No options found" })
    ).toBeInTheDocument();
  });

  it("should return 3 Across as the default value when experiment value is a", async () => {
    renderGridToggleDropdown();
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "3 Across", selected: true })
    ).toBeInTheDocument();
  });

  it("should return 4 Across as the default value when experiment value is x", async () => {
    renderGridToggleDropdown(GridToggle.Four);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "4 Across", selected: true })
    ).toBeInTheDocument();
  });

  it("should return 5 Across as the default value when experiment value is b", async () => {
    renderGridToggleDropdown(GridToggle.Five);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "5 Across", selected: true })
    ).toBeInTheDocument();
  });

  it("should return 6 Across as the default value when experiment value is c", async () => {
    renderGridToggleDropdown(GridToggle.Six);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "6 Across", selected: true })
    ).toBeInTheDocument();
  });

  it("should change to '3 Across' when user clicks on it", async () => {
    renderGridToggleDropdown(GridToggle.Six, "3-6");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    const optionBtn = screen.getByLabelText("3 Across");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(optionBtn); 

      }) 

     })
    expect(onChangeSpy).toHaveBeenCalledWith("3 Across", false, "dropdown");
  });

  it("should change to '5 Across' when user clicks on it", async () => {
    renderGridToggleDropdown(GridToggle.Three, "3-6");
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    const optionBtn = screen.getByLabelText("5 Across");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(optionBtn); 

      }) 

     })
    expect(onChangeSpy).toHaveBeenCalledWith("5 Across", false, "dropdown");
  });

  it("should get the value from storage and not from the experimentValue on the first render", async () => {
    renderGridToggleDropdown(
      GridToggle.Four,
      undefined,
      undefined,
      GridToggle.Five
    );
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "5 Across", selected: true })
    ).toBeInTheDocument();
  });

  it("should get the value from the experimentValue when the storage value is null", async () => {
    renderGridToggleDropdown(GridToggle.Three);
    const dropdownButton = screen.getByRole("button");
    await act(async () => { 

     await act(async () => { 

      fireEvent.click(dropdownButton); 

      }) 

     })
    expect(
      screen.getByRole("option", { name: "3 Across", selected: true })
    ).toBeInTheDocument();
  });

  describe("Gap Redesign 2024", () => {
    it("should have font-weight 500 if plp gap redesign is enabled", async () => {
      mockedPLPGapRedesign2024.mockReturnValue(true);
      renderGridToggleDropdown(GridToggle.Three);
      const dropdownButton = screen.getByRole("button");
      expect(dropdownButton).toHaveStyle("font-weight : 500");
    });

    it("should have font-weight 400 if plp gap redesign is disabled", async () => {
      mockedPLPGapRedesign2024.mockReturnValue(false);
      renderGridToggleDropdown(GridToggle.Three);
      const dropdownButton = screen.getByRole("button");
      expect(dropdownButton).toHaveStyle("font-weight : 400");
    });
  });
});
