// @ts-nocheck
'use client'

import { DropdownCommonStyles } from "./types";
import { Brands, Theme, forBrands, CSSObject } from "@ecom-next/core/react-stitch";

export const gridToggleCommonStyles = (
  theme: Theme,
  shouldChangeFontWeightForGapRedesign2024: boolean
): Record<string, object> => {
  const brandStylesMenu = forBrands(theme, {
    gap: () => ({
      toggleIcon: "2B2B2B",
      arrow: "2B2B2B",
      fontSizeMenu: "1rem",
      fontFamilyMenu: `${theme.brandFont.fontFamily}`,
      circleIcon: "666",
      fontSizeOption: "1rem",
      fontFamilyOption: `${theme.brandFont.fontFamily}`,
      border: `1px solid ${theme.color.gray20}`,
      borderBottom: `1px solid ${theme.color.gray20}`,
      color: `${theme.color.gray80}`,
    }),
    gapfs: () => ({
      toggleIcon: "2B2B2B",
      arrow: "2B2B2B",
      fontSizeMenu: "1rem",
      fontFamilyMenu: `${theme.brandFont.fontFamily}`,
      circleIcon: "666",
      fontSizeOption: "1rem",
      fontFamilyOption: `${theme.brandFont.fontFamily}`,
      border: `1px solid ${theme.color.gray20}`,
      borderBottom: `1px solid ${theme.color.gray20}`,
      color: `${theme.color.gray80}`,
    }),
    at: () => ({
      toggleIcon: "333",
      arrow: "333",
      fontSizeMenu: "0.94rem",
      fontFamilyMenu: `${theme.brandFont.fontFamily}`,
      circleIcon: "333",
      fontSizeOption: "0.94rem",
      fontFamilyOption: `${theme.brandFont.fontFamily}`,
      border: `1px solid ${theme.color.gray80}`,
      borderBottom: `1px solid ${theme.color.gray80}`,
      color: `${theme.color.bk}`,
    }),
    default: () => ({
      toggleIcon: "2B2B2B",
      arrow: "2B2B2B",
      fontSizeMenu: "1rem",
      fontFamilyMenu: `${theme.brandFont.fontFamily}`,
      circleIcon: "666",
      fontSizeOption: "1rem",
      fontFamilyOption: `${theme.brandFont.fontFamily}`,
      border: `1px solid ${theme.color.gray20}`,
      borderBottom: `1px solid ${theme.color.gray20}`,
      color: `${theme.color.gray80}`,
    }),
  }) as CSSObject;

  return {
    Control: {
      color: `${brandStylesMenu.color}`,
      backgroundColor: `${theme.color.wh}`,
      lineHeight: "20px",
      fontWeight: "400",
      fontFamily: `${brandStylesMenu.fontFamilyMenu}`,
      fontSize: `${brandStylesMenu.fontSizeMenu}`,
      padding: "0 1.5rem 0 2.2rem",
      height: "2.5rem",
      border: `${brandStylesMenu.border}`,
      borderBottom: `${brandStylesMenu.borderBottom}`,
      "&:before": {
        left: "10px",
        top: "50%",
        marginTop: "-8px",
        background: `url('data:image/svg+xml,<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 1H6.5V6H1.5V1ZM0.5 0H1.5H6.5H7.5V1V6V7H6.5H1.5H0.5V6V1V0ZM10.5 1H15.5V6H10.5V1ZM9.5 0H10.5H15.5H16.5V1V6V7H15.5H10.5H9.5V6V1V0ZM15.5 10H10.5V15H15.5V10ZM10.5 9H9.5V10V15V16H10.5H15.5H16.5V15V10V9H15.5H10.5ZM1.5 10H6.5V15H1.5V10ZM0.5 9H1.5H6.5H7.5V10V15V16H6.5H1.5H0.5V15V10V9Z" fill="%23${brandStylesMenu.toggleIcon}"/></svg>') no-repeat`,
      },
      "&:hover": {
        animation: "none !important",
        backgroundColor: `${theme.color.wh}`,
      },
      "&:focus": {
        animation: "none !important",
      },
      position: "relative",
      '&[aria-expanded="false"]:after': {
        content: '""',
        background: `url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23${brandStylesMenu.arrow};fill-rule:evenodd' /%3E%3C/svg%3E")
  8px 16px no-repeat
  `,
        width: "1.5rem",
        height: "2rem",
        display: "block",
        position: "absolute",
        right: "1px",
        top: "1px",
        backgroundPosition: "8px 16px",
      },
      '&[aria-expanded="true"]:after': {
        content: '""',
        background: `url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23${brandStylesMenu.arrow};fill-rule:evenodd' /%3E%3C/svg%3E")
  8px 16px no-repeat
  `,
        width: "1.5rem",
        height: "2rem",
        display: "block",
        position: "absolute",
        right: "1px",
        top: "7px",
        backgroundPosition: "8px 16px",
        transform: "rotate(180deg)",
      },
      ...(shouldChangeFontWeightForGapRedesign2024 && {
        fontWeight: "500",
      }),
    },
    Menu: {
      boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.3)",
      overflowY: "hidden",
      height: "auto",
      maxHeight: "200px",
      position: "absolute",
      width: "auto",
      zIndex: "10",
      transition: "max-height 0.25s ease-in-out",
      backgroundColor: `${theme.color.wh}`,
      "&:focus": {
        outline: "none",
      },
      right: 0,
      whiteSpace: "nowrap",
      padding: "0 6px",
    },
    Option: {
      display: "flex",
      color: "#2B2B2B",
      fontSize: `${brandStylesMenu.fontSizeOption}`,
      fontFamily: `${brandStylesMenu.fontFamilyOption}`,
      padding: "0.5rem",
      cursor: "pointer",
      "&:before": {
        content: '""',
        height: "22px",
        marginRight: "12px",
        width: "21px",
      },
      '&[aria-selected="true"]:before': {
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='21' height='22' viewBox='0 0 21 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='10.5' cy='10.625' r='10' stroke='%23${brandStylesMenu.circleIcon}'/%3E%3Ccircle cx='10.5014' cy='10.6264' r='5.26316' fill='%23333333'/%3E%3C/svg%3E")`,
      },
      '&[aria-selected="false"]:before': {
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='21' height='22' viewBox='0 0 21 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='10.5' cy='10.625' r='10' stroke='%23${brandStylesMenu.circleIcon}'/%3E%3C/svg%3E")`,
      },
      backgroundColor: `${theme.color.wh}`,
      "&:hover": {
        backgroundColor: `${theme.color.wh}`,
      },
      alignItems: "center",
      justifyContent: "left",
      ":first-child": {
        paddingTop: "16px",
      },
      ":last-child": {
        paddingBottom: "16px",
      },
      ...(shouldChangeFontWeightForGapRedesign2024 && {
        fontWeight: "500",
      }),
    },
  };
};
