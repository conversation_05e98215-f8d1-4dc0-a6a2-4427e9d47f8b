// @ts-nocheck
'use client'

/* eslint-disable react/no-unknown-property */
import { useTheme } from "@ecom-next/core/react-stitch";
import { SmallGridIconProps } from "../../types";

const SmallGridIconMobile = ({
  width = "40",
  height = "40",
  isActive,
  isHovering,
  onClick,
}: SmallGridIconProps) => {
  const theme = useTheme();
  const fillColor = isActive || isHovering ? theme.color.bk : theme.color.gray40;

  return (
    <svg
      width={width}
      data-testid="small-grid-icon-mobile"
      height={height}
      viewBox="0 0 40 40"
      onClick={onClick}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.20"
        y="0.20"
        width="40"
        height="40"
        stroke={theme.color.bk}
        strokeWidth="0"
      />
      <rect x="16" y="18" width="8" height="8.00032" fill={fillColor} />
    </svg>
  );
};

export default SmallGridIconMobile;
