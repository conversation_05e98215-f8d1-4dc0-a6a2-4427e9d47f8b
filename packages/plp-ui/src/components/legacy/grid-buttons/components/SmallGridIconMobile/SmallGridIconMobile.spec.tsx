// @ts-nocheck
import { render, act } from 'test-utils';
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import SmallGridIconMobile from './SmallGridIconMobile';

describe('<SmallGridIconMobile />', () => {
  it('can be rendered', () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <SmallGridIconMobile/>
      </StitchStyleProvider>
    );

    const SmallGridIcons = getByTestId('small-grid-icon-mobile')
    expect(SmallGridIcons).toBeInTheDocument();
  });
});
