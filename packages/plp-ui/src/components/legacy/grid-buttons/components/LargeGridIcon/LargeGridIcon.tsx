// @ts-nocheck
'use client'

/* eslint-disable react/no-unknown-property */

import { useTheme } from "@ecom-next/core/react-stitch";
import { LargeGridIconProps } from "../../types";

const LargeGridIcon = ({
  width = "56px",
  height = "56px",
  isActive,
  isHovering,
  onClick,
}: LargeGridIconProps) => {
  const theme = useTheme();
  const fillColor = isActive || isHovering ? theme.color.bk : theme.color.gray40;

  return (
    <svg
      data-testid="large-grid-icon"
      width={width}
      height={height}
      onClick={onClick}
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.25"
        y="0.25"
        width="55.5"
        height="55.5"
        stroke={theme.color.bk}
        strokeWidth="0"
      />
      <rect x="24.5713" y="24" width="1" height="1" fill={fillColor} />
      <rect x="22" y="24" width="1" height="1" fill={fillColor} />
      <rect x="29.7141" y="24" width="1" height="1" fill={fillColor} />
      <rect x="32.2856" y="24" width="1" height="1" fill={fillColor} />
      <rect x="27.1428" y="24" width="1" height="1" fill={fillColor} />
      <rect x="24.5713" y="26.5723" width="1" height="1" fill={fillColor} />
      <rect x="22" y="26.5723" width="1" height="1" fill={fillColor} />
      <rect x="29.7141" y="26.5723" width="1" height="1" fill={fillColor} />
      <rect x="32.2856" y="26.5723" width="1" height="1" fill={fillColor} />
      <rect x="27.1428" y="26.5723" width="1" height="1" fill={fillColor} />
      <rect x="24.5713" y="29.1426" width="1" height="1" fill={fillColor} />
      <rect x="22" y="29.1426" width="1" height="1" fill={fillColor} />
      <rect x="29.7141" y="29.1426" width="1" height="1" fill={fillColor} />
      <rect x="32.2856" y="29.1426" width="1" height="1" fill={fillColor} />
      <rect x="27.1428" y="29.1426" width="1" height="1" fill={fillColor} />
      <rect x="24.5713" y="31.7148" width="1" height="1" fill={fillColor} />
      <rect x="22" y="31.7148" width="1" height="1" fill={fillColor} />
      <rect x="29.7141" y="31.7148" width="1" height="1" fill={fillColor} />
      <rect x="32.2856" y="31.7148" width="1" height="1" fill={fillColor} />
      <rect x="27.1428" y="31.7148" width="1" height="1" fill={fillColor} />
      <rect x="24.5713" y="34.2852" width="1" height="1" fill={fillColor} />
      <rect x="22" y="34.2852" width="1" height="1" fill={fillColor} />
      <rect x="29.7141" y="34.2852" width="1" height="1" fill={fillColor} />
      <rect x="32.2856" y="34.2852" width="1" height="1" fill={fillColor} />
      <rect x="27.1428" y="34.2852" width="1" height="1" fill={fillColor} />
    </svg>
  );
};

export default LargeGridIcon;
