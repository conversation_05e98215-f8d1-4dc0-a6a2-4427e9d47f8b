// @ts-nocheck
import { render, act } from 'test-utils';
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import LargeGridIcon from './LargeGridIcon';

describe('<LargeGridIcon />', () => {
  it('rendered sucessfully', () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <LargeGridIcon/>
      </StitchStyleProvider>
    );

    const LargeGridIcons = getByTestId('large-grid-icon')
    expect(LargeGridIcons).toBeInTheDocument();
  });
});
