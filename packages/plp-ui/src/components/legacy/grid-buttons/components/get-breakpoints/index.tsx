// @ts-nocheck
'use client'

import {
  MEDIUM,
  LARGE,
  XLARGE,
  BreakpointProviderState,
} from "@ecom-next/core/breakpoint-provider";

export type BreakPoints = {
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  isXLarge: boolean;
};

export const getBreakpoints = (
  breakpointQuery: BreakpointProviderState
): BreakPoints => {
  const { minWidth, maxWidth, greaterOrEqualTo } = breakpointQuery;
  const isSmall = maxWidth(MEDIUM);
  const isMedium = minWidth(MEDIUM) && maxWidth(LARGE);
  const isLarge = greaterOrEqualTo(LARGE);
  const isXLarge = minWidth(XLARGE);

  return {
    isSmall,
    isMedium,
    isLarge,
    isXLarge,
  };
};
