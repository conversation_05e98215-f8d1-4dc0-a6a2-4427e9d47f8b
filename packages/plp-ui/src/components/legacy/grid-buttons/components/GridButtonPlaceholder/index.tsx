// @ts-nocheck
'use client'

import { LoadingPlaceholder } from '@ecom-next/core/legacy/loading-placeholder';

const GridButtonPlaceholder = ({
  placeholderWidth = 80,
}) => (
  <div
    data-testid="grid-button-placeholder"
    key="grid-button-placeholder"
    style={{ display: 'flex', justifyContent:'flex-end', width: '100%' }}
  >
    <LoadingPlaceholder
      fixedSize={{ width: placeholderWidth, height: 40 }}
    />
  </div>
);
export default GridButtonPlaceholder;
