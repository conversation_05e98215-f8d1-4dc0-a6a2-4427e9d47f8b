// @ts-nocheck
'use client'

import { Theme, CSSObject, forBrands } from "@ecom-next/core/react-stitch";


const containerStylesForBananRepublic = (isMobile: boolean): CSSObject => ({
    minHeight: isMobile ? '2.75rem' : '3.75rem',
  });
  
  export const toggleContainerStyles = (theme: Theme, isMobile:boolean): CSSObject => ({
    ...(forBrands(theme, {
      br: containerStylesForBananRepublic(isMobile),
      brfs: containerStylesForBananRepublic(isMobile),
    }) as CSSObject),
  });