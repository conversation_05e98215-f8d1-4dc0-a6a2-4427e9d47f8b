// @ts-nocheck
'use client'

import { useState } from "react";
import { useTheme } from "@ecom-next/core/react-stitch";
import {
  useIsBrWhiteBackground,
  useBrDefaultMobileGrid,
  DefaultMobileGrid,
} from "@ecom-next/plp-ui/legacy/plp-experiments";
import SmallGridIcon from "../SmallGridIcon";
import LargeGridIcon from "../LargeGridIcon";
import SmallGridIconMobile from "../SmallGridIconMobile";
import LargeGridIconMobile from "../LargeGridIconMobile";
import { BRGrid, GridButtonName, ToggleSwitchProps } from "../../types";
import useGridButtons from "../../hooks/useGridButtons/useGridButtons";
import "./styled/index";
import { toggleContainerStyles } from "./styled/toggleContainerStyles";
import { Brands } from "@ecom-next/core/legacy/utility";

const ToggleSwitch = (props: ToggleSwitchProps) => {
  const theme = useTheme();
  const { breakPoints, getGridToggleDataLayer } = props;
  const breakpoints = breakPoints;
  const isBrDefault2Grid =
    useBrDefaultMobileGrid(breakpoints.isSmall) === DefaultMobileGrid.Two;
  const isBRWhiteBackground = useIsBrWhiteBackground();
  const { onGridChange, isAlternateGrid } = useGridButtons();
  const isAltGridMobile = isBrDefault2Grid ? isAlternateGrid : !isAlternateGrid;
  const [hover, setHover] = useState<BRGrid | null>(null);
  const [select, setSelect] = useState(
    isBRWhiteBackground
      ? breakpoints.isSmall && isBrDefault2Grid
        ? GridButtonName.Switch
        : GridButtonName.Inner
      : ""
  );

  const onButtonClick = (
    gridOption: BRGrid,
    alternateGrid: boolean,
    buttonName: GridButtonName
  ) => {
    if (isBRWhiteBackground) {
      if (buttonName === GridButtonName.Inner) {
        setSelect(GridButtonName.Inner);
      }
      if (buttonName === GridButtonName.Switch) {
        setSelect(GridButtonName.Switch);
      }
    }
    onGridChange(gridOption, alternateGrid, Brands.BananaRepublic);
    setHover(null);
    if (getGridToggleDataLayer) getGridToggleDataLayer();
  };

  return (
    <div className="grid-toggle-container" css= {toggleContainerStyles(theme, breakPoints.isSmall)}>
      <div
        className={select === GridButtonName.Inner ? "inner-selected" : "inner"}
        onMouseEnter={() =>
          setHover(
            breakPoints.isSmall ? BRGrid.SmallMobile : BRGrid.SmallDesktop
          )
        }
        onMouseLeave={() => setHover(null)}
      >
        {breakpoints.isSmall ? (
          <SmallGridIconMobile
            isActive={isAltGridMobile}
            onClick={() =>
              onButtonClick(
                BRGrid.SmallMobile,
                isBrDefault2Grid,
                GridButtonName.Inner
              )
            }
          />
        ) : (
          <SmallGridIcon
            isActive={!isAlternateGrid}
            isHovering={hover === BRGrid.SmallDesktop}
            onClick={() =>
              onButtonClick(BRGrid.SmallDesktop, false, GridButtonName.Inner)
            }
          />
        )}
      </div>
      <div
        className={
          select === GridButtonName.Switch ? "switch-selected" : "switch"
        }
        onMouseEnter={() =>
          setHover(
            breakPoints.isSmall ? BRGrid.LargeMobile : BRGrid.LargeDesktop
          )
        }
        onMouseLeave={() => setHover(null)}
      >
        {breakpoints.isSmall ? (
          <LargeGridIconMobile
            isActive={!isAltGridMobile}
            onClick={() =>
              onButtonClick(
                BRGrid.LargeMobile,
                !isBrDefault2Grid,
                GridButtonName.Switch
              )
            }
          />
        ) : (
          <LargeGridIcon
            isActive={isAlternateGrid}
            isHovering={hover === BRGrid.LargeDesktop}
            onClick={() =>
              onButtonClick(BRGrid.LargeDesktop, true, GridButtonName.Switch)
            }
          />
        )}
      </div>
    </div>
  );
};

export default ToggleSwitch;
