// @ts-nocheck
import { render, act } from "test-utils";
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import GridToggleSmallIcon from "./GridToggleSmallIcon";

describe("<GridToggleSmallIcon />", () => {
  it("rendered sucessfully", () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <GridToggleSmallIcon />
      </StitchStyleProvider>
    );

    const SmallGridIcons = getByTestId("Grid-toggle-small-icon");
    expect(SmallGridIcons).toBeInTheDocument();
  });
});
