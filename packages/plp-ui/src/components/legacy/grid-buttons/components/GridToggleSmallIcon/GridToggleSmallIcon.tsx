// @ts-nocheck
'use client'

import { useTheme } from "@ecom-next/core/react-stitch";
import { GridToggleSmallIconProps } from "../../types";

const GridToggleSmallIcon = ({
  width = "40",
  height = "40",
  isActive,
  onClick,
}: GridToggleSmallIconProps) => {
  const theme = useTheme();
  const fillColor = isActive ? theme.color.bk : theme.color.gray40;

  const renderGridIcon = () => {
    if (isActive) {
      return (
        <svg
          width={width}
          data-testid="Grid-toggle-small-icon"
          onClick={onClick}
          height={height}
          viewBox="0 0 41 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask id="path-1-inside-1_5053_13167" fill="white">
            <path d="M0.5 0H40.5V40H0.5V0Z" />
          </mask>
          <path d="M0.5 0H40.5V40H0.5V0Z" fill="white" />
          <path
            d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
            fill={theme.color.g4}
            mask="url(#path-1-inside-1_5053_13167)"
          />
          <rect
            width="16"
            height="16"
            transform="translate(12.5 12)"
            fill={fillColor}
          />
          <rect x="13" y="12.5" width="15" height="15" stroke={fillColor} />
        </svg>
      );
    } else {
      return (
        <svg
          width="40"
          height="40"
          data-testid="Grid-toggle-small-icon"
          onClick={onClick}
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask id="path-1-inside-1_5332_26027" fill="white">
            <path d="M0 0H40V40H0V0Z" />
          </mask>
          <path d="M0 0H40V40H0V0Z" fill="white" />
          <path
            d="M0 0V-1H-1V0H0ZM0 40H-1V41H0V40ZM0 1H40V-1H0V1ZM40 39H0V41H40V39ZM1 40V0H-1V40H1Z"
            fill={theme.color.g5}
            mask="url(#path-1-inside-1_5332_26027)"
          />
          <rect x="12.5" y="12.5" width="15" height="15" stroke={theme.color.g4} />
        </svg>
      );
    }
  };

  return renderGridIcon();
};

export default GridToggleSmallIcon;
