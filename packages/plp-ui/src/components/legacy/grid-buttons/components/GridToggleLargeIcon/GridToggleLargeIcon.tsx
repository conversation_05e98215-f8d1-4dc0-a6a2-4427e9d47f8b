// @ts-nocheck
'use client'

import { useTheme } from "@ecom-next/core/react-stitch";
import { GridToggleLargeIconProps } from "../../types";

const GridToggleLargeIcon = ({
  width = "40",
  height = "40",
  isActive,
  onClick,
}: GridToggleLargeIconProps) => {
  const theme = useTheme();
  const fillColor = isActive ? theme.color.b1 : theme.color.g4;

  const renderGridIcon = () => {
    if (isActive) {
      return (
        <svg
          width={width}
          height={height}
          data-testid="Grid-toggle-large-icon"
          onClick={onClick}
          viewBox="0 0 41 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="1" y="0.5" width="39" height="39" fill="white" />
          <rect x="1" y="0.5" width="39" height="39" stroke={theme.color.g4} />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M19.5 12H12.5V19H19.5V12ZM28.5 12H21.5V19H28.5V12ZM21.5 21H28.5V28H21.5V21ZM19.5 21H12.5V28H19.5V21Z"
            fill={fillColor}
          />
        </svg>
      );
    } else {
      return (
        <svg
          width={width}
          height={height}
          data-testid="Grid-toggle-large-icon"
          onClick={onClick}
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask id="path-1-inside-1_5332_8749" fill="white">
            <path d="M0 0H40V40H0V0Z" />
          </mask>
          <path d="M0 0H40V40H0V0Z" fill="white" />
          <path
            d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
            fill={theme.color.g5}
            mask="url(#path-1-inside-1_5332_8749)"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
            fill={theme.color.g4}
          />
        </svg>
      );
    }
  };
  return renderGridIcon();
};

export default GridToggleLargeIcon;
