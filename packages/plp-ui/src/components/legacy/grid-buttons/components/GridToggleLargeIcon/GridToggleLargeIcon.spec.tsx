// @ts-nocheck
import { render, act } from "test-utils";
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import GridToggleLargeIcon from "./GridToggleLargeIcon";

describe("<GridToggleLargeIcon />", () => {
  it("rendered sucessfully", () => {
    const { getByTestId } = render(
      <StitchStyleProvider brand={Brands.Gap}>
        <GridToggleLargeIcon />
      </StitchStyleProvider>
    );

    const LargeGridIcons = getByTestId("Grid-toggle-large-icon");
    expect(LargeGridIcons).toBeInTheDocument();
  });
});
