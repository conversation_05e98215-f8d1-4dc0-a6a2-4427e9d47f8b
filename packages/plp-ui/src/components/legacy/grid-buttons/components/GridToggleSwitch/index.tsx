// @ts-nocheck
'use client';

import { useState, useContext } from 'react';
import { usePLPGridToggle, GridToggle } from '@ecom-next/plp-ui/legacy/plp-experiments';
import GridToggleLargeIcon from '../GridToggleLargeIcon';
import GridToggleSmallIcon from '../GridToggleSmallIcon';
import { GridButtonName, GridToggleSwitchProps } from '../../types';
import { useGridButtons } from '../..';
import { XLARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';

const GridToggleSwitch = (props: GridToggleSwitchProps) => {
  const { getGridToggleDataLayer } = props;
  const { onGridChange, isAlternatePLPGrid } = useGridButtons();
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const plpGridToggleExperiment = usePLPGridToggle();
  const isPlpGridToggleExperimentEnabled = plpGridToggleExperiment !== 'GRID_TOGGLE_OFF';

  const [select, setSelect] = useState(isPlpGridToggleExperimentEnabled ? GridButtonName.Inner : '');

  const onButtonClick = (gridOption: GridToggle, alternateGrid: boolean, buttonName: GridButtonName) => {
    if (isPlpGridToggleExperimentEnabled) {
      if (buttonName === GridButtonName.Inner) {
        setSelect(GridButtonName.Inner);
      }
      if (buttonName === GridButtonName.Switch) {
        setSelect(GridButtonName.Switch);
      }
    }
    onGridChange(gridOption, alternateGrid, 'plp');
    if (getGridToggleDataLayer) getGridToggleDataLayer('', gridOption);
  };

  return (
    <div className='grid-toggle-wrapper'>
      <div css={{ display: 'flex', alignItems: 'center' }} className={select === GridButtonName.Inner ? 'inner-selected' : 'inner'}>
        {<GridToggleSmallIcon isActive={!isAlternatePLPGrid} onClick={() => onButtonClick(GridToggle.One, false, GridButtonName.Inner)} />}
      </div>
      <div css={{ display: 'flex', alignItems: 'center' }} className={select === GridButtonName.Switch ? 'switch-selected' : 'switch'}>
        {<GridToggleLargeIcon isActive={isAlternatePLPGrid} onClick={() => onButtonClick(GridToggle.Two, true, GridButtonName.Switch)} />}
      </div>
    </div>
  );
};
export default GridToggleSwitch;
