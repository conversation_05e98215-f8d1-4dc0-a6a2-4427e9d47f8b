// @ts-nocheck
import GridToggleSwitch from "./index";
import { usePLPGridToggle } from "@ecom-next/plp-ui/legacy/plp-experiments";

import renderer from "react-test-renderer";
import { BreakpointContext } from "@ecom-next/core/breakpoint-provider";
import { BrandInfoContext } from "@ecom-next/sitewide/brand-info-provider";
import { BrandInfo } from "@ecom-next/core/legacy/brand-info-provider/types";
import { StitchStyleProvider, Brands } from "@ecom-next/core/react-stitch";
import { Provider as FeatureFlagProvider } from "@ecom-next/core/legacy/feature-flags";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";

jest.mock("@ecom-next/plp-ui/legacy/plp-experiments", () => ({
  usePLPGridToggle: jest.fn(),
  GridToggle: {},
}));

describe("GridToggleSwitch component for PLP Grid Toggle", () => {
  (usePLPGridToggle as Mock).mockReturnValue(true);
  ["gap", "gapfs", "at"].forEach((brand) => {
    it(`renders GridToggleSwitch with large icon selected`, () => {
      const tree = renderer
        .create(
          <PLPStateProvider abSeg={{}}>
            <BreakpointContext.Provider
              value={{
                smallerThan: () => true,
                greaterOrEqualTo: () => true,
                minWidth: () => true,
                maxWidth: () => true,
                size: "large",
                orientation: "",
                media: "",
              }}
            >
              <FeatureFlagProvider
                value={{
                  enabledFeatures: {
                    "plp-grid-toggle": true,
                  },
                }}
              >
                <BrandInfoContext.Provider
                  value={{ abbrBrand: brand, market: "us" } as BrandInfo}
                >
                  <StitchStyleProvider brand={Brands.Gap}>
                    <GridToggleSwitch getGridToggleDataLayer={undefined} />
                  </StitchStyleProvider>
                </BrandInfoContext.Provider>
              </FeatureFlagProvider>
            </BreakpointContext.Provider>
          </PLPStateProvider>
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it(`renders GridToggleSwitch with small icon selected`, () => {
      const tree = renderer
        .create(
          <PLPStateProvider abSeg={{}}>
            <BreakpointContext.Provider
              value={{
                smallerThan: () => true,
                greaterOrEqualTo: () => true,
                minWidth: () => true,
                maxWidth: () => true,
                size: "small",
                orientation: "",
                media: "",
              }}
            >
              <FeatureFlagProvider
                value={{
                  enabledFeatures: {
                    "plp-grid-toggle": true,
                  },
                }}
              >
                <BrandInfoContext.Provider
                  value={{ abbrBrand: brand, market: "us" } as BrandInfo}
                >
                  <StitchStyleProvider brand={Brands.Gap}>
                    <GridToggleSwitch getGridToggleDataLayer={undefined} />
                  </StitchStyleProvider>
                </BrandInfoContext.Provider>
              </FeatureFlagProvider>
            </BreakpointContext.Provider>
          </PLPStateProvider>
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });
});
