// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with large icon selected 1`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with large icon selected 2`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with large icon selected 3`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with small icon selected 1`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with small icon selected 2`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;

exports[`GridToggleSwitch component for PLP Grid Toggle > renders GridToggleSwitch with small icon selected 3`] = `
<div
  className="grid-toggle-wrapper"
>
  <div
    className="inner-selected css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-small-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 41 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5053_13167"
      >
        <path
          d="M0.5 0H40.5V40H0.5V0Z"
        />
      </mask>
      <path
        d="M0.5 0H40.5V40H0.5V0Z"
        fill="white"
      />
      <path
        d="M0.5 0V-1H-0.5V0H0.5ZM40.5 0H41.5V-1H40.5V0ZM40.5 40V41H41.5V40H40.5ZM0.5 40H-0.5V41H0.5V40ZM0.5 1H40.5V-1H0.5V1ZM39.5 0V40H41.5V0H39.5ZM40.5 39H0.5V41H40.5V39ZM1.5 40V0H-0.5V40H1.5Z"
        fill="#DFDFDF"
        mask="url(#path-1-inside-1_5053_13167)"
      />
      <rect
        fill="#000000"
        height="16"
        transform="translate(12.5 12)"
        width="16"
      />
      <rect
        height="15"
        stroke="#000000"
        width="15"
        x="13"
        y="12.5"
      />
    </svg>
  </div>
  <div
    className="switch css-1ae8bis-GridToggleSwitch"
  >
    <svg
      data-testid="Grid-toggle-large-icon"
      fill="none"
      height="40"
      onClick={[Function]}
      viewBox="0 0 40 40"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        fill="white"
        id="path-1-inside-1_5332_8749"
      >
        <path
          d="M0 0H40V40H0V0Z"
        />
      </mask>
      <path
        d="M0 0H40V40H0V0Z"
        fill="white"
      />
      <path
        d="M40 0H41V-1H40V0ZM40 40V41H41V40H40ZM0 1H40V-1H0V1ZM39 0V40H41V0H39ZM40 39H0V41H40V39Z"
        fill="#EDECEC"
        mask="url(#path-1-inside-1_5332_8749)"
      />
      <path
        clipRule="evenodd"
        d="M13 13H18V18H13V13ZM12 12H13H18H19V13V18V19H18H13H12V18V13V12ZM22 13H27V18H22V13ZM21 12H22H27H28V13V18V19H27H22H21V18V13V12ZM27 22H22V27H27V22ZM22 21H21V22V27V28H22H27H28V27V22V21H27H22ZM13 22H18V27H13V22ZM12 21H13H18H19V22V27V28H18H13H12V27V22V21Z"
        fill="#DFDFDF"
        fillRule="evenodd"
      />
    </svg>
  </div>
</div>
`;
