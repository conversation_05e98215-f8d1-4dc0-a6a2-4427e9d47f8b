// @ts-nocheck
import { render, RenderOptions, screen, act } from "test-utils";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";

import GridButtons from ".";
import { GridButtonsProps } from "./types";
import * as breakPoints from "./components/get-breakpoints";
jest.mock("./components/get-breakpoints", () => {
	const orig = jest.requireActual("./components/get-breakpoints");
return {...orig,
__esModule:true};
})
import { mount } from "test-utils";
import GridButtonPlaceholder from "./components/GridButtonPlaceholder";
import {
  usePLPGridToggle,
  GridToggle,
  useBrRedesignSlice2,
} from "@ecom-next/plp-ui/legacy/plp-experiments";

enum MockedDefaultMobileGrid {
  One = "default 1",
  Two = "default 2",
}

jest.mock("@ecom-next/plp-ui/legacy/plp-experiments", () => ({
  useBrRedesign2023: jest.fn(),
  useBrRedesignSlice2: jest.fn(),
  usePLPGapRedesign2024: jest.fn(),
  usePLPGridToggle: jest.fn(),
  useIsBrWhiteBackground: jest.fn(),
  GridToggle: {},
  useBrDefaultMobileGrid: jest.fn(),
  DefaultMobileGrid: MockedDefaultMobileGrid.Two,
}));

const getBreakPointsMock = jest.spyOn(breakPoints, "getBreakpoints");

describe("GridButtons for BR Redesign", () => {
  const renderGridButtons = (
    props?: GridButtonsProps,
    options?: RenderOptions
  ) =>
    render(<GridButtons />, {
      ...options,
    });
  ["br", "brfs"].forEach(brand => {
    it(`large breakpoint (desktop) grid buttons - ${brand}`, () => {
      const wrapper = mount(
        <GridButtons cid={""} url={""} getGridToggleDataLayer={undefined} />,
        {
          breakpoint: "large",
          appState: { brandName: brand },
          enabledFeatures: { "br-redesign-2023": true },
        }
      );
      expect(wrapper).toMatchSnapshot();
    });

    it(`small breakpoint (mobile) grid buttons - ${brand}`, () => {
      const wrapper = mount(
        <GridButtons cid={""} url={""} getGridToggleDataLayer={undefined} />,
        {
          breakpoint: "small",
          appState: { brandName: brand },
          enabledFeatures: { "br-redesign-2023": true },
        }
      );
      expect(wrapper).toMatchSnapshot();
    });
  });
});

describe("GridButtons for PLP Grid Toggle", () => {
  const renderGridButtons = (
    props?: GridButtonsProps,
    options?: RenderOptions
  ) =>
    render(<GridButtons />, {
      ...options,
    });
  const renderGridButtonsPlaceholder = (props: 80, options?: RenderOptions) =>
    render(<GridButtonPlaceholder />, {
      ...options,
    });
  ["gap", "gapfs", "at"].forEach(brand => {
    it(`should render grid toggle switch for small breakpoint - ${brand}`, () => {
      (usePLPGridToggle as Mock).mockReturnValue("2 Across");
      getBreakPointsMock.mockReturnValue({
        isXLarge: false,
        isSmall: true,
        isMedium: false,
        isLarge: false,
      });
      renderGridButtons();
      expect(screen.getByTestId("grid-toggle-container")).toBeInTheDocument();
    });

    it(`should render grid toggle switch for medium breakpoint - ${brand}`, () => {
      (usePLPGridToggle as Mock).mockReturnValue("2 Across");
      getBreakPointsMock.mockReturnValue({
        isXLarge: false,
        isSmall: false,
        isMedium: true,
        isLarge: false,
      });
      renderGridButtons();
      expect(screen.getByTestId("grid-toggle-container")).toBeInTheDocument();
    });

    it("should render placeholder for grid toggle  when desktop breakpoint and isPLPGridToggle is true", async () => {
      (usePLPGridToggle as Mock).mockReturnValue("4 Across");
      getBreakPointsMock.mockReturnValue({
        isXLarge: true,
        isSmall: false,
        isMedium: false,
        isLarge: false,
      });
      renderGridButtonsPlaceholder(80);
      expect(screen.getByTestId("grid-button-placeholder")).toBeInTheDocument();
    });

    it("should render placeholder for grid toggle  when mobile breakpoint and isPLPGridToggle is true", async () => {
      (usePLPGridToggle as Mock).mockReturnValue("2 Across");
      getBreakPointsMock.mockReturnValue({
        isXLarge: false,
        isSmall: true,
        isMedium: false,
        isLarge: false,
      });
      renderGridButtonsPlaceholder(80);
      const styledPlaceholder = screen.getByTestId("grid-button-placeholder");
      expect(styledPlaceholder).toBeInTheDocument();
    });
  });
});

describe("Grid Toggle Dropdown", () => {
  const renderGridButtons = (
    props?: GridButtonsProps,
    options?: RenderOptions
  ) =>
    render(
      <BreakpointContext.Provider
        value={{
          size: XLARGE,
          orientation: "portrait",
          media: "(min-width: 1024px) and (min-aspect-ratio: 1/1)",
          minWidth: () => true,
          greaterOrEqualTo: () => true,
          maxWidth: () => false,
          smallerThan: () => false,
        }}
      >
        <GridButtons />
      </BreakpointContext.Provider>,

      {
        ...options,
      }
    );

  it("should render grid toggle dropdown when desktop breakpoint and isPLPGridToggle is true", async () => {
    (usePLPGridToggle as Mock).mockReturnValue("4 Across");
    getBreakPointsMock.mockReturnValue({
      isXLarge: true,
      isSmall: false,
      isMedium: false,
      isLarge: false,
    });
    renderGridButtons();
    expect(screen.getByRole("listbox")).toBeInTheDocument();
  });

  it("should NOT render grid toggle dropdown when isPLPGridToggle is true and breakpoint is NOT desktop", async () => {
    (usePLPGridToggle as Mock).mockReturnValue("4 Across");
    getBreakPointsMock.mockReturnValue({
      isXLarge: false,
      isSmall: true,
      isMedium: false,
      isLarge: false,
    });
    renderGridButtons();
    expect(screen.queryByRole("listbox")).not.toBeInTheDocument();
  });

  it("should NOT render grid toggle dropdown when isPLPGridToggle is false and breakpoint is desktop", async () => {
    (usePLPGridToggle as Mock).mockReturnValue(GridToggle.Off);
    getBreakPointsMock.mockReturnValue({
      isXLarge: true,
      isSmall: false,
      isMedium: false,
      isLarge: false,
    });
    renderGridButtons();
    expect(screen.queryByRole("listbox")).not.toBeInTheDocument();
  });
});
