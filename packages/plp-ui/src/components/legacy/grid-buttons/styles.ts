// @ts-nocheck
'use client'

import { useContext } from "react";
import { Theme, styled } from "@ecom-next/core/react-stitch";

export interface GridContainerProps {
  theme?: Theme;
  isBRWhiteBackground: boolean;
  isBRRedesignSlice2: boolean;
  isMobile: boolean;
}

export interface GridToggleWrapperProps {
  theme?: Theme;
  shouldShowGridToggleDropdown?: boolean;
}

export const GridToggleWrapper = styled.div<GridToggleWrapperProps>(
  ({ shouldShowGridToggleDropdown }) => {
    return {
      display: "flex",
      marginLeft: `${!shouldShowGridToggleDropdown ? "8px" : "unset"}`,
      alignItems: "center",
      justifyContent: "flex-start",
      msOverflowStyle: "none",
      scrollbarWidth: "none",
      "&::-webkit-scrollbar": {
        display: "none",
      },
      ".grid-toggle-wrapper": {
        borderColor: "#000000",
        margin: 0,
        display: "flex",
      },
    };
  }
);

export const GridContainer = styled.div<GridContainerProps>(
  ({ theme, isBRWhiteBackground, isBRRedesignSlice2, isMobile }) => ({
    backgroundColor: theme?.color.b2,
    width: "fit-content",
    height: "auto",
    position: "relative",
    overflowX: "hidden",
    whiteSpace: "nowrap",
    border: "0.5px solid #000",
    msOverflowStyle: "none",
    svg: {
      borderColor: "#000000",
      pointerEvents: "all",
    },
    ".grid-toggle-container": {
      borderColor: "#000000",
      margin: 0,
      display: "flex",
      ".inner": {
        borderRight: isBRRedesignSlice2 ? "none" : "0.5px solid #000",
        "&:hover": {
          cursor: "pointer",
          backgroundColor: "rgba(255, 255, 255, 0.3)",
        },
      },
      ".switch": {
        "&:hover": {
          cursor: "pointer",
          backgroundColor: "rgba(255, 255, 255, 0.3)",
        },
      },
    },
    ...(isBRWhiteBackground && {
      backgroundColor: theme?.color.wh,
      border: "none",
      ".grid-toggle-container": {
        borderColor: "#D3D1CC",
        margin: 0,
        display: "flex",
        ".inner": {
          border: isBRRedesignSlice2 ? "none" : "0.5px solid #D3D1CC",
        },
        ".switch": {
          border: isBRRedesignSlice2 ? "none" : "0.5px solid #D3D1CC",
        },
        ".inner-selected, .switch-selected": {
          border: `0.5px solid ${theme?.color.bk}`,
          ...(isBRRedesignSlice2 && {
            position: "relative",
            border: "0",
            "::after": {
              content: "''",
              width: isMobile ? "0.5rem" : "0.6rem",
              height: "1px",
              position: "absolute",
              backgroundColor: theme?.color.bk,
              left: isMobile ? "50%" : "52%",
              bottom: isMobile ? "0.6rem" : "1rem",
              transform: isMobile ? "translateX(-50%)" : "translateX(-52%)",
            },
          }),
        },
        ".switch-selected": {
          ...(isBRRedesignSlice2 && {
            "::after": {
              left: isMobile ? "52%" : "50%",
              transform: isMobile ? "translateX(-52%)" : "translateX(-50%)",
            },
          }),
        },
      },
    }),
  })
);
