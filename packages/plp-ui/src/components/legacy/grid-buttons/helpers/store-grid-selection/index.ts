// @ts-nocheck
'use client'

import { storageHelper } from "@ecom-next/core/legacy/utility";
import { GRID_OPTION_SELECTED } from "../../utils/constants";

export const storeGridSelection = (gridOptionSelected: string) => {
  const { setItem } = storageHelper(sessionStorage);
  setItem(GRID_OPTION_SELECTED, gridOptionSelected);
};

export const getGridOptionFromStorage = () => {
  const { getItem } = storageHelper(sessionStorage);
  const storedGridOption = getItem(GRID_OPTION_SELECTED);
  return storedGridOption ? JSON.parse(JSON.stringify(storedGridOption)) : null;
};
