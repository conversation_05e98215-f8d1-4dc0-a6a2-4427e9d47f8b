// @ts-nocheck
import { storeGridSelection } from '.';
import { GRID_OPTION_SELECTED } from '../../utils/constants';
import { GridToggle } from '@ecom-next/plp-ui/legacy/plp-experiments';

const gridSelection = GridToggle.One;

describe('storeGridOptionSelected()', () => {
  it('stores the grid option selected on session storage', () => {
    // @ts-ignore
    storeGridSelection(gridSelection);

    expect(window.sessionStorage.getItem(GRID_OPTION_SELECTED)).toEqual('1 Across');
  });
});