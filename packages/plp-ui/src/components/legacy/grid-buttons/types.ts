// @ts-nocheck
'use client'

import { BreakPoints } from "./components/get-breakpoints";
import { Theme } from "@ecom-next/core/react-stitch";

export type GridButtonsProps = {
  cid?: string;
  url?: string;
  onClick?: () => void;
  getGridToggleDataLayer?: () => void;
  position?: string;
  identifier?: string;
};

export enum BRGrid {
  SmallDesktop = "smallDesktop",
  LargeDesktop = "largeDesktop",
  SmallMobile = "smallMobile",
  LargeMobile = "largeMobile",
}

export enum PLPGrid {
  SmallMobile = "smallMobile",
  LargeMobile = "largeMobile",
}

export enum GridButtonName {
  Inner = "inner",
  Switch = "switch",
}

export type ToggleSwitchProps = {
  breakPoints: BreakPoints;
  onClick?: () => void;
  getGridToggleDataLayer?: (
    event_name?: string,
    grid_layout_type?: string
  ) => void;
};

export type GridToggleSwitchProps = Omit<ToggleSwitchProps, "breakPoints">;

export interface SmallGridIconProps {
  width?: string;
  height?: string;
  isActive?: boolean;
  isHovering?: boolean;
  onClick?: () => void;
}

export interface GridToggleSmallIconProps {
  width?: string;
  height?: string;
  isActive?: boolean;
  onClick?: () => void;
}

export interface GridToggleLargeIconProps {
  width?: string;
  height?: string;
  isActive?: boolean;
  onClick?: () => void;
}

export interface LargeGridIconProps {
  width?: string;
  height?: string;
  isActive?: boolean;
  isHovering?: boolean;
  onClick?: () => void;
}

export interface GridContainerProps {
  theme?: Theme;
  isBRWhiteBackground: boolean;
  shouldShowGridToggleDropdown?: boolean;
}

export interface GridToggleWrapperProps {
  theme?: Theme;
  isPLPGridToggle: boolean;
}
