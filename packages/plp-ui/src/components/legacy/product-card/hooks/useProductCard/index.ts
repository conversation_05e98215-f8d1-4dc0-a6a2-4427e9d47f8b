// @ts-nocheck
'use client'

import { ProductCardState, usePsDataContext } from "@ecom-next/plp-ui/legacy/ps-data-provider";

export const useProductCard = (
  productId: string
): ProductCardState & { isSuccess: boolean } => {
  const context = usePsDataContext();
  const isSuccess = context?.state?.isSuccess ?? false;
  const productCardData = context?.state?.data.productCards[productId];

  return {
    ...productCardData,
    isSuccess,
  };
};
