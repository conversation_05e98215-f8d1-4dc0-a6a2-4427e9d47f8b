// @ts-nocheck
'use client'

import { EmotionJSX } from '@emotion/react/types/jsx-namespace';
import React from 'react';
import { Card } from './styled';
import { ProductCardProps } from './types';
import { useProductCard } from './hooks/useProductCard';

const addClassIf = (
  condition: boolean | undefined,
  className: string
): string[] => (condition ? [className] : []);

export const ProductCard: React.FC<ProductCardProps> = ({
  productInfo,
  children,
}: ProductCardProps): EmotionJSX.Element => {

  const { isSuccess, ...productCardContextData } =  useProductCard(productInfo.productID);
  const productCardInfo = isSuccess ? productCardContextData : productInfo;
  const cardClasses = [
    'product-card',
    ...addClassIf(productCardInfo?.outOfStock, 'product-card-out-of-stock'),
  ].join(' ');

  return <Card className={cardClasses}>{children}</Card>;
};