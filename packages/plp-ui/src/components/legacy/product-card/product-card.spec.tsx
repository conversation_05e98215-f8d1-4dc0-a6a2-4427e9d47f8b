// @ts-nocheck
import React, { ReactElement } from 'react';
import { render, act } from 'test-utils';
import { getLocationHref } from '@ecom-next/core/url-helper';
import { ProductCard } from './product-card';
import { ProductCardProps } from './types';
import translations from './translations';

import { PLPStateProvider } from '@ecom-next/plp-ui/legacy/plp-state-provider';

jest.mock('@ecom-next/core/url-helper');

describe('<ProductCard />', () => {
  const exampleProductInfo = {
    name: 'Sherpa fleece hoodie',
    productURL:
      'http://www.gap.com/browse/product.do?cid=1072543&pcid=1066503&vid=1&pid=864228032',
    productID: '123',
    url: 'http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg',
    outOfStock: false,
  };

  const propsObject = {
    productInfo: exampleProductInfo,
  };

  const defaultLocalizationProps = {
    locale: 'en_US' as const,
    translations,
  };

  const buildProductCardComponent = (
    props: Partial<ProductCardProps>
  ): ReactElement => (
    <PLPStateProvider abSeg={{}}>
      <ProductCard {...propsObject} {...props} />
    </PLPStateProvider>
  );

  // temporarily disabling, this code will go away once we clean up this new localization version
  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
  const renderProductCard = (props: Partial<ProductCardProps>) =>
    render(buildProductCardComponent(props), {
      localization: defaultLocalizationProps,
    });

  let hostname: string;
  beforeAll(() => {
    hostname = window.location.href;
  });

  beforeEach(() => {
    (getLocationHref as jest.Mock).mockReturnValue(
      `${hostname}browse/product.do`
    );
  });

  test('renders correctly', () => {
    const { container } = renderProductCard(propsObject);
    expect(container.firstChild).toMatchSnapshot();
  });

  test('renders out of stock correctly', () => {
    const outOfStockProps = {
      productInfo: {
        ...propsObject.productInfo,
        outOfStock: true,
      },
    };
    const { container } = renderProductCard(outOfStockProps);
    expect(container.firstChild).toMatchSnapshot();
  });
});
