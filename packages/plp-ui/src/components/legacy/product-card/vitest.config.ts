// @ts-nocheck
'use client'

import { defineConfig } from 'vitest/config';

// https://vitejs.dev/config/
export default defineConfig({
  test: {
    setupFiles: ['vitest-setup'],
    globals: true,
    environment: 'jsdom',
    environmentOptions: {
      jsdom: {
        resources: 'usable',
      },
    },
    testTimeout: 5000,
    coverage: {
      reporter:[
        'html',
        'lcov',
      ]
    },
  },
});
