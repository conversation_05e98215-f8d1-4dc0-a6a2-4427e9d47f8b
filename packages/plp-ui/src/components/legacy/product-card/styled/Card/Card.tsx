// @ts-nocheck
'use client'

import { useContext } from 'react';
import { forBrands, Theme, CSSObject, styled } from "@ecom-next/core/react-stitch";
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { useGapRedesignRowGap, usePLPGapRedesign2024 } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Brands } from "@ecom-next/core/legacy/utility";

const Card = styled.div(({ theme }) => {
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);
  const isPLPGapRedesign2024Enabled = usePLPGapRedesign2024();
  const rowGap = useGapRedesignRowGap();

  const { brandName } = useAppState();
  const increasedRowGap = isPLPGapRedesign2024Enabled && (brandName === Brands.Gap || brandName === Brands.GapFactoryStore) ? rowGap : "0rem";

  const defaultStyles: CSSObject = {
    minHeight: isDesktop ? '430px' : '340px',
    position: 'relative',
    '.product-card__image': {
      display: 'block',
    },
    marginBottom: increasedRowGap,
  };

  const brStyles = (theme: Theme): CSSObject =>
  ({
    ...defaultStyles,
    ...theme.brandFontAlt,
    minHeight: undefined,
    width: '100%',
    height: '100%',
  } as CSSObject);

  return forBrands(theme, {
    br: () => brStyles(theme),
    brfs: () => brStyles(theme),
    default: defaultStyles,
  });
});

export default Card;
