// @ts-nocheck
import { render, screen, act } from 'test-utils';

import Card from '.';
import { BreakpointProvider, LARGE } from '@ecom-next/core/breakpoint-provider';
import { usePLPGapRedesign2024, useGapRedesignRowGap } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Brands } from '@ecom-next/core/legacy/utility';

jest.mock('@ecom-next/plp-ui/legacy/plp-experiments', () => ({
  usePLPGapRedesign2024: jest.fn(),
  useGapRedesignRowGap: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/app-state-provider', () => ({
  useAppState: jest.fn(),
}));

const mockUsePLPGapRedesign2024 = usePLPGapRedesign2024 as jest.Mock;
const mockUseGapRedesignRowGap = useGapRedesignRowGap as jest.Mock;
const mockUseAppState = useAppState as jest.Mock;

const wrapper = ({ breakpoint }: { breakpoint: string }) => {
  return ({ children }: { children: JSX.Element }) => (
    <BreakpointProvider initialSizeClass={breakpoint}>{children}</BreakpointProvider>
  );
};

describe('<Card/>', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  const renderCard = (brand: string, breakpoint: string, isGapRedesign2024Enabled: boolean, rowGap: string) => {
    mockUsePLPGapRedesign2024.mockReturnValue(isGapRedesign2024Enabled);
    mockUseGapRedesignRowGap.mockReturnValue(rowGap);
    mockUseAppState.mockReturnValue({ brandName: brand });

    render(<Card data-testid="card" />, { wrapper: wrapper({ breakpoint }) });
  };

  it('renders correctly with default styles', () => {
    renderCard(Brands.Gap, LARGE, false, '0rem');

    expect(screen.getByTestId('card')).toBeInTheDocument();
  });

  it('applies increased margin-bottom when Gap redesign 2024 is enabled for Gap and GapFactoryStore brands', () => {
    renderCard(Brands.Gap, LARGE, true, '3.75rem');

    expect(screen.getByTestId('card')).toHaveStyle('margin-bottom: 3.75rem');
  });

  it('does not apply increased margin-bottom for non-Gap brands even if Gap redesign 2024 is enabled', () => {
    renderCard(Brands.BananaRepublic, LARGE, true, '3.75rem');

    expect(screen.getByTestId('card')).toHaveStyle('margin-bottom: 0rem');
  });
});
