// @ts-nocheck
'use client'

const translations = {
  'en-US': {
    translation: {
      'core.currency': '{{currencyValue, currency.USD}}',
      'fui.color_swatch.out_of_stock': 'out of stock',
      'fui.swatches.fieldset_name': 'Color Selector',
      'price.current_price_range_aria_label':
        'Now {{minPrice}} to {{maxPrice}}',
      'price.now_current_price': 'Now {{price}}',
      'price.percentage_off': '{{value}}% Off',
      'price.regular_price_aria_label': 'Was {{price}}',
      'price.regular_price_range_aria_label':
        'Was {{minPrice}} to {{maxPrice}}',
    },
  },
  'en-CA': {
    translation: {
      'core.currency': '{{currencyValue, currency.CAD}}',
      'fui.color_swatch.out_of_stock': 'out of stock',
      'fui.swatches.fieldset_name': 'Color Selector',
      'price.current_price_range_aria_label':
        'Now {{minPrice}} to {{maxPrice}}',
      'price.now_current_price': 'Now {{price}}',
      'price.percentage_off': '{{value}}% Off',
      'price.regular_price_aria_label': 'Was {{price}}',
      'price.regular_price_range_aria_label':
        'Was {{minPrice}} to {{maxPrice}}',
    },
  },
  'fr-CA': {
    translation: {
      'core.currency': '{{currencyValue, currency.CAD}}',
      'fui.color_swatch.out_of_stock': 'en rupture de stock',
      'fui.swatches.fieldset_name': 'sélecteur de couleurs',
      'price.current_price_range_aria_label':
        'Promo {{minPrice}} à {{maxPrice}}',
      'price.now_current_price': 'Promo {{price}}',
      'price.percentage_off': '{{value}} % De rabais',
      'price.regular_price_aria_label': 'Prix courant {{price}}',
      'price.regular_price_range_aria_label':
        'Prix courant {{minPrice}} à {{maxPrice}}',
    },
  },
};

export default translations;
