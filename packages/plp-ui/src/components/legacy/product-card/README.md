# `ProductCard`

This is a mediator component that handles the communication between various child components related to the product cards.

### Child Components:

- [ProductImage](https://github.gapinc.com/ecomfrontend/category-page/tree/main/packages/product-image)
- [QuickAdd](https://github.gapinc.com/ecomfrontend/product-page/tree/main/packages/quick-add)
- [Swatches](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/swatches)
- [ProductName](https://github.gapinc.com/ecomfrontend/category-page/tree/main/packages/product-name)
- [ReviewRatings](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/review-ratings)
- [Price](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/product-price)
- [ProductMarketingFlag](https://github.gapinc.com/ecomfrontend/category-page/tree/main/packages/product-marketing-flag)

## Using the `ProductCard`

```jsx
import ProductCard from '@cat/product-card';

<ProductCard productInfo={productInfo}>
  <AnyOtherComponent></AnyOtherComponent>
</ProductCard>;
```

## Default Behavior

- The `ProductCard` image and title are wrapped in a link that leads to a Product Detail Page.
- On a screenreader, the link will read out the image alt text and the product title, but not the prices or details. Prices and details can be reached via further arrow navigation.
- On a keyboard, the user can navigate quickly by tabbing once per product.

## Technical Notes

- Styling implemented using [React Stitch](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/react-stitch)
- `ProductCard` receives **Components** from styled which includes [Card, CardName, CardMedia, etc...]
- `ProductCard` inherits all required **props** from `ProductGroup`.

### Props

To view documentation about the props for `ProductCard`, go [here](https://github.gapinc.com/ecomfrontend/category-page/blob/main/packages/product-card/src/types.ts).

### Developing locally

- [Developing Packages Locally](https://github.gapinc.com/ecomfrontend/category-page/wiki/Developing-Packages-Locally)

## Storybook

You can see the Storybook from `ProductCard` [here](https://category-page-storybook-main.apps.cfcommerce.dev.azeus.gaptech.com/?path=%2Fstory%2Fpackages-productcard--default&brand=gap).

## Breaking Changes

To view information regarding BREAKING CHANGES, please view the [MIGRATION.md file](https://github.gapinc.com/ecomfrontend/category-page/blob/main/packages/product-card/MIGRATION.md).
