// @ts-nocheck
'use client'

import { CSSObject } from "@ecom-next/core/react-stitch";
import { CategoryNavSectionProps } from "./category-nav-section/types";

export interface InOutTransitionType {
  transitionDuration: string;
  transitionTimingFunction: string;
}

export interface Transition {
  in: InOutTransitionType;
  out: InOutTransitionType;
}

export type TransitionsType = {
  celebratory: Transition;
  expressive: Transition;
  performance: Transition;
  userTriggeredDelay: string;
  eventTriggeredDelay: string;
};

export type CategoryNavProps = {
  categoryNavData: Array<CategoryNavSectionProps>;
  activeLinkCSS?: CSSObject;
  saleLinkCSS?: CSSObject;
  navContainerCSS?: CSSObject;
};

export type CategoryNavContextType = Pick<CategoryNavProps, "activeLinkCSS" | "saleLinkCSS">;
