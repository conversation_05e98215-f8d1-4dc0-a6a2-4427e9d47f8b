// @ts-nocheck
'use client'

import { useContext } from "react";
import { CSSObject, forBrands, styled, Theme } from "@ecom-next/core/react-stitch";
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';

export const NavContainer = styled.ul(
    ({ theme }) => {
        const { greaterOrEqualTo } = useContext(BreakpointContext);
        const isDesktop = greaterOrEqualTo(LARGE);
        return forBrands(theme, {
            gap: gapStyles(theme, isDesktop),
            gapfs: gapStyles(theme, isDesktop),
            br: brStyles(theme, isDesktop),
            brfs: brStyles(theme, isDesktop),
            on: onStyles(theme, isDesktop),
            at: atStyles(theme, isDesktop),
        });
    }
);

const atStyles = (theme: Theme, isDesktop: boolean): CSSObject => ({
    backgroundColor: theme.color.gray05,
    ...defaultStyles(isDesktop),
});

const gapStyles = (theme: Theme, isDesktop: boolean): CSSObject => ({
    backgroundColor: theme.color.g5,
    ...defaultStyles(isDesktop),
});

const onStyles = (theme: Theme, isDesktop: boolean): CSSObject => ({
    backgroundColor: theme.color.g5,
    ...defaultStyles(isDesktop),
});

const brStyles = (theme: Theme, isDesktop: boolean): CSSObject => ({
    backgroundColor: theme.color.inverse.g1,
    ...defaultStyles(isDesktop),
});

const defaultStyles = (isDesktop: boolean): CSSObject => ({
    width: "100%",
    maxWidth: "-webkit-fill-available",
    padding: isDesktop ? "2rem 5rem 2rem 5rem" : "1.5rem 1rem 1.5rem 1rem",
    margin: 0,
    height: "auto"
});

export const contentStyle = () => ({
    display: 'block',
    height: 'auto',
    overflow: 'hidden',
});
