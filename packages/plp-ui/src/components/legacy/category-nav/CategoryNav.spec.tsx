// @ts-nocheck
import { render, act } from "test-utils";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";
import { CategoryNav } from ".";
import { CategoryNavProps } from "./types";

const navData: CategoryNavProps["categoryNavData"] = [
  {
    header: "Just Arrived",
    children: [
      {
        cid: "8792",
        name: "New Arrivals",
        type: "category",
        active: false,
        link: "/",
      },
      {
        cid: "1119499",
        name: "The Party Shop",
        type: "category",
        active: false,
        link: "/",
      },
    ],
  },
  {
    header: "Gift Cards",
    children: [
      {
        cid: "1185067",
        name: "Gift Cards",
        type: "category",
        active: false,
        link: "/",
      },
    ],
  },
];

describe("CategoryNav", () => {
  const renderCategoryNav = (props?: CategoryNavProps) =>
    render(
      <PLPStateProvider abSeg={{}}>
        <CategoryNav categoryNavData={navData} {...props} />
      </PLPStateProvider>
    );

  it("category nav snapshot", async () => {
    const { container } = renderCategoryNav();
    expect(container).toMatchSnapshot();
  });
});
