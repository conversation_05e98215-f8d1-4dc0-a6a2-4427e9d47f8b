// @ts-nocheck
'use client'

import { styled, forBrands, getFont, getFontWeight, CSSObject, Theme } from "@ecom-next/core/react-stitch";
import { ListItemProps } from "../types";
import { Brands } from "@ecom-next/core/react-stitch";

const atRedesign2024Styles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: getFontWeight('light').fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "0.963rem",
    letterSpacing: "0",
    '&:hover': {
        color: theme.color.g1,
    },
    ...defaultStyles(),
});

const gapStyles = (theme: Theme): CSSObject => ({
    color: theme.color.g1,
    fontWeight: theme.font.primary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    '&:hover': {
        color: theme.color.g1,
    },
    ...defaultStyles(),
});

const onStyles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: theme.font.primary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    '&:hover': {
        color: theme.color.bk,
    },
    ...defaultStyles(),
});

const brStyles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: theme.font.primary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    '&:hover': {
        color: theme.color.bk,
    },
    ...defaultStyles(),
});

const defaultStyles = (): CSSObject => ({
    textTransform: "capitalize",
    fontSize: "0.875rem",
    paddingTop: "0",
    paddingBottom: "0.375rem",
    marginRight: "1rem",
});

export const ListItemContainer = styled.li(({ theme }) => {
    return forBrands(theme, {
        gap: gapStyles(theme),
        gapfs: gapStyles(theme),
        br: brStyles(theme),
        brfs: brStyles(theme),
        on: onStyles(theme),
        at: atRedesign2024Styles(theme),
    });
});

export const ListItemLink = styled.a<Pick<ListItemProps, "active" | "isSaleLink">>(({ active, isSaleLink, theme }) => {
    const secondaryFont = getFont("secondary")({ theme, crossBrand: false });
    const saleColor = forBrands(theme, {
        at: () =>  theme.color.bk,
        default: () => theme.color.r1,
    }) as string;
    const activeColor = forBrands(theme, {
        at: () => theme.color.bk,
        gap: () => theme.color.b1,
        gapfs: () => theme.color.b1,
        on: () => theme.color.b1,
        default: () => theme.color.g1,
    }) as string;
    const currentColor = isSaleLink ? saleColor : activeColor;
    const baseActiveCSS = active && {
        ...secondaryFont,
        color: currentColor,
        borderBottom: "none",
        fontWeight: theme.brand === Brands.Athleta ? getFontWeight('medium').fontWeight : getFontWeight('semiBold').fontWeight
    };
    const baseSaleCSS = isSaleLink && { color: saleColor };
    const defaultCSS = { borderBottom: "1px solid" };
    return { ...defaultCSS, ...baseActiveCSS, ...baseSaleCSS };
});
