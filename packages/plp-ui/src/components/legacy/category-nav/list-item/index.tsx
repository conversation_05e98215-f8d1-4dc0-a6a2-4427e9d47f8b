// @ts-nocheck
'use client'

import { useContext } from "react";
import { ListItemProps } from "./types";
import { CategoryNavContext } from "../CategoryNavContext";
import { ListItemContainer, ListItemLink } from "./styled"

export const ListItem = ({ active, link, name, type, isSaleLink = type === "sale" }: ListItemProps): JSX.Element => {
  const { activeLinkCSS, saleLinkCSS } = useContext(CategoryNavContext);
  return (
    <ListItemContainer>
      <ListItemLink
        active={active}
        aria-label={name}
        css={[active && activeLinkCSS, isSaleLink && saleLinkCSS]}
        href={link}
        isSaleLink={isSaleLink}>
        {name}
      </ListItemLink>
    </ListItemContainer>
  );
};
