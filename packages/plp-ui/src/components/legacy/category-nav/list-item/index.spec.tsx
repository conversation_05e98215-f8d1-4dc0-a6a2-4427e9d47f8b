// @ts-nocheck
import { render, act } from "test-utils";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";
import { ListItem } from ".";
import { CategoryNavContext } from "../CategoryNavContext";
import { ListItemProps } from "./types";

const defaultCategoryData: ListItemProps = {
  cid: "8792",
  name: "New Arrivals",
  type: "category",
  active: false,
  link: "/",
};
const activeLinkCSS = { color: "pink" };
const saleLinkCSS = { color: "rainbow" };

describe("ListItem", () => {
  const renderListItem = (props?: Partial<ListItemProps>) =>
    render(
      <PLPStateProvider abSeg={{}}>
        <CategoryNavContext.Provider value={{ activeLinkCSS, saleLinkCSS }}>
          <ListItem {...defaultCategoryData} {...props} />
        </CategoryNavContext.Provider>
      </PLPStateProvider>
    );

  it("list item snapshot", async () => {
    const { container } = renderListItem();
    expect(container).toMatchSnapshot();
  });
});
