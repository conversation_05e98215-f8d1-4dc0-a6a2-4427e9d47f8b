# Category Nav

`CategoryNav` is a navigation menu component that comes with predefined styles.

Use this component when creating a navigation menu for products.

To see the maturity of this component in terms of Usability, Performance, Citizenship, and Code Quality, refer to the [CategoryNav Scorecard](**_ADD A SCORECARD LINK AT SOME POINT_**).

## Default Behavior

- `CategoryNav` comes with default styles per brand

## Technical Notes

- `CategoryNav` uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md).
- `activeLinkCSS` gets applied to the `ListItem` component via the [`css` prop](https://emotion.sh/docs/css-prop). Also, it will only be applied when `ListItem` has `active={true}`
- `saleLinkCSS` gets applied to the `ListItem` component via the `css` prop. Also, it will only be applied when `ListItem` has `isSaleLink={true}`
- `navContainerCSS` is applied to the top-most container element of `CategoryNav` via the `css` prop. This allows devs to style nested elements by using standard CSS selectors.

### API

#### Examples

##### `categoryNavData`

This shows an example of what `categoryNavData` might look like

```jsx
const navData = [
  {
    header: "New & Now",
    children: [
      {
        name: "New Arrivals",
        link: "/",
      },
      {
        name: "On Trend",
        link: "/",
      },
    ],
  },
  {
    header: "Shop By Category",
    children: [
      {
        name: "The Tee Shop",
        isSaleLink: true,
        link: "/",
      },
      {
        name: "Sleep & Lounge",
        active: true,
        link: "/",
      },
    ],
  },
];

<CategoryNav categoryNavData={navData} />;
```

##### `navContainerCSS`

This shows an example of `navContainerCSS` that targets `h2` styles

```jsx
const customCSS = {
  "& h2": {
    color: "black,
  },
  "& h2:hover": {
    color: "gray",
  }
};

<CategoryNav navContainerCSS={customCSS} />
```

## Props

To view documentation about the props for `CategoryNav`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/category-nav/types.ts).
