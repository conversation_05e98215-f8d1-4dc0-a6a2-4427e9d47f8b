// @ts-nocheck
'use client'

import { Localize } from '@ecom-next/core/legacy/i18n/types';
import { transitions } from '@ecom-next/core/legacy/motion';
import { CategoryNavSectionProps as SectionData } from '../types'
import { COLLAPSED_TOGGLE_BUTTON, EXPANDED_TOGGLE_BUTTON } from '../../localization-tokens';
import { TransitionsType } from '../../types'

export const transitionTimeout = {
    enter:
        parseFloat(transitions.expressive.in.transitionDuration) +
        parseFloat(transitions.userTriggeredDelay),
    exit:
        parseFloat(transitions.performance.out.transitionDuration) +
        parseFloat(transitions.userTriggeredDelay),
};

export const trimSection = (section: SectionData, length: number): SectionData => ({
    ...section,
    children: section.children.slice(0, length),
});

export const getSectionLength = (section: SectionData): number =>
    section?.children?.length ?? 0;

export const getShowToggleButton = (
    allSections: SectionData[],
    firstSectionLength: number,
    secondSectionLength: number,
    maxLength: number
) => {
    return (
        allSections.length > 2 ||
        firstSectionLength + secondSectionLength > maxLength
    );
};

export const getSections = (
    expanded: boolean,
    allSections: SectionData[],
    collapsedSections: any
) => {
    return expanded ? allSections : collapsedSections;
};

export const getToggleButtonText = (expanded: boolean, localize: Localize) => {
    return expanded
        ? localize(EXPANDED_TOGGLE_BUTTON)
        : localize(COLLAPSED_TOGGLE_BUTTON);
};

export const getTransitionCSS = (expanded: boolean, transitions: TransitionsType) => {
    return expanded
        ? `${transitions.expressive.in.transitionDuration} ${transitions.expressive.in.transitionTimingFunction} ${transitions.userTriggeredDelay}`
        : `${transitions.performance.out.transitionDuration} ${transitions.performance.out.transitionTimingFunction} ${transitions.userTriggeredDelay}`;
};

export const getCollapsedFirstSection = (
    firstSectionLength: number,
    maxLength: number,
    firstSection: SectionData
) => {
    if (firstSectionLength >= maxLength) {
        return trimSection(firstSection, maxLength);
    } else {
        return { ...firstSection };
    }
};

export const getCollapsedSecondSection = (
    firstSectionLength: number,
    maxLength: number,
    secondSectionLength: number,
    secondSection: SectionData
) => {
    if (firstSectionLength >= maxLength) {
        return {};
    } else if (firstSectionLength + secondSectionLength > maxLength) {
        const sectionLength = maxLength - firstSectionLength;
        return trimSection(secondSection, sectionLength);
    } else {
        return { ...secondSection };
    }
};
