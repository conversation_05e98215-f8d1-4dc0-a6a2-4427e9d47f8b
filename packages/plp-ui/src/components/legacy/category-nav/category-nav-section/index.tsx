// @ts-nocheck
'use client'

import { CategoryNavSectionProps } from "./types";
import { ListItem } from "../list-item";
import { SectionContainer, SectionHeader } from "./styled"

export const CategoryNavSection = ({ header, children, isFirstSection, isSecondSection, isLastSection, expanded, toggleButtonFragment, isCollapsedAtFirstSection }: CategoryNavSectionProps): JSX.Element => {
  return (
    <div>
      <SectionContainer>
        <SectionHeader>{header}</SectionHeader>
        <ul css={{ display: "contents" }}>
          {children.map(({ active, link, name, type, isSaleLink }) => (
            <ListItem
              key={`${name}-${link}`}
              active={active}
              isSaleLink={isSaleLink}
              link={link}
              name={name}
              type={type} />
          ))}
        </ul>
        {!expanded && (isCollapsedAtFirstSection ? isFirstSection : isSecondSection) && toggleButtonFragment}
      </SectionContainer>
      {expanded && isLastSection && toggleButtonFragment}
    </div>
  )
};
