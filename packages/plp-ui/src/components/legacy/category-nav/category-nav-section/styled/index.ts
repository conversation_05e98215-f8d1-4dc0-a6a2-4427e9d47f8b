// @ts-nocheck
'use client'

import { styled, forBrands, CSSObject, Theme, getFontWeight } from "@ecom-next/core/react-stitch";

export const SectionContainer = styled.div(() => ({
    display: "flex",
    flexWrap: "wrap",
    textWrap: "nowrap",
    marginTop: "1.125rem"
}));

export const SectionHeader = styled.h2(
    ({ theme }) => {
        return forBrands(theme, {
            gap: gapStyles(theme),
            gapfs: gapStyles(theme),
            br: brStyles(theme),
            brfs: brStyles(theme),
            on: onStyles(theme),
            at: atRedesign2024Styles(theme),
        });
    }
);

const defaultStyles = (): CSSObject => ({
    paddingTop: "0",
    paddingBottom: "0.375rem",
    paddingRight: "1rem",
    textTransform: "uppercase",
    fontSize: "0.875rem",
});

const atRedesign2024Styles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: getFontWeight('medium').fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "0.963rem",
    letterSpacing: "0",
    ...defaultStyles(),
});

const gapStyles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: theme.font.secondary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    ...defaultStyles(),
});

const onStyles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: theme.font.secondary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    ...defaultStyles(),
});

const brStyles = (theme: Theme): CSSObject => ({
    color: theme.color.bk,
    fontWeight: theme.font.secondary.fontWeight,
    fontFamily: theme.brandFont.fontFamily,
    lineHeight: "1.063rem",
    ...defaultStyles(),
});

const getColor = ({ theme }: { theme: Theme }): string => {
    return forBrands(theme, {
        at: (t) => t.color.b2,
        br: (t) => t.color.bk,
        brfs: (t) => t.color.bk,
        gap: (t) => t.color.b1,
        gapfs: (t) => t.color.b1,
        on: (t) => t.color.b1,
    }) as string;
};

export const ToggleButton = styled.button(
    ({ theme }): CSSObject => {
        return {
            ...theme.font.secondary,
            appearance: 'none',
            backgroundColor: 'transparent',
            border: 'none',
            width: 'auto',
            textTransform: "capitalize",
            fontSize: "0.875rem",
            paddingTop: "0",
            paddingBottom: "0.375rem",
            marginRight: "1rem",
            paddingRight: "0",
            paddingLeft: "0",
            textDecoration: "underline",
            textUnderlineOffset: "3px",
            color: getColor({ theme })
        };
    });
