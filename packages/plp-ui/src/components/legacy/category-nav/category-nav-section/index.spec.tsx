// @ts-nocheck
import { render, act } from "test-utils";
import { PLPStateProvider } from "@ecom-next/plp-ui/legacy/plp-state-provider";
import { CategoryNavSection } from ".";
import { CategoryNavSectionProps } from "./types";

describe("CategoryNavSection", () => {
  const sectionData: CategoryNavSectionProps = {
    header: "Just Arrived",
    children: [
      {
        cid: "8792",
        name: "New Arrivals",
        type: "category",
        active: false,
        link: "/",
      },
      {
        cid: "1119499",
        name: "The Party Shop",
        type: "category",
        active: false,
        link: "",
      },
    ],
  };
  const renderCategoryNavSection = (props?: CategoryNavSectionProps) =>
    render(
      <PLPStateProvider abSeg={{}}>
        <CategoryNavSection {...sectionData} {...props} />
      </PLPStateProvider>
    );

  it("category nav section snapshot", async () => {
    const { container } = renderCategoryNavSection();
    expect(container).toMatchSnapshot();
  });
});
