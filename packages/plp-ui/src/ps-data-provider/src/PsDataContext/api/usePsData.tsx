import { useEffect, useState } from 'react';
import { Brand, Market } from '@ecom-next/utils/server';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import logger from '@ecom-next/app/logger';
import { AppliedFacets, Data, SortByAdapterResult, SortByDir, SortByField } from '@/types/ps-api-response-types';
import { Endpoints } from '../../types';
import { getAdaptedData } from '../mappers';
import { PsDataProviderContext } from '../reducer/PsDataReducer/types';
import { PAGE_LIMIT } from './constants';

const emptyDataObj = (cid: string): PsDataProviderContext => ({
  cid,
  facets: { appliedFacets: {}, facetOptions: [] },
  inlineFacets: { facetOptions: [] },
  productCards: {},
  productImages: {},
  productInfos: {},
  productsGridIds: [],
  quickAdd: {},
  outOfStock: false,
  searchText: '',
  totalItemCount: 0,
  pagination: {},
});

const appliedFacetsToQueryParameters = (appliedFacets: AppliedFacets, endpoint: string) => {
  if (!appliedFacets) {
    return '';
  }
  const searchParams = new URLSearchParams();

  Object.values(appliedFacets).forEach(options => {
    options?.forEach(({ facetName = '', value, id }) => {
      const facetValue = endpoint === 'categories' ? id : value;
      if (searchParams.get(facetName)) {
        searchParams.set(facetName, `${searchParams.get(facetName)},${facetValue}`);
      } else {
        searchParams.set(facetName, facetValue as string);
      }
    });
  });
  return searchParams.toString();
};

const setDirection = (appliedDirection: SortByDir) => {
  const direction: SortByDir = appliedDirection === 'unset' ? '' : appliedDirection;
  const fixedDirection: string = direction ?? 'asc';
  return fixedDirection;
};

const setField = (appliedDirection: SortByDir, appliedField: SortByField) => {
  const field: SortByField = appliedDirection === '' ? '' : appliedField;
  const fixedField: string = field ?? 'price';
  return fixedField;
};

const sortByToQueryParameters = (sortByOptions?: SortByAdapterResult) => {
  if (sortByOptions && sortByOptions.sortByDir && sortByOptions.sortByField) {
    const searchParams = new URLSearchParams();
    const direction = setDirection(sortByOptions?.sortByDir);
    const field = setField(direction, sortByOptions?.sortByField);
    if (direction !== '') {
      searchParams.set('sortByDir', direction);
      searchParams.set('sortByField', field);
      return searchParams.toString();
    }
    return '';
  }
  return '';
};

const fetchProducts = async (url: string, endpoint: Endpoints, queryParameters: string, cid = '') => {
  const startTime = new Date().getTime();
  const path = endpoint === 'categories' ? 'cc' : 'style';
  const mountedUrl = `${url}/commerce/search/products/v2/${path}?${queryParameters}`;

  const response = await fetch(mountedUrl);
  const data: Data = await response.json();

  if (!data?.products?.length || data.products.length <= 0) {
    logger.error('No products found');
    // customAttribute("NO_PRODUCTS_FOUND_EVENT", "No Products Found");
  }
  const apiResponseTimeMs = new Date().getTime() - startTime;
  logger.info(`ps response time: ${apiResponseTimeMs}ms`);
  // customAttribute("API_RESPONSE_TIME_EVENT", apiResponseTimeMs.toString());

  return { ...getAdaptedData(data, endpoint), cid };
};

const brandObjectAbbreviation: Record<string, string> = {
  at: 'pgAT',
  br: 'pgBR',
  brfs: 'pgBRFS',
  gap: 'pgGP',
  gapfs: 'pgGPFS',
  on: 'pgON',
};
const checkIfIsntFactory = (ABSegIndex: string): boolean => ABSegIndex !== 'pgGPFS' && ABSegIndex !== 'pgBRFS';

const checkMarket = (market: string, ABSegIndex: string): string => {
  return market === 'ca' && checkIfIsntFactory(ABSegIndex) ? `${ABSegIndex}CA` : ABSegIndex;
};

const addVendor = (brand: Brand, market: Market, abSeg: Record<string, string>) => {
  const ABSegIndex = brandObjectAbbreviation[brand];
  const formattedABSegIndex = checkMarket(market, ABSegIndex);
  const ABSegValue = abSeg[formattedABSegIndex];
  if (ABSegValue === 'p') {
    return 'Certona';
  }
  return '';
};

const parseResIdFromCookie = (): string => {
  // const resIdCookie = document?.cookie
  //   .split(';')
  //   .find((cookie) => cookie.includes('RES_TRACKINGID'));
  // return resIdCookie === undefined ? '' : resIdCookie.split('=')[1];
  return '123456789';
};

type PsApiQueryParameters = {
  cid?: string;
  appliedFacets: AppliedFacets;
  sortByOptions?: SortByAdapterResult;
  keyword?: string;
};

export const usePsData = (endpoint: Endpoints, { cid, appliedFacets, sortByOptions, keyword }: PsApiQueryParameters) => {
  const [adaptedData, setAdaptedData] = useState<PsDataProviderContext>(emptyDataObj(cid || ''));
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const appliedFacetsQueryParameters = new URLSearchParams(appliedFacetsToQueryParameters(appliedFacets, endpoint));
  const sortByQueryParameters = new URLSearchParams(sortByToQueryParameters(sortByOptions));

  const { locale, brand, market, ecomApiBaseUrl } = usePageContext();
  const { abSeg } = /*useABContext() or something*/ { abSeg: {} };

  if (cid) {
    appliedFacetsQueryParameters.set('cid', cid);
  }

  if (keyword) {
    appliedFacetsQueryParameters.set('keyword', keyword);
  }

  if (endpoint === 'categories') {
    const vendor = addVendor(brand, market, abSeg);
    const trackingId = parseResIdFromCookie();
    if (vendor) {
      appliedFacetsQueryParameters.set('vendor', vendor);
      if (trackingId) {
        appliedFacetsQueryParameters.set('trackingid', trackingId);
      }
    }
  }

  appliedFacetsQueryParameters.set('brand', brand);
  appliedFacetsQueryParameters.set('locale', locale);
  appliedFacetsQueryParameters.set('market', market);
  appliedFacetsQueryParameters.set('pageSize', PAGE_LIMIT);

  const queryParameters = `${appliedFacetsQueryParameters.toString()}&${sortByQueryParameters.toString()}`;

  useEffect(() => {
    const fetchProductsFunc = async () => {
      try {
        const res = await fetchProducts(ecomApiBaseUrl, endpoint, queryParameters, cid);
        setAdaptedData(res);
        setIsSuccess(true);
        setIsLoading(false);
      } catch (err) {
        logger.error('Something went wrong fetching products', err);
        setIsError(true);
        setIsLoading(false);
      }
    };
    fetchProductsFunc();
  }, []);

  return { data: adaptedData, isLoading, isError, isSuccess };
};
