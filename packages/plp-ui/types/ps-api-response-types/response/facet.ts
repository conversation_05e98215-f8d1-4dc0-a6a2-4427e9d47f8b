export type FacetType = 'simple' | 'complex' | 'range';
export type FacetSelectionType = 'single-select' | 'multi-select' | FacetType;
export type FacetDisplay = 'checkbox' | 'radio' | 'swatch' | 'ratings';
export type FacetLayout = 'grid' | 'list';

export interface Facet {
  applied?: string;
  appliedRange?: FacetRange;
  displayName?: string;
  isActive?: boolean;
  localeName?: string;
  localizedName?: string;
  name?: string;
  options?: FacetOption[];
  order?: number;
  range?: FacetRange;
  type?: FacetSelectionType;
  facetName?: string;
  value?: string;
  id?: string;
}

export interface FacetOption {
  id?: string;
  applied?: string;
  appliedValues?: Option[];
  count?: number;
  localeName?: string;
  name?: string;
  options?: FacetOption[];
  selectedSizes?: SizeFacetOption[];
}

export interface FacetRange {
  min: number;
  max: number;
  count?: number;
}

export interface Option {
  applied?: boolean;
  id?: number;
  name?: string | null;
  options?: Option[];
}

export interface SizeFacetOption extends FacetOption {
  displayName: string;
  isActive: string;
  order?: number;
  sizeVariants: SizeVariant[];
  type: string;
}

export type Size = {
  description: string;
  dimId: string;
  id: string;
  name: string;
  searchFacetOptionId: string;
  selected: boolean;
  sfcId: string;
  sfcName: string;
  tagDisplayLabel?: string;
  variantName: string;
};

export type SizeVariant = {
  name: string;
  selected: boolean;
  styleGroups: StyleGroup[];
};

export type StyleGroup = {
  name: string;
  sizes: Size[];
};
