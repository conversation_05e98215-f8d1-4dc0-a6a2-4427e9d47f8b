import { Facet, FacetDisplay, FacetLayout, FacetOption, FacetRange, FacetSelectionType, FacetType, Option } from '../response';

export interface Direction {
  id: string;
}

export type AppliedFacetsOption = {
  value?: string;
  name: string;
  isActive?: boolean;
  applied?: boolean;
  id?: string;
  facetName?: string;
  displayName?: string;
};

export type AppliedFacets = Record<string, AppliedFacetsOption[]>;

export interface AdaptedFacet extends Facet {
  facetDisplay?: FacetDisplay;
  facetLayout?: FacetLayout;
  selectionType?: FacetSelectionType;
  order?: number;
  appliedRange?: FacetRange;
  range?: FacetRange;
}

export interface BaseOption extends FacetOption {
  id: string;
  isActive: string;
  value?: string;
  facetName?: string;
}

export interface FacetsAdapterResult {
  appliedFacets?: AppliedFacets;
  facetOptions: Facet[];
  hasTags?: boolean;
}

export interface SortByAdapterResult {
  sortByDir: string | undefined;
  sortByField: string | undefined;
}

export type SortByDir = SortByAdapterResult['sortByDir'];

export type SortByField = SortByAdapterResult['sortByField'];

export type FacetOptions = {
  id?: string;
  name: string;
  displayName?: string;
  facetName: string;
};

export interface InlineFacetAdapter {
  facetOptions: FacetOptions[];
  onClear?: (option: { id: string; name: string; facetName: string; applied: boolean }) => void;
}

export interface FacetWithConfig extends Facet {
  displayName?: string;
  facetDisplay?: string;
  facetLayout?: string;
  order?: number;
  hidden?: boolean;
  type: FacetType;
  isActive?: boolean;
  searchFacetName?: string;
}

export interface Options extends Option {
  options: Option[];
}

export interface V2Size {
  id?: string;
  name?: string;
  description?: string;
  selected?: boolean;
  tagDisplayLabel?: string;
  facetName?: string;
  value?: string;
}

export interface V2StyleGroup {
  id: string;
  name: string;
  sizes: V2Size[];
}

export interface V2Variant {
  id: string;
  name: string;
  styleGroups: V2StyleGroup[];
}

export interface V2SizeFacet extends Facet {
  sizeVariants: V2Variant[];
  appliedValues: V2Size[];
}
