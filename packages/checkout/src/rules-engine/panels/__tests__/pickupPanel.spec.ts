import { DraftOrderContextValue } from '../../../contexts/types';
import { PanelStates } from '../../types';
import { getPanelState } from '../pickupPanel';

describe('Pickup Panel state tests', () => {
  it('Should panel be required if no pickup person is selected', () => {
    const draftOrder = { session: { recognition_status: 'authenticated' }, pickupPanel: { isPickUpPersonSaved: false } } as unknown as DraftOrderContextValue;
    const panelState = getPanelState(draftOrder);
    expect(panelState).toBe(PanelStates.REQUIRED);
  });

  it('Should panel collapsed if pickup person is selected', () => {
    const draftOrder = { session: { recognition_status: 'guest' }, pickupPanel: { isPickUpPersonSaved: true } } as unknown as DraftOrderContextValue;
    const panelState = getPanelState(draftOrder);
    expect(panelState).toBe(PanelStates.COLLAPSED);
  });
});
