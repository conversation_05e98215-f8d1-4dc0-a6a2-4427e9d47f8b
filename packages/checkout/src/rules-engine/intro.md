# CheckoutUI Rules Engine

## Overview

In the checkout process, there are two types of panels: **mandatory panels** and **optional panels**. The panel change states based on user actions.

### Mandatory Panels

Mandatory panels are essential for completing an order. Users must interact with these panels to place an order. The mandatory panels include:

- **Shipping Address Panel**: This panel is where users add the shipping address for the item.
- **Pickup Panel**: If the item is for pickup (BOPIS - Buy Online, Pick Up In Store), users must specify the person who will pick up the item.
- **Payments Panel**: Users must specify their payment method to place an order.
- **Shipping Methods Panel**: Users select the shipping speed or method for their order.

### Optional Panels

Optional panels allow users to take additional actions if they choose, but these actions are not required to place an order. The optional panels include:

- **Rewards Panel**: Users can apply rewards if they wish.
- **Promos Panel**: Users can apply promotional codes.
- **Donations Panel**: Users can choose to make a donation.
- **Place Order Panel**: Users click a button to place the order. This panel is unique because it is optional in terms of taking an action, but necessary to complete the order.
- **Email Panel**: Panel where user's email address is shown.
- **Order Summary Panel**: Details related to the orders like total, discounts, rewards are shown.

Some optional panels can change states:

- **Rewards Panel**: Can be in an expanded, disabled, or collapsed state.
- **Promos Panel**: Can be in an expanded, disabled, or collapsed state.
- **Donations Panel**: Can be in an expanded or hidden state.
- **Place Order Panel**: Can be in an expanded or hidden state.

There are also optional panels that do not change state:

- **Email Panel**: Always remains in an expanded state.
- **Order Summary Panel**: Always remains in an expanded state.

### Panel States Details

- **Disabled State**: Only the title is shown, and users cannot interact with the panel (e.g., expanding, collapsing, or taking any action).
- **Required State**: Users must take an action, and the panel cannot be collapsed. For example, the Shipping Address Panel is in a required state until a shipping address is entered. **Note** that a mandatory panel can only be in a required state.
- **Expanded State**: The panel is open and users can interact with it. This state is similar to the required state but can apply to both mandatory and optional panels. For example, the Payments Panel can be in an expanded state after the user has entered payment information.
- **Collapsed State**: The panel shows a summary of the information entered and is not expanded. For example, after entering a shipping address and clicking continue, the Shipping Address Panel moves to a collapsed state.
- **Hidden State**: The panel is not shown on the UI. For example, the Donations panels can be in a hidden state. Additionally, the Shipping Address Panel can be hidden for BOPIS orders.

### Rules

- A required panel cannot be collapsed. For example, the Pickup Panel in a BOPIS order cannot be collapsed.
- Only mandatory panels can be in a required state.
- There cannot be two panels in a required state at the same time. The first panel requiring user action will be in a required state, and any subsequent panel will be in a disabled state.
- If any panel is in a required state, the Place Order Panel will be hidden.
- A mandatory panel with errors will be in a required state.
- An optional panel with errors will not be in a required state. Errors will be shown, but the panel will remain optional.

### Exceptions to the Rules

- **Shipping Methods Panel**: Although a shipping method is selected by default, this panel remains in an expanded state when the user lands on the checkout page. This is because the backend services select a shipping method and send it to the UI. The panel can be collapsed by the user, but it will expand again upon refresh due to the rules coming from the X API.
- **Payments Panel**: If there is a promotional banner (e.g., Barclays banner), the Payments Panel will remain in an expanded state even after the user has entered the correct payment details. If the user has not entered the required information, the Payments Panel will be in a required state. If another panel (e.g., Shipping Address Panel) is in a required state, the Payments Panel will be in a disabled state, and the Barclays banner will not be shown.

## FAQs

### What are optional panels?

Optional panels are sections where users can take actions if they wish, but these actions are not required to place an order.

### What are mandatory panels?

Mandatory panels are sections where users must take actions to complete an order. Without providing necessary details in these panels, an order cannot be placed.

### What happens to the Pickup Panel if the item is not for pickup?

If the item is not for pickup (BOPIS), the Pickup Panel will be in a hidden state.

### Is the Payments Panel always in a required state?

No, the Payments Panel is in a required state until the user enters payment information and clicks the submit button. After that, it can be in an expanded state or collapsed state.

### Can a member pay for everything using rewards?

No, rewards cannot be used to cover the entire order. Promos can be used to cover the complete order. In this case Payments Panel will still be shown in an expanded with a message indicating that the payment has been covered by promos.

### What happens if a user enters an invalid promo code?

If a user enters an invalid promo code, the Promos Panel will be in an expanded state, but not in a required state, because it is an optional panel.

### What is the difference between a required state and an expanded state?

In a required state, user input is necessary, and the panel cannot be collapsed. In an expanded state, the panel is open, but no input is required from the user, and it does not block the user from placing an order.

### Which panels can be hidden?

Only optional panels can be hidden, with some exceptions. For example, the Pickup Panel (a mandatory panel) will be hidden if there are no BOPIS items in the order. Similarly, the Shipping Address Panel will be hidden if the order contains only BOPIS items.

### Who determines the hidden state of panels?

The hidden states of the panels are determined by the rules engine, which takes information from the xAPI and applies the rules accordingly.
