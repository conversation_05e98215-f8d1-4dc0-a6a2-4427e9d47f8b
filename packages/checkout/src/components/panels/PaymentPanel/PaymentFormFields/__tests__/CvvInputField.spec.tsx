import React from 'react';
import { FormValidation } from '@ecom-next/my-account/validation';
import { render, fireEvent, screen } from '../../../../../utils/test-utils';
import { CvvInputField } from '../CvvInputField';

describe('CvvInputField', () => {
  const CvvInputFieldWithState = () => {
    return (
      <FormValidation>
        <CvvInputField />
      </FormValidation>
    );
  };

  const renderCVVInputField = () => render(<CvvInputFieldWithState />);

  test('renders the input field with correct label and supporting text with full story fs-exclude class for PII data.', () => {
    renderCVVInputField();
    const { getAllByLabelText } = render(<CvvInputField />);
    const elements = getAllByLabelText('Security Code');
    expect(elements.length).toBe(2);
  });

  test('updates the value when input is changed', () => {
    renderCVVInputField();
    const inputElement = screen.getByTestId('card-security-code') as HTMLInputElement;
    fireEvent.input(inputElement, { target: { value: '123' } });
    expect(inputElement).toHaveValue('123');
    const fsExcludeElement = document.querySelector('.fs-exclude');
    expect(fsExcludeElement).toBeInTheDocument();
  });

  test('normalizes the input value to only contain numbers', () => {
    renderCVVInputField();
    const inputElement = screen.getByTestId('card-security-code') as HTMLInputElement;
    fireEvent.input(inputElement, { target: { value: '1a2b3c' } });
    expect(inputElement).toHaveValue('123');
  });

  test('displays the error message when input is invalid', () => {
    renderCVVInputField();
    const inputElement = screen.getByTestId('card-security-code') as HTMLInputElement;
    fireEvent.input(inputElement, { target: { value: '12' } });
    fireEvent.blur(inputElement);
    const errorMessageElement = screen.getByText('wallet.errors.valid.cvv');
    expect(errorMessageElement).toBeInTheDocument();
  });
});
