import { getFirstAndLastElement } from '../getFirstAndLastElement';

describe('getFirstAndLastElement', () => {
  it('should handle empty strings', () => {
    expect(getFirstAndLastElement('')).toEqual(['', '']);
    expect(getFirstAndLastElement('    ')).toEqual(['', '']);
  });

  it('should handle single element', () => {
    expect(getFirstAndLastElement('John')).toEqual(['John', '']);
    expect(getFirstAndLastElement('    Bob   ')).toEqual(['Bob', '']);
  });

  it('should correctly extract first and last eleemnts from a single element', () => {
    expect(getFirstAndLastElement('John Doe')).toEqual(['<PERSON>', 'Do<PERSON>']);
    expect(getFirstAndLastElement('<PERSON>')).toEqual(['<PERSON>', '<PERSON>']);
    expect(getFirstAndLastElement('  Alice  ')).toEqual(['Alice', '']);
  });
});
