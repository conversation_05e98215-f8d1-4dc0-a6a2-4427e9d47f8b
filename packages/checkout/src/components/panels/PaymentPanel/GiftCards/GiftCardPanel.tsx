import React, { useState, useEffect, useContext } from 'react';
import { ModalDrawerWrapper } from '@ecom-next/core/migration/modal';
import { DrawerSlidePosition } from '@ecom-next/core/migration/drawer';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Button } from '@ecom-next/core/migration/button';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { Notification } from '@ecom-next/core/migration/notification';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { useCheckout } from '../../../../contexts/CheckoutProvider';
import { GiftCardAddState } from '../PaymentForms/types';
import { getVaultTokenForGiftCard } from '../VaultToken/vaultClient';
import { useBreakpoint } from '../../../utils/useBreakPoint';
import { logNewRelicError, Feature } from '../../../../utils/newrelic-logger';
import { GiftCardForm } from './GiftCardForm';

export interface GiftCardPanelProps {
  isMobile?: boolean;
}

export const GiftCardPanel = ({ isMobile: isMobileProp }: GiftCardPanelProps) => {
  const { draftOrder, makeCheckoutXapiCall } = useCheckout();
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const isVaultServiceEnabled = enabledFeatures['checkout-ui-vault-service'];
  const {
    draftOrderId,
    giftCardPanel: { errors, isFormShown },
  } = draftOrder;
  const { ecomApiBaseUrl = '' } = usePageContext() || {};
  const [isOpen, setIsOpen] = useState(false);
  const toggleComponent = (value: boolean) => setIsOpen(value);
  let { isMobile = false } = useBreakpoint();
  isMobile = isMobileProp ?? isMobile;
  const { localize } = useLocalize();
  const addCardText = localize('payment.paymentMethodContainer.addCardText');
  const [reset, setReset] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState('');

  const applyGiftCard = async ({ giftCardPinNumber, giftCardNumber }: GiftCardAddState) => {
    try {
      let vaultToken;
      if (isVaultServiceEnabled) {
        vaultToken = await getVaultTokenForGiftCard({ pin: giftCardPinNumber, number: giftCardNumber }, ecomApiBaseUrl);
      }
      const giftCardData: { cardToken: string; number: string; pin: string } = {
        cardToken: vaultToken?.[0]?.vaultId || '',
        pin: vaultToken?.[1]?.vaultId || '',
        number: giftCardNumber,
      };
      const formObj = { draftOrderId: draftOrderId, giftCards: [giftCardData] };
      await makeCheckoutXapiCall({
        endpoint: 'applyGiftCard',
        body: formObj,
      });
    } catch (err: unknown) {
      logNewRelicError(err as Error, { feature: Feature.GIFT_CARD, caller: 'applyGiftCard()', message: 'Error applying gift card' });
    }
  };

  const getErrorMessage = (errorCode: string) => {
    const errorText = localize(`giftCardErrors.${errorCode}`);
    return errorText.includes('giftCardErrors') ? 'Add gift card Error' : errorText;
  };

  useEffect(() => {
    if (errors?.length > 0) {
      setReset(true);
      toggleComponent(true);
      setErrorMessage(getErrorMessage(errors[0]));
    } else {
      toggleComponent(false);
      setErrorMessage('');
    }
  }, [errors]);

  return (
    <>
      {isFormShown && (
        <Button
          kind='inline-row'
          onClick={() => {
            setErrorMessage('');
            toggleComponent(true);
          }}
          fullWidth
          data-testid='add-gift-card-link'
        >
          {addCardText}
        </Button>
      )}
      <ModalDrawerWrapper
        position={DrawerSlidePosition.bottom}
        isLengthyContent={false}
        isOpen={isOpen}
        onClose={() => toggleComponent(false)}
        title={addCardText}
        closeButtonAriaLabel={'close-modal'}
        hasRoundedCorners={true}
        isMobile={isMobile}
        closeIcon={true}
      >
        <div data-testid='add-gift-card-modal'>
          {errorMessage !== '' && (
            <div className={isMobile ? 'mb-4' : 'mb-2'}>
              <Notification isDismissible={false} kind='error' showIcon={true} inline={false} dismissButtonLabel=''>
                {errorMessage}
              </Notification>
            </div>
          )}
          <GiftCardForm
            isMobile={isMobile}
            onCloseHandler={() => {
              toggleComponent(false);
            }}
            applyGiftCardHandler={applyGiftCard}
            reset={reset}
          />
        </div>
      </ModalDrawerWrapper>
    </>
  );
};
