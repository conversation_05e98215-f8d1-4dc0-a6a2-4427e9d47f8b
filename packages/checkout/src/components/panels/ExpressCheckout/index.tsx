import { FC, useState } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { Notification } from '@ecom-next/core/migration/notification';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useCheckout } from '../../../contexts/CheckoutProvider';
import { useCheckoutFeatures } from '../../../contexts/checkoutFeatures/useCheckoutFeatures';
import { PayPal } from '../../Express/PayPal/PayPal';
import { ApplePayButton } from '../../modules/ApplePay/ApplePayButton';
import { displayPayPal, displayApplePay, showExpressCheckout } from '../../../utils/displayThirdPartyUtil';
import { DisplayErrorMessage, type ExpressCheckoutError } from './DisplayErrorMessage';
import { Divider } from './Divider';

export const ExpressCheckout: FC = () => {
  const [error, setError] = useState<ExpressCheckoutError | null>(null);
  const { localize } = useLocalize();

  const { draftOrder } = useCheckout();
  const {
    orderSummaryPanel: { totalPrice },
    placeOrderPanel: { currencyCode },
  } = draftOrder;

  const { isExpressPayPalEnabled, isExpressApplePayEnabled } = useCheckoutFeatures();

  const pageContext = usePageContext();
  const { market = 'us' } = pageContext;

  const showPayPal = displayPayPal(draftOrder, isExpressPayPalEnabled);
  const showApplePay = displayApplePay(draftOrder, isExpressApplePayEnabled);

  const isExpressCheckoutEnabled = showExpressCheckout(isExpressPayPalEnabled, isExpressApplePayEnabled, showPayPal, showApplePay);

  if (!isExpressCheckoutEnabled) {
    return null;
  }

  return (
    <>
      <div className='bg-cb-coreColor-white p-4'>
        {error && (
          <div className='mb-2 sm:mb-4 '>
            <Notification kind='error' isDismissible={false} showIcon={true} inline={false}>
              <DisplayErrorMessage localize={localize} error={error} />
            </Notification>
          </div>
        )}
        <div className='flex gap-2' data-testid='express-checkout'>
          {showPayPal && (
            <div className={isExpressPayPalEnabled ? 'flex-1' : 'w-full'}>
              <PayPal setError={setError} />
            </div>
          )}
          {showApplePay && (
            <div className={isExpressApplePayEnabled ? 'flex-1' : 'w-full'}>
              <ApplePayButton total={Number(totalPrice)} currencyCode={currencyCode} market={market} setError={setError} />
            </div>
          )}
        </div>
      </div>
      <Divider localize={localize} />
    </>
  );
};
