import React, { useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ChevronDown } from '@ecom-next/core/migration/icons';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import classNames from 'classnames';
import { useCheckout } from '../../../contexts/CheckoutProvider';
import type { ShippingMethodDetails, DeliveryGroupLists } from '../../../contexts/types';
import { logNewRelicError, Feature } from '../../../utils/newrelic-logger';
import { ProductDetails } from './ProductDetails';
import { ShippingLineItem } from './ShippingLineItem';
import { CollapsedProductView } from './CollapsedProductView';
import { ShippingItemsCount } from './ShippingItemsCount';
import { fireTealiumLinkTag } from './../../../utils/tealium-utils';

export type ShippingChangeProps = {
  shippingId: ShippingMethodDetails['shippingId'];
  shippingTypeId: ShippingMethodDetails['shippingTypeId'];
};

export type ShippingOptionsProps = {
  deliveryGroupLists: DeliveryGroupLists[];
  displayBrandLogos: boolean;
};

export const ShippingOptions = ({ deliveryGroupLists, displayBrandLogos }: ShippingOptionsProps) => {
  const { draftOrder, makeCheckoutXapiCall } = useCheckout();
  const { localize } = useLocalize();
  const { brandAbbr, market } = usePageContext();

  const [showDetails, setShowDetails] = useState<{ [key: string]: boolean }>({});
  const { session, draftOrderId } = draftOrder;
  const { recognition_status } = session;

  const toggleDetails = (deliveryGroupId: string) => {
    fireTealiumLinkTag({
      event_name: 'checkout_cta_clicks',
      brandShortName: brandAbbr,
      countryCode: market.toUpperCase(),
      module: 'shipping',
      recognition_status: recognition_status,
    });
    setShowDetails(prevState => ({
      ...prevState,
      [deliveryGroupId]: prevState[deliveryGroupId] ? false : !prevState[deliveryGroupId],
    }));
  };

  const selectMethodAction = async ({ shippingId, shippingTypeId }: ShippingChangeProps) => {
    const shippingAddress = draftOrder.shippingAddressPanel.shippingAddressList?.find(({ isSelected }) => isSelected) ?? undefined;

    try {
      await makeCheckoutXapiCall({
        endpoint: 'selectShippingMethod',
        body: {
          deliveryGroupId: 'GapRegularGroup',
          draftOrderId,
          shippingAddress,
          shippingMethod: { shippingId, shippingTypeId },
        },
      });
    } catch (err: unknown) {
      logNewRelicError(err as Error, {
        feature: Feature.SHIPPING_METHOD,
        caller: 'selectMethodAction()',
        message: 'Error selecting shipping method',
      });
    }
  };

  return (
    <>
      {deliveryGroupLists.map(props => {
        const itemCount = Array.isArray(props?.lineItemList) ? props?.lineItemList.reduce((acc, item) => acc + Number(item.quantity), 0) : 0;

        return (
          <div key={props.deliveryGroupId}>
            <div
              onClick={() => toggleDetails(props.deliveryGroupId)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  toggleDetails(props.deliveryGroupId);
                }
              }}
              style={{ cursor: 'pointer' }}
              role='button'
              tabIndex={0}
            >
              <div className='textcb-base-compact-emphasis mb-1 font-bold'>
                <ShippingItemsCount itemCount={itemCount} />
              </div>
              <div className='cb-base-compact text-cb-textColor-link-critical pb-4'>
                {localize(showDetails[props.deliveryGroupId] ? 'shippingMethod.hideItemDetails' : 'shippingMethod.showItemDetails')}
                <span
                  className={classNames('inline-block transform transition-all', {
                    'rotate-180': showDetails[props.deliveryGroupId],
                  })}
                >
                  <ChevronDown svgClassName='w-6 h-6 text-cb-textColor-link-critical' />
                </span>
              </div>
            </div>
            <div className='mb-4'>
              {showDetails[props.deliveryGroupId] ? (
                <ProductDetails lineItemsList={props.lineItemList} {...props} key={props.deliveryGroupId} displayBrandLogos={displayBrandLogos} />
              ) : (
                <div
                  onClick={() => toggleDetails(props.deliveryGroupId)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      toggleDetails(props.deliveryGroupId);
                    }
                  }}
                  style={{ cursor: 'pointer' }}
                  role='button'
                  tabIndex={0}
                >
                  <CollapsedProductView lineItemsList={props.lineItemList} {...props} key={props.deliveryGroupId} displayBrandLogos={displayBrandLogos} />
                </div>
              )}
            </div>
            <ShippingLineItem {...props} key={props.deliveryGroupId} selectMethodAction={selectMethodAction} />
          </div>
        );
      })}
    </>
  );
};
