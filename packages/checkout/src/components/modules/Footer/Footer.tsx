import { Button } from '@ecom-next/core/migration/button';
import { Link } from '@ecom-next/core/migration/link';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { getFeedbackURL, getSessionId } from '../../../utils/executeFeedbackForm';
import FooterLinks, { LinkProp } from './Links';

const Footer = ({ isOCPPage = false, brand = 'gp', locale = 'en_US', market = 'US', brandCode = 'gp', environment = 'stage' }) => {
  const { localize } = useLocalize();

  const localization = {
    customerSupport: localize('footer.customerSupport'),
    availableTime: localize('footer.availableTime'),
    customerService: localize('footer.customerService'),
    shipping: localize('footer.shipping'),
    returns: localize('footer.returns'),
    storeLocator: localize('footer.storeLocator'),
    trackOrder: localize('footer.trackOrder'),
    helpAvailable: localize('footer.helpAvailable'),
    helpNumber: localize('footer.helpNumber'),
    giveUsFeedback: localize('footer.giveUsFeedback'),
  };

  const handleClick = () => {
    const feedBackUrl = getFeedbackURL({ market, brandCode, environment, locale });
    const feedBackUrlFS = `${feedBackUrl}&fullstoryID=${getSessionId()}`;
    if (typeof window !== 'undefined') {
      window.open(feedBackUrlFS);
    }
  };

  const links: LinkProp[] = [
    { label: localization.shipping, url: localize(`footer.${brand}.shipping`) },
    { label: localization.returns, url: localize(`footer.${brand}.returns`) },
    { label: localization.customerService, url: localize(`footer.${brand}.customerService`) },
    { label: localization.storeLocator, url: localize(`footer.${brand}.storeLocator`) },
    { label: localization.trackOrder, url: localize(`footer.${brand}.trackOrder`) },
    { label: localization.giveUsFeedback, url: '#', target: '_blank', isButton: true, handleClick: handleClick },
  ];

  const footerWrapper = `flex flex-col gap-4 py-8 md:max-w-[1044px] md:w-full`;

  return (
    <footer role='contentinfo' className={isOCPPage ? 'mb-0' : 'mb-[108px] md:mb-0'}>
      <div className='bg-cb-coreColor-black justify-left flex px-4 md:justify-center'>
        <div className={footerWrapper}>
          {isOCPPage ? (
            <>
              <div className='text-cb-textColor-inverse flex flex-col md:flex-row'>
                <div>{localization.customerSupport}</div>
                <div className='ml-0 md:ml-4'>{localization.availableTime}</div>
              </div>
              <FooterLinks links={links} />
            </>
          ) : (
            <>
              <div className='text-cb-textColor-inverse flex flex-col md:flex-row'>
                <div>{localization.helpAvailable}</div>
                <div className='ml-0 md:ml-4'>
                  <Link className='text-cb-textColor-inverse' to={`tel: ${localization.helpNumber}`} tabIndex={0}>
                    {localization.helpNumber}
                  </Link>
                </div>
              </div>
              <div>
                <Button
                  kind='text-underline-small'
                  className='text-cb-textColor-inverse border-cb-textColor-inverse hover:!text-cb-textColor-inverse hover:!border-cb-textColor-inverse !static border-b text-sm font-normal hover:!border-b'
                  onClick={handleClick}
                >
                  {localization.giveUsFeedback}
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
