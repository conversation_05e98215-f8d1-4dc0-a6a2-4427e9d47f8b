import { LoadingPlaceholder } from '@ecom-next/core/migration/loading-placeholder';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';

const OrderSummarySkeleton = (): JSX.Element => {
  const { localize } = useLocalize();

  return (
    <div className='bg-white p-4'>
      <h2 className='cb-base-default-emphasis mb-4'>{localize('orderSummary.header')}</h2>

      <div className='flex justify-between'>
        <div className='mb-2 h-[18px] w-[60px]'>
          <LoadingPlaceholder />
        </div>
        <div className='mb-2 h-[18px] w-[90px]'>
          <LoadingPlaceholder />
        </div>
      </div>

      <div className='flex justify-between'>
        <div className='mb-2 h-[18px] w-[55px]'>
          <LoadingPlaceholder />
        </div>
        <div className='mb-2 h-[18px] w-[65px]'>
          <LoadingPlaceholder />
        </div>
      </div>

      <div className='flex justify-between'>
        <div className='mb-2 h-[18px] w-[60px]'>
          <LoadingPlaceholder />
        </div>
        <div className='mb-2 h-[18px] w-[36px]'>
          <LoadingPlaceholder />
        </div>
      </div>

      <div className='flex justify-between'>
        <div className='mb-2 h-[18px] w-[55px]'>
          <LoadingPlaceholder />
        </div>
        <div className='mb-2 h-[18px] w-[65px]'>
          <LoadingPlaceholder />
        </div>
      </div>

      <div className='flex justify-between'>
        <div className='mb-2 h-[18px] w-[55px]'>
          <LoadingPlaceholder />
        </div>
        <div className='mb-2 h-[18px] w-[30px]'>
          <LoadingPlaceholder />
        </div>
      </div>
    </div>
  );
};

export { OrderSummarySkeleton };
