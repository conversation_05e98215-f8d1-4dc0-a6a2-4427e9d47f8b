import { Link } from '@ecom-next/core/migration/link';
import { ChevronLeft } from '@ecom-next/core/migration/icons';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { HeaderBrands } from '@ecom-next/core/migration/icons/BrandLogos';
import { Button } from '@ecom-next/core/migration/button';
import { ATHLETA_URL_PREFIX, BANANA_URL_PREFIX, GAP_URL_PREFIX, getBackToBagUrl, getHref, isAthletaShown, ON_URL_PREFIX } from '../../utils/HeaderUtil';
import { getFeedbackURL, getSessionId } from '../../../utils/executeFeedbackForm';
import BrandLogo from './BrandLogo';

const Header = ({ isOCPPage = false, locale = 'en_US', market = 'US', brandCode = 'gp', environment = 'stage' }) => {
  const { localize } = useLocalize();
  const localization = {
    customerSupport: localize('footer.customerSupport'),
    backToBag: localize('header.backToBag'),
    giveUsFeedback: localize('header.giveUsFeedback'),
    url: localize('header.url'),
  };

  const handleClick = () => {
    const feedBackUrl = getFeedbackURL({ market, brandCode, environment, locale });
    const feedBackUrlFS = `${feedBackUrl}&fullstoryID=${getSessionId()}`;
    if (typeof window !== 'undefined') {
      window.open(feedBackUrlFS);
    }
  };
  const baseBrands = [
    { brandProp: HeaderBrands.Gap, width: '22px', url: getHref(GAP_URL_PREFIX, localization.url) },
    { brandProp: HeaderBrands.OldNavy, width: '52px', url: getHref(ON_URL_PREFIX, localization.url) },
    { brandProp: HeaderBrands.BananaRepublic, width: '123.9px', url: getHref(BANANA_URL_PREFIX, localization.url) },
  ];

  // Handle Athleta conditionally
  const brands = isAthletaShown(locale)
    ? [...baseBrands, { brandProp: HeaderBrands.Athleta, width: '72.65px', url: getHref(ATHLETA_URL_PREFIX, localization.url) }]
    : baseBrands;

  const headerWrapper = `mx-auto flex h-10 items-center justify-center md:max-w-[1044px] sm:m-auto sm:justify-between`;
  return (
    <div>
      <header role='banner' data-testid='default-checkout-header'>
        <div className='bg-cb-coreColor-black px-4'>
          <div className={headerWrapper}>
            <div className={`hidden sm:block ${isOCPPage ? 'md:w-[118px]' : ''}`}>
              <Link to={getBackToBagUrl()} className={`text-cb-textColor-inverse ${isOCPPage ? 'hidden' : ''}`}>
                <ChevronLeft svgClassName={'text-cb-textColor-inverse mb-0.5'} />
                {localization.backToBag}
              </Link>
            </div>
            <div className='flex items-center'>
              {brands.map((brand, index) => (
                <BrandLogo isCheckoutPage={!isOCPPage} key={index} {...brand} />
              ))}

              {/* 
                Note: This below div is added as an experiement to see if it improves checkout's LCP.                
              */}
              <div aria-hidden='true' className='absolute z-[-999] bg-black'>
                This is a placeholder div for all the brand logos and is here just for informational purpose. It should not be displayed to the user.
              </div>
            </div>
            <div className='hidden sm:block'>
              <Button
                kind='text-underline-small'
                className='text-cb-textColor-inverse border-cb-textColor-inverse hover:!text-cb-textColor-inverse hover:!border-cb-textColor-inverse border-b text-base font-normal hover:!border-b'
                onClick={handleClick}
              >
                {localization.giveUsFeedback}
              </Button>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
};

export default Header;
