import { Button } from '@ecom-next/core/migration/button';
import { AfterPayIcon } from '@ecom-next/core/migration/icons';
import { FC } from 'react';
import { AfterPayPayLaterNoInterestImage } from '../../../assets/images/AfterPayPayLaterNoInterestImage';
import { useAfterPay } from './context/useAfterPay';
import { AfterPayProvider } from './context/AfterPayProvider';
import { AfterPayButtonProps } from './context/types';

export const AfterPayButtonLayout = () => {
  const { loading, error, openAfterPay } = useAfterPay();

  return (
    <div className='flex flex-col justify-center'>
      <div className='text-center'>
        <AfterPayPayLaterNoInterestImage />
      </div>
      <div className='text-center'>
        <Button isDisabled={Boolean(loading || error)} kind='secondary' onClick={openAfterPay}>
          <div className='flex'>
            <span className='text-nowrap'>Pay with </span>
            <span className='ms-[-27px] mt-[-8px]'>
              <AfterPayIcon height={42} />
            </span>
          </div>
        </Button>
      </div>
    </div>
  );
};

export const AfterPayButton: FC<AfterPayButtonProps> = props => (
  <AfterPayProvider {...props}>
    <AfterPayButtonLayout />
  </AfterPayProvider>
);
