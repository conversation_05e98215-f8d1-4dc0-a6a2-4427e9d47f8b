# Contexts Documentation

## Overview

The `contexts` folder contains various context providers and hooks that manage the state and data flow for the checkout process in the e-commerce application. These contexts are essential for maintaining a consistent state across different components and ensuring a seamless user experience.

## Contexts

### CheckoutProvider

#### Description

The `CheckoutProvider` context manages the state and actions related to the checkout process. It provides essential data such as the draft order, loading state, and API error handling.

#### Key Functions and State

- **State Variables**:

  - `loading`: Indicates if a checkout-related API call is in progress.
  - `draftOrder`: Stores the current draft order.
  - `apiError`: Indicates if there was an error during an API call.
  - `endpoint`: Stores the current API endpoint being called.
  - `isTealiumFiredOnPageLoad`: Tracks if Tealium has been fired on page load.

- **Functions**:
  - `makeCheckoutXapiCall`: Makes an API call to the checkout XAPI endpoint.

#### Example Usage

```tsx
import { CheckoutProvider } from 'path-to-checkout-provider';

const App = () => (
  <CheckoutProvider>
    <YourComponent />
  </CheckoutProvider>
);
```

### PaymentsProvider

#### Description

The `PaymentsProvider` context manages the state and actions related to payment methods. It handles the addition, removal, and selection of payment methods during the checkout process.

#### Key Functions and State

- **State Variables**:

  - `paymentMethods`: Stores the list of available payment methods.
  - `selectedPaymentMethod`: Stores the currently selected payment method.

- **Functions**:
  - `addPaymentMethod`: Adds a new payment method.
  - `removePaymentMethod`: Removes an existing payment method.
  - `selectPaymentMethod`: Selects a payment method for the current order.

#### Example Usage

```tsx
import { PaymentsProvider } from 'path-to-payments-provider';

const App = () => (
  <PaymentsProvider>
    <YourComponent />
  </PaymentsProvider>
);
```

### CheckoutProviderWrapper

TODO Add documentation for CheckoutProviderWrapper

### CheckoutUiStateProvider

TODO Add documentation for CheckoutUiStateProvider

## Disclaimer

This was generated using GitHub Copilot and revised by @jeromer
