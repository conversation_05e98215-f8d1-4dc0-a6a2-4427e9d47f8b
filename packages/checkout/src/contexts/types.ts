import { ItemBrand } from '@ecom-next/core/migration/product-image/types';
import { Locale } from '@ecom-next/utils/server';
import { CHECKOUT_XAPI_ENDPOINTS } from './constants';

type PanelObject = object;
type CurrencyCode = 'USD' | 'CAD';
export type BRAND = 'br' | 'on' | 'gapfs' | 'brfs' | 'at' | 'gap';

type PickupPanelObject = {
  bagTypeInfo: string;
  conditionals: { isBopisSMSEnabled: boolean };
  isPickUpPersonSaved: boolean;
  pickupPersonName: string;
  pickupPersonPhoneNumber: string;
  storeInfo: StoreInfoObject;
  storePickupInfoList: DeliveryGroupList[];
};

export type PickupAddressObj = {
  addressLine1: string;
  addressLine2: string;
  city: string;
  country: string;
  default: boolean;
  phone: string;
  postalCode: string;
  state: string;
};

type StoreInfoObject = {
  address: PickupAddressObj;
  storeCloseHours: string;
  storeId: string;
  storeName: string;
  storeOpenHours: string;
  storeTimeZone: string;
};

type BagTypeProps = {
  bopisOnlyBag: boolean;
  omniBag?: boolean;
  shipToHomeBag?: boolean;
};

export type BopisItems = {
  brand: string;
  color: string;
  discountedPrice: string;
  imagePath: string;
  inventoryStatus: string;
  price: Price;
  productName: string;
  productSkuId: string;
  productStyleId: string;
  productTypeName: string;
  quantity: string;
  regularPrice: string;
  salePrice: string;
  size: string;
  storeId: string;
  totalPrice: string;
};

export type BagLineItem = BopisItems & {
  appliedDiscounts: [
    {
      automatic: boolean;
      code: string;
      displayName: string;
      id: string;
      promoDiscountTotal: number;
      promoType: string;
      promotionDescription: string;
      shippingPromo: boolean;
    },
  ];
  price: {
    discountedPrice: number;
    mupPromoPrice: number;
    regularPrice: number;
    salePrice: number;
  };
};

export type BagPanel = {
  bagTotal: number;
  bopisItems: BopisItems[];
  lineItems: BagLineItem[];
};

export type CheckoutPanel = {
  bagType: BagTypeProps;
  // TODO: Once rules engine changes done then pass the status of the panel.
  checkoutModuleStatus?: string;
  conditionals?: {
    isOrderReadyToPlace?: boolean;
  };
  lineItems?: LineItem[];
  locale: Locale;
  pickupOrderType?: string;
  totalPrice: number;
};

export type OrderSummaryPanel = {
  donationsTotal: number;
  estimatedTax: number;
  giftCardsTotal: number;
  hasDonations: boolean;
  hasGiftCards: boolean;
  hasPromotion: boolean;
  hasRewards: boolean;
  hasSavings: boolean;
  hasShippingPrice: boolean;
  isAfterpaySelected: boolean;
  isBopisOnly: boolean;
  markdownPromoSavings: number;
  markdownSubtotal: number;
  promotionDescription: boolean;
  retailDeliveryFee: number;
  rewardsSavings: number;
  rewardsSubTotal: number;
  shippingMessage: string;
  shippingPrice: number;
  subTotal: number;
  totalPrice: string;
  totalSavings: number;
};

export interface ShippingMethodDetails {
  deliveryDate: string;
  deliveryMonth: string;
  deliveryWeekDay: string;
  isEnabled: boolean;
  isSelected: boolean;
  maxDays: number;
  minDays: number;
  shippingId: number;
  shippingMethodName: string;
  shippingPrice: number;
  shippingTypeDescription: string;
  shippingTypeId: number;
}

interface Price {
  discountedPrice: number;
  percentageOff: number;
  regularPrice: number;
  salePrice: number;
}

interface BackOrderShippingDate {
  deliveryDate: string;
  deliveryMonth: string;
  deliveryWeekDay: string;
}

export type ProductFlag = {
  date: string;
  message: string;
  type: string;
};

export interface LineItemDetails {
  appliedDiscounts: PanelObject;
  autoAdded: boolean;
  backOrdershippingDate: BackOrderShippingDate;
  brand: ItemBrand;
  color: string;
  colorStyleNumber: string;
  customerChoiceNumber: string;
  discountedPrice: string;
  eligibleReturnLocationCode: string;
  estimatedShippingDate: string;
  excludedFromPromotion: boolean;
  giftWrappable: boolean;
  imagePath: string;
  inventoryStatus: string;
  inventoryStatusId: number;
  isBackOrderItem: boolean;
  isDropShipItem: boolean;
  isExcludedFromRewardFreeShipping: boolean;
  itemId: string;
  madeToOrder: boolean;
  marketingFlag: string;
  merchandiseType: string;
  noIntlShipping: boolean;
  noReturnItem: boolean;
  price: Price;
  primaryCategoryName: string;
  productFlags: ProductFlag[];
  productName: string;
  productSkuId: string;
  productStyleDescription: string;
  productStyleId: string;
  productTypeName: string;
  productURL: string;
  quantity: string;
  regularPrice: string;
  returnByMailItem: boolean;
  salePrice: string;
  showSellerName: boolean;
  size: string;
  storeId: string;
  totalPrice: string;
  variantDescription: string;
  vendorId: string;
  vendorStyleNumber: string;
  vendorUPCCode: string;
  webVendorName: string;
}

export interface DeliveryGroupLists {
  deliveryGroupId: string;
  isDropShipItem?: boolean;
  lineItemList: LineItemDetails[];
  offerDetails: string;
  shippingMethodList: ShippingMethodDetails[];
}

export interface SummaryViewLists {
  deliveryBy: string;
  deliveryDate: string;
  deliveryMonth: string;
  deliveryWeekDay: string;
  id: string;
  itemCount: number;
  name: string;
  shippingPrice: number;
}

export interface DeliveryGroupPanel {
  currency: string;
  deliveryGroupLists: DeliveryGroupLists[];
  has4101Error: boolean;
  hasDropShipItems: boolean;
  isEasyEnrollEligible: boolean;
  summaryView: SummaryViewLists[];
}

export interface ShippingAddress {
  addressId?: string;
  addressLine1: string;
  addressLine2?: string;
  addressLocationType?: string;
  city: string;
  country: string;
  default?: boolean;
  deliveryPointValidation?: string;
  firstName: string;
  isDefault?: boolean;
  isSelected: boolean;
  lastName: string;
  phone: string;
  postalCode: string;
  shipToUpsIndicator?: boolean;
  state: string;
  verificationStatus?: string;
}

export interface ShippingAddressError {
  errorCode: string;
}

export interface ShippingAddressPanel {
  deliveryGroupId?: string;
  formInitialValues?: {
    fullName: string;
    phone: string;
  };
  giftingSavedMessage?: string;
  hasNoEligibleShippingItems: boolean;
  hasSavedShippingAddresses?: boolean;
  hasShippingAddressSelected: boolean;
  lineItemCount?: number;
  notEligibleShippingItems: LineItem[];
  shippingAddressErrorList?: ShippingAddressError[];
  shippingAddressList?: ShippingAddress[];
  shouldDisplayGifting?: boolean;
  showUPS?: boolean;
  summaryView?: {
    fullAddress: string;
    fullName: string;
    isUPSStore: boolean;
  };
}

export interface AddressValidationPayload {
  address: {
    addressLine1: string;
    addressLine2?: string;
    cityName: string;
    countryCode: string;
    postalCode: string;
    stateProvinceCode: string;
  };
}

export interface DeliveryGroupList {
  deliveryGroupId: string;
  isBackOrder: boolean;
  isBackOrderItem: boolean;
  isMadeToOrder: boolean;
  lineItemList: LineItemDetails[];
  offerDetails: string;
  pickupOrderType: string;
  shippingMethodList: ShippingMethodDetails[];
  storePickupInfo: {
    storeInfo: {
      address: {
        addressLine1: string;
        city: string;
        country: string;
        postalCode: string;
      };
      name: string;
    };
  };
  webVendorName: string;
}

export interface PaymentBillingAddress {
  addressId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  country: string;
  default?: boolean;
  deliveryPointValidation?: string;
  firstName: string;
  isDefault?: boolean;
  isSelected?: boolean;
  lastName: string;
  phone: string;
  postalCode: string;
  state: string;
  verificationStatus?: string;
}

interface CreditCard {
  billingAddress: PaymentBillingAddress;
  cardBrand: string;
  cardToken: string;
  cardTokenFormat: string;
  cardType: string;
  cvvRequired: boolean;
  cvvValidated: boolean;
  defaultCard: boolean;
  expiryMonth: string;
  expiryYear: string;
  isPLCC: boolean;
  lastFourDigits: string;
  temporary: boolean;
}

interface PaymentError {
  errorCode: string;
  moreInfo?: string;
  userMessage?: string;
}

export interface CardInfo {
  billingAddressId: string;
  brand: string;
  cardBrand: string;
  cardBrandNumber?: string;
  cardId: string;
  cardNumber: string;
  cardType: number;
  cvvRequired: boolean;
  defaultCard: boolean;
  expirationMonth: string;
  expirationYear: string;
  isCvvValidated: boolean;
  isPLCC: boolean;
  lastFourDigits: string;
  saveCCInProfile: boolean;
  temporaryCardIndicator: boolean;
}

export interface CardInfoForImage {
  cardBrand: string;
  cardNumber: string;
  cardType: number;
}

export interface DigitalWalletCardInfo {
  cardNumber: string;
  cardType: number;
  saveCCInProfile: boolean;
}

interface DigitalWallet {
  type: 'PAYPAL' | 'AFTERPAY' | 'KLARNA';
}

export interface PaymentMethod {
  billingAddress: PaymentBillingAddress;
  cardInfo: CardInfo;
  creditCard?: CreditCard;
  digitalWallet?: DigitalWallet;
  errorList?: PaymentError[];
  isCardExpired: boolean;
  isDefault: boolean;
  isSelected: boolean;
  paymentId?: string;
  paymentIndex?: number;
}

export interface PaymentShippingAddress {
  addressId: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  country: string;
  firstName: string;
  isDefault?: boolean;
  // Below 2 props are for selected Shipping address
  isSelected?: boolean;
  lastName: string;
  phone: string;
  postalCode: string;
  state: string;
}

export interface PaymentPanel {
  billingAddress: PaymentBillingAddress;
  billingErrors: [];
  conditionals: {
    bopisOnlyBag: boolean;
    cardProcessingError: boolean;
    cvvInvalidErrors: boolean;
    hasBillingAddressId: boolean;
    hasBopisItems: boolean;
    hasRewards: boolean;
    hasShipToAddressItems: boolean;
    hasStoreName: boolean;
    isAfterPayProcessingError: boolean;
    isAfterpaySelected: boolean;
    isBarclayCodeEnabled: boolean;
    isBarclaySavingCalculatorEnabled: boolean;
    isBopisGiftCardPurchase: boolean;
    isGiftCardCoversOrder: boolean;
    isGiftCardProcessingError: boolean;
    isKlarnaProcessingError: boolean;
    isLegacyRewardsCodeDisplayed: boolean;
    isOrderTotalZero: boolean;
    isPaymentPanelError: boolean;
    isPaypalButtonEnabled: boolean;
    isPaypalDisplayed: boolean;
    isPaypalProcessingError: boolean;
    isPaypalSelected: boolean;
    isPhoneError: boolean;
    isUpsAccessPoint: boolean;
    omniBag: boolean;
    paymentFailedError: boolean;
    shippingAddressSelected: boolean;
    showAfterPayButton: boolean;
    showGiftCard: boolean;
    showPaypalButton: boolean;
  };
  errors: string[];
  isCardInValid: boolean;
  paymentId: string;
  paymentMethods: PaymentMethod[];
  selectedAddress: PaymentShippingAddress;
  selectedPaymentMethod: PaymentMethod;
  shippingAddresses: PaymentShippingAddress[];
  summaryView: {
    cardBrand: string;
    cardNumber: string;
    cardType: number;
    giftCards: [];
    lastFourDigits: string;
  };
  totalPrice: number;
  validGiftCardSubTotal: number;
}

export interface GiftCard {
  amount: string;
  cardNumber: string;
  giftCardIdRo: string;
  paymentId: string;
  remainingAmount: string;
}
export interface GiftCardPanel {
  appliedGiftCards: GiftCard[];
  conditionals: {
    hasGiftCardsApplied: boolean;
  };
  errors: string[];
  isBopisGiftCardPurchase: boolean;
  isFormShown: boolean;
  isGiftcardPanelExpanded: boolean;
  totalPrice: number;
}

export type UserState = 'authenticated' | 'guest';

// TODO: fix this
export interface Session {
  session: {
    email: string;
    recognition_status: UserState;
  };
}

export type MarketCode = 'US' | 'CA';
export interface SignInPanel {
  emailId: string;
  isGuest: boolean;
  isLoggedIn: boolean;
  isRecognized: boolean;
  lineItems: LineItemDetails[];
  marketCode: MarketCode | Lowercase<MarketCode>;
}

export type PointsToValueList = { amount: number; points: number };

export interface AppliedPromotionsArbitrationMessage {
  promoCode: string;
  promoId: string;
  promoMessageCode: string;
  promoMsg: string;
}

type AppliedPromotions = {
  arbitrationMessage?: AppliedPromotionsArbitrationMessage;
  autoApplied: boolean;
  discountTotal: number;
  displayName: string;
  isApplied: boolean;
  promoDescription: string;
  promoId: string;
  requiresLogin?: boolean;
  rewardCode: string;
  rewardType: string;
};

export interface RewardsPanel {
  conditionals: {
    hasErrors: boolean;
    isPartialResponse: boolean;
    isPromoRewardsApplied: boolean;
  };
  merchandiseSubTotal: number;
  pointRewards: {
    actionParams: {
      loyaltyEventType: string;
      loyaltyTierStatus: string;
      reasonCode: string;
    };
    pointRewardsCard: {
      amountApplied: number;
      availablePoints: number;
      hasEnoughPoints: boolean;
      isApplied: boolean;
      lessThanHundred: number;
      minPoints: number;
      pointsApplied: number;
      pointsToValueList: PointsToValueList[];
    };
  };
  promoRewardsList: AppliedPromotions[];
  rewardErrorCode: string;
}

export interface DonationsPanel {
  availableDonations: {
    availableAmounts: Array<{ amount: number }>;
    brand: string;
    charityInfo: string;
    charityName: string;
    imageUri: string;
    taxId: string;
  };
  availableDonationsDropdownValues: Array<number>;
  donationId: string;
  selectedDonationOption: {
    amount: number;
  };
}

export interface PayPalLightBoxPanel {
  amount: number;
  currency: CurrencyCode;
  displayName: string;
  enableShippingAddress: boolean;
  flow: string;
  intent: string;
  locale: string;
  shippingAddressEditable: boolean;
  shippingAddressOverride: {
    city: string;
    countryCode: 'US' | 'CA';
    line1: string;
    line2?: string;
    postalCode: string;
    recipientName: string;
    state: string;
  };
}

export interface Panels {
  bagPanel: BagPanel;
  checkoutPanel: CheckoutPanel;
  deliveryGroupPanel: DeliveryGroupPanel;
  donationPanel: DonationsPanel;
  giftCardPanel: GiftCardPanel;
  orderSummaryPanel: OrderSummaryPanel;
  paymentPanel: PaymentPanel;
  paypalLightBoxPanel: PayPalLightBoxPanel;
  pickupPanel: PickupPanelObject;
  placeOrderPanel: {
    currencyCode: CurrencyCode;
    shippingChargesSubTotal?: number;
  };
  rewardsPanel: RewardsPanel;
  shippingAddressPanel: ShippingAddressPanel;
  signInPanel: SignInPanel;
}

// for now I have used all the viewtag data ptoperty, later we can add, modify or remove.
export interface ViewTag {
  afterpay_error_message?: unknown | string;
  bopis_enabled?: boolean;
  bopis_order_type?: string;
  brand_code?: string;
  brand_mix?: string;
  brand_name?: string;
  brand_number?: string;
  brand_short_name?: string;
  business_unit_abbr_name?: string;
  business_unit_description?: string;
  business_unit_id?: number;
  cardholder_status?: string;
  category_preference?: string;
  channel?: string;
  checkout_cta_name?: string;
  checkout_module_status?: string;
  checkout_type?: string;
  checkout_version?: string;
  country_code?: string;
  customer_uuid?: string;
  division_preference?: string;
  encrypted_customer_email?: string;
  encrypted_customer_email_mm?: string;
  event_name?: string;
  hashed_customer_email?: string;
  id?: string;
  language_code?: string;
  lv2?: string;
  mtl_member_status?: string;
  order_gross_merchandise?: number;
  page_name?: string;
  page_type?: string;
  paypal_error_message?: string;
  pfs_order_shipping_method?: string;
  product_brand?: string[];
  product_category?: string[];
  product_cc_id?: string[];
  product_dropship?: string[];
  product_gross_merchandise?: string[];
  product_gross_retail?: string[];
  product_id?: string[];
  product_markdown_amount?: string[];
  product_name?: string[];
  product_net_demand?: string[];
  product_page_type?: string[];
  product_quantity?: number[];
  product_seller_id?: string[];
  product_seller_name?: string[];
  product_sku?: string[];
  recognition_status?: string;
  shipping_options?: string;
  tier_status?: string;
}

export interface LineItem {
  autoAdded?: boolean;
  backOrdershippingDate?: boolean;
  brand: string;
  color?: string;
  colorStyleNumber?: string;
  customerChoiceNumber: string;
  effectivePrice?: string;
  inventoryStatusId?: number;
  isBackOrderItem?: boolean;
  itemId?: string;
  madeToOrder: boolean;
  markdownPrice?: string;
  merchandiseType?: string;
  mergeType?: string;
  name?: string;
  noReturnItem?: boolean;
  parentStyleNumber?: string;
  price: Price;
  primaryCategoryName?: string;
  productName?: string;
  productNameTmp?: string;
  productSkuId: string;
  productStyleId?: string;
  productTypeName?: string;
  quantity: string;
  returnByMailItem?: boolean;
  size?: string;
  variantDescription?: string;
  vendorId: string;
  webVendorName: string;
}

export interface EvergreenType {
  AttributeName: 'CARDSTATUS' | 'CATPREF' | 'DIVPREF' | 'BRDS';
  AttributeValue: string;
}

export interface PageContextData {
  brandAbbr: string;
  locale: Locale;
  market: string;
}

export interface PersonalizationData {
  customerAttributes?: {
    mtlMember?: string;
    mtlMemberId?: string;
    tier?: string;
  };
  customerUUID?: string;
  featureSelections?: { Evergreens?: EvergreenType[] };
  userContext?: {
    guest?: boolean | string;
    isAnonymousUser?: boolean | string;
    isLoggedInUser?: boolean | string;
    isRecognizedUser?: boolean | string;
    maskedEmailAddress?: string;
    userEmailEncrypt?: string;
    userEmailEncrypt256?: string;
    userEmailMMEncrypt?: string;
  };
}

/**
 * The draft order response nests the panels in the `panels` property.
 */
export interface DraftOrder extends Session {
  draftOrderId: string;
  orderNumber?: string;
  panels: Panels;
}

/**
 * When the draft order is is passed as the value for checkout context,
 * the panels are spread as siblings of other properties and **NOT**
 * nested inside the `panels` property.
 */
export type DraftOrderContextValue = Session &
  Panels & {
    draftOrderId: string;
    orderNumber?: string;
  };

type ErrorDetail = {
  errorCode: string;
};

export type ApiError = {
  errorDetails?: ErrorDetail[];
  status?: number;
};

export interface CheckoutContext {
  apiError: ApiError | null;
  draftOrder: DraftOrderContextValue;
  endpoint: string;
  loading: boolean;
  makeCheckoutXapiCall: MakeCheckoutXapiCall;
}

export type CheckoutXapiEndpoint = keyof typeof CHECKOUT_XAPI_ENDPOINTS;

type StructuredEndpoints = 'fetchDraftOrder' | 'createPaymentMethod' | 'selectShippingMethod' | 'selectShippingAddress';
type UnstructuredEndpoints = keyof Omit<CheckoutXapiEndpoint, StructuredEndpoints>;

interface SelectShippingMethodPayload {
  deliveryGroupId: string;
  draftOrderId: string;
  shippingAddress?: object;
  shippingMethod?: {
    shippingId?: number;
    shippingTypeId?: number;
  };
}

export interface SelectShippingAddressPayload {
  deliveryGroupId?: string;
  draftOrderId: string;
  shippingAddress?: ShippingAddress;
}

export interface CardInfoPayload {
  brand: string;
  cardBrandNumber?: string | null;
  cardHolderName: string;
  cardId?: string;
  cardNumber: string;
  cardToken?: string | undefined;
  cardType?: number;
  creditCardSecurityCode: string;
  expirationMonth: string | null;
  expirationYear: string | null;
  isDefault: boolean;
  saveCCInProfile?: boolean;
  temporaryCardIndicator: boolean;
  type: string;
}

export interface CreatePaymentMethodPayload {
  draftOrderId: string;
  paymentId?: string;
  paymentMethod: {
    billingAddress?: PaymentBillingAddress;
    cardInfo?: CardInfoPayload;
    paymentProvider?: {
      cardBrand?: string;
      cardType?: string;
      deviceData?: string;
      providerName: string;
      token: string;
    };
  };
  vendorOrderDetails?: {
    orderIdentifier: string;
    vendorIdentifier: string;
  };
}

export interface UpdatePaymentMethodPayload {
  paymentMethod: {
    billingAddress: PaymentBillingAddress;
    cardInfo: CardInfoPayload;
    draftOrderId: string;
    paymentId: string;
    paymentProvider?: {
      cardBrand?: string;
      cardType?: string;
      deviceData?: string;
      providerName: string;
      token: string;
    };
  };
}

export interface SelectPaymentMethodPayload {
  paymentMethod: {
    billingAddress: PaymentBillingAddress;
    cardInfo: {
      brand: string;
      cardId: string;
      cardNumber: string;
      cardType: number;
      expirationMonth: string;
      expirationYear: string;
      isDefault: boolean;
      saveCCInProfile: boolean;
      temporaryCardIndicator: boolean;
      type: string;
    };
    draftOrderId: string;
    paymentId: string;
  };
}

export interface RemovePaymentPayload {
  paymentMethod: {
    draftOrderId: string;
    paymentId: string;
  };
}

export interface CreateAfterPayPaymentMethodActionPayload {
  draftOrderId: string;
  paymentId: string;
  paymentMethod: {
    billingAddress?: {
      addressLine1: string;
      addressLine2?: string;
      city: string;
      country: string;
      firstName: string;
      lastName: string;
      phone: string;
      postalCode: string;
      state: string;
    };
    paymentProvider: {
      cardBrand: string;
      cardType: string;
      providerName: 'AFTERPAY';
      token: string;
    };
  };
  vendorOrderDetails: {
    vendorIdentifier: 'AFTERPAY';
  };
}

interface ApplyRewardsPayload {
  draftOrderId: string;
  rewardDetails: {
    amount: number;
    loyaltyEventType: string;
    loyaltyPoints: number;
    loyaltyTierStatus: string;
    reasonCode: string;
  };
}
interface ApplyDonationPayload {
  donation: {
    charityName: string;
    price: number | string;
  };
  donationId: string;
  draftOrderId: string;
}

interface RemoveGiftCardPayload {
  draftOrderId: string;
  giftCards: { giftCardId: string; giftCardNumber: string }[];
  paymentId: string;
}

interface AddGiftCardPayload {
  draftOrderId: string;
  giftCards: {
    cardToken: string;
    number: string;
    pin: string;
  }[];
}

interface PromosPayload {
  draftOrderId: string;
  rewards: {
    rewardCode: string;
  }[];
}

interface UpdateGiftMessagePayload {
  deliveryGroupId?: string;
  draftOrderId: string;
  giftMessage: {
    giftReceiptIndicator: boolean;
    message: string;
  };
}

export type CreateShippingAddressPayload = {
  deliveryGroupId?: string;
  draftOrderId: string;
  shippingAddress: ShippingAddress;
};

export type MakeCheckoutXapiConfig =
  | { endpoint: 'fetchDraftOrder' }
  | { endpoint: 'afterPayButton' }
  | { body: SelectShippingMethodPayload; endpoint: 'selectShippingMethod' }
  | { body: SelectShippingAddressPayload; endpoint: 'selectShippingAddress' }
  | { body: CreateShippingAddressPayload; endpoint: 'createShippingAddress' }
  | { body: AddressValidationPayload; endpoint: 'validateShippingAddress' }
  | { body: CreatePaymentMethodPayload; endpoint: 'createPaymentMethod' }
  | { body: UpdatePaymentMethodPayload; endpoint: 'updatePaymentMethod' }
  | { body: SelectPaymentMethodPayload; endpoint: 'selectPaymentMethod' }
  | { body: RemovePaymentPayload; endpoint: 'removePaymentMethod' }
  | { body: CreateAfterPayPaymentMethodActionPayload; endpoint: 'createPaymentMethod' }
  | { body: object; endpoint: 'placeOrderAction' }
  | { body: ApplyRewardsPayload; endpoint: 'applyRewards' }
  | { body: { draftOrderId: string }; endpoint: 'removeRewards' }
  | { body: ApplyDonationPayload; endpoint: 'applyDonation' }
  | { body: RemoveGiftCardPayload; endpoint: 'removeGiftCard' }
  | { body: AddGiftCardPayload; endpoint: 'applyGiftCard' }
  | { body: PromosPayload; endpoint: 'applyPromos' }
  | { body: PromosPayload; endpoint: 'removePromos' }
  | { body: UpdateGiftMessagePayload; endpoint: 'updateGiftMessage' }
  | { body: object; endpoint: 'updatePickupInfoAction' }
  | { body?: object; endpoint: UnstructuredEndpoints };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type MakeCheckoutXapiCall = (config: MakeCheckoutXapiConfig) => Promise<any>;

export type TealiumLinkProps = {
  brandShortName: string;
  checkout_cta_name?: string;
  countryCode: string;
  errorMessage?: string;
  event_name: string;
  module: string;
  promoCode?: string;
  recognition_status?: string | undefined;
};

interface ComponentData {
  data: Record<string, unknown>;
  description: string;
  experimentRunning: boolean;
  instanceName: string;
  lazy: boolean;
  name: string;
  redpointExperimentRunning: boolean;
  type: string;
  useGreyLoadingEffect: boolean;
}

export interface RedpointPersonalizedMarketingData {
  components: Record<string, ComponentData>;
}
