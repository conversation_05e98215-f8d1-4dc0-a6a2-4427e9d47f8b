import { displayPayPal, displayApplePay, showExpressCheckout } from '../displayThirdPartyUtil';
import { DraftOrderContextValue } from '../../contexts/types';
import { draftOrder as draftOrderMock } from '../__mockData__/draftorder-mock';

const draftOrder = { ...draftOrderMock.panels } as unknown as DraftOrderContextValue;

let isApplePayEnabled = false;
let isPaypalEnabled = false;

const mockApplePaySession = {
  canMakePayments: jest.fn(() => true),
  canMakePaymentsWithActiveCard: jest.fn(() => true),
  openPaymentSetup: jest.fn(),
  supportsVersion: jest.fn(() => true),
  prototype: {},
} as unknown as typeof ApplePaySession;

beforeEach(() => {
  window.ApplePaySession = mockApplePaySession;
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('displayPayPal', () => {
  it('should return false when PayPal is not enabled', () => {
    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
  });

  it('should return true when all conditions are met', () => {
    isPaypalEnabled = true;

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(true);
  });

  it('should return false when there is a backorder item', () => {
    // @ts-ignore
    draftOrder.checkoutPanel.lineItems[0].inventoryStatusId = 4;

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
  });
});

describe('displayApplePay', () => {
  it('should return false when Apple Pay is not enabled', () => {
    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });

  it('should return true when all conditions are met', () => {
    isApplePayEnabled = true;

    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(true);
  });

  it('should return false when there is Bopis item in the bag', () => {
    draftOrder.paymentPanel.conditionals.hasBopisItems = true;

    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });

  it('should returns false if ApplePaySession is not available', () => {
    // @ts-ignore
    window.ApplePaySession = undefined;

    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });
});

describe('mutual conditions for both PayPal and Afterpay', () => {
  // @ts-ignore
  draftOrder.checkoutPanel.lineItems[0].inventoryStatusId = 1;
  draftOrder.checkoutPanel.totalPrice = 50;

  it('should return false when omniBag is true', () => {
    draftOrder.paymentPanel.conditionals.omniBag = true;

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });

  it('should return false when order total is zero', () => {
    draftOrder.paymentPanel.conditionals.isOrderTotalZero = true;

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });

  it('should return false when there are multiple stores', () => {
    // @ts-ignore
    draftOrder.pickupPanel.storePickupInfoList = [{}, {}];

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
  });

  it('should return false when there is a dropship item', () => {
    // @ts-ignore
    draftOrder.checkoutPanel.lineItems[0].productTypeName = 'Dropship';

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });

  it('should return false when there is a made-to-order item', () => {
    // @ts-ignore
    draftOrder.checkoutPanel.lineItems[0].madeToOrder = true;

    expect(displayPayPal(draftOrder, isPaypalEnabled)).toBe(false);
    expect(displayApplePay(draftOrder, isApplePayEnabled)).toBe(false);
  });
});

describe('showExpressCheckout', () => {
  it('should return true when Express PayPal is enabled and PayPal rules are met', () => {
    const isExpressPayPalEnabled = true;
    const isExpressApplePayEnabled = false;
    const displayPayPalRules = true;
    const displayApplePayRules = false;

    expect(showExpressCheckout(isExpressPayPalEnabled, isExpressApplePayEnabled, displayPayPalRules, displayApplePayRules)).toBe(true);
  });

  it('should return true when Express Apple Pay is enabled and Apple Pay rules are met', () => {
    const isExpressPayPalEnabled = false;
    const isExpressApplePayEnabled = true;
    const displayPayPalRules = false;
    const displayApplePayRules = true;

    expect(showExpressCheckout(isExpressPayPalEnabled, isExpressApplePayEnabled, displayPayPalRules, displayApplePayRules)).toBe(true);
  });

  it('should return false when neither PayPal nor Apple Pay flags are enabled', () => {
    const isExpressPayPalEnabled = false;
    const isExpressApplePayEnabled = false;
    const displayPayPalRules = true;
    const displayApplePayRules = true;

    expect(showExpressCheckout(isExpressPayPalEnabled, isExpressApplePayEnabled, displayPayPalRules, displayApplePayRules)).toBe(false);
  });

  it('should return false when neither PayPal nor Apple Pay rules are met', () => {
    const isExpressPayPalEnabled = true;
    const isExpressApplePayEnabled = true;
    const displayPayPalRules = false;
    const displayApplePayRules = false;

    expect(showExpressCheckout(isExpressPayPalEnabled, isExpressApplePayEnabled, displayPayPalRules, displayApplePayRules)).toBe(false);
  });
});
