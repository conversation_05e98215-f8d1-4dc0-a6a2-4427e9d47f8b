import { Breadcrumbs } from './../types';

const TRACKING_COMPONENT_NAME = 'breadcrumbs';

const buildDivisionUrl = (name: string, url: string): string => {
  return url ? url.concat('&nav=', encodeURIComponent(`${TRACKING_COMPONENT_NAME}:${name}::`)) : '';
};

export const appendDivisionUrlTracking = (breadcrumbs: Breadcrumbs): Breadcrumbs => {
  const name = breadcrumbs?.division?.name ?? '';
  const url = breadcrumbs?.division?.url ?? '';

  return {
    ...breadcrumbs,
    division: {
      ...breadcrumbs.division,
      url: buildDivisionUrl(name, url),
    },
  };
};
