import { getFacetsData } from '@seo/category-facets';
import logger from '../../../lib/logger';
import { getFacetsDataForValidFacetsParams, invalidFacetsValuesHandler, validateCid, getValidFacets, getIndexableFeatureVariablesValues } from '.';

jest.spyOn(logger, 'info');
jest.spyOn(logger, 'warn');
jest.mock('@seo/category-facets', () => ({
  getFacetsData: jest.fn(),
}));

describe('sanitize-req-params', () => {
  describe('getIndexableFeatureVariableValues', () => {
    it('should return feature variable values', () => {
      const mockIndexableFacets = {
        sleeveLength: {
          name: 'sleeveLength',
          indexableUrlFacetParamKey: 'sleeve-length',
          enableFacet: true,
        },
        color: {
          name: 'color',
          indexableUrlFacetParamKey: 'color',
          enableFacet: true,
        },
        fabricMaterial: {
          name: 'fabricMaterial',
          indexableUrlFacetParamKey: 'fabric-material',
          enableFacet: true,
        },
        style: {
          name: 'style',
          indexableUrlFacetParamKey: 'style',
          enableFacet: true,
        },
        fit: {
          name: 'fit',
          indexableUrlFacetParamKey: 'fit',
          enableFacet: true,
        },
        occasion: {
          name: 'occasion',
          indexableUrlFacetParamKey: 'occasion',
          enableFacet: true,
        },
        collection: {
          name: 'collection',
          indexableUrlFacetParamKey: 'collection',
          enableFacet: false,
        },
        activity: {
          name: 'activity',
          indexableUrlFacetParamKey: 'activity',
          enableFacet: false,
        },
      };

      const mockFacetsRefreshTime = 86400000;

      const mockFacetsTTL = 604800000;

      const mockFeatureVariables = {
        'cat-alt-view-us-gap': {
          categoryId: '5664,13658,1011761,1041308,1117374,6998,80799,5156,1120779,6013,1008565,1014425,1025764,1050384',
        },
        'bopis-us-on': { fulfillmentDisplay: true },
        'bopis-ca-on': { fulfillmentDisplay: true },
        'bopis-us-gap': { fulfillmentDisplay: true },
        'bopis-ca-gap': { fulfillmentDisplay: true },
        'bopis-us-gapfs': { fulfillmentDisplay: true },
        'bopis-us-at': { fulfillmentDisplay: true },
        'bopis-ca-at': { fulfillmentDisplay: true },
        'bopis-us-br': { fulfillmentDisplay: true },
        'bopis-ca-br': { fulfillmentDisplay: true },
        'bopis-us-brfs': { fulfillmentDisplay: true },
        'cat-model-toggle-us': {
          gapCIDs: '5664,13658,1011761,1041308',
          gapfsCIDs: '3034136',
          onCIDs:
            '85729,1124176,35158,79586,5475,5508,1030828,1031032,1162886,1009584,1034235,1162568,15292,1051876,72808,50058,1182448,72087,72091,1035712,1011528,68066,1182450,1182451,3008611,3008632,3008587,3008585,3008595,3008604,3008607,300633,3008634,3008635,10018,20408,55474,1185233,26190,96964,1167062,3007908,1174785,3017177,3017727,3018094',
          sizeGroups: {
            gap: {},
            on: {},
            br: {},
            at: {},
            gapfs: {},
          },
        },
        'cat-model-toggle-ca': {
          atCIDs: 'all',
          brCIDs: '5030,5389,35878,1072457,67595',
          gapCIDs: 'all',
          onCIDs:
            '1171870,1169983,5475,5508,10018,15292,35158,50058,72087,72808,79586,85729,1030828,1031032,1034235,1051876,1124176,1182450,1182451,72091,1035712,1011528,35158,68066,1182448,1188401,1159705,1166584,10018,20408,55474',
          sizeGroups: { gap: {}, on: {}, br: {}, at: {} },
        },
        'seo-indexable-flex-facets': {
          indexableFacets: mockIndexableFacets,
          facetsRefreshTime: mockFacetsRefreshTime,
          facetsTTL: mockFacetsTTL,
          handleInvalidFacetsValues: true,
        },
      };

      const result = getIndexableFeatureVariablesValues(mockFeatureVariables);

      expect(result).toEqual({
        indexableFacetsFFConfig: mockIndexableFacets,
        facetsRefreshTime: mockFacetsRefreshTime,
        facetsTTL: mockFacetsTTL,
        handleInvalidFacetsValues: true,
      });
    });
  });

  describe('validateCid', () => {
    it('should return cid if it is valid', () => {
      const validatedCid = validateCid('1234');
      expect(validatedCid).toEqual('1234');
    });

    it('should log warning if cid is invalid', () => {
      const validatedCid = validateCid('ABCD');
      expect(logger.warn).toHaveBeenCalledWith(`Improper query parameter passed: cid=ABCD`);
      expect(validatedCid).toEqual('');
    });
  });

  describe('getValidFacets', () => {
    it('should return the valid and enabled facets', () => {
      const mockParameters = {
        queryParams: { cid: '5225', style: '1142578', color: 'blue', 'sleeve-length': 'sleeveless' },
        indexableFacetsFFConfig: {
          sleeveLength: {
            name: 'sleeveLength',
            indexableUrlFacetParamKey: 'sleeve-length',
            enableFacet: false,
          },
          color: {
            name: 'color',
            indexableUrlFacetParamKey: 'color',
            enableFacet: true,
          },
          fabricMaterial: {
            name: 'fabricMaterial',
            indexableUrlFacetParamKey: 'fabric-material',
            enableFacet: true,
          },
          style: {
            name: 'style',
            indexableUrlFacetParamKey: 'style',
            enableFacet: true,
          },
          fit: { name: 'fit', indexableUrlFacetParamKey: 'fit', enableFacet: true },
          occasion: {
            name: 'occasion',
            indexableUrlFacetParamKey: 'occasion',
            enableFacet: true,
          },
          collection: {
            name: 'collection',
            indexableUrlFacetParamKey: 'collection',
            enableFacet: false,
          },
          activity: {
            name: 'activity',
            indexableUrlFacetParamKey: 'activity',
            enableFacet: false,
          },
        },
      };

      const result = getValidFacets(mockParameters.queryParams, mockParameters.indexableFacetsFFConfig);

      expect(result).toEqual({ style: '1142578', color: 'blue' });
    });
  });

  describe('invalidFacetsValuesHandler', () => {
    const mockCid = 5225;
    const mockFacetsData = {
      color: {
        black: { id: '1017', name: 'Black' },
        gray: { id: '1018', name: 'Gray' },
        red: { id: '1019', name: 'Red' },
        pink: { id: '1020', name: 'Pink' },
        blue: { id: '1021', name: 'Blue' },
        green: { id: '1022', name: 'Green' },
        brown: { id: '1023', name: 'Brown' },
        multi: { id: '1025', name: 'Multi' },
        gold: { id: '1026', name: 'Gold' },
        purple: { id: '1028', name: 'Purple' },
        white: { id: '1029', name: 'White' },
        yellow: { id: '1031', name: 'Yellow' },
        orange: { id: '1034', name: 'Orange' },
        beige: { id: '1040', name: 'Beige' },
      },
      style: {
        'short-sleeve': { id: '1142578', name: 'Short Sleeve' },
        crewneck: { id: '1053997', name: 'Crewneck' },
        graphic: { id: '1130075', name: 'Graphic' },
        'v-neck': { id: '1053999', name: 'V-Neck' },
        henley: { id: '95787', name: 'Henley' },
        'pocket-tee': { id: '1130657', name: 'Pocket Tee' },
        original: { id: '1188064', name: 'Original' },
        'waffle-tee': { id: '1142579', name: 'Waffle Tee' },
        tanks: { id: '1059968', name: 'Tanks' },
        slub: { id: '1130658', name: 'Slub' },
        classic: { id: '1154150', name: 'Classic' },
        'long-sleeve': { id: '5228', name: 'Long Sleeve' },
        organic: { id: '1174543', name: 'Organic' },
        'multi-packs': { id: '1193967', name: 'Multi-Packs' },
      },
    };
    it('should return nothing if valid facets are provided', () => {
      const mockParameters = {
        cid: mockCid,
        validFacetsNames: ['color'],
        validFacets: { color: 'blue' },
        facetsData: mockFacetsData,
      };

      const result = invalidFacetsValuesHandler(mockParameters);
      expect(result).toBeUndefined();
    });

    it('should return error if invalid non-style facets are provided', () => {
      const invalidQueryFacetValue = 'blurple';
      const mockParameters = {
        cid: mockCid,
        validFacetsNames: ['color'],
        validFacets: { color: invalidQueryFacetValue },
        facetsData: mockFacetsData,
      };

      const result = invalidFacetsValuesHandler(mockParameters);
      expect(result).toEqual({
        errorType: 'Invalid Facet Value',
        message: `Invalid value "${invalidQueryFacetValue}" for facet "color" with CID "${mockCid}"`,
      });
    });

    it('should return nothing if valid style facet is provided', () => {
      const invalidQueryStyleFacetValue = '1142578';

      const mockParameters = {
        cid: mockCid,
        validFacetsNames: ['style'],
        validFacets: { style: invalidQueryStyleFacetValue },
        facetsData: mockFacetsData,
      };

      const result = invalidFacetsValuesHandler(mockParameters);
      expect(result).toBeUndefined();
    });

    it('should return error if invalid style facet is provided', () => {
      const invalidQueryStyleFacetValue = 1234;

      const mockParameters = {
        cid: mockCid,
        validFacetsNames: ['style'],
        validFacets: { style: invalidQueryStyleFacetValue },
        facetsData: mockFacetsData,
      };

      const result = invalidFacetsValuesHandler(mockParameters);
      expect(result).toEqual({
        errorType: 'Invalid Facet Value',
        message: `Invalid value "${invalidQueryStyleFacetValue}" for facet "style" with CID "${mockCid}"`,
      });
    });
  });

  describe('getFacetsDataForValidFacetsParams', () => {
    const facetsRefreshTime = 86400000;
    const facetsTTL = 604800000;
    const brand = 'gap';
    const market = 'us';
    const locale = 'en_US';
    const cid = 5225;

    const mockHostName = 'hostNameValue';
    const req = {
      hostname: mockHostName,
    };

    const expectedHost = `https://${mockHostName}`;

    const mockSource = 'mockSource';
    const mockFacetsData = {
      source: mockSource,
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return facetsData for valid facet names', async () => {
      const validFacets = { color: 'blue' };

      getFacetsData.mockReturnValueOnce(mockFacetsData);

      const result = await getFacetsDataForValidFacetsParams({ validFacets, facetsRefreshTime, facetsTTL, brand, market, locale, cid, req });
      expect(getFacetsData).toHaveBeenCalledWith({
        brand,
        market,
        locale,
        cid,
        facetsRefreshTime,
        facetsTTL,
        facetsToTransform: Object.keys(validFacets),
        host: expectedHost,
        previewDate: undefined,
        mode: undefined,
      });
      expect(getFacetsData).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockFacetsData);
      expect(logger.info).toHaveBeenCalledWith(`Received facets data from ${mockSource}`);
    });

    it('should return nothing when no valid facet names are passed', async () => {
      const validFacets = {};

      getFacetsData.mockReturnValueOnce(mockFacetsData);

      const result = await getFacetsDataForValidFacetsParams({ validFacets, facetsRefreshTime, facetsTTL, brand, market, locale, cid, req });
      expect(getFacetsData).toHaveBeenCalledTimes(0);
      expect(result).toEqual({});
      expect(logger.info).toHaveBeenCalledTimes(0);
    });
  });
});
