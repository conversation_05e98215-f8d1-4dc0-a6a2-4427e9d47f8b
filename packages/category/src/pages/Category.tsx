import { PropsWithChildren } from 'react';
import PageWrapper, { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import Marketing from '@ecom-next/marketing-ui/category';
import { getPageContext } from '@ecom-next/utils/server';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { MarketingProvider } from '@ecom-next/marketing-ui/legacy-marketing-provider';
import { PLPStateProvider as LegacyPLPStateProvider } from '@ecom-next/plp-ui/legacy/plp-state-provider';
import { SitewideContextProvider } from '@ecom-next/core/legacy/sitewide';
import { GridButtonsProvider } from '@ecom-next/plp-ui/legacy/grid-buttons';
import { CID } from '@mui/fetchMarketing';
import { isValidCid } from '@ecom-next/marketing-ui/fetch';
import { redirect } from 'next/navigation';
import { StitchInverseStyleProvider } from '@ecom-next/core/react-stitch';
import { BreadcrumbComponent as HuiBreadcrumbComponent } from '@ecom-next/plp';
import { reportCustomAttributes } from '../utils/reportCustomAttributes';
import { Props } from '../components/legacy/components/category-page/types';
import App from './App';
import getCategoryState, { getCategoryDataPromises, getPageMetaData } from './state-builder';
import { BreadcrumbComponent, RelatedCategoriesWrapper } from './LegacyCategoryPage';
import './style.css';
import SeoComponentsProvider from './SeoComponentsProvider';

interface SeoComponentsWrapperProps extends PropsWithChildren<PageParams> {
  isPlpHuiRewriteEnabled: boolean;
}

export type CategoryProps = Omit<Props, 'breadcrumbs'>;

export async function getMetaData(props: PageParams) {
  const { searchParams } = props;

  const { cid: cidParams } = searchParams;
  const cid = Array.isArray(cidParams) ? cidParams[0] : cidParams;
  if (!isValidCid(cid)) {
    return redirect('/browse/PageNotFound.do');
  }
  return getPageMetaData(props);
}

async function SeoSchema(props: PageParams) {
  const { metaDataPromise } = await getCategoryDataPromises(props.searchParams);

  const { pageSchema } = await metaDataPromise;

  return pageSchema ? <script type='application/ld+json' dangerouslySetInnerHTML={{ __html: pageSchema }} /> : null;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function formatQueryParams(params: any) {
  const searchParams = new URLSearchParams(params);
  return `?${searchParams.toString()}`;
}

async function RelatedCategoriesServerWrapper(props: PropsWithChildren<PageParams>) {
  const { smartCrosslinksPromise, enabledFeaturesPromise, categoryDataPromise } = getCategoryDataPromises(props.searchParams);

  const [smartCrosslinks, enabledFeatures, categoryData] = await Promise.all([smartCrosslinksPromise, enabledFeaturesPromise, categoryDataPromise]);
  const { relatedCategoryLinks, cid, brand, market, abSeg } = categoryData;

  reportCustomAttributes({
    brand,
    market,
    pageType: 'category',
    searchQuery: formatQueryParams(props.searchParams),
    cid,
    crosslinkFailure: smartCrosslinks.smartCrosslinkError,
    abSeg,
  });

  return (
    <RelatedCategoriesWrapper
      smartCrosslinks={smartCrosslinks}
      relatedLinks={relatedCategoryLinks}
      enabledFeatures={enabledFeatures.enabledFeatures}
      cid={cid as CID}
      brand={brand}
    >
      {props.children}
    </RelatedCategoriesWrapper>
  );
}

const SeoComponentsWrapper: React.FC<SeoComponentsWrapperProps> = async props => {
  const { isPlpHuiRewriteEnabled, children } = props;
  const { metaDataPromise } = getCategoryDataPromises(props.searchParams);

  const [metaData] = await Promise.all([metaDataPromise]);
  const { breadcrumbs } = metaData;

  const topBreadcrumb = isPlpHuiRewriteEnabled ? <HuiBreadcrumbComponent breadcrumbs={breadcrumbs} /> : <BreadcrumbComponent breadcrumbs={breadcrumbs} />;
  const bottomBreadcrumb = isPlpHuiRewriteEnabled ? (
    <HuiBreadcrumbComponent breadcrumbs={breadcrumbs} isBottom={true} />
  ) : (
    <BreadcrumbComponent breadcrumbs={breadcrumbs} isBottom={true} />
  );

  return (
    <SeoComponentsProvider topBreadcrumb={topBreadcrumb} bottomBreadcrumb={bottomBreadcrumb}>
      {children}
    </SeoComponentsProvider>
  );
};

async function AppMain(props: PageParams) {
  const [state] = await Promise.all([getCategoryState(props)]);
  return <App {...state} />;
}

async function CatMarketingProviderWrapper(props: PropsWithChildren<PageParams>) {
  const { searchParams, children } = props;
  const { marketingDataPromise } = getCategoryDataPromises(searchParams);

  const [contentData] = await Promise.all([marketingDataPromise]);

  return <MarketingProvider value={contentData}>{children}</MarketingProvider>;
}

async function CategoryMain(props: PageParams) {
  const { searchParams } = props;
  const { cid: cidParams } = searchParams;
  const cid = Array.isArray(cidParams) ? cidParams[0] : cidParams;
  if (!isValidCid(cid)) {
    return redirect('/browse/PageNotFound.do');
  }

  const { staticDataPromise, enabledFeaturesPromise } = getCategoryDataPromises(props.searchParams);

  const [staticData, featureFlags] = await Promise.all([staticDataPromise, enabledFeaturesPromise]);

  const { enabledFeatures } = featureFlags;
  const pageContext = getPageContext();
  const { brand } = pageContext;
  const { abSeg } = staticData;
  const isBRRedesignedEnabled = !!enabledFeatures['cat-br-redesign'];
  const isPlpHuiRewriteEnabled = enabledFeatures['plp-hui-q1-rewrite'] && abSeg[`${brand}238`] === 'a';

  return (
    <>
      <div id='category-page'>
        <LegacyPLPStateProvider abSeg={abSeg}>
          <SitewideContextProvider>
            <CatMarketingProviderWrapper {...props}>
              <RelatedCategoriesServerWrapper {...props}>
                <StitchInverseStyleProvider invert={isBRRedesignedEnabled}>
                  <GridButtonsProvider>
                    <SeoComponentsWrapper isPlpHuiRewriteEnabled={isPlpHuiRewriteEnabled} {...props}>
                      <AppMain {...props} />
                    </SeoComponentsWrapper>
                  </GridButtonsProvider>
                </StitchInverseStyleProvider>
              </RelatedCategoriesServerWrapper>
            </CatMarketingProviderWrapper>
          </SitewideContextProvider>
        </LegacyPLPStateProvider>
        <Marketing cid={cid as CID} marketingType='seocategorydescription' />
      </div>
      <SeoSchema {...props} />
    </>
  );
}

export default function Category(props: PageParams) {
  const pageContext = getPageContext();
  const { locale, market } = pageContext;
  const translations = getLocaleSpecificTranslations(locale, ['category-page']);

  return (
    <PageWrapper {...props} pageType='category'>
      <LocalizationProvider locale={locale} market={market} supportNesting translations={translations}>
        <CategoryMain {...props} />
      </LocalizationProvider>
    </PageWrapper>
  );
}
