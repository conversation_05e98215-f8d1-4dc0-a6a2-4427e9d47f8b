const contentData = {
  '1006482/ebb': {
    contentItems: [
      {
        _meta: {
          name: '2024_SPR3_F1_CatBanner_NewArrivals',
          schema: 'https://cms.gap.com/schema/content/v2/category-banner.json',
          deliveryId: '599fe217-3665-479c-8a04-f0b7850f2f58',
        },
        richTextArea: {
          mobileHeadline:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="color:#FFFFFF;font-weight:600">NEW ARRIVALS</span></p>',
          desktopHeadline:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="color:#FFFFFF;font-weight:600">NEW ARRIVALS</span></p>',
          mobileSubcopy: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1"> </span></p>',
        },
        desktopImages: [
          {
            image: {
              _meta: { schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link' },
              id: '2cc23c27-6c39-4b8f-ace3-b11bb261af16',
              name: 'SUM24_D2_Flip3_NA_Cat_Banner_XL',
              endpoint: 'athletaprod',
              defaultHost: 'cdn.media.amplience.net',
            },
            altText: '',
            variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        mobileImages: [
          {
            image: {
              _meta: { schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link' },
              id: '1ff3aa60-bc2c-4e5a-9149-cb9b5709e08b',
              name: 'SUM24_D2_Flip3_NA_Cat_Banner_S',
              endpoint: 'athletaprod',
              defaultHost: 'cdn.media.amplience.net',
            },
            altText: '',
            variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        webAppearance: {
          showHideBasedOnScreenSize: 'alwaysShow',
          includeAthletaGirlLogo: false,
          headlineDivider: true,
          imageRatioDesktop: '100',
          imageRatioMobile: '100',
          dividerOrLogoColorMobile: '#FFFFFF',
          dividerOrLogoColor: '#FFFFFF',
        },
      },
    ],
  },
  '1182084/ism': {
    contentItems: [
      {
        _meta: {
          name: 'ISM_FeelPowerful_NA',
          schema: 'https://cms.gap.com/schema/content/v1/ism-double-partial-image.json',
          deliveryId: 'e2d5185d-ac2a-4ea0-a6a5-ac3cc6e5df7e',
        },
        background: {
          type: 'image',
          images: [
            {
              image: {
                _meta: { schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link' },
                id: '2debadf7-1389-4497-9a19-2281420ace7b',
                name: 'HOL3_NA_ISM_Winter_Wonders',
                endpoint: 'athletaprod',
                defaultHost: 'cdn.media.amplience.net',
              },
              variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
        },
        rowPosition: { desktop: 1, mobile: 1 },
        webAppearance: {
          showHideBasedOnScreenSize: 'alwaysShow',
          imageOrIconHorizontalAlignment: 'center',
          imageOrIconPlacement: 'above',
          ctaButton1Styling: { buttonStyle: 'border', buttonColor: 'dark' },
          ctaButton2Styling: { buttonStyle: 'border', buttonColor: 'dark' },
          verticalTextAlignment: 'top',
          desktopImageOrIconSize: '24px',
          mobileImageOrIconSize: '14px',
        },
        bodyCopy:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="font-weight:600">WINTER WONDERS</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="font-weight:400">Elevated essentials you’ll layer all season.</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><a href="/browse/category.do?cid=1022034#style=3025051&mlink=1,1,W_Cat_New_Arrivals_ISM1" class="amp-cms--body-3" style="font-weight:600">Shop Now</a></p>',
      },
    ],
  },
  '1006482/seocategorydescription': {
    contentItems: [
      {
        _meta: {
          name: 'SEO_AllNewArrivals_Spring1',
          schema: 'https://cms.gap.com/schema/content/v1/seo-rich-text.json',
          deliveryId: 'a1603930-1a7a-4826-9b9a-2b0bab61e6a7',
        },
        richText:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Women’s New Arrivals</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Athleta’s new arrivals have got you covered for any occasion and every season. Our newest selection of </span><a href="https://athleta.gap.com/browse/category.do?cid=1025878" class="amp-cms--body-1">bottoms</a><span class="amp-cms--body-1"> include the classic leggings, joggers and shorts that you’ve relied on from gym and yoga activities to the office and beyond. Need a pair of </span><a href="https://athleta.gap.com/browse/category.do?cid=1059471" class="amp-cms--body-1">pant</a><span class="amp-cms--body-1"> refresh that takes you from working in the office to grabbing lunch with friends? Our assortment of new colors and prints will give you the reliably stylish look that’ll suit any day in your lifestyle. Stock up on the latest styles and size options. Our newest versatile </span><a href="https://athleta.gap.com/browse/category.do?cid=89745" class="amp-cms--body-1">dresses</a><span class="amp-cms--body-1"> can be paired with accessories and your favorite heels for a night out or with a hat and sneakers for everyday comfort. Find the latest </span><a href="https://athleta.gap.com/browse/category.do?cid=1032080" class="amp-cms--body-1">tops</a><span class="amp-cms--body-1"> selection that are comfortable for any activity thanks to our seamless </span><a href="https://athleta.gap.com/browse/category.do?cid=1191635" class="amp-cms--body-1">fabric</a><span class="amp-cms--body-1"> options, and we also have new tops that will complement and dazzle up a casual outfit, like our poplin and button-down tops. Plus, check out </span><a href="https://athleta.gap.com/browse/category.do?cid=1054844" class="amp-cms--body-1">new arrivals for girls</a><span class="amp-cms--body-1"> as well and stock up on the latest girl’s tops and bottoms. For an easy outfit, we have a ton of new performance girls’ clothing in built-to-last fabrics. From the classroom to after school sports and activities, rest assured that she&#39;ll be ready to take on the day.</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><a href="https://athleta.gap.com/browse/category.do?cid=1006482" class="amp-cms--body-1">https://athleta.gap.com/browse/category.do?cid=1006482</a></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" />',
      },
    ],
  },
  '1006482/page-properties': {
    contentItems: [
      {
        _meta: {
          name: "Shop Women's New Arrivals | Athleta | 1006482",
          schema: 'https://cms.gap.com/schema/content/v1/page-properties.json',
          deliveryKey: '1006482/pageproperties',
          deliveryId: '905adb6c-d2b8-4ff2-b076-a76b658e9f78',
        },
        pageDescription:
          "Discover new arrivals at Athleta that are ideal for all your active endeavors. Find the latest women's activewear available in stylish colors and prints.",
        pageTitle: "Shop Women's New Arrivals | Athleta",
      },
    ],
  },
};

export default contentData;
