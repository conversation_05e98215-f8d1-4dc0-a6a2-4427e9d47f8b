// @ts-nocheck
import { bannerEngagementAdapter } from './banner';

describe('Datalayer/events/facet-engagement/adapters/banner', () => {
  it('should return banner=value if banner exists in query string', () => {
    const queryString = 'foo=bar&banner=marketing_banner_engagement&color=blue';
    expect(bannerEngagementAdapter(queryString)).toEqual(
      'banner=marketing_banner_engagement'
    );
  });

  it('should return empty string if banner is not in query string', () => {
    const queryString = 'foo=bar&color=blue';
    expect(bannerEngagementAdapter(queryString)).toEqual('');
  });
});
