// @ts-nocheck
import categoryPageAttributes from '.';
import { getPreFacetEngagement } from './collaborators/pre-facet-engagement';
import { appProps } from './collaborators/specs/fixtures/appPropsMock';

jest.mock('./collaborators/brand-short-name');
jest.mock('./collaborators/channel');
jest.mock('./collaborators/page-name');
jest.mock('./collaborators/page-type');
jest.mock('./collaborators/br-iuid');
jest.mock('./collaborators/related-rid');
jest.mock('./collaborators/pre-facet-engagement');

describe('Datalayer/category-page-attributes', () => {
  const expectedAttributes = [
    'brand_short_name',
    'browse_refine_type',
    'browse_refine_value',
    'business_unit_id',
    'category_name',
    'channel',
    'page_name',
    'page_type',
    'br_iuid',
    'br_related_rid',
  ];

  const colorFacet = {
    indexableUrlFacetParamKey: "color",
    indexableUrlFacetParamValue: 'black',
    facetName: 'color',
    facetData: { id: '1017', name: 'Black' }
  };
  const sleeveLengthFacet = {
    indexableUrlFacetParamKey: "sleeve-length",
    indexableUrlFacetParamValue: 'short-sleeve',
    facetName: 'sleeveLength',
    facetData: { id: '1928', name: 'Short Sleeve' }
  }

  it('should contains all expected attributes', () => {
    const attributes = categoryPageAttributes(appProps);

    expect(Object.keys(attributes)).toEqual(
      expect.arrayContaining(expectedAttributes)
    );
  });

  it('should contain subcategory_name when subcategory is defined', () => {
    const attributes = categoryPageAttributes({
      ...appProps,
      subcategoryName: 'Subcategory',
    });
    expect(attributes).toHaveProperty('subcategory_name');
  });

  it.each([
    {
      name: 'Color',
      mockedEngagement: 'mocked',
      params: {
        cid: '',
        style: '',
        indexableFacets: [colorFacet]
      },
    },
    {
      name: 'Sleeve Length',
      mockedEngagement: 'mocked',
      params: {
        cid: '',
        style: '',
        indexableFacets: [sleeveLengthFacet]
      },
    },
  ])(
    'should contain pre_facet_engagement when facet $name is defined',
    ({ params, mockedEngagement }) => {
      getPreFacetEngagement.mockReturnValueOnce(mockedEngagement);

      const attributes = categoryPageAttributes({
        ...appProps,
        params,
      });

      expect(getPreFacetEngagement).toHaveBeenCalledWith(
        appProps.categoryName,
        params
      );
      expect(attributes).toHaveProperty('pre_facet_engagement');
      expect(attributes.pre_facet_engagement).toBeDefined();
      expect(attributes.pre_facet_engagement).toBe(mockedEngagement);
    }
  );

  it('should NOT contain pre_facet_engagement when is not pre facet page', () => {
    const attributes = categoryPageAttributes(appProps);
    expect(attributes).not.toHaveProperty('pre_facet_engagement');
  });
});
