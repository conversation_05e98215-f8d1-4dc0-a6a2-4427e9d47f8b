// @ts-nocheck
import { DataLayer } from '@ecom-next/core/legacy/app-state-provider/types';
import { SmartCrossLinks } from 'src/app/App';

export type SelectedNodes = Array<{ type: string; name: string }>;

export interface CategoryPageAttributesProps {
  categoryName: string;
  businessUnitId: string;
  subcategoryName: string;
  params: string[];
  brandName: string;
  abbrNameForTealium: string;
  selectedNodes: SelectedNodes;
  pageType: string;
  smartCrossLinks: SmartCrossLinks;
}

export interface CategoryPageAttributes {
  brand_short_name: string;
  browse_refine_type: [];
  browse_refine_value: [];
  business_unit_id: DataLayer;
  category_name: string;
  channel: string;
  page_name: string;
  page_type: string;
  br_iuid: string;
  br_related_rid: string;
  subcategory_name?: string;
  pre_facet_engagement?: string | null;
}
