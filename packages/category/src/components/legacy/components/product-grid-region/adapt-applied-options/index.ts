// @ts-nocheck
import {
  AdaptAppliedOptionsType,
  FacetName,
  GridRegionFacetOption,
  InlineFacetTagOptions,
} from '../types';

const addFacetNameToOption =
  (facetName: FacetName) => (option: GridRegionFacetOption) =>
    Object.assign(option, { facetName });

const ignoreFacets = (facetsToIgnore: string[]) => (facetName: string) =>
  !facetsToIgnore.includes(facetName);

const ignoreOrderedFacets =
  (facetsToIgnore: string[]) =>
  ({ facetName }: GridRegionFacetOption) =>
    !facetsToIgnore.includes(facetName);

const extractOptionsFrom = (appliedFacets) => (facetName) =>
  appliedFacets[facetName].map(addFacetNameToOption(facetName));

const combineAllOptions = (
  combinedOptions: GridRegionFacetOption[],
  options: GridRegionFacetOption[]
) => combinedOptions.concat(options);

const getId = (option: {id?: InlineFacetTagOptions["id"], searchFacetOptionId?: GridRegionFacetOption["searchFacetOptionId"]}) =>
  option.id! || option.searchFacetOptionId!;

const getName = (option: {name?: InlineFacetTagOptions["name"], searchFacetOptionValue: GridRegionFacetOption["searchFacetOptionValue"]}) =>
  option.name || option.searchFacetOptionValue;

const getDisplayName = (option: GridRegionFacetOption) =>
  option.tagDisplayLabel ?? getName(option);

const adaptAppliedOption = (option: GridRegionFacetOption) => ({
  id: getId(option),
  name: getName(option),
  facetName: option.facetName,
  displayName: getDisplayName(option),
});

const adaptAppliedOptions = (
  appliedFacets: AdaptAppliedOptionsType,
  ignoredFacets: string[]
): GridRegionFacetOption[] =>
  Object.keys(appliedFacets)
    .filter(ignoreFacets(ignoredFacets))
    .map(extractOptionsFrom(appliedFacets))
    .reduce(combineAllOptions, [])
    .map(adaptAppliedOption);

export const adaptOrderedOptions = (
  appliedFacets: GridRegionFacetOption[],
  ignoredFacets: string[]
): InlineFacetTagOptions[] =>
  // @ts-ignore
  appliedFacets
    ?.filter(ignoreOrderedFacets(ignoredFacets))
    .map(adaptAppliedOption)
    .reverse() || [];

export default adaptAppliedOptions;
