// @ts-nocheck
import React, { useContext } from "react";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { BrandInfoContext } from "@ecom-next/sitewide/brand-info-provider";
import { LoadingPlaceholder } from "@ecom-next/core/legacy/loading-placeholder";
import { useBrRedesignSlice2 } from "@ecom-next/plp-ui/legacy/plp-experiments";
import { styled } from "@ecom-next/core/react-stitch";

const BRRailPlaceholderFragment = styled.div(
  ({ isMobile }: { isMobile: boolean }) => ({
    minHeight: isMobile ? "3.038rem" : "3.125rem",
    width: "100%",
    position: "initial",
    marginTop: isMobile ? "65px" : "118px",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: isMobile ? "1.038rem" : "0.625rem",
    ".RailContainerPlaceholder__title": {
      width: isMobile ? "45%" : "35%",
      height: isMobile ? "2.75rem" : "3.75rem",
    },
    ".RailContainerPlaceholder__buttons": {
      width: isMobile ? "40%" : "15%",
      height: isMobile ? "2.75rem" : "3.75rem",
    },
  })
);

const RailPlaceHolder = (): JSX.Element => {
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);

  const { abbrBrand } = useContext(BrandInfoContext);
  const isBR = abbrBrand === "br" || abbrBrand === "brfs";
  const isBRRedesignSlice2Enabled = useBrRedesignSlice2();

  if (isBRRedesignSlice2Enabled) {
    return (
      <BRRailPlaceholderFragment data-testid="rail-placeholder" isMobile>
        <LoadingPlaceholder
          className="RailContainerPlaceholder__title"
          fixedSize={{ width: 100, height: 40 }}
        />
        <LoadingPlaceholder
          className="RailContainerPlaceholder__buttons"
          fixedSize={{ width: 100, height: 40 }}
        />
      </BRRailPlaceholderFragment>
    );
  }

  if (!isBR) {
    if (isMobile) {
      return (
        <div
          data-testid="rail-placeholder"
          style={{ display: "flex", width: "100%", padding: "0px 1rem" }}
        >
          <LoadingPlaceholder fixedSize={{ width: 100, height: 226 }} />
        </div>
      );
    }

    return (
      <div style={{ width: "16rem" }} data-testid="rail-placeholder"></div>
    );
  }

  return (
    <div
      style={{ width: "0", height: "0" }}
      data-testid="rail-placeholder"
    ></div>
  );
};

export default RailPlaceHolder;
