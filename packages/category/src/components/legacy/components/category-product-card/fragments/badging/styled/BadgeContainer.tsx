// @ts-nocheck
import { styled } from "@ecom-next/core/react-stitch";
interface BreakPoints {
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  isXLarge: boolean;
}
interface BadgeContainerProps {
  breakPoints: BreakPoints;
}
const getPaddingForOtherViewPorts = ({ isLarge }: BreakPoints) =>
  isLarge ? '1.25rem' : '1.1875rem';
const BadgeContainer = styled.div<BadgeContainerProps>(
  ({ theme, breakPoints }) => ({
    width: '171px',
    height: '13px',
    color: theme.color.wh,
    lineHeight: '13px',
    paddingBottom: breakPoints.isXLarge
      ? '1.5rem'
      : getPaddingForOtherViewPorts(breakPoints),
    fontStyle: 'italic',
    fontFamily: 'Banana',
    fontWeight: 400,
    fontSize: breakPoints.isSmall ? '11px' : '15px',
  })
);

export default BadgeContainer;
