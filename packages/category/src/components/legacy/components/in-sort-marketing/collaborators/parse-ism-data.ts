// @ts-nocheck
import parseAmplienceIsms from './amplience/parse-amplience-isms';
import parseLegacyIsms from './legacy/parse-legacy-isms';
import {
  AmplienceContentData,
  ContentData,
  ContentDataIDs,
  SubcatISMContent,
} from '../types';
import { LegacyContentData } from 'src/app/App';

const isAmplienceData = (contentData: ContentData): boolean =>
  typeof (contentData as AmplienceContentData).pmcsEdgeCacheTag !== 'undefined';

const parseIsmData = (
  contentData: ContentData,
  contentDataIDs: ContentDataIDs
): SubcatISMContent[] =>
  isAmplienceData(contentData)
    ? parseAmplienceIsms(contentData as AmplienceContentData, contentDataIDs)
    : parseLegacyIsms(contentData as LegacyContentData, contentDataIDs);

export default parseIsmData;
