// @ts-nocheck
import {
  Facet,
  FacetOption,
} from 'src/app/components/left-faceted-grid/collaborators/fetch-products/new-contract/types';

const multiSelectStrategy = ({ options = [] }: Partial<Facet>): boolean =>
  options.length > 0;

const singleSelectStrategy = ({ options = [] }: Partial<Facet>): boolean =>
  options.length > 1;

const hiddenSimpleFacetStrategy = (facet: Facet): boolean => {
  const optionHasAnInvalidName = (option: FacetOption) => !option.name;

  if (facet.options?.every(optionHasAnInvalidName)) {
    return false;
  }
  return facet.type === 'multi-select'
    ? multiSelectStrategy(facet)
    : singleSelectStrategy(facet);
};

export default hiddenSimpleFacetStrategy;
