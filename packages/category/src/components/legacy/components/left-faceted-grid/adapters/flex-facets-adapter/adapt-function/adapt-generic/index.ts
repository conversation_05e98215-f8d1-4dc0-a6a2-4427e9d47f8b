// @ts-nocheck
import { Facet, FacetOption, FacetWithApplied } from '../../../types';
import getAppliedValues from './get-applied-values';

export const adaptGeneric = (
  facet: Facet,
  optionsModifier?: (option: FacetOption) => FacetOption
): FacetWithApplied => {
  const outOptionsWithInvalidName = (option: FacetOption) => !!option.name;

  const withIsActive = (option: FacetOption) => ({
    ...option,
    value: option.name,
    isActive: 'true',
  });

  const options =
    facet.options &&
    facet.options
      .filter(outOptionsWithInvalidName)
      .map(withIsActive)
      .map(optionsModifier || ((option) => option));

  return {
    facet: { ...facet, options },
    appliedValues: getAppliedValues(options),
  };
};
