// @ts-nocheck
import { FacetWithConfig } from '../../../types';
import pushIf from './push-if';

const getHiddenFacetsByConfiguration = (
  { name, hidden }: FacetWithConfig,
  departmentHidden: boolean,
  priceHidden: boolean,
  ratingsHidden: boolean
): Array<string> => [
  ...pushIf(hidden === true, name),
  ...pushIf(departmentHidden, 'department'),
  ...pushIf(priceHidden, 'price'),
  ...pushIf(ratingsHidden, 'reviewScore'),
];

export default getHiddenFacetsByConfiguration;
