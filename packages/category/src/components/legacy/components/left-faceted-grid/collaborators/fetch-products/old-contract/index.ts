// @ts-nocheck
import { productGridAdapter, flexFacetsAdapter, ProductsResponse } from '@ecom-next/plp-ui/legacy/legacy-pds-adapter';
import buildPdsUrl from '../build-pds-url';
import parseTotalItemCount from '../../parse-total-item-count';
import { AdaptedResponse } from '../types';
import { Locales } from '@ecom-next/core/legacy/app-state-provider/types';
import { WebItem } from '../../../../../App';
import { SelectedNode } from '@ecom-next/plp-ui/legacy/bopis-bar';

export default (
  cid: number,
  departmentId: string,
  facetQueryString: string,
  pageId: number,
  productsUrl: string,
  productsApiKey: string,
  hideDepartmentFacet: boolean,
  hidePriceFacet: boolean,
  abSeg: {
    pgAT: string;
    pgBR: string;
    pgGP: string;
    pgON: string;
    pgGPFS: string;
    pgBRFS: string;
    pgATCA: string;
    pgBRCA: string;
    pgGPCA: string;
    pgONCA: string;
  },
  previewState: Record<string, unknown>,
  isFlexFacetsEnabled: boolean,
  locale: Locales,
  ignoreInventory: boolean,
  isRatingsFacetEnabled: boolean,
  isDynamicStyleFacetNameEnabled: boolean,
  webHierarchy: WebItem[],
  SelectedNodes: SelectedNode[]
): Promise<AdaptedResponse> =>
  fetch(
    buildPdsUrl({
      cid,
      departmentId,
      pageId,
      facetQueryString,
      productsUrl,
      productsApiKey,
      abSeg,
      previewState,
      isFlexFacetsEnabled,
      locale,
      ignoreInventory,
    }),
    {
      credentials: 'same-origin',
      headers: {
        ...(productsApiKey ? { apikey: productsApiKey } : {}),
        "x-client-application-name" : "Browse"
      },
    }
  )
    .then((data) => data.json())
    .then((data: ProductsResponse) => ({
      gridData: productGridAdapter(data),
      paginator: data.productCategoryFacetedSearch.productCategory
        .productCategoryPaginator,
      facetData: flexFacetsAdapter(
        data,
        hideDepartmentFacet,
        hidePriceFacet,
        !isRatingsFacetEnabled,
        isDynamicStyleFacetNameEnabled,
        webHierarchy,
        SelectedNodes
      ),
      totalItemCount: parseTotalItemCount(data),
    } as unknown as AdaptedResponse));
