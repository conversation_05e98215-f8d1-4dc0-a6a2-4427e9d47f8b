// @ts-nocheck
import type { FacetOption, FacetList, AppliedFacets } from './types';

const isPriceFacet = (facetName: string) => facetName === 'price';

const alreadyOrderedFacet =
  (orderedFacets: FacetList) =>
  ({ facetName, id }: FacetOption): boolean =>
    !orderedFacets.some(
      (orderedFacet) =>
        orderedFacet.id === id ||
        (isPriceFacet(orderedFacet.facetName) && isPriceFacet(facetName))
    );

const noLongerAppliedFacet =
  (appliedFacets: AppliedFacets) =>
  ({ facetName, id }: FacetOption): boolean =>
    !!appliedFacets[facetName].some(
      (facet) => facet.id === id || (isPriceFacet(facetName) && !!facet)
    );

const putPriceOnLastPosition = (orderedFacets) =>
  orderedFacets.sort((facetA, facetB) => {
    if (isPriceFacet(facetA.facetName)) {
      return 1;
    }
    if (isPriceFacet(facetB.facetName)) {
      return -1;
    }
    return 0;
  });

const getUpdatedOrderedFacetList = (
  appliedFacets: AppliedFacets,
  orderedFacets: FacetList,
  newFacetSelectionIsPriceFacet: boolean
): FacetList => {
  const newFacetSelections = Object.values(appliedFacets)
    // $FlowFixMe [our version of flow does not support flat func for Arrays]
    .flat()
    .filter(alreadyOrderedFacet(orderedFacets));

  const newOrderedFacets = orderedFacets.filter(
    noLongerAppliedFacet(appliedFacets)
  );

  const updatedOrderedFacetList = [...newOrderedFacets, ...newFacetSelections];
  return newFacetSelectionIsPriceFacet
    ? putPriceOnLastPosition(updatedOrderedFacetList)
    : updatedOrderedFacetList;
};

export default getUpdatedOrderedFacetList;
