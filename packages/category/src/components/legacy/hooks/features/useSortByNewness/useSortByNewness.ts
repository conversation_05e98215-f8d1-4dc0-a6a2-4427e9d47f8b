// @ts-nocheck
import { useContext } from "react";
import { BrandInfoContext } from "@ecom-next/sitewide/brand-info-provider";
import useEnabledFeature from "../../useEnabledFeature";
import ABSegmentContext from "../../../components/ab-segment-context";

const useSortByNewness = (): boolean => {
  const { abbrBrand } = useContext(BrandInfoContext);
  const featureEnabled = useEnabledFeature("search-product-search"); //TODO: we may need to create a new FF to support this feature.

  const segments = useContext(ABSegmentContext);
  const sortByNewnessSegmentValue = segments[`${abbrBrand}164`];
  const sortByNewnessSegmentEnabled =
    sortByNewnessSegmentValue === "a" || sortByNewnessSegmentValue === "b";

  return featureEnabled && sortByNewnessSegmentEnabled;
};

export default useSortByNewness;
