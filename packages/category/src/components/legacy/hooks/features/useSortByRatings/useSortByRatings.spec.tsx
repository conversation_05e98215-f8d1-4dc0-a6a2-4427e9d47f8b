// @ts-nocheck
import React from 'react'; // eslint-disable-line
import { renderHook } from '@testing-library/react-hooks';
import { TestContext } from '@ecom-next/core/legacy/test';
import { BrandInfoProvider } from '@ecom-next/sitewide/brand-info-provider';
import { ABSegmentContextProvider } from '../../../components/ab-segment-context/ABSegmentContext';
import useSortByRatings from '.';

describe('useSortByRatings Hook', () => {
  const wrapper =
    ({ enabledFeatures = {}, segments = {} } = {}) =>
    ({ children }: { children: JSX.Element }) =>
      (
        <BrandInfoProvider
          brand={'gap'}
          abbrBrand={'gap'}
          brandCode={1}
          market={'us'}
        >
          <TestContext
            enabledFeatures={enabledFeatures}
            appState={{
              brandName: 'gap',
            }}
          >
            <ABSegmentContextProvider value={segments}>
              {children}
            </ABSegmentContextProvider>
          </TestContext>
        </BrandInfoProvider>
      );

  it('returns enabled if cat-star-ratings is enabled and segment b is on', () => {
    const {
      result: { current },
    } = renderHook(() => useSortByRatings(), {
      wrapper: wrapper({
        enabledFeatures: { 'cat-star-ratings': true },
        segments: { gap87: 'b' },
      }),
    });

    expect(current).toBe(true);
  });

  it('returns enabled if cat-star-ratings is enabled and segment c is on', () => {
    const {
      result: { current },
    } = renderHook(() => useSortByRatings(), {
      wrapper: wrapper({
        enabledFeatures: { 'cat-star-ratings': true },
        segments: { gap87: 'c' },
      }),
    });

    expect(current).toBe(true);
  });

  it('returns disabled if cat-star-ratings is enabled and segment a is on', () => {
    const {
      result: { current },
    } = renderHook(() => useSortByRatings(), {
      wrapper: wrapper({
        enabledFeatures: { 'cat-star-ratings': true },
        segments: { gap87: 'a' },
      }),
    });

    expect(current).toBe(false);
  });

  it('returns disabled if cat-star-ratings is disabled', () => {
    const {
      result: { current },
    } = renderHook(() => useSortByRatings(), {
      wrapper: wrapper({
        enabledFeatures: { 'cat-star-ratings': false },
        segments: { gap87: 'b' },
      }),
    });

    expect(current).toBe(false);
  });
});
