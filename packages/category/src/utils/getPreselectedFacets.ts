import { AppliedFacetOption } from '@ecom-next/plp-ufa/types';
import { Params } from '../components/legacy/components/left-faceted-grid/types';

export const preselectDepartment = (customUrl?: string): { department?: AppliedFacetOption[] } => {
  const { id } = /.*department=(?<id>[\d]+).*/.exec(customUrl || '')?.groups || {};
  return id ? { department: [{ id, name: '' }] } : {};
};

export const preselectStyle = ({ style }: { style?: string }): { style?: AppliedFacetOption[] } => (style ? { style: [{ id: style, name: '' }] } : {});

type IndexableFacetParams = {
  indexableFacets?: Array<{
    facetData: {
      id: string;
      name: string;
    };
    facetName: string;
  }>;
};

export const preselectIndexableFacets = (params: IndexableFacetParams): { [key: string]: AppliedFacetOption[] } =>
  (params.indexableFacets || []).reduce(
    (acc, indexedFacet) => {
      const { facetName, facetData } = indexedFacet;
      acc[facetName] = [facetData];
      return acc;
    },
    {} as { [key: string]: { id: string; name: string }[] }
  );

export const hasPreselectedFacets = (customUrl: string = '', params: Params) => {
  const initialFacets = {
    ...preselectDepartment(customUrl),
    ...preselectStyle(params),
    ...preselectIndexableFacets(params),
  };
  return Object.keys(initialFacets).length > 0;
};
