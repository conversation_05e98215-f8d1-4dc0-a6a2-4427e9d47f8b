'use client';
import { usePsDataContext, PsDataProvider } from '@ecom-next/plp-ui';
import React from 'react';

const ProductGrid = () => {
  const { state } = usePsDataContext();
  return !state?.isLoading ? (
    <>
      {Object.values(state.data.productsGridIds).map(({ productId }) => (
        <div key={state.data.productInfos[productId].pcid}>
          <img src={`https://www.gap.com${state.data.productImages[productId].src}`} alt={state.data.productInfos[productId].productName.name} />
          <h3>{state.data.productInfos[productId].productName.name}</h3>
          <p>${state.data.productInfos[productId].productPrice.adapter.currentMaxPrice}</p>
        </div>
      ))}
    </>
  ) : (
    <div>Loading...</div>
  );
};

const ProductListingApp = ({ keyword }: { keyword: string }) => (
  <PsDataProvider keyword={keyword} endpoint="search">
    <ProductGrid />
  </PsDataProvider>
);

export default ProductListingApp;
