.button-base,
.sds_button-base,
.button_secondary_sm,
.sds_button_secondary_sm,
.button_primary_sm,
.sds_button_primary_sm,
.button_cat-page-filter,
.sds_button_cat-page-filter,
.sds_button_tertiary--flat,
.sds_button_secondary--flat,
.button_tertiary,
.sds_button_tertiary,
.button_secondary,
.sds_button_secondary,
.button_primary,
.sds_button_primary {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  width: 100%;
  display: block;
  border: none;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  box-sizing: border-box;
  transition: all 200ms ease;
}

.button_secondary_sm,
.sds_button_secondary_sm, .button_primary_sm,
.sds_button_primary_sm,
.button_cat-page-filter,
.sds_button_cat-page-filter, .button_primary,
.sds_button_primary {
  background-color: #000;
}
.disabled.button_secondary_sm,
.disabled.sds_button_secondary_sm, .disabled.button_primary_sm,
.disabled.sds_button_primary_sm,
.disabled.button_cat-page-filter,
.disabled.sds_button_cat-page-filter, .disabled.button_primary,
.disabled.sds_button_primary, .button_secondary_sm:disabled,
.sds_button_secondary_sm:disabled, .button_primary_sm:disabled,
.sds_button_primary_sm:disabled,
.button_cat-page-filter:disabled,
.sds_button_cat-page-filter:disabled, .button_primary:disabled,
.sds_button_primary:disabled {
  background-color: #b3b3b3;
  background-color: rgba(0, 0, 0, 0.3);
}

.button_inline,
.sds_button_inline {
  display: inline;
  width: auto;
}

/* Primary Button Styles */
.button_primary,
.sds_button_primary {
  height: 2.75rem;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #FFF;
  font-size: 1.0625rem;
  letter-spacing: 2px;
  padding: 0.53em;
}
.button_primary:hover,
.sds_button_primary:hover {
  background-color: #404040;
}
.button_primary.disabled,
.sds_button_primary.disabled {
  cursor: default;
}

/* Secondary Button Styles */
.button_secondary,
.sds_button_secondary {
  height: 2.75rem;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  font-size: 1.0625rem;
  letter-spacing: 2px;
  background-color: transparent;
  border: 1px solid #000;
  padding: 0.48em;
}
.button_secondary:hover,
.sds_button_secondary:hover {
  color: #404040;
  border-color: #404040;
}

/* Tertiary Button Styles */
.button_tertiary,
.sds_button_tertiary {
  padding-left: 1rem;
  padding-right: 1rem;
  height: 2.0625rem;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 93.75%;
  padding-top: 0.313em;
  padding-bottom: 0.313em;
  color: #000;
  background-color: #FFF;
  border: 1px solid #000;
}
.button_tertiary:hover,
.sds_button_tertiary:hover {
  color: #404040;
  border-color: #404040;
}

/* Secondary Flat Button Styles */
.sds_button_secondary--flat {
  height: 2.75rem;
  font-size: 81.25%;
  background-color: transparent;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  font-size: 1.0625rem;
  letter-spacing: 2px;
  padding: 0.53em;
}
.sds_button_secondary--flat:hover {
  color: #404040;
}

/* Tertiary Flat Button Styles */
.sds_button_tertiary--flat {
  padding: 0.25rem 1rem;
  height: 2.0625rem;
  font-size: 81.25%;
  background-color: transparent;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375rem;
  color: #000;
}
.sds_button_tertiary--flat:hover {
  color: #404040;
}

.button_cat-page-filter,
.sds_button_cat-page-filter {
  width: 70%;
  margin: 0 auto;
}
@media (min-width: 430px) {
  .button_cat-page-filter,
  .sds_button_cat-page-filter {
    width: 50%;
  }
}
.department-not-selected .button_cat-page-filter,
.department-not-selected .sds_button_cat-page-filter {
  display: none;
}

/* Small Primary Button Styles */
.button_primary_sm,
.sds_button_primary_sm,
.button_cat-page-filter,
.sds_button_cat-page-filter {
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #FFF;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.4;
  line-height: 1.43;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding: 0.5em;
}
@media (min-width: 768px) {
  .button_primary_sm,
  .sds_button_primary_sm,
  .button_cat-page-filter,
  .sds_button_cat-page-filter {
    font-weight: 700;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 0.875rem;
    font-family: "Lynstone", Helvetica, Arial, sans-serif;
    color: #FFF;
    font-family: "Lynstone", Helvetica, Arial, sans-serif;
    font-weight: 600;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.button_secondary_sm,
.sds_button_secondary_sm {
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.4;
  line-height: 1.43;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: transparent;
  border: 1px solid #000;
  padding: 0.5em;
}

.sort-by-price-dropdown,
.sort-by-price-dropdown-native {
  margin-top: 0;
}
.sort-by-price-dropdown button,
.sort-by-price-dropdown-native button {
  border-radius: 3px;
}
.sort-by-price-dropdown button:focus,
.sort-by-price-dropdown-native button:focus {
  outline: 1px auto -webkit-focus-ring-color;
  outline-offset: 2px;
}
.sort-by-price-dropdown button:focus-visible,
.sort-by-price-dropdown-native button:focus-visible {
  outline: 1px auto -webkit-focus-ring-color;
}
.sort-by-price-dropdown button:focus:not(:focus-visible),
.sort-by-price-dropdown-native button:focus:not(:focus-visible) {
  outline: none;
}
.sort-by-price-dropdown__control,
.sort-by-price-dropdown-native__control {
  border: none;
  padding-right: 1.375em;
  animation: none;
  background-image: none;
  padding-right: 1.375rem;
}
.sort-by-price-dropdown__control::before,
.sort-by-price-dropdown-native__control::before {
  top: -0.125rem;
  background-color: transparent;
}
.sort-by-price-dropdown__control, .sort-by-price-dropdown__option, .sort-by-price-dropdown select,
.sort-by-price-dropdown-native__control,
.sort-by-price-dropdown-native__option,
.sort-by-price-dropdown-native select {
  text-transform: uppercase;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-size: 0.8125rem;
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: auto;
  text-align: right;
  text-transform: uppercase;
  color: #333;
  letter-spacing: 0.1rem;
}
.sort-by-price-dropdown select#sortByPriceSelect,
.sort-by-price-dropdown-native select#sortByPriceSelect {
  text-align-last: right;
}
.sort-by-price-dropdown__option,
.sort-by-price-dropdown-native__option {
  border: 1px #CCC solid;
  border-bottom: none;
  padding-left: 2rem;
  padding-right: 0.5rem;
  position: relative;
  min-width: 6rem;
}
.sort-by-price-dropdown__option:last-of-type,
.sort-by-price-dropdown-native__option:last-of-type {
  border-bottom: 1px #CCC solid;
}
@media (min-width: 1024px) {
  .sort-by-price-dropdown__option,
  .sort-by-price-dropdown-native__option {
    right: 0;
  }
}
.sort-by-price-dropdown__option[aria-selected=true],
.sort-by-price-dropdown-native__option[aria-selected=true] {
  background-color: inherit;
}
.sort-by-price-dropdown__option[aria-selected=true]::before,
.sort-by-price-dropdown-native__option[aria-selected=true]::before {
  content: "";
  position: absolute;
  border-bottom: 3px solid #000;
  border-right: 3px solid #000;
  padding-top: 1px;
  left: 1.25rem;
  transform: rotate(45deg);
  height: 10px;
  width: 4px;
  left: 1.5rem;
}
@media (min-width: 1024px) {
  .sort-by-price-dropdown__option[aria-selected=true]::before,
  .sort-by-price-dropdown-native__option[aria-selected=true]::before {
    left: 0.75rem;
  }
}
.sort-by-price-dropdown__option--highlighted[aria-selected=true],
.sort-by-price-dropdown-native__option--highlighted[aria-selected=true] {
  background-color: #F2F2F2;
}
.sort-by-price-dropdown__menu,
.sort-by-price-dropdown-native__menu {
  right: 0;
  width: 16rem;
  margin-top: 0.125rem;
}
.sort-by-price-dropdown select,
.sort-by-price-dropdown-native select {
  border-bottom: none;
  top: 0.2em;
  padding-right: 1.25rem;
}
@media (min-width: 1024px) {
  .sort-by-price-dropdown,
  .sort-by-price-dropdown-native {
    width: 16rem;
  }
  .sort-by-price-dropdown__control,
  .sort-by-price-dropdown-native__control {
    height: 2.25rem;
  }
  .sort-by-price-dropdown__control::before,
  .sort-by-price-dropdown-native__control::before {
    display: block;
    top: 1px;
  }
}

#search-page .product-grid {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  width: 100%;
  min-height: 500px;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}
#search-page .product-card-grid__inner {
  padding-bottom: 2rem;
}
#search-page .category__item-count {
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
}
@media (min-width: 1024px) {
  #search-page .facet-department {
    width: 195px;
  }
}
@media (min-width: 1024px) {
  #search-page .facet-style {
    width: 170px;
  }
}
#search-page .cat_quick-add__toggle-button:focus circle:first-of-type {
  stroke: #0466CA;
  stroke-width: 1.5px;
}
#search-page .cat_quick-add__toggle-button:hover circle:first-of-type {
  stroke: #000;
  stroke-width: 1px;
}

#search-page {
  min-height: 70vh;
}

.search-page__grid {
  margin-left: auto;
  margin-right: auto;
  max-width: 512px;
}
@media (min-width: 768px) {
  .search-page__grid {
    max-width: 768px;
  }
}
@media (min-width: 1280px) {
  .search-page__grid {
    max-width: 1024px;
  }
}

.search-page__grid_br_enabled {
  display: inherit;
  margin-left: auto;
  margin-right: auto;
  max-width: 1920px;
  width: 100%;
}
@media (max-width: 1920px) {
  .search-page__grid_br_enabled {
    margin-left: auto;
    margin-right: auto;
    flex-wrap: wrap;
  }
}
.search-page__grid_br_enabled .search-page__product-recommendations {
  max-width: 1920px;
  margin: 0 auto;
}
.search-page__grid_br_enabled .brredesign_search-page__product-recommendations div:first-child > section {
  margin-bottom: 0.938rem;
}
.search-page__grid_br_enabled .brredesign_search-page__product-recommendations div:first-child > section > h2 {
  padding-top: 0;
}

.search-page__left-column {
  display: flex;
  width: 100%;
  margin-top: 5px;
}
.search-page__left-column > div {
  margin-bottom: 0;
}
.search-page__left-column div > button {
  padding-right: 15px;
}
@media (min-width: 1024px) {
  .search-page__left-column {
    max-width: 256px;
    padding: 0 0.5em;
  }
}

.search-page__content {
  width: 100%;
  max-width: 512px;
  justify-content: flex-end;
}
.search-page__content > div {
  box-sizing: border-box;
}
@media (min-width: 768px) {
  .search-page__content {
    margin: 0 0px auto;
    max-width: 768px;
    width: 768px;
  }
}
@media (min-width: 1024px) {
  .search-page__content {
    margin: 0 0px auto;
    width: 768px;
    max-width: 768px;
  }
}
@media (min-width: 1280px) {
  .search-page__content {
    margin: 0 0px auto;
    max-width: 1024px;
    width: 1024px;
  }
}
.search-page__content .search-card-grid {
  box-sizing: border-box;
}

@media (max-width: 1920px) {
  .search-page__content_br_enabled {
    margin-left: auto;
    margin-right: auto;
    flex-wrap: wrap;
  }
}
.search-page__content_br_enabled .search-card-grid {
  box-sizing: border-box;
}

.search-page__content_plp_enabled {
  display: inherit;
  margin-bottom: 0.5rem;
  box-sizing: border-box;
}
@media (max-width: 768px) {
  .search-page__content_plp_enabled {
    max-width: 100%;
    width: 100%;
  }
}
@media (max-width: 1024px) {
  .search-page__content_plp_enabled {
    max-Width: 1024px;
    width: 100%;
  }
}
@media (max-width: 1280px) {
  .search-page__content_plp_enabled {
    max-width: 1920px;
    width: 100%;
    padding-left: 4em;
    padding-right: 4em;
  }
}
@media (max-width: 1920px) {
  .search-page__content_plp_enabled {
    max-width: 1920px;
    width: 100%;
    padding-left: 4em;
    padding-right: 4em;
  }
}

.error-message--container {
  margin-top: 0;
  padding-top: 0;
  padding-bottom: 0;
}
@media (min-width: 768px) {
  .error-message--container {
    margin-top: 1rem;
  }
}
.error-message--container p:last-child {
  margin-bottom: 16px;
}

.sort-by-price-dropdown {
  width: auto !important;
}

.quick-filter__loading-container {
  width: 100%;
}

.search-quick-filter .clear-button {
  font-weight: bold;
  font-size: 0.9rem;
}

.product-card div[data-test-id=badge-message] {
  min-height: 12px;
}

.link {
  color: #000;
  text-decoration: underline;
}

.link_default {
  color: #333399;
}
.link_default:link, .link_default:visited, .link_default:hover, .link_default:active {
  color: #333399;
}
.link_default:hover, .link_default:focus {
  text-decoration: underline;
}

.link_default_universal {
  font-family: "Lynstone", Helvetica, Arial, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #0073c8;
}
.link_default_universal:link, .link_default_universal:visited, .link_default_universal:hover, .link_default_universal:active {
  color: #0073c8;
}
.link_default_universal:hover, .link_default_universal:focus {
  text-decoration: underline;
}