// @ts-nocheck
import { FeatureFlagsContext } from "@ecom-next/core/legacy/feature-flags";
import { SearchConfigContext } from "@ecom-next/plp-ui/legacy/search-config-provider";
import React, { useContext, useEffect, useState } from "react";
import { HighPriorityEvent } from "../../../../lib/state-builder/types";
import { LegacyFacetParameters, SearchConfig } from "../../../types";
import { useAppState } from "../../app-state/use-app-state";
import { fetchFromSearch } from "./fetch-from-search-service";
import {
  addSearchTextToResponse,
  resolveCat1ImageLoadingIfNoProducts,
} from "./search-function-helpers";
import {
  LegacyPDSSearchResponse,
  MappedUrlQueryParameters,
  STATUSES,
  UpdateUrlQueryParameter,
} from "./types";
import { useQueryManager } from "./use-query-manager";
import { useNewPsApi } from "../../common/hooks/experiments/use-new-ps-api";
import {
  useSearchColorSwatches,
  useOldNavyPercentOff,
  usePLPDynamicBadge,
  usePLPLevelFields,
} from "@ecom-next/plp-ui/legacy/plp-experiments";

export type SearchResponseContextType = {
  searchResponse?: LegacyPDSSearchResponse;
  searchText: string;
  updateParameter: UpdateUrlQueryParameter;
  urlQueryParametersState: MappedUrlQueryParameters;
};

type SearchResponseProviderProps = {
  searchText: string;
  highPriorityEvents?: HighPriorityEvent[];
};

export const SearchResponseContext =
  React.createContext<SearchResponseContextType | null>(null);

const setInitialProductResponse = (response: LegacyPDSSearchResponse) => {
  if (
    typeof window.__SEARCH_PAGE_STATE__ !== "undefined" &&
    typeof response !== "undefined"
  ) {
    window.__SEARCH_PAGE_STATE__.initialProductResponse = response;
  }
};

const setCampaignOnState = (response: LegacyPDSSearchResponse) => {
  if (
    typeof window.__SEARCH_PAGE_STATE__ !== "undefined" &&
    typeof response !== "undefined"
  ) {
    window.__SEARCH_PAGE_STATE__.campaign = response.campaign;
  }
  if (
    typeof window.__SEARCH_PAGE_STATE__ !== "undefined" &&
    typeof response !== "undefined" &&
    typeof response.campaign === "undefined"
  ) {
    delete window.__SEARCH_PAGE_STATE__.campaign;
  }
};

export const SearchResponseProvider = ({
  children,
  searchText,
  highPriorityEvents,
}: SearchResponseProviderProps & { children: React.ReactNode }) => {
  const { urlQueryParametersState, updateParameter } = useQueryManager();
  const { brandName, market, locale, experiments } = useAppState();
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const searchEngineConfig = useContext<SearchConfig>(SearchConfigContext);

  const [searchResponse, setSearchResponse] =
    useState<LegacyPDSSearchResponse>();
  const isNewProductSearchCall = useNewPsApi();
  const { swatchesEnableSort, swatchesSortBy } = useSearchColorSwatches();
  const isONPercentOff = useOldNavyPercentOff();
  const isPLPDynamicBadgeEnabled = usePLPDynamicBadge();
  const isPLPLevelFieldsEnabled = usePLPLevelFields();

  useEffect(() => {
    const highPriorityEventDispatcher = resolveCat1ImageLoadingIfNoProducts(
      highPriorityEvents ?? []
    );

    const handleAsyncFetch = async () => {
      setSearchResponse(state => ({ ...state, status: STATUSES.WAITING }));
      let mappedFetchParameters: LegacyFacetParameters = {};

      Object.entries(urlQueryParametersState).forEach(
        ([, { fetchParameters }]) => {
          mappedFetchParameters = Object.assign(
            mappedFetchParameters,
            fetchParameters
          );
        }
      );

      const response = await fetchFromSearch(
        brandName,
        mappedFetchParameters,
        searchText,
        market,
        locale,
        enabledFeatures,
        searchEngineConfig,
        experiments,
        isNewProductSearchCall,
        swatchesEnableSort,
        swatchesSortBy,
        isONPercentOff,
        isPLPDynamicBadgeEnabled,
        isPLPLevelFieldsEnabled
      );

      highPriorityEventDispatcher(response);
      setCampaignOnState(response);
      setInitialProductResponse(response);
      const responseWithSearchText = addSearchTextToResponse(
        searchText,
        response
      );
      setSearchResponse(responseWithSearchText);
    };

    handleAsyncFetch();
  }, [
    brandName,
    enabledFeatures,
    experiments,
    highPriorityEvents,
    locale,
    market,
    searchEngineConfig,
    searchText,
    urlQueryParametersState,
    isNewProductSearchCall,
    swatchesEnableSort,
    swatchesSortBy,
    isONPercentOff,
    isPLPDynamicBadgeEnabled,
  ]);

  return (
    <SearchResponseContext.Provider
      value={{
        searchResponse,
        searchText,
        updateParameter,
        urlQueryParametersState,
      }}
    >
      {children}
    </SearchResponseContext.Provider>
  );
};

export const useSearchResponse = (): SearchResponseContextType => {
  const context = useContext(SearchResponseContext);
  if (!context) {
    throw new Error(
      "useSearchResponse must be used within a SearchResponseProvider"
    );
  }

  return context;
};
