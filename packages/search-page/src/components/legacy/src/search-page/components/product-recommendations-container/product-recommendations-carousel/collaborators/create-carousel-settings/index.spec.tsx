// @ts-nocheck
import createCarouselSettings from './';
import { carouselSettingsWithCustomSettings } from './fixtures/carousel-setting-with-custom-settings';

describe('createCarouselSettings', () => {
  it('should return a setting with a custom setting', () => {
    const shouldAddCarouselArrows = true;
    const customConfig = { dots: true, swipe: true };
    const carouselSettings = createCarouselSettings(shouldAddCarouselArrows, customConfig);

    expect(carouselSettings).toMatchObject(carouselSettingsWithCustomSettings);
  });
});
