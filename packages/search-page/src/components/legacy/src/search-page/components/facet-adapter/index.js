// @ts-nocheck
import adaptFacets from './adapt-facets';
import controlBrandsConfig from './order-facets/control-filter-facet-config';


const getAllowedFacets = options => {
  const {brandName, isPLPRedesignControl, market} = options

  if (isPLPRedesignControl)
    {return controlBrandsConfig[market][brandName].manualFacets ?? {};}
};

export const orderFacetData = (facets, allowedFacets) => {
  const orderedFacets = [];
  if (allowedFacets && allowedFacets.length > 0) {
      allowedFacets.forEach(name => {
          facets?.forEach(facet => {
              if (facet.searchFacetId.toLowerCase() === name.toLowerCase()) {
                  orderedFacets.push(facet);
              }
          });
      });
      return orderedFacets;
  }
  return facets;
};


const facetsAdapter = (searchResponse, options) => {
  const { searchFacetList = [] } = searchResponse.searchFacetInfo || {};

  const facets = adaptFacets(searchFacetList);
  const allowedFacets = (options && getAllowedFacets(options)) ?? {}
  const orderedFacets = orderFacetData(facets, allowedFacets)

  return {
    facets: orderedFacets,
  };
};

export default facetsAdapter;
