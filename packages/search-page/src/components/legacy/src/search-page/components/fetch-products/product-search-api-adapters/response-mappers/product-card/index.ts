// @ts-nocheck
import { ChildProduct, SearchedProduct } from '../../../types';
import { colorSwatchesMapper } from './color-swatches-mapper';
import { FIRST_COLOR, FIRST_IMAGE } from './constants';
import dropshipMapper from './dropship-mapper';
import { marketingFlagMapper } from './marketing-flag-mapper';
import { priceMapper } from './price-mapper';
import { SearchSwatchesColorProps } from './types';

// TODO: fix reviewCount and reviewScore types on mfe app helpers
export const productsMapper = (
  products: SearchedProduct[],
  selectedSizes: string[],
  isONPercentOff: boolean,
  isPLPDynamicBadgeEnabled: boolean,
  isPLPLevelFieldsEnabled: boolean,
) =>
  products?.map(
    (product): ChildProduct => {
      const {
        brand,
        colors,
        defaultThumbImage,
        name,
        priceRange,
        reviewCount,
        reviewScore,
        salePriceRange,
      } = product;
      const styleID = product.id;
      const firstColor = colors?.[FIRST_COLOR];
      const businessCatalogItemId = firstColor?.id;
      const zoomImage = firstColor?.images?.find(({ type }) => type === 'zoomImage');
      const categoryLargeImage = {
        path: defaultThumbImage || firstColor?.images?.[FIRST_IMAGE]?.path,
      };
      const zoomImages = {
        p01ZoomImagePath: zoomImage?.path,
      };
      const inventoryStatusId = firstColor?.backOrderInventoryCount;
      const { sellerName, isDropship, marketingFlag, vendorId } = dropshipMapper(product);
      const { newMarketingFlag } = marketingFlagMapper(product, isPLPDynamicBadgeEnabled, isPLPLevelFieldsEnabled);

      const badgingName = () => {
          return newMarketingFlag.badgingName;
      };

      const marketingFlagName = () => {
          return isDropship ? marketingFlag : newMarketingFlag.marketingFlagName
      }

      const marketingFlagData = {
        marketingFlagName: marketingFlagName(),
        badgingName: badgingName(),
      };

      const swatchesProps = colorSwatchesMapper(
        colors ?? [],
        name ?? '',
        selectedSizes ?? [],
        brand,
        isONPercentOff,
        isPLPDynamicBadgeEnabled,
        isPLPLevelFieldsEnabled,
      );

      const firstPriceType = (swatchesProps?.colorArray[0] as SearchSwatchesColorProps)?.priceType;
      const pricePercentageOff = (swatchesProps?.colorArray[0] as SearchSwatchesColorProps)?.price?.maxPercentageOff;

      const price = priceMapper({ brand, priceRange, salePriceRange, priceType: firstPriceType, pricePercentageOff }, isONPercentOff);

      return {
        // TODO: we don't know what avImages means, it'll be empty object for now
        styleID,
        altText: name,
        avImages: {},
        businessCatalogItemId,
        categoryLargeImage,
        defaultSizeVariantId: '1',
        inventoryStatusId,
        isDropship,
        marketingFlag: marketingFlagData,
        name,
        price,
        reviewCount: (reviewCount as unknown) as number,
        reviewScore: reviewScore as number,
        sellerName,
        swatchesProps,
        vendorId,
        zoomImages,
      };
    },
  );
