// @ts-nocheck
import { styled, css, Brands } from "@ecom-next/core/react-stitch";
import { Theme } from '@ecom-next/core/react-stitch/types';

interface SortByTitleProps {
  theme: Theme;
  withChevron?: boolean;
}

const getChevronBackground = (color: string) => {
  const viewbox =
    "0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z";
  const chevron = `svg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='${viewbox}' style='fill:${color.replace(
    '#',
    '',
  )};fill-rule:evenodd'`;
  return `url("data:image/svg+xml;charset=utf8,%3C${chevron} /%3E%3C/svg%3E") no-repeat`;
};

export const SortByTitle = styled.label<SortByTitleProps>(
  ({ theme, withChevron = true }: SortByTitleProps) => {
    const background = getChevronBackground(theme.color.b1);
    const isAt = theme.brand === Brands.Athleta;

    return css({
      color: theme.color.b1,
      fontWeight: isAt ? 'bold' : 600,
      fontFamily: theme.brandFont.fontFamily,
      textTransform: 'uppercase',
      fontSize: isAt ? '1.125rem' : '0.88rem',
      background: withChevron ? background : '',
      backgroundPositionX: 'right',
      backgroundPositionY: '50%',
      paddingRight: withChevron ? '0.8rem' : 0,
      textAlign: 'right',
    });
  },
);

export default SortByTitle;
