// @ts-nocheck
import { Facet } from '@ecom-next/plp-ui/legacy/search-service/types/engines/productsearch-service/types/search/response';
import { simpleFacetMapper } from './simple-facet-mapper';
import { rangeFacetMapper } from './range-facet-mapper';
import { SearchFacet } from '../../../types';
import { complexFacetMapper } from './complex-facet-mapper';

export const facetsMapper = (facets?: Facet[]): SearchFacet[] =>
  facets
    ?.filter(facet => facet.name !== 'gender')
    .map(facet => {
      switch (facet.type) {
        case 'complex':
          return complexFacetMapper(facet);
        case 'range':
          return rangeFacetMapper(facet);
        case 'multi-select':
        case 'single-select':
          return simpleFacetMapper(facet);
      }
    }) as SearchFacet[];
