// @ts-nocheck
import { LegacyFacetParameters } from '../../../types';
import {
  RemoveEventHandlers,
  RegisterEventHandlers,
  UrlQueryParametersValue,
  UrlFetchParameters,
} from '../fetch-products';

export type FacetsParameter = {
  getDefaultValue: () => UrlFetchParameters;
  registerEventHandlers: RegisterEventHandlers;
  removeEventHandlers: RemoveEventHandlers;
  getStateOnEvent: (
    eventData: HashChangeEvent,
    previousState: UrlFetchParameters,
  ) => UrlFetchParameters;
  getStateOnParameterUpdate: (
    previousState: UrlFetchParameters,
    paramId: string,
    updatedFacetData: UrlQueryParametersValue,
  ) => UrlFetchParameters;
  persistState: (state: UrlFetchParameters) => void;
};

export const removeNonFacetKeysFromHashObject: (object: unknown) => LegacyFacetParameters;
export const facetParameter: FacetsParameter;
