// @ts-nocheck
import { createSearchService } from "@ecom-next/plp-ui/legacy/search-service";
import { addPageAction, logError } from "@ecom-next/core/reporting";
import { removeNonFacetKeysFromHashObject } from "../facet-query-parameter";
import { Brands } from "@ecom-next/core/legacy/utility";
import { Locales, Market } from "@ecom-next/core/legacy/app-state-provider/types";
import {
  LegacyPDSSearchResponse,
  STATUSES,
} from "./types";
import { SearchResponseSchema } from "./types/zod-types";
import {
  LegacyFacetParameters,
  SearchConfig,
  SearchService,
} from "../../../types";
import {
  QueryParameters,
  requestMapper,
  responseMapper,
} from "./product-search-api-adapters";
import { features } from "../../experiments-config/constants";
import { AbSeg } from "../../../search-page/app-state/types";
import { z } from "zod";
import { sendMockData } from "../../../mock-product-search/mock-product-search-helper";

const hasFacetsSelected = (
  searchParameters: LegacyFacetParameters | Record<string, string>
) => {
  const searchParameterKeys = removeNonFacetKeysFromHashObject(
    Object.keys(searchParameters)
  );

  return Object.keys(searchParameterKeys).length > 0;
};

type PSApiSearchResponseType = z.infer<typeof SearchResponseSchema>;

const getStatus = (
  { productCategoryFacetedSearch, error }: LegacyPDSSearchResponse,
  searchParameters: LegacyFacetParameters | Record<string, string>
): STATUSES => {
  const { totalItemCount = "0", autoCorrectedText } =
    productCategoryFacetedSearch ?? {};
  const hasProductsToShow = Number.parseInt(totalItemCount as string, 10) > 0;

  if (error || (!hasProductsToShow && !hasFacetsSelected(searchParameters))) {
    return STATUSES.NO_PRODUCTS;
  }
  if (!hasProductsToShow && hasFacetsSelected(searchParameters)) {
    return STATUSES.NO_PRODUCTS_WITH_FACETS;
  }
  if (autoCorrectedText) {
    return STATUSES.AUTO_CORRECTED;
  }
  return STATUSES.NORMAL;
};

const addStatus = (
  searchResponse: LegacyPDSSearchResponse,
  searchParameters: LegacyFacetParameters | Record<string, string | string[]>
): LegacyPDSSearchResponse => ({
  ...searchResponse,
  status: getStatus(searchResponse, searchParameters),
});

let searchService: SearchService;

export const fetchFromSearch = async (
  brand: Brands,
  rawFacetParameters: LegacyFacetParameters,
  searchText: string,
  market: Market,
  locale: Locales,
  enabledFeatures: Record<string, boolean>,
  engineConfig: SearchConfig,
  abSeg: AbSeg,
  isNewProductSearchCall?: boolean,
  swatchesEnableSort?: boolean,
  swatchesSortBy?: string,
  isONPercentOff: boolean = false,
  isPLPDynamicBadgeEnabled: boolean = false,
  isPLPLevelFieldsEnabled: boolean = false
): Promise<LegacyPDSSearchResponse> => {
  if (!searchService) {
    searchService = createSearchService({
      abbrBrand: brand,
      market,
      locale,
      engineConfig,
      enabledFeatures,
      abSeg,
    });
  }

  const isMockProductSearchEnabled =
    isNewProductSearchCall && enabledFeatures[features.SEARCH_MOCK_DATA];

  const facetParameters = isNewProductSearchCall
    ? requestMapper(rawFacetParameters)
    : rawFacetParameters;
  
  const parameters: QueryParameters = {
    ...facetParameters,
  };

  isNewProductSearchCall && (parameters.includeMarketingFlagsDetails = "true");
  if (swatchesEnableSort) {
    parameters.enableSwatchSort = true;
    parameters.sortSwatchesBy = swatchesSortBy;
  }

  try {
    const response: LegacyPDSSearchResponse | PSApiSearchResponseType =
      isMockProductSearchEnabled
        ? sendMockData(brand, locale)
        : await searchService.search(parameters, searchText);

    const transformedResponse = isNewProductSearchCall
      ? responseMapper(
          response as PSApiSearchResponseType,
          rawFacetParameters,
          enabledFeatures,
          isONPercentOff,
          isPLPDynamicBadgeEnabled,
          isPLPLevelFieldsEnabled
        )
      : (response as LegacyPDSSearchResponse);

    const parsedData = SearchResponseSchema.safeParse(transformedResponse);
    if (!parsedData.success) {
      // eslint-disable-next-line no-console
      console.log(parsedData);
    }

    return addStatus(transformedResponse, facetParameters);
  } catch (error) {
    const status = STATUSES.ERROR;
    logError(error as Error, { status });
    addPageAction("SearchResponseError", { status });
    return { status };
  }
};
