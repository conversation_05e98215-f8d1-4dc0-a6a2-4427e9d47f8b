import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardEventHandler, useState } from 'react';
import { Magnifier } from '@ecom-next/core/migration/icons';
import { ClearIconWrapper as ClearIcon } from './ClearIconWrapper';

type SearchInputProps = {
  onResults: (results: Array<string>) => void;
};

export const SearchInput = ({ onResults }: SearchInputProps) => {
  const [userInput, setUserInput] = useState('');

  const router = useRouter();
  const url = `https://athleta.gap.com/browse/search.do?searchText=${userInput}`;
  const routeToSearch = () => router.push(url);

  const clearInput = () => {
    setUserInput('');
  };

  const onKeyDown: KeyboardEventHandler = e => {
    if (e.code === 'Escape') {
      clearInput();
    }
    if (e.code === 'Enter' && userInput) {
      routeToSearch();
    }
  };

  const onInput: FormEventHandler = e => {
    const inputValue = (e.target as HTMLInputElement).value;
    setUserInput(inputValue);
    // Here we would make a call to search service with "inputValue"
    // and get the search "results". The we would set the results using
    // the on results function provided to the component
    onResults([]);
  };

  const verticalBar = <div className={`border-[#A7A9AC"] h-[24px] w-[6px] border-l-[2px]`} />;

  return (
    <>
      <ClearIcon clearInput={clearInput}>
        <input
          type='search'
          spellCheck='false'
          aria-label='Search site content'
          className='search-input group-hover:placeholder:text-bk placeholder:text-wh w-[180px] bg-transparent text-base font-light outline-none placeholder:transition placeholder:duration-200 placeholder:ease-in-out'
          placeholder={'Search'}
          onInput={onInput}
          onKeyDown={onKeyDown}
          value={userInput}
        />
      </ClearIcon>
      {userInput ? verticalBar : <span className='w-[6px]' />}
      <button
        data-testid='search-button'
        disabled={userInput.length === 0}
        className='group-hover:fill-b2 h-6 w-6 fill-white duration-200'
        onClick={routeToSearch}
      >
        <Magnifier />
      </button>
    </>
  );
};
