import { useContext } from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useContent, MarketingPlacementWithSitewide } from '../legacy/content';
import { NavigationContext } from '../legacy/navigation/navigation-provider';
import { FindStateProvider } from '@ecom-next/plp-ui/legacy/find-state-provider';
import { KeywordsFromWCDType } from '../legacy/brand-bar/types';
import { useHeaderTheming } from '../legacy/compressed-header/HeaderContext';
import { useStickyStatus } from '../legacy/sticky-manager';
import { HamburgerNav } from './v1';
import { type NavigationContext as NavigationContextType } from './v1/utils/types';

const defaultBrandSiteData = {
  currentBrandCode: '',
  currentBrandSiteId: '',
  gidBrandSites: {},
};

export default function RewriteHamNav({ exclusionIds }: { exclusionIds?: string[] }) {
  const { abSeg = {}, errorLogger = () => {}, brandSiteData = defaultBrandSiteData, topSearchTerms } = useAppState();
  const { contentData } = useContent();
  const navigationData = useContext(NavigationContext);
  const { isCompressedHeader } = useHeaderTheming();

  const keywordsFromWCD: KeywordsFromWCDType = (contentData?.sitewide?.search || {}) as KeywordsFromWCDType;

  return (
    <FindStateProvider
      abSeg={abSeg}
      errorLogger={errorLogger}
      isCompressedHeader={isCompressedHeader}
      keywordsFromWCD={keywordsFromWCD}
      topSearchTerms={topSearchTerms}
      useStickyStatus={useStickyStatus}
    >
      <HamburgerNav
        brandSiteData={brandSiteData}
        exclusionIds={exclusionIds as string[]}
        MarketingPlacementWithSitewide={MarketingPlacementWithSitewide}
        navigationContextProps={navigationData as NavigationContextType}
        shouldUseHeaderRedesign2024={false}
      />
    </FindStateProvider>
  );
}
