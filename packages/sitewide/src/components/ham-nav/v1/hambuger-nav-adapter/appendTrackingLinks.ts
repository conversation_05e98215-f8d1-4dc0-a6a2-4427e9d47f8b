import { HamburgerNavDataItem, HamburgerNavData } from '../utils/types/navigation';

// eslint-disable-next-line @typescript-eslint/default-param-last
const insertTrackingIntoUrl = (url = '', trackingLink: string): string => {
  let [beforeHash, afterHash] = url.split('#');

  beforeHash = /\?/.test(url) ? beforeHash : `${beforeHash}?`;
  afterHash = afterHash === undefined ? '' : `#${afterHash}`;

  return `${beforeHash}&nav=${trackingLink}${afterHash}`;
};

const TRACKING_COMPONENT_NAME = 'hamnav';

type LinkNode = Extract<HamburgerNavDataItem, { link: string }>;

function hasLink(node: HamburgerNavDataItem): node is LinkNode {
  return ['category', 'link', 'sale', 'subcategory'].includes(node.type) && !!(node as LinkNode).link;
}

const appendLinkToCategoryNode = (node: HamburgerNavDataItem, divisionName = '', sectionName = '', categoryName = ''): HamburgerNavDataItem => {
  let hamNavItem = { ...node } as HamburgerNavDataItem;

  const appendLinkParams = {
    divisionName: node.type === 'division' ? node.name : divisionName,
    sectionName: node.type === 'section' ? node.name : sectionName,
    categoryName: node.type === 'category' ? node.name : categoryName,
  };

  if (hasLink(node)) {
    let url = `${TRACKING_COMPONENT_NAME}:${divisionName}:${sectionName}`;
    if (categoryName !== '') {
      url += `:${categoryName}`;
    }
    url += `:${node.name}`;

    hamNavItem = {
      ...hamNavItem,
      link: insertTrackingIntoUrl(node.link, encodeURIComponent(url)),
    } as HamburgerNavDataItem;
  }

  if ((node.type === 'division' || node.type === 'section' || node.type === 'category') && node.children && node.children?.length > 0) {
    const children = (node.children || []) as HamburgerNavDataItem[];
    hamNavItem = {
      ...hamNavItem,
      children: children.map((childNode: HamburgerNavDataItem) =>
        appendLinkToCategoryNode(childNode, appendLinkParams.divisionName, appendLinkParams.sectionName, appendLinkParams.categoryName)
      ),
    } as HamburgerNavDataItem;
  }

  return hamNavItem;
};

const appendTrackingLinks = (inputData: HamburgerNavData): HamburgerNavData => inputData.map(node => appendLinkToCategoryNode(node, '', ''));

export default appendTrackingLinks;
