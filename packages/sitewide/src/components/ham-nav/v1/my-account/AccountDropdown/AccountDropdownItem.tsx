'use client';

import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';

interface AccountDropdownItemProps {
  link: string;
  text: string;
  shouldSeparate?: boolean;
}

const ItemContainer = styled.li<{ shouldSeparate?: boolean }>(({ theme, shouldSeparate }) => {
  return {
    ...theme.brandFont,
    borderTop: shouldSeparate ? `1px solid ${theme.color.gray20}` : undefined,
    color: theme.color.gray60,
    fontSize: '86.7%',
    lineHeight: 1.38,
  };
});

const ItemLink = styled.a({
  display: 'block',
  paddingTop: '.65em',
  paddingBottom: '.65em',
});

export const AccountDropdownItem = ({ link, text, shouldSeparate }: AccountDropdownItemProps): JSX.Element => (
  <ItemContainer shouldSeparate={shouldSeparate}>
    <ItemLink href={link}>{text}</ItemLink>
  </ItemContainer>
);
