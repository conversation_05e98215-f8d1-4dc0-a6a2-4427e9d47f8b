import React, { createRef } from 'react';
import { render, RenderResult, RenderOptions, act } from 'test-utils';
import { HamburgerNavRedesignProps, HamNavProviderProps, HamNavMenuProps } from '../../utils/types';
import { Provider as FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { EnabledFeatures } from '@ecom-next/plp-ui/legacy/experiments';
import { HamNavProvider } from '../HamNavContext';
import { AbSeg } from '@ecom-next/plp-ui/legacy/utils';
import { FindStateProvider } from '@ecom-next/plp-ui/legacy/find-state-provider';
import { HamNavMenu } from '../HamNavMenu';

jest.mock('../../utils/useMediaQuery');

describe('HamNavMenu', () => {
  const scrollHolder = createRef<HTMLDivElement>();

  const defaultProps: HamNavMenuProps = {
    activateLevel: jest.fn(),
    activatedLevels: [],
    animationDirection: 'right',
    backOneLevel: jest.fn(),
    currentlyDisplayedItems: [],
    hamburgerNavFooterData: undefined,
    scrollHolder,
  };

  type RenderHamNavMenuProps = {
    hamNavState?: Partial<HamNavProviderProps>;
    enabledFeatures?: EnabledFeatures;
    abSeg?: AbSeg;
    options?: RenderOptions;
  };

  const renderHamNavMenu = (props: Partial<HamburgerNavRedesignProps> = {}, renderProps: RenderHamNavMenuProps = {}): RenderResult => {
    const { hamNavState = {}, enabledFeatures = {}, abSeg = {}, options } = renderProps;
    const { ...hamNavProviderRest } = hamNavState;

    // TODO: Create a wrapper for these providers
    return render(
      <FindStateProvider abSeg={abSeg} errorLogger={jest.fn()} isCompressedHeader={false} keywordsFromWCD={{}} topSearchTerms={[]} useStickyStatus={jest.fn()}>
        <HamNavProvider {...(hamNavProviderRest as HamNavProviderProps)}>
          <FeatureFlagsProvider value={{ enabledFeatures }}>
            <HamNavMenu {...defaultProps} {...props} />
          </FeatureFlagsProvider>
        </HamNavProvider>
      </FindStateProvider>,
      options
    );
  };

  describe('when rendering HamNavMenu component', () => {
    const { useMediaQuery } = require('../../utils/useMediaQuery');

    beforeEach(() => {
      useMediaQuery.mockClear();
    });

    it('should render in landscape view', () => {
      useMediaQuery.mockReturnValue(true);
      const { container } = renderHamNavMenu();
      expect(container).toMatchSnapshot();
    });

    it('should render in portrait view', () => {
      useMediaQuery.mockReturnValue(false);
      const { container } = renderHamNavMenu();
      expect(container).toMatchSnapshot();
    });
  });
});
