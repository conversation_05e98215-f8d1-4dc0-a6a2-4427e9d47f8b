import React from 'react';
import { Accordion } from '@ecom-next/core/migration/accordion';
import { render, screen, fireEvent, RenderOptions, act } from 'test-utils';
import { HamburgerNavCategory, HamburgerNavDataCollectionItem } from '../../utils/types/navigation';
import { HamNavPanel } from '../HamNavPanel';

const hamNavItem: HamburgerNavCategory = {
  active: false,
  cid: '1',
  link: '/hamNavItemLink',
  name: 'hamNavItemName',
  type: 'category',
};

const anotherHamNavItem: HamburgerNavCategory = {
  active: false,
  cid: '2',
  link: '/anotherHamNavItemLink',
  name: 'anotherHamNavItemName',
  type: 'category',
};
const item: HamburgerNavDataCollectionItem = {
  active: false,
  children: [hamNavItem, anotherHamNavItem],
  cid: '3',
  name: 'panelToggleText',
  type: 'section',
};

const collapsedAccordionChildren: HamburgerNavCategory = {
  active: false,
  cid: '5',
  link: '/categorylink',
  name: 'categorylink',
  type: 'category',
};

const collapsedAccordion: HamburgerNavDataCollectionItem = {
  active: false,
  children: [collapsedAccordionChildren],
  cid: '4',
  name: 'collapsedDivision',
  type: 'section',
  isAccordionOpened: false,
};

function renderAccordion(renderConfig: RenderOptions = {}, isHamNavSubcatsEnabled = false): void {
  render(
    <Accordion defaultExpanded={[item.cid, hamNavItem.cid, anotherHamNavItem.cid]}>
      <HamNavPanel activateLevel={() => {}} isHamNavSubcatsEnabled={isHamNavSubcatsEnabled} item={item} />
      <HamNavPanel activateLevel={() => {}} isHamNavSubcatsEnabled={isHamNavSubcatsEnabled} item={collapsedAccordion} />
    </Accordion>,
    renderConfig
  );
}

describe('HamNavPanel', () => {
  it('when the panel starts closed, we should be able to open and click in a category', async () => {
    renderAccordion();
    // console.log(screen.debug());
    const hiddenCategoryLink = screen.queryByRole('link', {
      name: /categorylink/i,
    });
    expect(hiddenCategoryLink).not.toBeInTheDocument();

    const collapsedDivision = screen.getByRole('button', {
      name: /collapseddivision/i,
    });

    await act(async () => {
      fireEvent.click(collapsedDivision);
    });

    const visibleCategoryLink = screen.queryByRole('link', {
      name: /categorylink/i,
    });
    expect(visibleCategoryLink).toBeInTheDocument();
  });

  it('renders section headers', () => {
    renderAccordion();
    expect(screen.queryByRole('button', { name: item.name })).toBeInTheDocument();
  });

  it('displays section children', async () => {
    renderAccordion();
    expect(screen.queryByRole('link', { name: hamNavItem.name })).toBeInTheDocument();
    expect(screen.queryByRole('link', { name: anotherHamNavItem.name })).toBeInTheDocument();
  });
});
