'use client';

import React, { useContext } from 'react';
import { Brands } from '@ecom-next/core/react-stitch';
import { FeatureFlagsContext, FeatureFlagsContextData } from '@ecom-next/core/legacy/feature-flags';
import { AbSeg, isSearchExperimentActive, FIND_HAMNAV_SUBCATS_KEY } from '@ecom-next/plp-ui/legacy/utils';
import { Locales, useAppState } from '@ecom-next/sitewide/app-state-provider';
import { HamNavMenuProps } from '../utils/types';
import { useMediaQuery } from '../utils/useMediaQuery';
import { features } from '../utils/constants';
import { useFindState } from '@ecom-next/plp-ui/legacy/find-state-provider';
import { HamNavMenuTopArea } from './HamNavMenuTopArea';
import { HamNavMenuScrollableArea } from './HamNavMenuScrollableArea';

const getHamNavSubcatsStatus = (abSeg: AbSeg, brandName: Brands, enabledFeatures: Record<string, boolean>, locale: Locales): boolean => {
  const isHamNavSubcatsFeatureFlagEnabled = enabledFeatures[features.FIND_HAMNAV_SUBCATS];
  const isHamNavSubcatsExperimentActive = isSearchExperimentActive(brandName, locale, FIND_HAMNAV_SUBCATS_KEY, abSeg);
  return isHamNavSubcatsFeatureFlagEnabled && isHamNavSubcatsExperimentActive;
};

export const HamNavMenu = ({
  activateLevel,
  activatedLevels,
  animationDirection,
  backOneLevel,
  currentlyDisplayedItems,
  hamburgerNavFooterData,
  scrollHolder,
}: HamNavMenuProps): JSX.Element => {
  const { abSeg } = useFindState();
  const { brandName, locale } = useAppState();
  const { enabledFeatures } = useContext(FeatureFlagsContext as React.Context<FeatureFlagsContextData>);
  const isLandscape = useMediaQuery('(orientation: landscape)');

  const isHamNavSubcatsEnabled = getHamNavSubcatsStatus(abSeg, brandName as Brands, enabledFeatures, locale);

  const topArea = (
    <HamNavMenuTopArea
      activatedLevels={activatedLevels}
      animationDirection={animationDirection}
      backOneLevel={backOneLevel}
      isHamNavSubcatsEnabled={isHamNavSubcatsEnabled}
    />
  );

  const scrollableArea = (
    <HamNavMenuScrollableArea
      activateLevel={activateLevel}
      activatedLevels={activatedLevels}
      animationDirection={animationDirection}
      currentlyDisplayedItems={currentlyDisplayedItems}
      hamburgerNavFooterData={hamburgerNavFooterData}
      isHamNavSubcatsEnabled={isHamNavSubcatsEnabled}
    />
  );

  return isLandscape ? (
    <div className='no-scrollbar overflow-y-scroll' id='hamnavScrollHolder' ref={scrollHolder}>
      {topArea}
      {scrollableArea}
    </div>
  ) : (
    <>
      {topArea}
      <div className='no-scrollbar overflow-y-scroll' id='hamnavScrollHolder' ref={scrollHolder}>
        {scrollableArea}
      </div>
    </>
  );
};
