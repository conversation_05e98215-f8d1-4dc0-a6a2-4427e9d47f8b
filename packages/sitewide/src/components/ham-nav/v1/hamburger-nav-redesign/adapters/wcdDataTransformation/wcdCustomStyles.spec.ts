import { CSSProperties } from 'react';
import { wcdCustomStyles } from './wcdCustomStyles';

describe('wcdCustomStyles', () => {
  describe('when passing CSS properties and fontSize on custom styles', () => {
    const styles: CSSProperties = {
      color: 'red',
      fontWeight: 'bold',
      fontSize: 18,
    };

    it('should return CSS object with all properties other fontSize', () => {
      const stylesResult = wcdCustomStyles(styles);
      expect(stylesResult).toHaveProperty('color', 'red');
      expect(stylesResult).not.toHaveProperty('fontSize');
    });
  });

  describe('when passing CSS properties and fontSize on custom styles to a child element', () => {
    const styles: CSSProperties = {
      ' button': {
        backgroundColor: 'yellow',
        fontSize: 16,
        color: 'blue',
        ' span': {
          fontSize: 22,
          fontWeight: 'bold',
        },
      },
    };

    it('should return CSS object with all properties other fontSize', () => {
      const stylesChildResult = wcdCustomStyles(styles)[' button'] as CSSProperties;
      expect(stylesChildResult).toHaveProperty('backgroundColor', 'yellow');
      expect(stylesChildResult).not.toHaveProperty('fontSize');

      const stylesDeepResult = stylesChildResult[' span'];
      expect(stylesDeepResult).toHaveProperty('fontWeight', 'bold');
      expect(stylesDeepResult).not.toHaveProperty('fontSize');
    });
  });
});
