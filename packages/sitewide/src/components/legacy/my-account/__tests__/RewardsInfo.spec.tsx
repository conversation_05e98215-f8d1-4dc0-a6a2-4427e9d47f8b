// @ts-nocheck
import { merge } from 'lodash';
import { RenderOptions, render, screen, act } from '@sitewide/components/legacy/setupTests/test-helpers';
import { RewardsInfo } from '../AccountDropdown/RewardsInfo';
import { Brand } from '@sitewide/components/legacy/types';
import { features } from '@sitewide/constants';

const bouncebackText = 'GapCash';
const totalRewardValue = 500;
const renderOptionsPersonalizationData = {
  personalizationData: {
    virtualValueInterrupterStatus: {
      bouncebackActive: true,
      bouncebackText,
      rewardsActive: true,
      totalRewardValue,
    },
  },
};

const renderRewardsInfo = (options: RenderOptions = {}): void => {
  render(<RewardsInfo />, {
    ...options,
  });
};

const renderRewardsInfoWithGapCash = (options: RenderOptions = {}): void => {
  renderRewardsInfo({
    ...renderOptionsPersonalizationData,
    ...options,
  });
};

const renderRewardsInfoUserWithLoggedInUserWithGapCash = (options: RenderOptions = {}): void => {
  renderRewardsInfoWithGapCash({
    ...merge(renderOptionsPersonalizationData, {
      personalizationData: {
        isLoggedInUser: true,
      },
    }),
    ...options,
  });
};
const renderRewardsInfoUserWithRecognizedUserWithGapCash = (options: RenderOptions = {}): void => {
  renderRewardsInfoWithGapCash({
    ...merge(renderOptionsPersonalizationData, {
      personalizationData: {
        isLoggedInUser: false,
        isRecognizedUser: true,
      },
    }),
    ...options,
  });
};

const queryRewardsInfo = (): HTMLElement | null => screen.queryByTestId('rewards-info');

const queryTotalRewardsValue = (totalRewardValue: number): HTMLElement | null => screen.queryByText(`$${totalRewardValue}`);

const queryRewardsMessage = (): HTMLElement | null => screen.queryByText(`You have Rewards & ${bouncebackText}!`);

const queryLink = (): HTMLElement | null => screen.queryByRole('link');

describe('RewardsInfo', () => {
  it('should not render when should hide rewards feature flag is active', () => {
    renderRewardsInfo({
      enabledFeatures: { [features.SWF_HIDE_REWARDS_INFO]: true },
    });
    expect(queryRewardsInfo()).not.toBeInTheDocument();
  });

  describe('when there is a bounceback active', () => {
    beforeEach(() => {
      renderRewardsInfo({
        personalizationData: {
          virtualValueInterrupterStatus: {
            bouncebackActive: true,
            bouncebackText,
            rewardsActive: true,
            totalRewardValue,
          },
        },
      });
    });

    it('should render rewards message', () => {
      expect(queryRewardsMessage()).toBeInTheDocument();
    });

    it('should render rewards value', () => {
      expect(queryTotalRewardsValue(totalRewardValue)).toBeInTheDocument();
    });
  });

  describe('should render a link redirecting to', () => {
    it("'/my-account/sign-in?targetURL=/loyalty/customer-value' when user is not logged in", () => {
      renderRewardsInfoWithGapCash();

      const link = queryLink();
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/my-account/sign-in?targetURL=/loyalty/customer-value');
    });

    it("'/loyalty/customer-value' when logged in user browses Athleta US", () => {
      renderRewardsInfoUserWithLoggedInUserWithGapCash({
        appState: {
          brandName: Brand.Athleta,
          market: 'us',
        },
      });

      const link = queryLink();
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/loyalty/customer-value');
    });

    it("'/loyalty/customer-value' when logged in user is from CA", () => {
      renderRewardsInfoUserWithLoggedInUserWithGapCash({
        appState: {
          brandName: Brand.Gap,
          market: 'ca',
        },
      });

      const link = queryLink();
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/loyalty/customer-value');
    });

    it("'/loyalty/customer-value?target=EarnAndRedeem' when user is logged in a brand that has Gap Cash", () => {
      renderRewardsInfoUserWithLoggedInUserWithGapCash();

      const link = queryLink();
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/loyalty/customer-value?target=EarnAndRedeem');
    });
    it("'/loyalty/customer-value?target=EarnAndRedeem' when user is recognized in a brand that has Gap Cash", () => {
      renderRewardsInfoUserWithRecognizedUserWithGapCash();

      const link = queryLink();
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/loyalty/customer-value?target=EarnAndRedeem');
    });
  });

  describe('when there is no personalization data', () => {
    beforeEach(() => {
      renderRewardsInfo();
    });

    it('should not render', () => {
      expect(queryRewardsInfo()).not.toBeInTheDocument();
    });
  });

  describe('when there is no bounceback active', () => {
    beforeEach(() => {
      renderRewardsInfo({
        personalizationData: {
          virtualValueInterrupterStatus: {
            bouncebackActive: false,
            bouncebackText,
          },
        },
      });
    });

    it('should not render', () => {
      expect(queryRewardsInfo()).not.toBeInTheDocument();
    });
  });
});
