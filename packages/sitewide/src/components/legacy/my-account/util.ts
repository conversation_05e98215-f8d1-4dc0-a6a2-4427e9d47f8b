// @ts-nocheck
"use client";
import {PersonalizationContextData} from "@ecom-next/core/legacy/personalization-provider/types"

type RewardsData = PersonalizationContextData["virtualValueInterrupterStatus"]

export const getRewardsMessage = (
  virtualValueInterrupterStatus: RewardsData
): string => {
  if (!virtualValueInterrupterStatus) {
    return ""
  }
  const {
    rewardsActive,
    bouncebackActive,
    bouncebackText,
  } = virtualValueInterrupterStatus
  if (bouncebackActive && rewardsActive) {
    return `You have Rewards & ${bouncebackText}!`
  }
  if (bouncebackActive && !rewardsActive) {
    return `You have ${bouncebackText}!`
  }
  if (!bouncebackActive && rewardsActive) {
    return "Redeem your Rewards!"
  }
  return ""
}
