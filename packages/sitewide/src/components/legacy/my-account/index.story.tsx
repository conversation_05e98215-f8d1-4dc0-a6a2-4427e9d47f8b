// @ts-nocheck
"use client";
import {Provider as FeatureFlagsProvider} from "@ecom-next/core/legacy/feature-flags"
import {
  StitchInverseStyleProvider,
  StitchStyleProvider,
} from "@ecom-next/core/react-stitch"
import {PersonalizationContext} from "@ecom-next/sitewide/personalization-provider"
import {PersonalizationContextData} from "@ecom-next/core/legacy/personalization-provider/types"
import {merge} from "lodash"
import {experiments, features} from "@sitewide/components/legacy/sitewide-constants"
import MyAccount from "."
import personalizationDataBothRewards from "./data/personalization/personalizationDataBothRewards.json"
import personalizationDataBounceback from "./data/personalization/personalizationDataBounceback.json"
import personalizationDataLoggedIn from "./data/personalization/personalizationDataLoggedIn.json"
import personalizationDataVirtualValueRewards from "./data/personalization/personalizationDataVirtualValueRewards.json"
import {clientSideSessionStorage} from "@ecom-next/plp-ui/legacy/utils"
import {AbSeg, Brand} from "@sitewide/components/legacy/types"
import {SitewideStateProvider} from "../app-state"
import {defaultSitewideAppState} from "../../../docs/constants"

const renderWithData = ({
  personalizationData = {},
  brandName = Brand.Gap,
  enabledFeatures = {},
  abSeg = {},
}: {
  personalizationData?: Partial<PersonalizationContextData>
  brandName?: Brand
  enabledFeatures?: Record<string, boolean>
  abSeg?: AbSeg
} = {}): JSX.Element => {
  clientSideSessionStorage?.setItem("vvi_closed", "false")
  const siteWideState = merge({}, defaultSitewideAppState, {
    enabledFeatures: {
      [features.PDP_REDESIGN_2022]: true,
      [features.SWF_INVERT_UNIVERSAL_BAR]: true,
      [features.BR_COLORS_2023]: true,
      ...enabledFeatures,
    },
    abSeg,
  })
  return (
    <SitewideStateProvider value={siteWideState}>
      <FeatureFlagsProvider
        value={{enabledFeatures: {...siteWideState.enabledFeatures}}}
      >
        <StitchStyleProvider
          brand={brandName}
          enabledFeatures={siteWideState.enabledFeatures}
        >
          <StitchInverseStyleProvider
            invert={!!siteWideState.enabledFeatures?.[features.BR_COLORS_2023]}
          >
            <div css={{textAlign: "center"}}>
              <PersonalizationContext.Provider
                // @ts-ignore TODO: remove ignore once proper types are exported for personalization provider
                value={personalizationData as PersonalizationContextData}
              >
                <MyAccount componentProps={{}} />
              </PersonalizationContext.Provider>
            </div>
          </StitchInverseStyleProvider>
        </StitchStyleProvider>
      </FeatureFlagsProvider>
    </SitewideStateProvider>
  )
}

export default {
  title: "Sitewide/MyAccount",
  parameters: {
    backgrounds: {default: "universal-bar"},
  },
}

export const userIsLoggedIn = (): JSX.Element =>
  renderWithData({
    personalizationData: (personalizationDataLoggedIn as unknown) as PersonalizationContextData,
  })

export const userIsNotLoggedInOrRecognized = (): JSX.Element => renderWithData()

export const personalizationDataHasYetNotArrived = (): JSX.Element =>
  renderWithData({
    personalizationData: {isEmpty: true, isLoggedInUser: false},
  })

export const userHasVirtualValueRewards = (): JSX.Element =>
  renderWithData({
    personalizationData: (personalizationDataVirtualValueRewards as unknown) as PersonalizationContextData,
  })

export const userHadBounceback = (): JSX.Element =>
  renderWithData({
    personalizationData: (personalizationDataBounceback as unknown) as PersonalizationContextData,
  })

export const userHasVirtualValueRewardsPlusBounceback = (): JSX.Element =>
  renderWithData({
    personalizationData: (personalizationDataBothRewards as unknown) as PersonalizationContextData,
  })

export const InvertButton = (): JSX.Element =>
  renderWithData({
    brandName: Brand.BananaRepublic,
  })

export const HeaderRedesign2024 = ({
  brand,
  loginStatus,
}: {
  brand: Brand
  loginStatus: "logged in" | "logged out"
}): JSX.Element => {
  const personalizationData =
    loginStatus === "logged in"
      ? ((personalizationDataLoggedIn as unknown) as PersonalizationContextData)
      : {}
  return renderWithData({
    personalizationData,
    brandName: brand,
    abSeg: {
      [experiments.SWE_2024_HEADER_REDESIGN]: "a",
    },
    enabledFeatures: {
      [features.SWF_2024_HEADER_REDESIGN_GAP]: brand === Brand.Gap,
      [features.SWF_2024_HEADER_REDESIGN_GAPFS]:
        brand === Brand.GapFactoryStore,
      [features.SWF_2024_HEADER_REDESIGN_BR]: brand === Brand.BananaRepublic,
      [features.SWF_2024_HEADER_REDESIGN_BRFS]:
        brand === Brand.BananaRepublicFactoryStore,
      [features.SWF_2024_HEADER_REDESIGN_ON]: brand === Brand.OldNavy,
      [features.SWF_2024_HEADER_REDESIGN_AT]: brand === Brand.Athleta,
    },
  })
}
HeaderRedesign2024.argTypes = {
  brand: {
    name: "Brand",
    description: "Brand name",
    control: {type: "select"},
    options: Object.values(Brand),
  },
  loginStatus: {
    name: "Logged status",
    description: "Status of user login",
    control: {type: "select"},
    options: ["logged in", "logged out"],
  },
}
HeaderRedesign2024.args = {
  brand: Brand.Gap,
  loginStatus: "logged out",
}

personalizationDataHasYetNotArrived.parameters = {eyes: {include: false}}
