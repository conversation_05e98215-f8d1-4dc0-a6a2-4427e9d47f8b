// @ts-nocheck
"use client";
import {features} from "@sitewide/components/legacy/sitewide-constants"
import {useFeature} from "@sitewide/components/legacy/sitewide-constants"
import {
  insertCommunityLink,
  AccountDropdownItems,
  USandCADropdownProps,
} from "./updateAccountDropdownMenuItemsLinks"

export const USDropdownItems = ({
  accountDropdownItems,
  isLoggedInUser,
  shouldUseInverseBrColors,
}: USandCADropdownProps): JSX.Element => {
  const dropdownItems = [...accountDropdownItems]

  const isUsCommunityEnabled = useFeature(features.US_COMMUNITY_DROPDOWN, {
    isBranded: true,
  })

  if (isUsCommunityEnabled) {
    insertCommunityLink(dropdownItems)
  }

  return (
    <AccountDropdownItems
      {...{
        shouldUseInverseBrColors,
        dropdownItems,
        isLoggedInUser,
        shouldInsertCommunityLink: isUsCommunityEnabled,
      }}
    />
  )
}
