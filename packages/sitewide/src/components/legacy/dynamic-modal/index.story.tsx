// @ts-nocheck
"use client";
import {AnalyticsEvents} from "@ecom-next/core/legacy/analytics/types"
import {
  miniData,
  standardData,
  maxData,
  withMarketingData,
  pageVisitData,
  redisplayAfterData,
  mobileFitSizeToContentData,
} from "./__fixtures__"
import readme from "./README.md"
import DynamicModal from "./DynamicModal"
import {DynamicModalProps} from "./types"

type DefaultDynamicModalProps = {
  content: {
    analytics: AnalyticsEvents<string>
  } & Omit<DynamicModalProps, string>
}

const defaultProperties = {
  title: "Sitewide/DynamicModal",
  parameters: {
    notes: readme,
  },
}

export default defaultProperties

export const Mini = ({content}: DefaultDynamicModalProps): JSX.Element => (
  <DynamicModal businessUnitId="test-id" data={content} />
)

Mini.args = {
  content: miniData,
}
Mini.parameters = {eyes: {waitBeforeCapture: 4000}}

export const Standard = ({content}: DefaultDynamicModalProps): JSX.Element => (
  <DynamicModal businessUnitId="test-id" data={content} />
)

Standard.args = {
  content: standardData,
}
Mini.parameters = {eyes: {waitBeforeCapture: 4000}}

export const Max = ({content}: DefaultDynamicModalProps): JSX.Element => (
  <DynamicModal businessUnitId="test-id" data={content} />
)

Max.args = {
  content: maxData,
}
Mini.parameters = {eyes: {waitBeforeCapture: 4000}}

export const WithMarketingContent = ({
  content,
}: DefaultDynamicModalProps): JSX.Element => (
  <DynamicModal businessUnitId="test-id" data={content} />
)

WithMarketingContent.args = {
  content: withMarketingData,
}

export const ShowOnNthPageVisit = ({
  content,
}: DefaultDynamicModalProps): JSX.Element => {
  const showDocument = (
    <div style={{margin: "50px", maxWidth: "50%"}}>
      <div>pageVisit={pageVisitData.displayOptions?.pageVisit}</div>
      <br />
      <br />
      <div>
        For the modal to show: Refresh page{" "}
        {pageVisitData.displayOptions?.pageVisit} times
      </div>
      <br />
      <div style={{color: "grey"}}>
        Note:
        <div>Clear local storage before viewing this example</div>
        <div>The modal will not show if:</div>
        <div>
          Page visits for this doc are greater than{" "}
          {pageVisitData.displayOptions?.pageVisit} in the local storage. If
          that is the case, clear local storage to see the desired functionality
          again.
        </div>
      </div>
    </div>
  )

  return (
    <div>
      {showDocument}
      <DynamicModal businessUnitId="test-id" data={content} />
    </div>
  )
}

ShowOnNthPageVisit.parameters = {eyes: {include: false}}
ShowOnNthPageVisit.args = {
  content: pageVisitData,
}

export const ReDisplayAfter = ({
  content,
}: DefaultDynamicModalProps): JSX.Element => {
  const showDocument = (
    <div style={{margin: "50px", maxWidth: "50%"}}>
      <div>pageVisit={redisplayAfterData.displayOptions?.pageVisit}</div>
      <div>
        redisplayAfter={redisplayAfterData.displayOptions?.redisplayAfter}
      </div>
      <br />
      <br />
      <div>
        For the modal to show: Refresh page{" "}
        {redisplayAfterData.displayOptions?.pageVisit} times or
        <br />
        Wait for {redisplayAfterData.displayOptions?.redisplayAfter}ms from the
        initial load, and then refresh page{" "}
        {redisplayAfterData.displayOptions?.pageVisit} times
      </div>
      <br />
      <div style={{color: "grey"}}>
        Note:
        <div>Clear local storage before viewing this example</div>
        <div>The modal will not show if:</div>
        <div>
          Page visits for this doc are greater than{" "}
          {redisplayAfterData.displayOptions?.pageVisit} in the local storage
          and/or the modal reappearance time is less than{" "}
          {redisplayAfterData.displayOptions?.redisplayAfter}ms. If that is the
          case, clear local storage or wait for{" "}
          {redisplayAfterData.displayOptions?.redisplayAfter}ms, and refresh
          page to see the desired functionality again.
        </div>
      </div>
    </div>
  )

  return (
    <div>
      {showDocument}
      <DynamicModal businessUnitId="test-id" data={content} />
    </div>
  )
}

ReDisplayAfter.parameters = {eyes: {include: false}}
ReDisplayAfter.args = {
  content: redisplayAfterData,
}

export const MobileFitSizeToContent = ({
  content,
}: DefaultDynamicModalProps): JSX.Element => (
  <DynamicModal businessUnitId="test-id" data={content} />
)

MobileFitSizeToContent.args = {
  content: mobileFitSizeToContentData,
}
MobileFitSizeToContent.parameters = {eyes: {waitBeforeCapture: 4000}}
