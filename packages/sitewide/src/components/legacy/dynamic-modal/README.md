# `DynamicModal`

DynamicModal is a component in progress that deprecates the `EmailPopUp` Component.
It uses the `LayoutComponent` for laying out {n} number of components in a modal.

## Accessibility Requirements

In order to create an a11y compliant `DynamicModal`, please follow the following guidelines:

- If the `DynamicModal` has `noHeader` set to `true`, an `aria-label` must be passed in to describe the purpose of the `DynamicModal`. This will be read out by screen readers when the `DynamicModal` first opens.
- If the `DynamicModal` has `noHeader` set to `false`, provide a `title`. This `title` will be visible to sighted users and will also be read by screen readers when the `DynamicModal` first opens.

## Keys in `data`

### Required Keys

- `layoutData`: Data passed into created `LayoutComponent`
- `localStorageKey`: (Use displayOptions.localStorageKey instead. Since this key will be deprecated) Unique key to allow for multiple modals.

### Other Keys

- `mobileFitSizeToContent`: The modal fit size to content on mobile mode.
- `modalSize`: Size of the Modal. Valid values are `mini`, `standard`, and `max`
- `modalTitle`: Title displayed on Modal
- `style`: CSS styles applied to the container of the Modal
- `analytics`: Analytics object will send tracking data to Adobe Analytics on each open or close
- `displayOptions`:

```ts
      {
        pageVisit: number;
        redisplayAfter: number;
        localStorageKey: string;
      }
```

- `pageVisit`: Wait until the _nth_ page visit to display `Dynamic Modal`
  - For example, if `pageVisit` is set to `3`, the modal will not appear until the user's 3rd visit to the page containing the`Dynamic Modal`
- `redisplayAfter`: Starts a time counter to reDisplay the modal after the specified amount of time. It accepts time in milliseconds. 
  - For example, to show modal every 2 days, you would pass in the value 172800000. Since 2 days is `2 * 24 * 60 * 60 * 1000 = 172800000ms`.
- `localStorageKey`: Unique key to allow for multiple modals
- `oneTimeView`: if set to false, it will show up on every page visit. By default, it is set to true, letting `pageVisit` decide when to show the modal. 
  - Note: To show modal on other pages such as cat pages, add the component in Sitewide section in JSON. For it to show on only home page, add it to the home section in JSON

## JSON Example

### With Analytics

```json
    "data": {
      "closeButtonAriaLabel": "close email sign up modal",
      "localStorageKey": "EmaiSignUp",
      "localStorageVal": "hasSeenEmailSignUp",
      "layoutData": {
        "desktop": {
          "shouldDisplay": true,
          "data": {
            "components": [{
              "type": "sitewide",
              "name": "TextHeadline",
              "data": {
                "text": "Sign up to receive 20% off",
              }
            }]
          }
        }
      },
      "analytics": {
        "onClose": {
          "content_id": "dynamic_modal_close_content",
          "link_name": "dynamic_modal_close_link"
        },
        "onOpen": {
          "content_id": "dynamic_modal_open_content",
          "link_name": "dynamic_modal_open_link"
        }
      }
    }
```

For JSON examples for other use cases, please refer to the control sections of storybook.
