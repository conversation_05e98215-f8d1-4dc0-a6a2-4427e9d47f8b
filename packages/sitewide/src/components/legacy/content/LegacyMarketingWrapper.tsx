// @ts-nocheck
'use client';
import React from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Marketing } from '@ecom-next/marketing-ui';
import { withOptimizelyDecorator } from '@ecom-next/marketing-ui/components/legacy/helper/withOptimizelyDecorator';
import { DynamicMarketing } from '@ecom-next/marketing-ui/json-marketing.client';

const OptimizelyMarketing = withOptimizelyDecorator(DynamicMarketing);

interface LegacyMarketingWrapperProps {
  slot: string;
  cid?: string;
  pageType?: string;
  defaultMarketing?: React.ReactNode;
  marketingType?: string;
  dataTestId?: string;
}

export const LegacyMarketingWrapper: React.FC<LegacyMarketingWrapperProps> = ({
  slot,
  cid = 'sitewide',
  pageType,
  defaultMarketing,
  marketingType,
  dataTestId = 'legacy-marketing-wrapper'
}) => {
  const { brand, locale, market } = useAppState();

  // Use the legacy approach with OptimizelyDecorator
  return (
    <div data-testid={dataTestId}>
      <OptimizelyMarketing
        brand={brand}
        cid={cid}
        locale={locale}
        market={market}
        pageType={pageType}
        slot={slot}
        marketingType={marketingType}
        defaultMarketing={defaultMarketing}
        // Key settings from legacy implementation
        data={{
          shouldWaitForOptimizely: false, // Always render content
          placeholderSettings: {
            useGreyLoadingEffect: false // No loading state
          }
        }}
        // Always run experiment to ensure content is in DOM
        experimentRunning={true}
      />
    </div>
  );
};

export default LegacyMarketingWrapper; 