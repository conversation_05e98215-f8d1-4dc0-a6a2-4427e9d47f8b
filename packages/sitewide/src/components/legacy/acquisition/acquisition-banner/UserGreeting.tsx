// @ts-nocheck
"use client";
import {useContext} from "react"
import {PersonalizationContext} from "@ecom-next/sitewide/personalization-provider"
// eslint-disable-next-line no-restricted-imports
import {PersonalizationContextData} from "@ecom-next/core/legacy/personalization-provider/src/types"
import {LARGE, BreakpointContext} from "@ecom-next/core/breakpoint-provider"

import {CSSObject} from "@emotion/react"
import {mapDataToProps} from "@mui/components/legacy/helper"
import {
  DesktopAndMobileStylesProps,
  GreetingVisibility,
  UserGreetingProps,
} from "./types"

const getStylesBasedOnBreakpoint = (
  isDesktop: boolean,
  breakpointStyles: DesktopAndMobileStylesProps = {}
): CSSObject | undefined => {
  const {style, desktopStyle} = breakpointStyles
  return isDesktop ? {...style, ...desktopStyle} : style
}

export const UserGreeting = ({
  dynamicCustomerName = {},
  greetingVisibility,
}: UserGreetingProps): JSX.Element | null => {
  const {firstName} = (useContext(
    PersonalizationContext
  ) as unknown) as PersonalizationContextData
  const {minWidth} = useContext(BreakpointContext)

  const isDesktop = minWidth(LARGE)

  if (greetingVisibility === GreetingVisibility.Hidden) {
    return null
  }

  const {greeting, firstName: firstNameStyle} = dynamicCustomerName

  const greetingStyles = getStylesBasedOnBreakpoint(isDesktop, greeting)
  const firstNameStyles = getStylesBasedOnBreakpoint(isDesktop, firstNameStyle)
  const containerStyles = getStylesBasedOnBreakpoint(
    isDesktop,
    dynamicCustomerName
  )

  return (
    <div css={containerStyles} data-testid="user-greeting-container">
      {firstName && greeting?.text ? (
        <span css={greetingStyles} data-testid="user-greeting-text">
          {greeting?.text}
        </span>
      ) : null}
      <span css={firstNameStyles}>{firstName}</span>
    </div>
  )
}

export default mapDataToProps(UserGreeting)
