## General Description

The `AcquisitionBanner` component creates a banner following the [acquisiton feature guidelines](https://www.figma.com/file/7uXZ9eLIWpICstpFIIYyd1/Barclays-Ecom?node-id=32%3A24) with placements described in this document.

## How to Use

The `AcquisitionBanner` component requires a developer to pass in 4 props: `loyaltyLogo`, `dynamicCustomerName` (OPTIONAL), `offer` and `applyButtom`. On top of that `AcquisitionBanner` has the same requirements of `Standard` Headline.

### Required Prop: `loyaltyLogo`

- Type: `Object` from DynamicMarketing component from core-ui. REQUIRED
- Description: The Loyalty Logo that the `AcquisitionBanner` is associated with.

  | Key             | Type      | Required? | Description                                                       |
  |-----------------|-----------|-----------|-------------------------------------------------------------------|
  | `component`     | `Object`  | Yes       | object that stores the values returned from DynamicMarketingProps |
  | `style`         | `Object`  | No        | default css styles to apply on mobile and desktop breakpoints     |
  | `desktopStyle`  | `Object`  | No        | css styles to apply to desktop element                            |

### Not Required Prop: `dynamicCustomerName`

- Type: `Object` NOT REQUIRED
- Description: This object is parsed from a JSON object. It contains all necessary information to style and text of the first name. Returned from PersonalizationContext.

  | Key             | Type      | Required? | Description                                                   |
  |-----------------|-----------|-----------|---------------------------------------------------------------|
  | `showGreeting`  | `Boolean` | No        | boolean to show greeting or not                               |
  | `greeting`      | `Object`  | No        | css styles and text to apply to element                       |
  | `firstName`     | `Object`  | No        | css styles to apply to element                                |
  | `style`         | `Object`  | No        | default css styles to apply on mobile and desktop breakpoints |
  | `desktopStyle`  | `Object`  | No        | css styles to apply to desktop element                        |

### Not Required Prop: `sitecode`

- Type: `string` NOT REQUIRED
- Description: This string is parsed from a JSON object. It contains the string that will be added to the url as a query parameter.

  | Key            | Type           | Required?      | Description                                          |
  |----------------|----------------|----------------|------------------------------------------------------|
  | `sitecode`     | `String`       | No             | string to add to url                                 |


### Required Prop: `offer`

- Type: `Object` from DynamicMarketing component from core-ui. REQUIRED
- Description: The Offer that the `AcquisitionBanner` is associated with.

  | Key             | Type      | Required? | Description                                                       |
  |-----------------|-----------|-----------|-------------------------------------------------------------------|
  | `component`     | `Object`  | Yes       | object that stores the values returned from DynamicMarketingProps |
  | `style`         | `Object`  | No        | default css styles to apply on mobile and desktop breakpoints     |
  | `desktopStyle`  | `Object`  | No        | css styles to apply to desktop element                            |

### Required Prop: `applyButtom`

- Type: `Object` REQUIRED
- Description: This object contains the style, desktopStyle and text of the Apply Button

  | Key             | Type      | Required? | Description                                                          |
  |-----------------|-----------|-----------|----------------------------------------------------------------------|
  | `button`        | `Object`  | Yes       | object with text key as well as style & desktopStyle described below |             
  | `style`         | `Object`  | No        | default css styles to apply on mobile and desktop breakpoints        |
  | `desktopStyle`  | `Object`  | No        | css styles to apply to desktop element                               |


## Accessibility

`Acquisition` component is susceptible to general accessibility rules but also __very sensitive to implementation design__.

Promo text should come into focus after the main menu, should be navigable, and should exit focus to the next component (or page area). When not dismissible and placed at the top of the page, any mistake, in regards to a11y guidelines and tabbed navigation, will prevent the user from being able to shop, navigate, and browse the core content.

### Color Contrast

Every element should respect AA color contrast standards. Font typeface should be readable and of appropriate dimension, as well as on brand.

### Voice Over

This alternative navigation system should recite text and CTA properly in the desired sequence. Focus should be placed on subsequent messages with related CTAs. A hidden required title must explain what the message is about and give some context to audio users.

### Tabbed Navigation

Consistent tabbed navigation must be implemented depending on the message layout of the headline component. ESC key should set the focus onto the next component/page area so `AcquisitionBanner` component is always avoidable by visually impaired people.
