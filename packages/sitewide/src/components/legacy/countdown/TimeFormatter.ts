// @ts-nocheck
"use client";
import {Dayjs} from "dayjs"
import {Locale} from "@sitewide/components/legacy/types"
import {TimeDisplayProps, TimeUnit} from "./types"

const defaultTimeDisplay = {
  day: {
    text: "Days",
    textSingular: "Day",
    mod: 365,
  },
  hour: {
    text: "Hrs",
    textSingular: "Hr",
    mod: 24,
  },
  minute: {
    text: "Mins",
    textSingular: "Min",
    mod: 60,
  },
  second: {
    text: "Secs",
    textSingular: "Sec",
    mod: 60,
  },
}

const getUnitText = (
  locale: Locale,
  timeRemaining: number,
  textPlural: string,
  textSingular: string
): string => {
  const isFrench = locale === "fr_CA"
  const isTimeRemainingUnitMoreThanOne = timeRemaining > 1
  const isTimeRemainingDifferentFromOne = timeRemaining !== 1

  const shouldBePlural = isFrench
    ? isTimeRemainingUnitMoreThanOne
    : isTimeRemainingDifferentFromOne

  return shouldBePlural ? textPlural : textSingular
}

const getTimeRemaining = (
  shouldDisplayDays: boolean,
  endDate: Dayjs,
  currentDate: Dayjs,
  unit: TimeUnit,
  timeModule: number
): number => {
  const shouldApplyModule = shouldDisplayDays || unit !== "hour"
  const timeDiff = endDate.diff(currentDate, unit)
  const timeRemaining = shouldApplyModule ? timeDiff % timeModule : timeDiff

  return timeRemaining < 0 ? 0 : timeRemaining
}

const shouldDisplayTime = (
  timeRemaining: number,
  showAtZero: boolean
): boolean => Boolean(timeRemaining || showAtZero)

interface TimeToDisplayByUnitProps {
  locale: Locale
  currentDate: Dayjs
  endDate: Dayjs
  shouldDisplayDays: boolean
  timeDisplayText?: Partial<Record<TimeUnit, TimeDisplayProps>>
}

export const getTimeToDisplayByUnit = ({
  locale,
  currentDate,
  endDate,
  shouldDisplayDays,
  timeDisplayText,
}: TimeToDisplayByUnitProps) => {
  return (unit: TimeUnit): string => {
    const {showAttribute = true, showAtZero = true, mod, text, textSingular} = {
      ...defaultTimeDisplay[unit],
      ...timeDisplayText?.[unit],
    }

    if (!showAttribute) {
      return ""
    }

    const timeRemaining = getTimeRemaining(
      shouldDisplayDays,
      endDate,
      currentDate,
      unit,
      mod
    )

    const unitText = getUnitText(locale, timeRemaining, text, textSingular)

    if (shouldDisplayTime(timeRemaining, showAtZero)) {
      return `${timeRemaining.toString().padStart(2, "0")} ${unitText}`
    }

    return ""
  }
}
