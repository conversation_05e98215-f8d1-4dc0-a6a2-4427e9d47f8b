// @ts-nocheck
import {AccountDropdownItem} from "@sitewide/components/legacy/types"

const orderAndReturnTitle = {
  en: "Orders & Returns",
  fr: "Commandes et retours",
}

const loyaltyTitle = {
  en: "My Points and Rewards",
  fr: "Mes points et récompenses",
}

const loyaltyTitleCaAthleta = {
  en: "My Rewards",
  fr: "Mes récompenses",
}

const giftCardTitle = {
  en: "Check Gift Card Balances",
  fr: "Vérifier le solde des cartes-cadeaux",
}

export type AbbreviatedLocale = "en" | "fr"

const ordersAndReturnsItem = (
  locale: AbbreviatedLocale
): AccountDropdownItem => ({
  text: orderAndReturnTitle[locale],
  link: "/my-account/order-history",
})

const loyaltyItem = (locale: AbbreviatedLocale): AccountDropdownItem => {
  return {text: loyaltyTitle[locale], link: "/loyalty/customer-value"}
}

const loyaltyItemCaAthleta = (
  locale: AbbreviatedLocale
): AccountDropdownItem => {
  return {text: loyaltyTitleCaAthleta[locale], link: "/loyalty/customer-value"}
}

export {ordersAndReturnsItem, loyaltyItem, loyaltyItemCaAthleta, giftCardTitle}
