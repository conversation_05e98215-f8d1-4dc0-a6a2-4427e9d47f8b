{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "WCD HP CSS Modifications - 2023-02-01", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#main-content *{box-sizing:border-box}#main-content img,#main-content video{display:block}.slick-slide>div>div{display:block!important}.fullBleedCertona div.productCard{max-width:unset}.fullBleedCertona button.slick-arrow.slick-disabled.sitewide-0,.fullBleedCertona button.slick-arrow.slick-next.sitewide-0,.fullBleedCertona button.slick-arrow.slick-prev.sitewide-0{margin-top:0}.fullBleedCertona .mkt-certona-recs{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products{max-width:none}div.wcd_hp-cta{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-color:#fff;border-style:solid;border-width:1px;color:#767676;display:-ms-flexbox;display:flex;font-size:13px;font-weight:400;height:32px;letter-spacing:0;padding:0 15px;text-transform:uppercase;width:auto}.wcd_hp-cta a:active,.wcd_hp-cta a:focus,.wcd_hp-cta a:hover,.wcd_hp-cta button:active,.wcd_hp-cta button:focus,.wcd_hp-cta button:hover,a .wcd_hp-cta:active,a .wcd_hp-cta:focus,a .wcd_hp-cta:hover,a.wcd_hp-cta:active,a.wcd_hp-cta:focus,a.wcd_hp-cta:hover,button.wcd_hp-cta:active,button.wcd_hp-cta:focus,button.wcd_hp-cta:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta button,button.wcd_hp-cta{-ms-flex-pack:justify;justify-content:space-between;min-width:100px;border-color:#767676}.wcd_hp-cta button:focus,button.wcd_hp-cta:focus{outline:0}.wcd_hp-cta button span,button.wcd_hp-cta span{font-size:1.625em;height:.25em;line-height:0;margin-left:.5em;padding:0}.wcd_hp-cta>div{position:relative;width:auto}.wcd_hp-cta>div:first-child{z-index:10}.wcd_hp-cta>div:nth-child(2){z-index:9}.wcd_hp-cta>div:nth-child(3){z-index:8}.wcd_hp-cta>div:nth-child(4){z-index:7}.wcd_hp-cta>div:nth-child(5){z-index:6}.wcd_hp-cta>div:nth-child(6){z-index:5}.wcd_hp-cta>div:nth-child(7){z-index:4}.wcd_hp-cta>div:nth-child(8){z-index:3}.wcd_hp-cta>div:nth-child(9){z-index:2}.wcd_hp-cta>div:nth-child(10){z-index:1}.wcd_hp-cta>div ul{background-color:#fff;border-color:#fff;border-style:solid;border-width:0 1px 1px;box-shadow:rgba(0,0,0,.3) 0 1px 6px 0;padding:0;position:absolute}.wcd_hp-cta>div li{border-width:0;padding:0}.wcd_hp-cta>div li:first-child a{border-top-width:1px}.wcd_hp-cta>div a{background-color:transparent;border-color:#fff;border-style:solid;border-width:0;color:#767676}.wcd_hp-cta>div a:active,.wcd_hp-cta>div a:focus,.wcd_hp-cta>div a:hover{background-color:#e9e9e9;border-color:#fff;color:#2b2b2b}.wcd_hp-cta.white a,.wcd_hp-cta.white button,a .wcd_hp-cta.white,a.wcd_hp-cta.white,button.wcd_hp-cta.white{background-color:#fff;border-color:#fff;color:#767676}.wcd_hp-cta.white a:active,.wcd_hp-cta.white a:focus,.wcd_hp-cta.white a:hover,.wcd_hp-cta.white button:active,.wcd_hp-cta.white button:focus,.wcd_hp-cta.white button:hover,a .wcd_hp-cta.white:active,a .wcd_hp-cta.white:focus,a .wcd_hp-cta.white:hover,a.wcd_hp-cta.white:active,a.wcd_hp-cta.white:focus,a.wcd_hp-cta.white:hover,button.wcd_hp-cta.white:active,button.wcd_hp-cta.white:focus,button.wcd_hp-cta.white:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.arrow a:hover,.wcd_hp-cta.arrow button:hover,a .wcd_hp-cta.arrow:hover,a.wcd_hp-cta.arrow:hover,button.wcd_hp-cta.arrow:hover{background-color:transparent;background-position-x:calc(100% - 8px);border-color:transparent;color:#767676}.wcd_hp-cta.arrow a.white,.wcd_hp-cta.arrow button.white,a .wcd_hp-cta.arrow.white,a.wcd_hp-cta.arrow.white,button.wcd_hp-cta.arrow.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(.5em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.caret-vcn a:hover,.wcd_hp-cta.caret-vcn button:hover,a .wcd_hp-cta.caret-vcn:hover,a.wcd_hp-cta.caret-vcn:hover,button.wcd_hp-cta.caret-vcn:hover{background-color:transparent;border-color:transparent;color:#767676}.wcd_hp-cta.caret-vcn a.white,.wcd_hp-cta.caret-vcn button.white,a .wcd_hp-cta.caret-vcn.white,a.wcd_hp-cta.caret-vcn.white,button.wcd_hp-cta.caret-vcn.white{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;color:#767676;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out;padding-left:10px;padding-right:24px;-ms-flex-pack:start;justify-content:flex-start}.wcd_hp-cta.caret a:hover,.wcd_hp-cta.caret button:hover,a .wcd_hp-cta.caret:hover,a.wcd_hp-cta.caret:hover,button.wcd_hp-cta.caret:hover{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg);border-color:#999;color:#767676}.wcd_hp-cta.caret a.white,.wcd_hp-cta.caret button.white,a .wcd_hp-cta.caret.white,a.wcd_hp-cta.caret.white,button.wcd_hp-cta.caret.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.details a,.wcd_hp-cta.details button,a .wcd_hp-cta.details,a.wcd_hp-cta.details,button.wcd_hp-cta.details{background-color:transparent;border-width:0;color:#fff;font-size:10px;min-height:16px;min-width:32px;padding:0;text-decoration:underline}.wcd_hp-cta.details a:hover,.wcd_hp-cta.details button:hover,a .wcd_hp-cta.details:hover,a.wcd_hp-cta.details:hover,button.wcd_hp-cta.details:hover{color:#fff}.wcd_hp-cta.details a.dark,.wcd_hp-cta.details button.dark,a .wcd_hp-cta.details.dark,a.wcd_hp-cta.details.dark,button.wcd_hp-cta.details.dark{color:#2b2b2b}.wcd_hp-cta.details a.dark:hover,.wcd_hp-cta.details button.dark:hover,a .wcd_hp-cta.details.dark:hover,a.wcd_hp-cta.details.dark:hover,button.wcd_hp-cta.details.dark:hover{color:#000}.wcd_hp-cta.outline a,.wcd_hp-cta.outline button,a .wcd_hp-cta.outline,a.wcd_hp-cta.outline,button.wcd_hp-cta.outline{background-color:#fff;border-color:#767676;border-width:1px}.wcd_hp-cta.outline a:active,.wcd_hp-cta.outline a:focus,.wcd_hp-cta.outline a:hover,.wcd_hp-cta.outline button:active,.wcd_hp-cta.outline button:focus,.wcd_hp-cta.outline button:hover,a .wcd_hp-cta.outline:active,a .wcd_hp-cta.outline:focus,a .wcd_hp-cta.outline:hover,a.wcd_hp-cta.outline:active,a.wcd_hp-cta.outline:focus,a.wcd_hp-cta.outline:hover,button.wcd_hp-cta.outline:active,button.wcd_hp-cta.outline:focus,button.wcd_hp-cta.outline:hover{background-color:#999;color:#fff;border-color:#fff}.wcd_hp-cta.full-width,.wcd_hp-cta.full-width a,.wcd_hp-cta.full-width a>div,.wcd_hp-cta.full-width button,.wcd_hp-cta.full-width button>div,.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,.wcd_hp-cta.full-width>div,a .wcd_hp-cta.full-width,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width>div{width:100%}.wcd_hp-visnav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin-left:auto;margin-right:auto;max-width:640px}.wcd_hp-visnav>div{position:relative;width:50%}.wcd_hp-visnav>div .wcd_hp-cta button,.wcd_hp-visnav>div button.wcd_hp-cta{border-color:transparent}.wcd_hp-visnav>div .wcd_hp-cta{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);background-color:#fff;color:#767676}.wcd_hp-visnav>div .wcd_hp-cta:hover{background-color:#999;color:#767676;border-color:#999}.wcd_hp-visnav>div:first-child .wcd_hp-cta{z-index:32}.wcd_hp-visnav>div:nth-child(2) .wcd_hp-cta{z-index:31}.wcd_hp-visnav>div:nth-child(3) .wcd_hp-cta{z-index:30}.wcd_hp-visnav>div:nth-child(4) .wcd_hp-cta{z-index:29}.wcd_hp-visnav>div:nth-child(5) .wcd_hp-cta{z-index:28}.wcd_hp-visnav>div:nth-child(6) .wcd_hp-cta{z-index:27}.wcd_hp-visnav>div:nth-child(7) .wcd_hp-cta{z-index:26}.wcd_hp-visnav>div:nth-child(8) .wcd_hp-cta{z-index:25}.wcd_hp-visnav>div:nth-child(9) .wcd_hp-cta{z-index:24}.wcd_hp-visnav>div:nth-child(10) .wcd_hp-cta{z-index:23}.wcd_hp-visnav>div:nth-child(11) .wcd_hp-cta{z-index:22}.wcd_hp-visnav>div:nth-child(12) .wcd_hp-cta{z-index:21}.wcd_hp-visnav>div:nth-child(13) .wcd_hp-cta{z-index:20}.wcd_hp-visnav>div:nth-child(14) .wcd_hp-cta{z-index:19}.wcd_hp-visnav>div:nth-child(15) .wcd_hp-cta{z-index:18}.wcd_hp-visnav>div:nth-child(16) .wcd_hp-cta{z-index:17}.wcd_hp-news{background-color:#fff;color:#2b2b2b;padding:16px 0 12px;width:100%}.wcd_hp-news>div{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:100%}.wcd_hp-news .wcd_hp-news_tile{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin:0 auto;max-width:672px;padding:10px 16px;width:100%}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:19px;margin-bottom:3px;text-transform:uppercase}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:13px}@media only screen and (min-width:768px){.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{font-size:calc(.625rem + (1vw - 7.68px) * .6944)}.wcd_hp-cta button,button.wcd_hp-cta{min-width:10.25em}.wcd_hp-cta.exposed-at-desk button{background-color:transparent;border-width:0;color:inherit;margin-bottom:.75em;padding:0;text-align:left}.wcd_hp-cta.exposed-at-desk button span{display:none}.wcd_hp-cta.exposed-at-desk ul{background-color:transparent;box-shadow:none;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start;max-height:none;position:relative;visibility:visible;z-index:0}.wcd_hp-cta.exposed-at-desk ul li{width:auto}.wcd_hp-cta.exposed-at-desk ul li:not(:last-child){margin-bottom:8px;margin-right:8px}.wcd_hp-cta.exposed-at-desk ul li a{border-width:1px}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-size:auto .9em}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-size:auto .9em}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-size:auto .9em}.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div{width:auto}.wcd_hp-cta.full-width-at-desk,.wcd_hp-cta.full-width-at-desk a,.wcd_hp-cta.full-width-at-desk a>div,.wcd_hp-cta.full-width-at-desk button,.wcd_hp-cta.full-width-at-desk button>div,.wcd_hp-cta.full-width-at-desk>div,a .wcd_hp-cta.full-width-at-desk,a .wcd_hp-cta.full-width-at-desk>div{width:100%}.wcd_hp-visnav{max-width:1920px}.wcd_hp-visnav>div{background-color:#fff;width:25%}.wcd_hp-visnav>div:hover img{opacity:.85}.wcd_hp-news{padding:0}.wcd_hp-news>div{-ms-flex-direction:row;flex-direction:row;margin:0 auto;max-width:1920px}.wcd_hp-news .wcd_hp-news_tile{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;max-width:none;padding:20px 22px 12px;position:relative}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta,.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-news_copy{color:transparent}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta{background-image:none}.wcd_hp-news .wcd_hp-news_tile:hover{background-color:#efefed}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header{background-image:none;color:transparent}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header img{display:none!important}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{-ms-flex-align:center;align-items:center;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:100% 50%;background-repeat:no-repeat;background-size:auto 1em;display:-ms-flexbox;display:flex;font-size:13px;left:50%;padding-right:24px;position:absolute;top:50%;transform:translate(-50%,-50%);white-space:nowrap}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:10px}}@media only screen and (min-width:1024px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.75em}.wcd_hp-news .wcd_hp-news_tile{padding-left:3vw;padding-right:3vw}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:Min(Max(17px, calc(1.0625rem + ((1vw - 10.24px) * 1.6741))), 32px);padding-right:1.5em}.wcd_hp-news .wcd_hp-news_tile:nth-child(2) .wcd_hp-news_header{min-width:17.5em}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:Min(Max(12px, calc(.75rem + ((1vw - 10.24px) * 1.3393))), 24px)}}@media only screen and (min-width:1280px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.375em}}@media only screen and (min-width:1440px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:8.875em}}@media only screen and (max-width:767px){.wcd_hp-cta.two-column-at-mob a,.wcd_hp-cta.two-column-at-mob>div{margin-bottom:8px;width:calc(50% - 4px)}.wcd_hp-cta.two-column-at-mob li a{margin-bottom:0;width:100%}.wcd_hp-cta.two-column-at-mob>div button{width:100%}.wcd_hp-cta.two-column-at-mob.odd-number a:first-child,.wcd_hp-cta.two-column-at-mob.odd-number>div:first-child{width:100%}}@media only screen and (min-width:768px){#main-content > div:nth-child(4) > div > div > div > div > div.sitewide-x6zmea > div > div > div:nth-child(1) > button{border-right:0}}</style>"}}, {"instanceDesc": "WCD HP CSS Modifications - 2023-02-01", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>.sitewide-1u7iqb2{margin:0px auto 2vw}@media only screen and (max-width:767px){.wcd_hp-cta.caret-vcn.caret a{padding-left:1vw;font-size:3vw!important}#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(1),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(2),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(3),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(4),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(5),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(6),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(7),#main-content > div:nth-child(7) > div > div > div > div > div:nth-child(2) > div > div:nth-child(8){padding:.8vw .8vw 2vw}#main-content > div:nth-child(7) > div > div > div > div{margin-bottom:0;}#main-content .sitewide-ccodyh{max-width:757px!important;margin:0px 3vw 15vw}.wcd_hp-cta.two-column-at-mob a, .wcd_hp-cta.two-column-at-mob>div{width:50%;margin-bottom:0;border-color:#2B2B2B;color:#2B2B2B;background-image:url(https://gapfactoryprod.a.bigcontent.io/v1/static/MOBCTA_Caret_2224db)}.wcd_hp-cta.two-column-at-mob a:nth-child(1){display:none}.wcd_hp-cta.two-column-at-mob a:nth-child(2),.wcd_hp-cta.two-column-at-mob a:nth-child(4),.wcd_hp-cta.two-column-at-mob a:nth-child(6){border-right:0;border-bottom:0}.wcd_hp-cta.two-column-at-mob a:nth-child(8){border-right:0}.wcd_hp-cta.two-column-at-mob a:nth-child(3),.wcd_hp-cta.two-column-at-mob a:nth-child(5),.wcd_hp-cta.two-column-at-mob a:nth-child(7){border-bottom:0}.wcd_hp-cta.caret-vcn.caret{top:-.5rem!important}}@media only screen and (min-width:768px){#main-content > div:nth-child(4) > div > div > div > div > div.sitewide-x6zmea > div > div > div:nth-child(1) > button{border-right:0}}.wcd_hp-cta.caret-vcn.caret a:hover{background-color:#FFF!important;background-repeat:no-repeat!important;background-position:calc(100% - 12px) 50%!important;background-size:auto 13px!important;border:0!important;background:url('Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg')}.wcd_hp-cta.caret-vcn.caret{left:0;top:auto;transform:none;position:relative}.wcd_hp-visnav img{margin-bottom:1rem}#main-content > div:nth-child(10) > div > div > div > div > div.sitewide-1hva3d3 > div > div > div:nth-child(2){width:174px}#main-content > div:nth-child(10) > div > div > div > div > div.sitewide-1hva3d3 > div > div{display:block}#main-content > div:nth-child(10) > div > div > div > div > div.sitewide-1hva3d3 > div > div > div:nth-child(1){margin-bottom:10px}#main-content > div:nth-child(5) > div > div > div > div > div.sitewide-1kmuf7v > div > div > div:nth-child(1) > button,#main-content > div:nth-child(5) > div > div > div > div > div.sitewide-1kmuf7v > div > div > div:nth-child(2) > button,#main-content > div:nth-child(9) > div > div > div > div > div.sitewide-zf108r > div > div > div > button,#main-content > div:nth-child(10) > div > div > div > div > div.sitewide-u4a4mn > div > div > div > button,#main-content > div:nth-child(11) > div > div > div > div > div.sitewide-x2jrde > div > div > div > button{padding:1vw!important}#main-content > div:nth-child(5) > div > div > div > div > div.sitewide-1kmuf7v > div > div > div:nth-child(2) > ul{min-width:94%;left:3%}#main-content > div:nth-child(4) > div > div > div > div > div > div > div:nth-child(1) > div > div.sitewide-623szr > div > div > div:nth-child(1) > button,#main-content > div:nth-child(4) > div > div > div > div > div > div > div:nth-child(1) > div > div.sitewide-623szr > div > div > div:nth-child(2) > button{margin-right:.5rem!important;}.wcd_hp-visnav > div{padding:.8vw .8vw 2vw}#main-content .sitewide-ccodyh{max-width:82.5%}.wcd_hp-cta.caret-vcn.caret a{background-image:none;text-decoration:underline;color:#2B2B2B;text-underline-offset:15%;font-size:1vw;padding:0!important;}.wcd_hp-visnav>div{padding:.8vw .8vw 1vw}}</style>"}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG Placeholder1", "name": "OptimizelyPlaceholder", "type": "sitewide", "experimentRunning": true, "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "HP_PromoTB1", "instanceDesc": "HP_PromoTB1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 1rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_SiteHPPromoTB1_1_7927_copy_MOB", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "image": {"alt": "Black Friday Preview Ev 40-70% Off + Extra 10% off + Card Member Extra 25%", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_SiteHPPromoTB1_1_7927_img_MOB", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "75%"}, "@media only screen and (min-width:1280px)": {"paddingTop": "0"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "1rem", "zIndex": "0", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0"}}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta two-column-at-mob caret outline", "style": {"&.wcd_hp-cta.caret a": {"&:first-child": {"width": "100%"}}}, "ctas": [{"linkData": {"to": ""}, "composableButtonData": {"children": "None"}}, {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Women"}}, {"linkData": {"to": "/browse/category.do?cid=1092773#pageId=0&department=75&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Men "}}, {"linkData": {"to": "/browse/category.do?cid=1092850#pageId=0&department=48&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Girls"}}, {"linkData": {"to": "/browse/category.do?cid=1092793#pageId=0&department=16&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Boys"}}, {"linkData": {"to": "/browse/category.do?cid=1092869#pageId=0&department=165&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "<PERSON>ler Girl"}}, {"linkData": {"to": "/browse/category.do?cid=1092872#pageId=0&department=166&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}, {"linkData": {"to": "/browse/category.do?cid=1092873#pageId=0&department=165&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Baby Girl"}}, {"linkData": {"to": "/browse/category.do?cid=1092875#pageId=0&department=166&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "composableButtonData": {"children": "Baby Boy"}}]}}}]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077,998061,997457", "height": "500px"}}, "composableButtonData": {"children": ["Ends 11/19. Excludes licensed and special edition styles. "], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "-8%"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077,998061,997457", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "-8%"}}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_SiteHPPromoTB1_1_7927_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, "image": {"alt": "Black Friday Preview Ev 40-70% Off + Extra 10% off + Card Member Extra 25%", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_SiteHPPromoTB1_1_7927_img_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTA Lockup", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "position": "absolute", "top": "86%", "left": "8.25%", "transform": "translate(-10%, 0)", "width": "auto", "button, ul li a": {"color": "#2B2B2B", "backgroundColor": "#FFF", "borderColor": "#FFF", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap", "marginTop": "0rem"}, "button": {"display": "none", "borderWidth": "1px 0", "fontSize": "24px", "&:focus": {"borderColor": "#FFF !important", "backgroundColor": "#FFF", "color": "#2B2B2B", "outline": "0"}, "span": {"display": "none"}}, "ul": {"backgroundColor": "transparent", "borderStyle": "solid", "borderWidth": "0", "boxShadow": "none", "display": "flex", "flexDirection": "row", "flexWrap": "wrap", "maxHeight": "none", "padding": "0", "visibility": "visible", "zIndex": "397", "li": {"backgroundColor": "transparent", "borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 0 1px", "boxSizing": "border-box", "padding": "0", "width": "50%", "&:last-child": {"borderBottom": "1px solid #FFF", "borderLeft": "1px solid #FFF"}, "&:nth-of-type(even)": {"borderLeftWidth": "1px"}, "&:nth-of-type(odd):last-of-type": {"width": "100%"}, "a": {"backgroundColor": "#FFF", "borderWidth": "0", "&:hover": {"borderColor": "#FFF", "backgroundColor": "#2B2B2B", "color": "#FFF"}}}}}, "desktopStyle": {"padding": "0", "div[data-testid='button-dropdown-container']": {"borderWidth": "0", "display": "flex", "flexDirection": "row wrap", "flexWrap": "wrap", "justifyContent": "center"}, "button, ul li a": {"fontSize": "calc(.625rem + (1vw - 7.68px) * .6944)", "padding": ".5vw 1vw"}, "ul": {"borderWidth": "0", "display": "flex", "flexWrap": "wrap", "justifyContent": "center", "marginLeft": "auto", "marginRight": "auto", "maxHeight": "none", "minWidth": "0", "visibility": "visible", "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "li": {"borderWidth": "0", "marginRight": "8px", "marginTop": "0px", "width": "auto", "&:nth-of-type(even)": {"borderLeftWidth": "0"}, "&:last-child": {"borderBottom": "0", "borderLeft": "0", "marginRight": "0"}, "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "a": {"borderWidth": "1px", "borderStyle": "solid"}}}}, "ctas": [{"buttonDropdownData": {"submenu": [{"text": "Women", "href": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "Men", "href": "/browse/category.do?cid=1092773#pageId=0&department=75&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "Girls", "href": "/browse/category.do?cid=1092850#pageId=0&department=48&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "Boys", "href": "/browse/category.do?cid=1092793#pageId=0&department=16&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1092869#pageId=0&department=165&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1092872#pageId=0&department=166&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1092873#pageId=0&department=165&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1092875#pageId=0&department=166&mlink=1038092,aaf5f778-11be-435b-8ac9-551e73e270fc,HP_PromoTB1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "left": "2.5%", "bottom": "2%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077,998061,997457", "height": "500px"}}, "composableButtonData": {"children": ["Ends 11/19. Excludes licensed and special edition styles. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#FFF"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077,998061,997457", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#FFF"}}}]}}}]}}}}, {"instanceName": "HP_PromoTB2", "instanceDesc": "HP_PromoTB2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 6rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_copyHeader_MOB"}, "image": {"alt": "Doorbusters Just in", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_copyHeader_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, "image": {"alt": "50% off PJs", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_img_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, "image": {"alt": "50% off PJs", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_copySub_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "31", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop The Deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041644#pageId=0&department=136&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Men", "href": "/browse/category.do?cid=1109191#pageId=0&department=75&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041732#pageId=0&department=48&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041825#pageId=0&department=16&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041929#pageId=0&department=165&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041961#pageId=0&department=166&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064649#pageId=0&department=165&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064664#pageId=0&department=166&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Family Matching", "href": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}]}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1045907#pageId=0&department=136&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, "image": {"alt": "50% off CWA", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPA_V1_7685_SA1419_img_MOB", "style": {"display": "block", "marginTop": "6rem"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1045907#pageId=0&department=136&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, "image": {"alt": "50% off CWA", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPA_V1_7685_SA1419_copy_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "31", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop The Deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045907#pageId=0&department=136&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1045955#pageId=0&department=75&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1041735#pageId=0&department=48&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1041827#pageId=0&department=16&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041931#pageId=0&department=165&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041963#pageId=0&department=166&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064672#pageId=0&department=165&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064666#pageId=0&department=166&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, "image": {"alt": "Doorbusters Just In", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_copyHeader_DESK_new", "style": {"display": "block"}}}}}, {"instanceDesc": "2column_20", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "100%"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"instanceDesc": "Sub1 Content", "name": "HoverImageWithTransition", "type": "sitewide", "data": {"background": {"altText": "", "desktopImg": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_imgDefault_DESK"}, "backgroundHover": {"altText": "", "desktopImg": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_imgHover_DESK", "style": {"transition": "opacity .75s ease-in"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, "image": {"alt": "50% off PJs", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPPromoBanner_V1_7726_SA1416_copySub_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "94%", "right": "-.75%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop the deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041644#pageId=0&department=136&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Men", "href": "/browse/category.do?cid=1109191#pageId=0&department=75&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041732#pageId=0&department=48&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041825#pageId=0&department=16&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041929#pageId=0&department=165&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041961#pageId=0&department=166&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064649#pageId=0&department=165&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064664#pageId=0&department=166&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, {"text": "Family Matching", "href": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "right": "3%", "bottom": "12%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}]}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"instanceDesc": "Sub1 Content", "name": "HoverImageWithTransition", "type": "sitewide", "data": {"background": {"altText": "", "desktopImg": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPA_V1_7685_SA1419_imgDefault_DESK"}, "backgroundHover": {"altText": "", "desktopImg": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPA_V1_7685_SA1419_imgHover_DESK", "style": {"transition": "opacity .75s ease-in"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,58c3ba4d-f705-470f-8b3f-3abc94247cea,HP_PromoTB2"}, "image": {"alt": "50% off PJs", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriDBMer_SiteHPA_V1_7685_SA1419_copy_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "94%", "right": "1%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop the deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045907#pageId=0&department=136&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1045955#pageId=0&department=75&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1041735#pageId=0&department=48&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1041827#pageId=0&department=16&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041931#pageId=0&department=165&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041963#pageId=0&department=166&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064672#pageId=0&department=165&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064666#pageId=0&department=166&mlink=1038092,eeb0be81-efbd-4668-a6e8-1ff581950e46,HP_A"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "right": "4.5%", "bottom": "12%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}]}}}]}}}}]}}}}]}}}}, {}, {"instanceName": "HP_C2", "instanceDesc": "HP_C2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 0", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1050905#pageId=0&department=136&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, "image": {"alt": "50% off Puffer Vests", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriday50_SiteHPC2_V1_8138_SA1879_img_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1050905#pageId=0&department=136&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, "image": {"alt": "50% off Puffer Vests", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriday50_SiteHPC2_V1_8138_SA1879_copy_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "99!important", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop the deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1050905#pageId=0&department=136&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Men", "href": "/browse/category.do?cid=1041801#pageId=0&department=75&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041731#pageId=0&department=48&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041824#pageId=0&department=16&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041913#pageId=0&department=165&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041952#pageId=0&department=166&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064634#pageId=0&department=165&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064657#pageId=0&department=166&mlink=1038092,,HP_C2"}]}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%", "marginBottom": "5.5rem"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "14%"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "14%"}}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriday50_SiteHPC2_V1_8138_SA1879_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1050905#pageId=0&department=136&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, "image": {"alt": "50% off Puffer Vests", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriday50_SiteHPC2_V1_8138_SA1879_img_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "51%", "left": "7.5%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#FFF!important", "color": "#FFF!important", "&:hover": {"backgroundColor": "#FFF!important", "color": "#2B2B2B!important"}, "&:active": {"backgroundColor": "#FFF!important", "color": "#2B2B2B!important"}, "&:focus": {"backgroundColor": "#FFF!important", "color": "#2B2B2B!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop the deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1050905#pageId=0&department=136&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Men", "href": "/browse/category.do?cid=1041801#pageId=0&department=75&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041731#pageId=0&department=48&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041824#pageId=0&department=16&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041913#pageId=0&department=165&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041952#pageId=0&department=166&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064634#pageId=0&department=165&mlink=1038092,a1c78319-454d-41e1-825e-852d9d983e38,HP_C2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064657#pageId=0&department=166&mlink=1038092,,HP_C2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "right": "2%", "bottom": "2%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998039", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}]}}}]}}}}, {"instanceName": "VCN/VDN", "instanceDesc": "VCN/VDN_050421", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": true, "certonaTitle": {"title": "Shop By Division", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 2px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "defaultHeight": {"small": "134px", "large": "71px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "maxWidth": "1920px", "backgroundColor": "#FFF", "padding": "0", "margin": "0 auto 0"}, "components": [{"name": "TextHeadline", "type": "siteide", "data": {"text": "Shop By Division", "defaultHeight": "34px", "className": {"mobile": {}, "desktop": {}}, "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 2px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none", "margin": "0 auto 24px", "maxWidth": "640px", "@media only screen and (min-width:768px)": {"maxWidth": "1920px", "margin": "0 auto 0"}}}}}, {"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-visnav"}, "style": {"margin": "0 auto 24px", "maxWidth": "640px", "@media only screen and (min-width:768px)": {"maxWidth": "1920px", "margin": "0 auto 36px"}}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_WOM", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_WOM"}, "linkData": {"title": "Women", "to": "/browse/category.do?cid=1150781#pageId=0&department=136&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_1_W"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1150781#pageId=0&department=136&mlink=1038092,30015285,HP_VCN_1_W"}, "composableButtonData": {"children": "Women"}}]}}}, {"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Men", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_MEN", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_MEN"}, "linkData": {"title": "Men", "to": "/browse/category.do?cid=1151154#pageId=0&department=75&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_2_M"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151154#pageId=0&department=75&mlink=1038092,30015285,HP_VCN_2_M"}, "composableButtonData": {"children": "Men"}}]}}}, {"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Girls", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_KG", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_KG"}, "linkData": {"title": "Girls", "to": "/browse/category.do?cid=1151275#pageId=0&department=48&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_3_G"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151275#pageId=0&department=48&mlink=1038092,30015285,HP_VCN_3_G"}, "composableButtonData": {"children": "Girls"}}]}}}, {"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Boys", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_KB", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_KB"}, "linkData": {"title": "Boys", "to": "/browse/category.do?cid=1151296#pageId=0&department=16&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_4_B"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151296#pageId=0&department=16&mlink=1038092,30015285,HP_VCN_4_B"}, "composableButtonData": {"children": "Boys"}}]}}}, {"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON>ler Girl", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_TG", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_TG"}, "linkData": {"title": "<PERSON>ler Girl", "to": "/browse/category.do?cid=1151336#pageId=0&department=165&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_5_TG"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151336#pageId=0&department=165&mlink=1038092,30015285,HP_VCN_5_TG"}, "composableButtonData": {"children": "<PERSON>ler Girl"}}]}}}, {"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON><PERSON>", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_TB", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_TB"}, "linkData": {"title": "<PERSON><PERSON>", "to": "/browse/category.do?cid=1151463#pageId=0&department=166&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_6_TB"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151463#pageId=0&department=166&mlink=1038092,30015285,HP_VCN_6_TB"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}, {"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Baby Girl", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_BG", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_BG"}, "linkData": {"title": "Baby Girl", "to": "/browse/category.do?cid=1151862#pageId=0&department=166&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_7_BG"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151862#pageId=0&department=166&mlink=1038092,30015285,HP_VCN_7_BG"}, "composableButtonData": {"children": "Baby Girl"}}]}}}, {"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Baby Boy", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_MOB_BB", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/VDN_DESK_BB"}, "linkData": {"title": "Baby Boy", "to": "/browse/category.do?cid=1151906#pageId=0&department=166&mlink=1038092,3480c8e1-f7bf-4930-9228-ed0db78899bc,HP_VCN_8_BB"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn caret", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151906#pageId=0&department=166&mlink=1038092,30015285,HP_VCN_8_BB"}, "composableButtonData": {"children": "Baby Boy"}}]}}}]}}]}}}}, {"instanceName": "HP_B2", "instanceDesc": "HP_B2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 6rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"left": "6%", "position": "absolute", "bottom": "2%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/953/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/953/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1152550#pageId=0&department=136&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, "image": {"alt": "Sherpa Collection: Design Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_img1_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1152588#department=165&style=1173449&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, "image": {"alt": "Sherpa Collection: Design Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_img2_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1152585#department=166&style=1173454&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, "image": {"alt": "Sherpa Collection: Design Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_img3_MOB", "style": {"display": "block"}}}}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Sherpa Collection: Design Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_copy_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "31", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop for all"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1152550#pageId=0&department=136&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Men", "href": "/browse/category.do?cid=1153334#pageId=0&department=75&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Girls", "href": "/browse/category.do?cid=1152553#pageId=0&department=48&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Boys", "href": "/browse/category.do?cid=1152554#pageId=0&department=16&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1152588#department=165&style=1173449&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1152588#department=166&style=1173450&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1152585#department=165&style=1173452&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1152585#department=166&style=1173454&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"instanceDesc": "2column_20", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "100%"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1152588#department=165&style=1173449&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, "image": {"alt": "Sherpa Collection: Design Range", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_imgL_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "54%", "left": "14.5%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop now"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1152550#pageId=0&department=136&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Men", "href": "/browse/category.do?cid=1153334#pageId=0&department=75&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Girls", "href": "/browse/category.do?cid=1152553#pageId=0&department=48&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Boys", "href": "/browse/category.do?cid=1152554#pageId=0&department=16&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1152588#department=165&style=1173449&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1152588#department=166&style=1173450&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1152585#department=165&style=1173452&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1152585#department=166&style=1173454&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1152550#pageId=0&department=136&mlink=1038092,0500e68b-b73c-4fcf-aadc-a44c03df5ec6,HP_B2"}, "image": {"alt": "Sherpa Collection: Design Range", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_SherpaCollectionD_SiteHPB2_V1_7752_SA1414_imgR_DESK", "style": {"display": "block"}}}}}]}}}}]}}}}]}}}}, {"instanceName": "HP_B1", "instanceDesc": "HP_B1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "className": "hidethis", "data": {"lazy": true, "defaultHeight": {"small": "1px", "large": "1px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 6rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"left": "6%", "position": "absolute", "bottom": "2%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/953/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/953/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1127842#pageId=0&department=136&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img1_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1151154#department=75&style=1151158&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img2_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img3_MOB", "style": {"display": "block"}}}}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_copy_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "31", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop tees, polos, & more"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1127842#pageId=0&department=136&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Men", "href": "/browse/category.do?cid=1151154#department=75&style=1151158&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Girls", "href": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1151296#department=16&style=1151297,1151300&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041874#pageId=0&department=165&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041941#pageId=0&department=166&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064172#pageId=0&department=165&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064652#pageId=0&department=166&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"position": "absolute", "left": "2%", "bottom": "2%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/905/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/015/905/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1127842#pageId=0&department=136&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img1_DESK", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1151154#department=75&style=1151158&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img2_DESK", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, "image": {"alt": "LS Tees: Color Range + Dress-Up Styling Versatility", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_LSTeesColorRange_SiteHPB1_V1_7689_SA1409_img3_DESK", "style": {"display": "block"}}}}}]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "51%", "left": "8%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"display": "block", "button": {"marginBottom": 0, "padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop tees, polos, & more"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1127842#pageId=0&department=136&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Men", "href": "/browse/category.do?cid=1151154#department=75&style=1151158&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Girls", "href": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1151296#department=16&style=1151297,1151300&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041874#pageId=0&department=165&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041941#pageId=0&department=166&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064172#pageId=0&department=165&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064652#pageId=0&department=166&mlink=1038092,17a6a841-373c-4669-acb7-efc5c70f5198,HP_B1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceName": "013123_Certona_1", "instanceDesc": "013123_Certona_1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto 7vw", "flexDirection": "column", "padding": "0", "maxWidth": "82.5%", "@media only screen and (max-width:767px)": {"maxWidth": "95%!important;"}}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>@media only screen and (max-width: 767px){#mui-certona-recs-container .sitewide-0{font-size:2.5vw!important;}#mui-certona-recs-container .slick-slide p,#mui-certona-recs-container .sitewide-0{font-size:2.5vw}#mui-certona-recs-container .slick-slide{width:158px!important;}#mui-certona-recs-container div[data-testid='recommended-product-card']{padding:2vw}}@media only screen and (min-width:768px){#mui-certona-recs-container div[data-testid='recommended-product-card']{padding:.5vw}#mui-certona-recs-container > div > div > div > button.slick-arrow.slick-prev.sitewide-0 > img{bottom:40px!important;position:relative!important}#mui-certona-recs-container > div > div > div > button.slick-arrow.slick-next.sitewide-0 > img{bottom:-40px!important;position:relative!important}}</style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "Shop All New Everything", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "3.9vw", "textAlign": "left", "padding": "0", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none", "marginLeft": "2vw"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "1.3vw", "textAlign": "left", "padding": "0", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none", "marginLeft": ".5vw"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 5, "slidesToScroll": 5}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 2}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "priceOffText": "off", "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "30px", "productTextStyles": {"productTitle": {"style": {"color": "#2B2B2B", "textAlign": "left", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "marginTop": "1vw"}}, "productPrice": {"style": {"color": "#2b2b2b", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "float": "left", "display": "block", "marginLeft": "0", "marginTop": "-2px"}}, "productPercentage": {"style": {"color": "#CD2026", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "float": "left", "marginTop": "-2px", "display": "block", "fontWeight": "600"}}, "productSalePrice": {"style": {"color": "#CD2026", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "display": "block", "marginLeft": "0", "marginTop": "1rem", "fontWeight": "600", "width": "fit-content"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "HP_C1", "instanceDesc": "HP_C1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 6rem", "maxWidth": "640px", "position": "relative"}, "components": [{"instanceDesc": "2column_20", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "100%"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1150781#department=136&style=1150790&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, "image": {"alt": "Dresses & Skirts: Fashion Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_imgL_MOB", "style": {"display": "block"}}}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1151275#department=48&style=1153440&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, "image": {"alt": "Dresses & Skirts: Fashion Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_imgR_MOB", "style": {"display": "block"}}}}}]}}}}]}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Dresses & Skirts: Fashion Range", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_copy_MOB", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "99!important", "padding": "1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop now"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1150781#department=136&style=1150790&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "Girls", "href": "/browse/category.do?cid=1151275#department=48&style=1153440&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1151336#department=165&style=1151346&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1151862#department=165&style=1151865&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"instanceDesc": "2column_20", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "100%"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_copy_DESK", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1150781#department=136&style=1150790&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, "image": {"alt": "Dresses & Skirts: Fashion Range", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_imgL_DESK", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "58%", "left": "14.5%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop now"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1150781#department=136&style=1150790&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "Girls", "href": "/browse/category.do?cid=1151275#department=48&style=1153440&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1151336#department=165&style=1151346&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1151862#department=165&style=1151865&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1151275#department=48&style=1153440&mlink=1038092,075736c0-bd11-4528-bc94-2c96573bd6c0,HP_C1"}, "image": {"alt": "Dresses & Skirts: Fashion Range", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_DressesSkirtsFa_SiteHPC1_V1_7735_SA1637_imgR_DESK", "style": {"display": "block"}}}}}]}}}}]}}}}]}}}}, {"instanceName": "013123_Certona_2", "instanceDesc": "013123_Certona_2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto 7vw", "flexDirection": "column", "padding": "0", "maxWidth": "82.5%", "@media only screen and (max-width:767px)": {"maxWidth": "95%!important;"}}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>@media only screen and (max-width: 767px){#mui-certona-recs-container .sitewide-0{font-size:2.5vw!important;}#mui-certona-recs-container .slick-slide p,#mui-certona-recs-container .sitewide-0{font-size:2.5vw}#mui-certona-recs-container .slick-slide{width:158px!important;}#mui-certona-recs-container div[data-testid='recommended-product-card']{padding:2vw}}@media only screen and (min-width:768px){#mui-certona-recs-container div[data-testid='recommended-product-card']{padding:.5vw}#mui-certona-recs-container > div > div > div > button.slick-arrow.slick-prev.sitewide-0 > img{bottom:40px!important;position:relative!important}#mui-certona-recs-container > div > div > div > button.slick-arrow.slick-next.sitewide-0 > img{bottom:-40px!important;position:relative!important}}</style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "These look like you", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "3.9vw", "textAlign": "left", "padding": "0", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none", "marginLeft": "2vw"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2B2B2B", "lineHeight": "1.6rem", "fontSize": "1.3vw", "textAlign": "left", "padding": "0", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none", "marginLeft": ".5vw"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 5, "slidesToScroll": 5}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 2}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "priceOffText": "off", "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "30px", "productTextStyles": {"productTitle": {"style": {"color": "#2B2B2B", "textAlign": "left", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "marginTop": "1vw"}}, "productPrice": {"style": {"color": "#2b2b2b", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "float": "left", "display": "block", "marginLeft": "0", "marginTop": "-2px"}}, "productPercentage": {"style": {"color": "#CD2026", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "float": "left", "marginTop": "-2px", "display": "block", "fontWeight": "600"}}, "productSalePrice": {"style": {"color": "#CD2026", "fontSize": "1vw", "@media only screen and (max-width:767px)": {"fontSize": "2.5vw"}, "display": "block", "marginLeft": "0", "marginTop": "1rem", "fontWeight": "600", "width": "fit-content"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "HP_F1", "instanceDesc": "HP_F1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 7.5rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/102323_HolidayHPVDNRefre_SiteHPVDN_V1_7688_SA1424_MOB_TXT"}, "background": {"linkData": {"to": "/browse/category.do?cid=1119017#department=136&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, "image": {"alt": "Gift Guide: COZY Collection Breadth + Gifts at Great Price", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/102323_HolidayHPVDNRefre_SiteHPVDN_V1_7688_SA1424_MOB_IMG", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width dotw_button", "style": {"width": "100%", "zIndex": "99!important", "padding": "0 1rem", "button": {"color": "#2B2B2B!important", "borderColor": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop gift picks"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119017#department=136&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Men", "href": "/browse/category.do?cid=1119048#department=75&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Girls", "href": "/browse/category.do?cid=1119043#department=48&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Boys", "href": "/browse/category.do?cid=1119041#department=16&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1118992#department=165&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1118992#department=166&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1118994#department=165&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1118994#department=166&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}]}}]}}}, {"instanceDesc": "CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"container": {"style": {"maxWidth": "100%", "position": "absolute", "top": "104.5%", "zIndex": "1", "left": "50%", "transform": "translateX(-50%)", "width": "91.75%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#FFF", "borderColor": "#2b2b2b", "color": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "13px", "justifyContent": "left", "letterSpacing": "0", "padding": "10px 15px", "width": "100%", "height": "32px", "&:hover": {}}, "button": {"padding": "9px 9px 9px 9px", "border": "1px solid #2b2b2b", "color": "#2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#fff", "padding": "8px 12px"}}}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1184945&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, "composableButtonData": {"children": "Shop Matching Family Pajamas", "font": "primary"}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "100%"}, "components": [{"instanceDesc": "2column_20", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "100%"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/102323_HolidayHPVDNRefre_SiteHPVDN_V1_7688_SA1424_DESK_TXT", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1119017#department=136&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, "image": {"alt": "Gift Guide: COZY Collection Breadth + Gifts at Great Price", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/102323_HolidayHPVDNRefre_SiteHPVDN_V1_7688_SA1424_A_DESK_IMG", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "54%", "left": "5.5%", "transform": "translate(-10%,0)", "zIndex": "50"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"padding": "1vw!important", "backgroundColor": "transparent!important", "borderColor": "#2B2B2B!important", "color": "#2B2B2B!important", "&:hover": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#2B2B2B!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop gift picks"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119017#department=136&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Men", "href": "/browse/category.do?cid=1119048#department=75&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Girls", "href": "/browse/category.do?cid=1119043#department=48&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Boys", "href": "/browse/category.do?cid=1119041#department=16&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1118992#department=165&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1118992#department=166&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1118994#department=165&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1118994#department=166&price=0-25&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "CTA Lockup", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "position": "absolute", "top": "60%", "left": "6.75%", "transform": "translate(-10%,0)", "width": "auto", "button, ul li a": {"color": "#2B2B2B", "backgroundColor": "transparent", "borderColor": "2B2B2B", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap", "marginTop": "0rem"}, "button": {"display": "none", "borderWidth": "1px 0", "fontSize": "24px", "&:focus": {"borderColor": "#2B2B2B !important", "backgroundColor": "#2B2B2B", "color": "#FFF", "outline": "0"}, "span": {"display": "none"}}, "ul": {"backgroundColor": "transparent", "borderStyle": "solid", "borderWidth": "0", "boxShadow": "none", "display": "flex", "flexDirection": "row", "flexWrap": "wrap", "maxHeight": "none", "padding": "0", "visibility": "visible", "zIndex": "397", "li": {"backgroundColor": "transparent", "borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 0 1px", "boxSizing": "border-box", "padding": "0", "width": "50%", "&:last-child": {"borderBottom": "1px solid #FFF", "borderLeft": "1px solid #FFF"}, "&:nth-of-type(even)": {"borderLeftWidth": "1px"}, "&:nth-of-type(odd):last-of-type": {"width": "100%"}, "a": {"backgroundColor": "transparent", "borderWidth": "0", "&:hover": {"borderColor": "#2B2B2B", "backgroundColor": "#2B2B2B", "color": "#FFF"}}}}}, "desktopStyle": {"padding": "0", "div[data-testid='button-dropdown-container']": {"borderWidth": "0", "display": "flex", "flexDirection": "row wrap", "flexWrap": "wrap", "justifyContent": "center"}, "button, ul li a": {"fontSize": "calc(.625rem + (1vw - 7.68px) * .6944)", "padding": ".5vw 1vw"}, "ul": {"borderWidth": "0", "display": "flex", "flexWrap": "wrap", "justifyContent": "center", "marginLeft": "auto", "marginRight": "auto", "maxHeight": "none", "minWidth": "0", "visibility": "visible", "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "li": {"borderWidth": "0", "marginRight": "8px", "marginTop": "0px", "width": "auto", "&:nth-of-type(even)": {"borderLeftWidth": "0"}, "&:last-child": {"borderBottom": "0", "borderLeft": "0", "marginRight": "0"}, "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "a": {"borderWidth": "1px", "borderStyle": "solid"}}}}, "ctas": [{"buttonDropdownData": {"submenu": [{"text": "Shop Matching Family Pajamas", "href": "/browse/category.do?cid=1184945&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "50%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1119017#department=136&price=0-35&mlink=1038092,8df2353b-6243-49f7-8192-e83e042d1911,HP_F1"}, "image": {"alt": "Gift Guide: COZY Collection Breadth + Gifts at Great Price", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/102323_HolidayHPVDNRefre_SiteHPVDN_V1_7688_SA1424_B_DESK_IMG", "style": {"display": "block"}}}}}]}}}}]}}}}]}}}}, {"instanceName": "dpg-banner2", "instanceDesc": "DPG-placeholder-2", "name": "OptimizelyPlaceholder", "type": "sitewide", "experimentRunning": true, "data": {"defaultHeight": {"small": "0", "large": "0"}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"ciid": "296d9ce8-2c95-46f5-ba6e-ed8f7a3860a0", "type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": []}}, "headline": {"type": "builtin", "name": "div", "data": {"components": []}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "gpec_secondaryheadline-gsb-111423", "instanceDesc": "11/14 GSB - keep instanceName 111423", "ciid": "49b017c3-ee50-423c-be64-776acdf7a1b4", "experimentRunning": false, "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#000", "position": "relative", "width": "100%", "maxWidth": "2560px", "height": "auto", "lineHeight": "0", "margin": "0 auto", "display": "flex"}}, "components": [{"name": "Carousel", "type": "sitewide", "meta": {"excludePageTypes": ["home"]}, "data": {"carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": false, "arrows": false, "arrowPosition": "0", "prevArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_left--blue.svg", "nextArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_right--blue.svg", "displayArrows": {"mobile": false, "desktop": false}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"instanceDesc": "GSB_11_14_2023_1", "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#000", "position": "relative", "width": "100%", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Pre Black-<PERSON><PERSON>v 40-70% Off", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_1_7928_MOB/", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_1_7928_DESK/"}, "linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037460,49b017c3-ee50-423c-be64-776acdf7a1b4,GSB_Promo", "target": "_self", "title": "Pre Black-<PERSON><PERSON>v 40-70% Off"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"container": {"className": "", "style": {"position": "absolute", "top": "70%", "right": "50%", "transform": "translateX(50%)", "whiteSpace": "nowrap"}, "desktopStyle": {"position": "absolute", "top": "65%", "right": "0.5%", "transform": "translateY(-50%)", "whiteSpace": "nowrap"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply."], "style": {"display": "inline", "fontSize": "1.9vw", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#fff", "position": "relative", "bottom": "3px"}, "desktopStyle": {"color": "#fff", "fontSize": "10px", "left": "0"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998077", "height": "500px"}}, "composableButtonData": {"children": ["details"], "style": {"fontSize": "1.9vw", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "textTransform": "uppercase", "padding": "0", "outline": "none", "color": "#fff", "position": "relative", "bottom": "3px"}, "desktopStyle": {"color": "#fff", "fontSize": "10px", "left": "0"}}}]}}}]}}, {"instanceDesc": "GSB_11_14_2023_2", "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#fff", "position": "relative", "width": "100%", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Card: 25% Off | NonCard: 10% Off", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_2_7929_img_MOB/", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_2_7929_img_DESK/"}, "linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037460,49b017c3-ee50-423c-be64-776acdf7a1b4,GSB_Promo_GGR_CARD_RET_PROMO", "target": "_self", "title": "Card: 25% Off | NonCard: 10% Off"}}, "overlay": {"alt": "Card: 25% Off | NonCard: 10% Off", "srcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_2_7929_copy_MOB/", "desktopSrcUrl": "https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_GlobalSkinnyBanner_2_7929_copy_DESK/"}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"container": {"className": "", "style": {"position": "absolute", "top": "70%", "right": "19%", "transform": "translateX(50%)", "whiteSpace": "nowrap"}, "desktopStyle": {"position": "absolute", "top": "65%", "right": "0.5%", "transform": "translateY(-50%)", "whiteSpace": "nowrap"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=997457,998061", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply."], "style": {"display": "inline", "fontSize": "1.9vw", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#fff", "position": "relative", "bottom": "6vw", "right": "-15.1vw"}, "desktopStyle": {"right": "unset", "color": "#fff", "fontSize": "10px", "left": "0", "bottom": "3px"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=997457,998061", "height": "500px"}}, "composableButtonData": {"children": ["Ends 11/19."], "style": {"display": "inline", "fontSize": "1.9vw", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#fff", "position": "relative", "bottom": "3.6vw"}, "desktopStyle": {"color": "#fff", "fontSize": "10px", "left": "0", "bottom": "3px"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=997457,998061", "height": "500px"}}, "composableButtonData": {"children": ["details"], "style": {"fontSize": "1.9vw", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "textTransform": "uppercase", "padding": "0", "outline": "none", "color": "#fff", "position": "relative", "bottom": "3px", "right": "10.6vw"}, "desktopStyle": {"right": "unset", "color": "#fff", "fontSize": "10px", "left": "2px"}}}]}}}]}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}]}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large", "ciid": "db9d219e-5212-4e15-8dbe-a59474f38ee2", "type": "sitewide", "name": "MktEdfsLarge", "experimentRunning": true, "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px", "textTransform": "uppercase"}, "data": {"lazy": false, "experimentRunning": false, "defaultData": {"text": "Free Shipping on $50+ for Rewards Members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194686", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign In or Join", "path": "/my-account/sign-in", "style": {"display": "inline", "fontSize": "1em", "fontWeight": "400", "letterSpacing": "0", "position": "relative", "textTransform": "uppercase", "top": "-1px"}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "ciid": "08cc7c61-f03b-49d9-9153-169b949400c7", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div.sitewide-13o7eu2 > div > div:nth-child(2) > div > div > div > button {text-transform:uppercase}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"padding": "0", "paddingLeft": "10px", "paddingRight": "10px", "fontSize": "10px", "textTransform": "uppercase"}}, "defaultData": {"textStrong": "", "text": "Free Shipping on $50+ for Rewards Members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194686", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign In or Join", "path": "/my-account/sign-in", "style": {"display": "inline", "fontSize": "10px", "fontWeight": "400", "letterSpacing": "0", "paddingRight": "0", "position": "relative", "textTransform": "uppercase", "top": "-2px"}}}}]}}}}]}}, "hamnavRedesignBanner": {"type": "builtin", "name": "div", "data": {"components": []}}, "hamburgerNavTopBanner": {"type": "builtin", "name": "div", "data": {"components": []}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"ciid": "fc512cf1-d9fb-4228-9b33-8b37792205d8", "name": "DynamicModal", "type": "sitewide", "instanceName": "082323-email-popup", "sitewide-popup-ciid": "********", "experimentRunning": true, "data": {"excludePageTypes": ["Information", "ShoppingBag", "Profile"], "placeholderSettings": {"useGreyLoadingEffect": false}, "lazy": false, "defaultHeight": "1px", "shouldWaitForOptimizely": true, "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "emailPopup", "modalSize": "max", "title": "", "style": {"padding": "1rem"}, "breakpoints": ["large"], "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>.sitewide-1hf9k7u{background-color:#2b2b2b}</style>"}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "3rem 0 0 0", "margin": "0 1rem 0 0"}, "mobile": {"padding": "20px", "margin": "1rem"}}, "targetURL": "/profile/info.do?cid=1044955&mlink=1038092,19344795,HP_POPUP&clink=19344795", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:ON_Persado8;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_sp sds_relative optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header sds_font--secondary sds_line-height--1-0 optly-main-header\" style=\"color: #2b2b2b; font-size: 1.5rem; margin-bottom: 1rem\">Perk Alert! Get 20% off Your Purchase</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"sds_line-height--1-4 heading-d optly-lighter\" style=\"font-weight:400;\">As a thank you for signing up for emails, take 20% off your online purchase. Plus, you’ll get early access to new arrivals, exclusive offers, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_label-a sds_color--g2", "style": {"marginTop": "8em", "fontSize": ".6em"}, "html": "<p class=\"sds_line-height--1-4 optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">*Valid for first-time registrants & applies to regular price items only.<a href=\"/customerService/info.do?cid=1165787\" style=\"font-size:1em;text-decoration:underline;text-transform:uppercase;margin-left: 4px;\">Details</a></p><p><a href=\"https://www.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">*Privacy Policy</a></p>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false}, "mobile": {"className": "sds_sp", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "style": {"marginTop": "2rem", "backgroundColor": "#2b2b2b"}, "desktop": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"marginTop": "2rem"}}, "mobile": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary"}}, ".sitewide-1hf9k7u": {"style": {"backgroundColor": "#2b2b2b"}}, "errorNotificationAriaLabel": "Error"}}, {"type": "builtin", "name": "img", "tileStyle": {"desktop": {"maxWidth": "325px", "marginLeft": "0.35rem"}}, "data": {"props": {"src": "https://gapfactoryprod.a.bigcontent.io/v1/static/082323_EmailSignUpPopupSemi_SitePopUp_V1_7383_SA1634", "alt": "Email Sign Up Pop-up"}}}]}}}}]}}, "mobile": {"shouldDisplay": false, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {}}, "targetURL": "/profile/info.do?cid=82637&mlink=5151,8980857,persado_signup_CONTROL", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:ON_Persado8;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_sp sds_relative optly-header-container", "style": {"fontSize": "1.5rem!important", "marginBottom": "1rem!important"}, "html": "<h1 class=\"optly-main-header sds_font--secondary sds_line-height--1-0 sds_font-size--24 optly-main-header\" style=\"color: #2b2b2b; font-size: 2rem; text-transform: uppercase;\">Perk Alert! Get 20% off Your Purchase</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"sds_line-height--1-4 heading-d optly-lighter\" style=\"font-weight:400;\">As a thank you for signing up for emails, take 20% off your online purchase. Plus, you’ll get early access to new arrivals, exclusive offers, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_label-a sds_color--g2", "style": {"marginTop": "8em"}, "html": "<p class=\"sds_line-height--1-4 optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">*Valid for first-time registrants & applies to regular price items only.<a href=\"/customerService/info.do?cid=1165787\" style=\"font-size:1em;text-decoration:underline;text-transform:uppercase;margin-left: 4px;\">Details</a></p><p><a href=\"/customerService/info.do?cid=2331\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">*Privacy Policy</a></p>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false}, "mobile": {"className": "sds_sp", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "backgroundColor": "#2b2b2b"}, "mobile": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary"}}, ".sitewide-1hf9k7u": {"style": {"backgroundColor": "#2b2b2b"}}, "errorNotificationAriaLabel": "Error"}}]}}}}]}}}, "analytics": {"on_close": {"tracking_enabled": true, "content_id": "email_popup_close", "link_name": "email_popup_close"}, "on_submit": {"tracking_enabled": true, "content_id": "email_popup_submit", "link_name": "email_popup_submit"}}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": []}}, "prefooter": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"ciid": "20fcbf8c-03a3-42b2-8c21-f8aa9e531fec", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "544px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 class=\"wcd_footer_h1 uppercase\">See It First</h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p class=\"wcd_footer_copy legal\"><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" class=\"uppercase nowrap\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>#sitewide-footer,#sitewide-footer button,#sitewide-footer input,#sitewide-footer select,#sitewide-footer textarea{font-family:'Gap Sans',Helvetica,Arial,Roboto,sans-serif}#sitewide-footer{background-color:#000;color:#fff}.gap-footer *{box-sizing:border-box}.gap-footer .nowrap{white-space:nowrap}.gap-footer .uppercase{text-transform:uppercase}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline}.gap-footer .wcd_footer_h1{font-size:14px;font-weight:500;line-height:1.125;margin-bottom:.25em}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em}.gap-footer .wcd_footer_copy.legal{font-size:10px}.gap-footer .wcd_footer_copy a{text-decoration:underline}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#767676;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:uppercase}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta.full-width{width:100%}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:10px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline}.gap-footer .wcd_footer_cta.details button:hover{color:#fff}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;padding-bottom:4px}.gap-footer .wcd_footer_cta li{border-bottom-width:0;border-color:#fff;padding:0}.gap-footer .wcd_footer_cta li a{font-weight:400;padding-left:32px;text-transform:none}.gap-footer [data-testid=prefooter-row]{margin-bottom:0}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px)}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0}span.sitewide-1sr49e6{display:none}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:10px;text-transform:none;top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg)}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#2b2b2b;display:-ms-flexbox;display:flex;font-weight:400;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem}.gap-footer .footer-copyright-section{background-color:#000;border-top-color:#fff;border-width:0;color:#fff;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px)}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:11px;line-height:1.5}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:uppercase}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px}.gap-footer .footer-container-wrapper>div:nth-child(5){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child{margin-bottom:30px}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta button{background-color:transparent;color:inherit;height:24px;-ms-flex-pack:start;justify-content:flex-start}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){margin-bottom:6px;padding:0 16px}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:10px;line-height:1.25}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:uppercase}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em}.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:14px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1920px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){display:none}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:24px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:18px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:20px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:26px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:21px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}@media only screen and (max-width:767px){.gap-footer .footer-container-wrapper>div:nth-child(2){display:none}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Customer Service", "href": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService"}, {"text": "Buy Online. Pick Up In-Store.", "href": "/browse/info.do?cid=1178485&mlink=1037267,********,Footer_BOPIS"}, {"text": "Store Locator", "href": "/stores?mlink=1037267,********,cs_footer_storelocator"}, {"text": "GapCash", "href": "/browse/info.do?cid=1077281&mlink=1037267,********,Footer_GapCash"}, {"text": "GiftCards", "href": "/customerService/info.do?cid=1067353&mlink=1037267,********,Footer_GiftCards"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "Join <PERSON> Good Rewards", "href": "/my-account/sign-in?mlink=1037267,********,UNIFOOTER_GGR_ACQ"}, {"text": "Apply for a Credit Card", "href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPFSUNIFTM&mlink=1037267,********,UNIFOOTER_GGR_CARD_ACQ"}, {"text": "My Rewards & Benefits", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1037267,********,UNIFOOTER_MTL_RET"}, {"text": "Pay Credit Card Bill", "href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, {"text": "Learn More", "href": "/customerService/info.do?cid=1099133&sitecode=GPFSUNIFTILPM&mlink=1037267,d58814bd-263f-417a-94a8-97d0bb6ad02b,UNIFOOTER_GGR_LP_CARD_ACQ"}]}}, {"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Our Values", "href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, {"text": "Equality and Belonging", "href": "https://www.gapinc.com/en-us/values/equality-belonging", "target": "_blank"}, {"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1178485&mlink=1037267,********,Footer_BOPIS", "target": "_blank"}, "components": ["Buy Online. Pick Up In-Store."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores?mlink=1037267,********,cs_footer_storelocator"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1077281&mlink=1037267,********,Footer_GapCash"}, "components": ["GapCash"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1067353&mlink=1037267,********,Footer_GiftCards"}, "components": ["GiftCards"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=1037267,********,UNIFOOTER_GGR_ACQ"}, "components": ["Join <PERSON> Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPFSUNIFTD&mlink=1037267,********,UNIFOOTER_GGR_CARD_ACQ"}, "components": ["Apply for a Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1037267,********,UNIFOOTER_MTL_RET"}, "components": ["My Rewards & Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1099133&sitecode=GPFSUNIFTILPD&mlink=1037267,d58814bd-263f-417a-94a8-97d0bb6ad02b,UNIFOOTER_GGR_LP_CARD_ACQ"}, "components": ["Learn More"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, "components": ["Our Values"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values/equality-belonging", "target": "_blank"}, "components": ["Equality and Belonging"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}]}}]}}]}}}}}}]}}}}, "header": {"CIID": "ed02bd71-3077-4234-9bde-88b9015806a5", "byCid": [{"details": "Pages that want transparent nav to overlay content", "configurationForCids": []}], "byPageType": [{"details": "Adjustments for if HP needs overrides", "configurationForPageTypes": ["home"], "stickyBackground": "contrast", "contrast": "dark", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false, "fullBleedContrast": "light"}}, {"details": "Adjustments for if non-HP needs overrides", "configurationForPageTypes": ["category", "product", "Information", "customlandingpage", "search", "dynamicerror", "division"], "stickyBackground": "contrast", "contrast": "dark"}], "default": {"isUtilityLinksEnabled": false, "headerLayout": "sameRow", "stickyScrollDirection": "up", "isStickyEnabled": true, "styles": {}}}, "topnav": {"ciid": "f693599d-c0c5-4c02-b754-845ebddf1130", "name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": false, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; /*color: #2b2b2b;*/ display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2.75vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #e51937", "topnav a.divisionLink.navlink-gift": "color: #e51937;", "topnav a.catnav--item--link.sitewide-4l9vad[data-categoryid='1187473']": "color: #e51937;", "topnav .catnav--item--link": "max-width: 265px;", "topnav li.catnav--item.sitewide-1l5zl4xli": "color: #e28743;", "meganav": "border-top-width: 0;", "topnav li": "/*margin-left: auto; margin-right: 10%;*/", "topnav": "/*max-width: 100%; min-width: fit-content;*/", "topnav-container": "min-width: 61vw;"}, "activeDivisions": [{"name": "New", "divisionId": ["1137421"], "megaNavOrder": [["1137422"], ["3020915"], ["3019886"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1184945' href='/browse/category.do?cid=1184945&mlink=1037059,Megnav_Pajamas' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapfactoryprod.a.bigcontent.io/v1/static/092623_MatchingFamilyPJs_SiteMegaNavTile__7532_SA1697_NEW' alt='Girls New Arrivals image'><img style='position:absolute;left:0;top:0;' src='https://gapfactoryprod.a.bigcontent.io/v1/static/092623_MatchingFamilyPJs_SiteMegaNavTile__7532_SA1697_TXT' alt='Family PJ'></a></li></ul></li>"]], "numberOfColumns": {"1137422": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "Women", "subtitle": "New! 0-30 & XS-4X", "divisionId": ["/browse/division.do?cid=1040941&mlink=1037059,20188717,Megnav_Women&clink=20188717", "1040941"], "megaNavOrder": [["1075602", "1144615", "1092843", "1042792"], ["1083398"], ["1099767", "1089399"]], "numberOfColumns": {"1083398": 2}, "exclusionIds": [], "customStyles": {"1042792": {"inlineStyle": {"color": "#D00000"}}, "1092843": {"inlineStyle": {"color": "#D00000"}}, "1144615": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=1040942&mlink=1037059,29624941,Megnav_Men&clink=29624941", "1040942"], "megaNavOrder": [["1084557", "1144614", "1092773"], ["1083400"], ["1144612", "1162911"]], "numberOfColumns": {"1083400": 2}, "exclusionIds": [], "customStyles": {"1042764": {"colorScheme": "sale"}, "1092773": {"inlineStyle": {"color": "#D00000"}}, "1144613": {"inlineStyle": {"color": "#006646"}}, "1144614": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1040943&mlink=1037059,20188717,Megnav_Girls&clink=20188717", "1040943"], "megaNavOrder": [["1084367", "1180883", "1144611", "1092850", "1042785"], ["1084370"], ["1093865", "1144609"]], "numberOfColumns": {"1084370": 2}, "exclusionIds": [], "customStyles": {"1042785": {"inlineStyle": {"color": "#D00000"}}, "1092850": {"inlineStyle": {"color": "#D00000"}}, "1144578": {"inlineStyle": {"color": "#006646"}}, "1144611": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1079661&mlink=1037059,20188717,Megnav_Boys&clink=20188717", "1079661"], "megaNavOrder": [["1083130", "1180885", "1144606", "1092793", "1042766"], ["1083133"], ["1093866", "1144607"]], "numberOfColumns": {"1083133": 2}, "exclusionIds": [], "customStyles": {"1042766": {"inlineStyle": {"color": "#D00000"}}, "1083131": {"colorScheme": "sale"}, "1092793": {"inlineStyle": {"color": "#D00000"}}, "1144579": {"inlineStyle": {"color": "#006646"}}, "1144606": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1091867&mlink=1037059,29624941,Megnav_<PERSON><PERSON>&clink=29624941", "1091867"], "megaNavOrder": [["1093607", "1144054", "1092869", "1092872", "1042786", "1042787"], ["1049875"], ["1049876"], ["1093549", "1144264"]], "exclusionIds": [], "customStyles": {"1042786": {"inlineStyle": {"color": "#D00000"}}, "1042787": {"inlineStyle": {"color": "#D00000"}}, "1092869": {"inlineStyle": {"color": "#D00000"}}, "1092872": {"inlineStyle": {"color": "#D00000"}}, "1144054": {"inlineStyle": {"color": "#D00000"}}, "1144266": {"inlineStyle": {"color": "#006646"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1040944&mlink=1037059,29624941,Megnav_Boys&clink=29624941", "1040944"], "megaNavOrder": [["1093606", "1144585", "1092873", "1092875", "1091767", "1091779"], ["1064166"], ["1064167"], ["1075607", "1144583"]], "exclusionIds": [], "customStyles": {"1091767": {"inlineStyle": {"color": "#D00000"}}, "1091779": {"inlineStyle": {"color": "#D00000"}}, "1092873": {"inlineStyle": {"color": "#D00000"}}, "1092875": {"inlineStyle": {"color": "#D00000"}}, "1144584": {"inlineStyle": {"color": "#006646"}}, "1144585": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Gifts & PJs", "divisionId": ["1144537"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1184945' href='/browse/category.do?cid=1184945&mlink=1037059,Megnav_Pajamas' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapfactoryprod.a.bigcontent.io/v1/static/092623_MatchingFamilyPJs_SiteMegaNavTile__7532_SA1697_NEW' alt='Girls New Arrivals image'><img style='position:absolute;left:0;top:0;' src='https://gapfactoryprod.a.bigcontent.io/v1/static/092623_MatchingFamilyPJs_SiteMegaNavTile__7532_SA1697_TXT' alt='Family PJ'></a></li></ul></li>"], ["1184945"], ["1196503"], ["1146228", "3024577"], ["1185081", "1185108"]], "exclusionIds": [], "customStyles": {}}, {"name": "Deals & Clearance", "divisionId": ["/browse/division.do?cid=1150511", "1150511"], "megaNavOrder": [["1184866"], ["1150513"]], "numberOfColumns": {"1150513": 2, "1184866": 2}, "exclusionIds": [], "customStyles": {"1150511": {"className": "clearance navlink-red"}, "1150513": {"colorScheme": "sale"}, "1184866": {"colorScheme": "sale"}}}, {"name": "Gap.com", "divisionId": ["http://www.gap.com/browse/home.do?cid=5058&mlink=1037059,18446873,GFOL_MainNav_GOLHP&clink=18446873", "5058"], "megaNavOrder": [], "exclusionIds": [], "customStyles": {"5058": {"className": "gapTextLink"}}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "2023-11-14_1037345_PromoDrawer", "instanceName": "promoDrawer-en_US", "experimentRunning": false, "data": {"buildInfo": ["2023-11-14_1037345_PromoDrawer", "GF"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter"], "anchor": "bottom"}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_TILE_GGR_CARD_RET_PROMO\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_PromoDrawer_1_7526_\" alt=\"Card extra 25 off\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "997457", "genericCode": "GGRRARE"}, "promoId": "lo7dx75m"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_TILE2\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_PromoDrawer_2_7922_\" alt=\"Noncard extra 10 off\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998061", "genericCode": "GFHURRY"}, "promoId": "lo7dzch5"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1135847&mlink=1037345,&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_TILE3\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_PromoDrawer_3_7923_\" alt=\"Extra 50 off clearance\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "discount automatically applied at checkout", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998059", "genericCode": ""}, "promoId": "lo7e0k9x"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n  .pd_four-cta-lo7e1qz9 {\n    background-color: #122344; /* default */\n    color: #fff; /* default */\n    height: 100%;\n    position: relative;\n    width: 100%;\n  }\n  .pd_four-cta-lo7e1qz9 img {\n    margin: 0 auto;\n    max-width: 100%;\n  }\n  .pd_four-cta-lo7e1qz9 .pd_four-cta--cta-container {\n    bottom: 4%;\n    box-sizing: border-box;\n    display: flex;\n    flex-flow: row nowrap;\n    padding: 0 3%;\n    position: absolute;\n    width: 100%;\n  }\n  .pd_four-cta-lo7e1qz9 .pd_four-cta_button {\n    border: #fff solid 1px;\n    box-sizing: border-box;\n    color: #fff;\n    font-size: 10px;\n    font-weight: 600;\n    min-height: 24px;\n    padding: 6px 8px;\n    text-align: center;\n    text-transform: uppercase;\n    width: 48.5%;\n  }\n  .pd_four-cta-lo7e1qz9 .pd_four-cta_button:not(:first-child) {\n    margin-left: 3%;\n  }\n  </style>\n  \n  \n  <div class=\"pd_four-cta-lo7e1qz9\">\n    <!-- <a href=\"\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\"> -->\n      <img id=\"PDImageTag\" src=\"https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_PromoDrawer_4_7924_\" alt=\"Doorbuster 5 0ff pjs, cold weather accessories, and puffer vests\">\n<!-- </a> -->\n    <div class=\"pd_four-cta--cta-container\">\n      <a href=\"/browse/category.do?cid=1185389#pageId=0&department=136&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_Tile4\" class=\"pd_four-cta_button\">Women</a>\n      <a href=\"/browse/category.do?cid=1185390#pageId=0&department=75&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_Tile4\" class=\"pd_four-cta_button\">Men</a>\n      <a href=\"/browse/category.do?cid=1185391#pageId=0&department=48&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_Tile4\" class=\"pd_four-cta_button\">Girls</a>\n      <a href=\"/browse/category.do?cid=1185392#pageId=0&department=16&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_Tile4\" class=\"pd_four-cta_button\">Boys</a>\n    </div>\n  </div>\n  ", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "Exclusions Apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998039", "genericCode": ""}, "promoId": "lo7e1fge"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/my-account/sign-in?creditOffer=barclays&sitecode=GPFSPD&retUrl=https://www.gapfactory.com/customerService/info.do?cid=1099133&mlink=1037345,b5995209-ebe8-4fae-bb00-fe7303c09acf,PD_TILE_GGR_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://gapfactoryprod.a.bigcontent.io/v1/static/111423_PreBlackFriEv40_PromoDrawer_5_7925_\" alt=\"Card Acquisition\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile5"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "1000397", "genericCode": ""}, "promoId": "lo7e39ao"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".8em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/minus-white.svg", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(5 available)"}, "closedState": {"headerText": "extra 50% off clearance", "subHeaderText": "and more big deals", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/plus-white.svg", "iconAltText": "Closed icon"}, "aria-label": "extra 50% off clearance"}, "pd_id": "pdid_1534541049574"}}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"fontSize": "10.5px"}, "brandBarShortcutLinks": []}}, "hamnav": {"sitewide-hamnav-desc": "hamnav add gift shop and gap external link", "type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": ["1137421", "1040941", "1040942", "1040943", "1079661", "1091867", "1040944", "1144537", {"cid": "1150511", "customStyles": {"button": {"div": {"color": "#D00000"}}}}, "cGap5058"], "exclusionIds": ["1170807"]}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sweaters", "<PERSON><PERSON>", "Gap Logo Hoodie", "Dresses"]}, "style": {"paddingRight": "25px;"}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap Factory Store logo", "lightLogoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_WHITE.svg", "darkLogoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_BLK.svg", "logoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_BLK.svg", "isSquare": true, "className": "", "style": {"& > a > img": {"width": "auto", "height": "90px", "@media only screen and (max-width:767px)": {"height": "60px"}, "padding-top": "0!important", "&:hover": {"padding-top": "0!important"}}, "& > a ": {"width": "auto", "height": "90px", "@media only screen and (max-width:767px)": {"height": "60px"}, "&:hover": {"padding-top": "0!important", "padding-bottom": "0!important"}}}}}, "brand": "gapfs", "type": "meta", "pmcsEdgeCacheTag": "gapfs-homepage-en-us-prod"}