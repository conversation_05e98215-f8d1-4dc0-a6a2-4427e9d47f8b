// @ts-nocheck
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>LAR<PERSON>} from "@ecom-next/core/breakpoint-provider"
import {CSSProperties} from "react"
import {<PERSON><PERSON>} from "@ecom-next/core/react-stitch"
import { expectToNotBeSticky, fireEvent, render, RenderResult, screen, act } from "@sitewide/components/legacy/setupTests/test-helpers";

import Header from ".."
import {fakeStickyHeights} from "@sitewide/components/legacy/setupTests/sticky"
import {FakeContentResolver} from "@sitewide/components/legacy/setupTests/withFakeContentProvider"
import {ContentData} from "../../content"
import * as HeaderContext from "../../compressed-header/HeaderContext"
import {renderWithHeaderContext} from "../../compressed-header/__fixtures__/renderWithHeaderContext"
import {headerContent} from "../__fixtures__/headerContent"
import {features} from "@sitewide/components/legacy/sitewide-constants"
import {
  FeatureVariables,
  SITEWIDE_HEADER_SCROLL_ON_PDP,
  Brand,
} from "@sitewide/components/legacy/types"
import {
  compressedHeaderContent,
  compressedHeaderRenderOptions,
} from "../../compressed-header/__fixtures__/compressedHeaderContent"
import {defaultContentData} from "@sitewide/components/legacy/fixtures/DefaultContentData"
import {ExperimentMarketingPlacement} from "../../content/ExperimentMarketingPlacement"

jest.mock("../../content/ContentPlacement", () => ({
  ContentPlacement: ({name}: {name: string}) => (
    <div data-testid={name}>{name}</div>
  ),
}))

jest.mock("../../compressed-header", () => () => (
  <div data-testid="compressed-sticky-header">compressed header</div>
))





jest.mock("../../content/ExperimentMarketingPlacement", () => {
  const {ExperimentMarketingPlacement} = jest.requireActual(
    "../../content/ExperimentMarketingPlacement"
  )
  return {
    ExperimentMarketingPlacement: jest.fn(ExperimentMarketingPlacement),
  }
})
const mockedExperimentMarketingPlacement = ExperimentMarketingPlacement

global.IS_STORYBOOK = false

describe("<Header />", () => {
  const renderFullHeader = (): RenderResult => {
    return renderWithHeaderContext(<Header />, {})
  }

  const queryCompressedHeader = (): HTMLElement | null => {
    return screen.queryByTestId("compressed-sticky-header")
  }

  const renderHeaderWithBreakpoint = (
    breakpoint: Size,
    divId = "",
    containerStyles?: CSSProperties,
    featureVariables: FeatureVariables = {}
  ) => {
    renderWithHeaderContext(
      <>
        <Header />
        <div className="product-container" style={containerStyles}>
          <div id={divId}>
            <div className="product-info">
              <h2>Product Name</h2>
            </div>
          </div>
        </div>
      </>,
      {
        breakpoint,
        appState: {
          brandName: Brands.BananaRepublic,
          pageType: "product",
          featureVariables,
        },
        enabledFeatures: {
          [features.HEADER_SCROLL_ON_PDP]: true,
        },
      }
    )
  }

  it("renders the compressed header when enabled", () => {
    renderFullHeader()
    expect(queryCompressedHeader()).toBeInTheDocument()
  })

  it("does not render the compressed header when disabled", () => {
    renderWithHeaderContext(<Header />, {contentData: defaultContentData})
    expect(queryCompressedHeader()).not.toBeInTheDocument()
  })

  const queryForPlacements = (): HTMLElement[] =>
    screen.queryAllByTestId(
      /\buniversalComponent\b|\bheadline\b|\bdesktopemergencybanner\b|\bsecondary-headline\b|\banimatedheadline\b|\bmobileemergencybanner\b|\bedfssmall\b|\bbelow-topnav\b|\bcompressed-sticky-header\b|\bcountdown\b/
    )
  describe("banner placements", () => {
    const getPlacementIndex = (
      placements: HTMLElement[],
      name: string,
      after = -1
    ): number =>
      placements.findIndex(
        (placement, index) =>
          placement.getAttribute("data-testid") === name && index > after
      )

    const desktopEmergencyBannerIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "desktopemergencybanner")
    const headlineIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "headline")
    const secondaryHeadlineIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "secondary-headline")
    const animatedheadlineIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "animatedheadline")
    const mobileemergencybannerIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "mobileemergencybanner")
    const edfssmallIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "edfssmall")
    const countdownIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "countdown")
    const someUniversalComponentIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "universalComponent")
    const someUniversalComponentIndexAfter = (
      placements: HTMLElement[],
      afterIndex: number
    ): number => getPlacementIndex(placements, "universalComponent", afterIndex)
    const compressedHeaderIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "compressed-sticky-header")
    const belowTopNapIndex = (placements: HTMLElement[]): number =>
      getPlacementIndex(placements, "below-topnav")

    describe("in desktop", () => {
      const renderDesktopHeader = (
        contentData: ContentData = headerContent()
      ): RenderResult => {
        return render(<Header />, {
          breakpoint: XLARGE,
          content: {
            contentData,
          },
        })
      }

      it("renders the desktop banners in the correct order", () => {
        renderDesktopHeader()
        const placements = queryForPlacements()
        expect(placements.length).toBe(7)

        // can't test universal components well :( this is the desktop universal bar and sister brands
        expect(someUniversalComponentIndex(placements)).toEqual(0)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(1)
        expect(headlineIndex(placements)).toEqual(2)
        expect(secondaryHeadlineIndex(placements)).toEqual(3)
        expect(animatedheadlineIndex(placements)).toEqual(4)
        // can't test universal components well :( this is the brand bar
        expect(someUniversalComponentIndexAfter(placements, 1)).toEqual(5)

        expect(mobileemergencybannerIndex(placements)).toEqual(-1)
        expect(edfssmallIndex(placements)).toEqual(-1)

        expect(countdownIndex(placements)).toEqual(6)
      })

      it("does not render the animated headline if missing from configuration", () => {
        renderDesktopHeader(headerContent({animatedheadline: undefined}))
        const placements = queryForPlacements()

        expect(animatedheadlineIndex(placements)).toEqual(-1)

        expect(desktopEmergencyBannerIndex(placements)).toBeGreaterThan(-1)
        expect(headlineIndex(placements)).toBeGreaterThan(-1)
        expect(secondaryHeadlineIndex(placements)).toBeGreaterThan(-1)
      })

      it("header is not sticky when header is on top", () => {
        renderDesktopHeader()
        fireEvent.scroll(window, {target: {pageYOffset: 0}})
        const desktopMobileHeader = screen.getByTestId("desktop-mobile-header")
        expectToNotBeSticky(desktopMobileHeader)
      })
    })

    describe("in mobile", () => {
      const renderMobileHeader = (
        contentData: ContentData = headerContent()
      ): RenderResult => {
        return render(<Header />, {
          breakpoint: SMALL,
          appState: {
            pageType: "home",
          },
          content: {
            contentData,
            ContentResolver: FakeContentResolver,
          },
        })
      }

      it("renders the mobile banners in the correct order", () => {
        renderMobileHeader(headerContent({animatedheadline: undefined}))

        const placements = queryForPlacements()
        expect(placements.length).toBe(7)

        expect(headlineIndex(placements)).toEqual(0)
        // can't test universal components well :( this is the brand bar
        expect(someUniversalComponentIndex(placements)).toEqual(1)
        expect(edfssmallIndex(placements)).toEqual(2)
        expect(secondaryHeadlineIndex(placements)).toEqual(3)
        expect(mobileemergencybannerIndex(placements)).toEqual(4)
        expect(countdownIndex(placements)).toEqual(5)
        expect(belowTopNapIndex(placements)).toEqual(6)

        expect(animatedheadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })

      describe("ExperimentMarketingPlacement", () => {
        beforeEach(() => {
          mockedExperimentMarketingPlacement.mockClear()
        })

        it("should not render ExperimentMarketingPlacement when not using AnimatedHeadline", () => {
          renderMobileHeader(headerContent({animatedheadline: undefined}))
          expect(mockedExperimentMarketingPlacement).not.toHaveBeenCalledWith(
            {
              name: "mobileemergencybanner",
            },
            {}
          )
          expect(screen.getByTestId("mobileemergencybanner")).toHaveTextContent(
            "mobileemergencybanner"
          )
        })

        it("should render ExperimentMarketingPlacement when AnimatedHeadline", () => {
          renderMobileHeader(
            headerContent({
              animatedheadline: {
                name: "animatedheadline",
                type: "Sitewide",
                data: {},
              },
            })
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "mobileemergencybanner",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "mobileemergencybanner",
            },
            {}
          )
          expect(screen.getByTestId("mobileemergencybanner")).toHaveTextContent(
            "mobileemergencybanner"
          )
        })

        it("should render ExperimentMarketingPlacement when AnimatedHeadline is defined", () => {
          renderMobileHeader(
            headerContent({
              animatedheadline: {
                name: "animatedheadline",
                type: "Sitewide",
                data: {},
              },
            })
          )

          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "headline",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "edfssmall",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "secondary-headline",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "mobileemergencybanner",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "animatedheadline",
            },
            {}
          )
          expect(mockedExperimentMarketingPlacement).toHaveBeenCalledWith(
            {
              name: "below-topnav",
            },
            {}
          )
        })
      })

      it("renders the mobile banners with default animated headline", () => {
        renderMobileHeader(
          headerContent({
            animatedheadline: {
              name: "animatedheadline",
              type: "Sitewide",
              data: {},
            },
          })
        )

        const placements = queryForPlacements()
        expect(placements.length).toBe(4)

        // can't test universal components well :( this is the brand bar
        expect(someUniversalComponentIndex(placements)).toEqual(0)
        expect(mobileemergencybannerIndex(placements)).toEqual(1)
        expect(animatedheadlineIndex(placements)).toEqual(2)
        expect(countdownIndex(placements)).toEqual(3)

        expect(headlineIndex(placements)).toEqual(-1)
        expect(edfssmallIndex(placements)).toEqual(-1)
        expect(secondaryHeadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })

      it("renders the mobile banners with included placement configured animated headline", () => {
        renderMobileHeader(
          headerContent({
            animatedheadline: {
              name: "animatedheadline",
              type: "Sitewide",
              data: {},
              placementConfiguration: {
                home: {
                  include: ["headline"],
                  exclude: [],
                },
              },
            },
          })
        )

        const placements = queryForPlacements()
        expect(placements.length).toBe(5)

        expect(headlineIndex(placements)).toEqual(0)
        // can't test universal components well :( this is the sister brands
        expect(someUniversalComponentIndex(placements)).toEqual(1)
        expect(mobileemergencybannerIndex(placements)).toEqual(2)
        expect(animatedheadlineIndex(placements)).toEqual(3)
        expect(countdownIndex(placements)).toEqual(4)

        expect(edfssmallIndex(placements)).toEqual(-1)
        expect(secondaryHeadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })

      it("renders the mobile banners with excluded placement configured animated headline", () => {
        renderMobileHeader(
          headerContent({
            animatedheadline: {
              name: "animatedheadline",
              type: "Sitewide",
              data: {},
              placementConfiguration: {
                home: {
                  include: [],
                  exclude: ["edfssmall", "mobileemergencybanner"],
                },
              },
            },
          })
        )

        const placements = queryForPlacements()
        expect(placements.length).toBe(3)

        // can't test universal components well :( this is the brand bar
        expect(someUniversalComponentIndex(placements)).toEqual(0)
        expect(animatedheadlineIndex(placements)).toEqual(1)
        expect(countdownIndex(placements)).toEqual(2)

        expect(edfssmallIndex(placements)).toEqual(-1)
        expect(mobileemergencybannerIndex(placements)).toEqual(-1)
        expect(headlineIndex(placements)).toEqual(-1)
        expect(secondaryHeadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })
    })

    describe("with the compressed header", () => {
      const renderCompressedHeader = (
        brand: Brand,
        breakpoint: Size,
        contentData: ContentData = headerContent(
          compressedHeaderContent().sitewide
        )
      ): RenderResult => {
        return render(
          <HeaderContext.HeaderProvider>
            <Header />
          </HeaderContext.HeaderProvider>,
          {
            ...compressedHeaderRenderOptions(brand, {breakpoint}),
            content: {
              contentData,
              ContentResolver: FakeContentResolver,
            },
          }
        )
      }

      it("renders the desktop banners in the correct order for Gap", () => {
        renderCompressedHeader(Brand.Gap, XLARGE)
        const placements = queryForPlacements()
        expect(placements.length).toBe(8)
        // can't test universal components well :( this is the desktop universal bar and sister brands
        expect(someUniversalComponentIndex(placements)).toEqual(0)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(1)
        expect(headlineIndex(placements)).toEqual(2)
        expect(secondaryHeadlineIndex(placements)).toEqual(3)
        expect(animatedheadlineIndex(placements)).toEqual(4)
        expect(compressedHeaderIndex(placements)).toEqual(5)
        expect(countdownIndex(placements)).toEqual(6)
        expect(belowTopNapIndex(placements)).toEqual(7)

        expect(mobileemergencybannerIndex(placements)).toEqual(-1)
        expect(edfssmallIndex(placements)).toEqual(-1)
      })

      it("renders the desktop banners in the correct order for Br", () => {
        renderCompressedHeader(Brand.BananaRepublic, XLARGE)
        const placements = queryForPlacements()
        expect(placements.length).toBe(9)
        // can't test universal components well :( this is the desktop universal bar and sister brands
        expect(someUniversalComponentIndex(placements)).toEqual(0)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(2)
        expect(headlineIndex(placements)).toEqual(3)
        expect(secondaryHeadlineIndex(placements)).toEqual(4)
        expect(animatedheadlineIndex(placements)).toEqual(5)
        expect(compressedHeaderIndex(placements)).toEqual(6)
        expect(countdownIndex(placements)).toEqual(7)
        expect(belowTopNapIndex(placements)).toEqual(8)

        expect(mobileemergencybannerIndex(placements)).toEqual(-1)
        expect(edfssmallIndex(placements)).toEqual(-1)
      })

      it("renders the mobile banners in the correct order for Gap", () => {
        renderCompressedHeader(Brand.Gap, SMALL, {
          ...headerContent({
            animatedheadline: undefined,
            ...compressedHeaderContent().sitewide,
          }),
        })

        const placements = queryForPlacements()
        expect(placements.length).toBe(7)
        expect(headlineIndex(placements)).toEqual(0)
        expect(edfssmallIndex(placements)).toEqual(1)
        expect(secondaryHeadlineIndex(placements)).toEqual(2)
        expect(mobileemergencybannerIndex(placements)).toEqual(3)
        expect(compressedHeaderIndex(placements)).toEqual(4)
        expect(countdownIndex(placements)).toEqual(5)
        expect(belowTopNapIndex(placements)).toEqual(6)

        expect(animatedheadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })

      it("renders the mobile banners in the correct order for Br", () => {
        renderCompressedHeader(Brand.BananaRepublic, SMALL, {
          ...headerContent({
            animatedheadline: undefined,
            ...compressedHeaderContent().sitewide,
          }),
        })

        const placements = queryForPlacements()
        expect(placements.length).toBe(8)
        expect(headlineIndex(placements)).toEqual(1)
        expect(edfssmallIndex(placements)).toEqual(2)
        expect(secondaryHeadlineIndex(placements)).toEqual(3)
        expect(mobileemergencybannerIndex(placements)).toEqual(4)
        expect(compressedHeaderIndex(placements)).toEqual(5)
        expect(countdownIndex(placements)).toEqual(6)
        expect(belowTopNapIndex(placements)).toEqual(7)

        expect(animatedheadlineIndex(placements)).toEqual(-1)
        expect(desktopEmergencyBannerIndex(placements)).toEqual(-1)
      })
    })
  })

  xdescribe("header height", () => {
    const updateHeaderHeightSpy = jest.fn()

    beforeEach(() => {
      global.IS_SSR = false
      fakeStickyHeights()
      jest.spyOn(HeaderContext, "useHeaderReducers").mockImplementation(() => {
        return {
          updateHeaderHeight: updateHeaderHeightSpy,
          needsHighContrast: jest.fn(),
        }
      })
    })

    it("should not be called before scroll or resize", () => {
      renderFullHeader()

      expect(updateHeaderHeightSpy).not.toHaveBeenCalled()
    })
    it("should update header height on scroll", () => {
      renderFullHeader()

      fireEvent.scroll(window, {target: {pageYOffset: 10}})

      expect(updateHeaderHeightSpy).toHaveBeenCalledWith(20)
    })

    it("should update header height on resize", () => {
      renderFullHeader()

      window.resizeTo = function resizeTo(width) {
        Object.assign(this, {
          innerWidth: width,
          outerWidth: width,
        }).dispatchEvent(new this.Event("resize"))
      }

      window.resizeTo(200, 200)

      expect(updateHeaderHeightSpy).toHaveBeenCalledWith(20, true)
    })

    describe("when PDP Header Scroll is enabled and pageType is product on mobile", () => {
      describe("on desktop", () => {
        it("should update header considering product title header", () => {
          renderHeaderWithBreakpoint(XLARGE, "buy-box")

          fakeStickyHeights()

          fireEvent.scroll(window, {target: {pageYOffset: 100}})
          expect(updateHeaderHeightSpy).toHaveBeenCalledWith(50)
        })
      })

      describe("on mobile", () => {
        beforeEach(() => {
          jest.clearAllMocks()
        })

        it("should update header considering product title header", () => {
          renderHeaderWithBreakpoint(SMALL, "sitewide-meganav-anchor")

          fakeStickyHeights()

          fireEvent.scroll(window, {target: {pageYOffset: 100}})
          expect(updateHeaderHeightSpy).toHaveBeenCalledWith(50)
        })

        it("should update header considering product title header and padding from parent", () => {
          renderHeaderWithBreakpoint(SMALL, "sitewide-meganav-anchor", {
            paddingTop: 20,
          })

          fakeStickyHeights()

          fireEvent.scroll(window, {target: {pageYOffset: 100}})
          expect(updateHeaderHeightSpy).toHaveBeenCalledWith(70)
        })

        it("should update header considering product title header and margin from parent", () => {
          renderHeaderWithBreakpoint(SMALL, "sitewide-meganav-anchor", {
            margin: 10,
          })

          fakeStickyHeights()

          fireEvent.scroll(window, {target: {pageYOffset: 200}})
          expect(updateHeaderHeightSpy).toHaveBeenCalledWith(60)
        })

        it("should update header considering product title header and padding + margin from parent + variable scale", () => {
          renderHeaderWithBreakpoint(
            SMALL,
            "sitewide-meganav-anchor",
            {
              paddingTop: 20,
              marginTop: 30,
            },
            {
              [SITEWIDE_HEADER_SCROLL_ON_PDP]: {
                productImageScaleHeight: 90,
              },
            }
          )

          fakeStickyHeights()

          fireEvent.scroll(window, {target: {pageYOffset: 200}})
          expect(updateHeaderHeightSpy).toHaveBeenCalledWith(190)
        })
      })
    })
  })
})
