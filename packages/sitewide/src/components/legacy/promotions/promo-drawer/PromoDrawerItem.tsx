// @ts-nocheck
"use client";
import {CSSProperties} from "react"
import {styled, css} from "@ecom-next/core/react-stitch"
import {mapDataToProps} from "@mui/components/legacy/helper"
import {DynamicMarketing} from "@mui/components/json-marketing.client";
import PromoDrawerItemDetail from "./PromoDrawerItemDetail"
import DetailsButton from "./DetailsButton"
import {usePromotion} from "../usePromotion"
import {Feature} from "../types"
import {PromoDrawerItemProps} from "./types"

export const TTAMask = styled.div({
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "75%",
  backgroundColor: "#000000",
  opacity: 0.9,
  whiteSpace: "normal",
  "@media(max-width: 767px)": {
    height: "71%",
  },
})

const TTAMaskContent = styled.span({
  color: "#FFFFFF",
  fontSize: "1em",
  textAlign: "center",
  position: "absolute",
  top: "50%",
  left: "49%",
  width: "95%",
  transform: "translate(-50%, -50%)",
})

const StyledPromoDrawerItemContent = styled.div<{isAnchorTop?: boolean}>(
  ({isAnchorTop, theme}) => {
    const promoDrawerContentBottomStyle = css`
      width: 240px;
    `

    return css`
      ${theme.brandFont}
      background-color: #fff;
      border: 1px solid #dadada;
      display: inline-block;
      margin: 20px 6px;
      position: relative;
      vertical-align: top;
      @media only screen and (min-width: 768px) {
        width: 320px;
      }
      ${!isAnchorTop && promoDrawerContentBottomStyle}
    `
  }
)

const StyledPromoMessage = styled.div<{isAnchorTop?: boolean}>(
  ({isAnchorTop}) => {
    const promoMessageBottomStyles = css`
      margin-bottom: 1em;
    `

    return css`
      height: 138px;
      margin: 0 auto;
      width: 240px;
      @media only screen and (min-width: 768px) {
        height: 184px;
        width: 320px;
      }
      ${!isAnchorTop && promoMessageBottomStyles}
    `
  }
)

const StyledContentItemMessage = styled.div<{isAnchorTop?: boolean}>(
  ({isAnchorTop}) => {
    const promoActionTopStyles = css`
      font-size: 0.7em;
      margin-top: 13px;
      padding: 0 1em;
      text-align: left;
      text-transform: uppercase;
    `

    const promoActionBottomStyles = css`
      align-items: center;
      display: flex;
      font-size: 0.56rem;
      justify-content: space-between;
      @media only screen and (min-width: 768px) {
        font-size: 0.75rem;
      }
    `

    return css`
      color: #aaa;
      height: 35px;
      margin-bottom: 13px;
      padding: 0 1em;
      text-transform: uppercase;
      ${isAnchorTop ? promoActionTopStyles : promoActionBottomStyles}
    `
  }
)

const PromoDrawerItem = ({
  legalDetails,
  onPromoApplied,
  onPromoNotApplied,
  applicationDetails: {type},
  bannerContent: {isBannerClickable = true, id, ...bannerContent},
  applicationDetails: {defaultMessage = "", ...applicationDetails},
  isAnchorTop,
  onClickDetails,
  "aria-label": ariaLabel,
}: PromoDrawerItemProps): JSX.Element => {
  const {genericCode, genericCodeId} = legalDetails
  const feature = Feature.PromoDrawer
  const {applyPromotion, isPromoTapped, showTTAMask} = usePromotion({
    genericCodeId,
    genericCode,
    onPromoApplied,
    onPromoNotApplied,
    feature,
  })

  const applyTapToImage = async (): Promise<void> => {
    if (type === "tap" && isBannerClickable) {
      await applyPromotion()
    }
  }

  const displayDetailsButton =
    legalDetails.legalOverride !== "" || legalDetails.genericCodeId !== ""

  const clickableBannerProps = isBannerClickable
    ? {
        role: "button",
        onKeyPress: applyTapToImage,
        onClick: applyTapToImage,
      }
    : {}

  return (
    <StyledPromoDrawerItemContent className="promoDrawer__content__item">
      <StyledPromoMessage
        aria-label={ariaLabel}
        className="promoDrawer-content-tta"
        isAnchorTop={isAnchorTop}
        {...clickableBannerProps}
      >
        <DynamicMarketing {...bannerContent} />
      </StyledPromoMessage>
      <StyledContentItemMessage
        className="promoDrawer__content__item__msg"
        isAnchorTop={isAnchorTop}
      >
        <PromoDrawerItemDetail
          isAnchorTop={isAnchorTop}
          isTapped={isPromoTapped}
          onApplyPromo={() => applyPromotion()}
          style={applicationDetails.style as CSSProperties}
          text={
            isPromoTapped ? applicationDetails.isTappedMessage : defaultMessage
          }
          textTransformValue={applicationDetails.textTransform}
          type={applicationDetails.type}
        />
        {displayDetailsButton && (
          <DetailsButton
            legalDetails={legalDetails}
            onClick={() => {
              onClickDetails?.({promotion_code: legalDetails.genericCode})
            }}
            promodrawerItemId={id}
          />
        )}
      </StyledContentItemMessage>
      {showTTAMask && (
        <TTAMask>
          <TTAMaskContent>{applicationDetails.overlay}</TTAMaskContent>
        </TTAMask>
      )}
    </StyledPromoDrawerItemContent>
  )
}

export default PromoDrawerItem
