// @ts-nocheck
import {DynamicMarketing as Marketing} from "@mui/components/json-marketing.client";

import {PartialDeep} from "type-fest"
import { render, screen, fireEvent, waitFor, merge, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import PromoDrawerItem from "../PromoDrawerItem"
import {promoDrawerTestData} from "../__fixtures__/testData"
import {addPromoDrawerPromotion} from "../../promotionClient"
import {PromoDrawerItemProps} from "../types"

const mockAddPromotion = addPromoDrawerPromotion

jest.mock("../../promotionClient")

const BannerContentComponent = (): JSX.Element => (
  <div id="bannerContentComponent" />
)

const defaultPromotionProps: PromoDrawerItemProps = {
  ...promoDrawerTestData.promos[0],
  onPromoApplied: () => {},
  onPromoNotApplied: () => {},
  onClickDetails: () => {},
}

const errorLogger = jest.fn()

function renderPromoDrawerItem(
  props?: PartialDeep<PromoDrawerItemProps>
): void {
  const promoDrawerItemProps = merge(defaultPromotionProps, props)
  {(Marketing as jest.Mock).mockImplementation(BannerContentComponent);}
  render(
    <PromoDrawerItem {...promoDrawerItemProps} />,
    {appState: {errorLogger}, localization: {}}
  )
}

const legalDetailsAriaLabel = /promodrawer.item.detailbuttonarialabel/i
const promoAppliedText = /promo applied/i
const applyPromoText = /apply promo/i
const bannerAriaLabel = /this is the first promotional message/i

function findAppliedButton(): Promise<HTMLElement> {
  return screen.findByRole("button", {name: promoAppliedText})
}

function getAppliedButton(): HTMLElement {
  return screen.getByRole("button", {name: promoAppliedText})
}

function getNotAppliedButton(): HTMLElement {
  return screen.getByRole("button", {name: applyPromoText})
}

function queryAppliedButton(): HTMLElement | null {
  return screen.queryByRole("button", {
    name: promoAppliedText,
  })
}

function getBanner(): HTMLElement {
  return screen.getByLabelText(bannerAriaLabel)
}

describe("PromoDrawer Item", () => {
  beforeEach(() => {
    jest.resetAllMocks()
  })

  afterEach(() => {
    mockAddPromotion.mockReset()
  })

  test("should render banner content component", () => {
    renderPromoDrawerItem(defaultPromotionProps)
    expect(getBanner()).toBeInTheDocument()
  })

  test("should pass the aria label", () => {
    renderPromoDrawerItem(defaultPromotionProps)
    expect(
      screen.getByLabelText(defaultPromotionProps["aria-label"])
    ).toBeInTheDocument()
  })

  test("that style is being passed", async () => {
    renderPromoDrawerItem(defaultPromotionProps)
    expect(
      await screen.findByRole("button", {name: /apply promo/i})
    ).toHaveAttribute("style", "font-size: 12px;")
  })

  describe("legal DetailsButton", () => {
    const someRandomString = "a string"
    const legalDetails = [
      {
        genericCodeId: someRandomString,
        legalOverride: "",
      },
      {
        genericCodeId: "",
        legalOverride: someRandomString,
      },
      {
        genericCodeId: someRandomString,
        legalOverride: someRandomString,
      },
    ]

    legalDetails.forEach((details) => {
      test(`should render when legalOverride is '${details.legalOverride}' and genericCodeId is '${details.genericCodeId}'`, () => {
        const props = merge(defaultPromotionProps, {
          legalDetails: {
            legalDetails: details,
          },
        })
        renderPromoDrawerItem(props)
        expect(
          screen.getByRole("button", {
            name: legalDetailsAriaLabel,
          })
        ).toBeInTheDocument()
      })
    })

    test("when rendered, should call onClickDetails with genericCode", async () => {
      const spyOnClickDetails = jest.fn()
      const genericCode = "some code"

      const props = {
        legalDetails: {
          genericCodeId: someRandomString,
          legalDetails: someRandomString,
          genericCode,
        },
        onClickDetails: spyOnClickDetails,
      }

      renderPromoDrawerItem(props)

      await act(async () => { 

       fireEvent.click(
         screen.getByRole("button", {
           name: legalDetailsAriaLabel,
         })
       ); 

       })
      expect(spyOnClickDetails).toHaveBeenCalledWith({
        promotion_code: genericCode,
      })
    })

    test("should not render if legalOverride and genericCodeId are both empty string", () => {
      const props = {
        legalDetails: {
          genericCode: someRandomString,
          genericCodeId: "",
          legalOverride: "",
        },
      }

      renderPromoDrawerItem(props)

      expect(
        screen.queryByRole("button", {
          name: legalDetailsAriaLabel,
        })
      ).not.toBeInTheDocument()
    })
  })

  describe("applyTap", () => {
    const newRelicStub = {addPageAction: jest.fn()}
    const onPromoApplied = jest.fn()
    const onPromoNotApplied = jest.fn()
    const props = {
      onPromoApplied,
      onPromoNotApplied,
    }

    describe("onSuccess", () => {
      beforeEach(async () => {
        mockAddPromotion.mockImplementation(({addPageActionCallback}) => {
          addPageActionCallback?.({statusCode: 200, data: {}})
          return Promise.resolve()
        })
        window.newrelic = newRelicStub
        renderPromoDrawerItem(props)
        fireEvent.click(getNotAppliedButton())
        await findAppliedButton()
      })

      afterEach(() => {
        window.newrelic = undefined
      })

      test("should set local storage with ttaPromo value", () => {
        expect(sessionStorage.setItem).toHaveBeenCalledWith(
          `ttaPromo-${defaultPromotionProps.legalDetails.genericCodeId}`,
          "1"
        )
      })

      test("should add a new relic page action", () => {
        expect(newRelicStub.addPageAction).toHaveBeenCalledWith(
          "pdTapToApply",
          {
            pdTapToApplyCode: "genericCode",
            pdTapToApplyCodeId: "genericCodeId",
            pdTapToApplyResponseEmpty: true,
            pdTapToApplyCookiesShopperId: "",
            pdTapToApplyErrorMessage: "",
            pdTapToApplyStatusCode: 200,
            unknownShopperId: undefined,
          }
        )
      })

      test("should update button text", () => {
        expect(getAppliedButton()).toBeInTheDocument()
      })
    })

    describe("onFailure", () => {
      test("calls onPromoNotApplied", async () => {
        mockAddPromotion.mockImplementation(() => Promise.reject())
        renderPromoDrawerItem(props)
        await act(async () => { 

         fireEvent.click(getNotAppliedButton()); 

         })
        await waitFor(() => {
          expect(onPromoNotApplied).toHaveBeenCalledWith({
            promotion_code: "genericCode",
          })
        })
      })
    })
  })

  describe("is tap to apply promotion", () => {
    const isTapToApplyProps = {
      applicationDetails: {
        type: "tap" as const,
        overlay: "OVERLAY TEXT",
      },
    }

    beforeEach(() => {
      mockAddPromotion.mockImplementation(() => Promise.resolve())
    })

    test("should apply promo when the button is clicked", async () => {
      renderPromoDrawerItem(isTapToApplyProps)
      await act(async () => { 

       fireEvent.click(getNotAppliedButton()); 

       })
      expect(await findAppliedButton()).toBeInTheDocument()
      expect(screen.getByText(/overlay text/i)).toBeInTheDocument()
    })

    test("should call applyTap when the image is clicked with BannerContent present and isBannerClickable key not present (default)", async () => {
      renderPromoDrawerItem(isTapToApplyProps)
      await act(async () => { 

       fireEvent.click(getBanner()); 

       })
      expect(await findAppliedButton()).toBeInTheDocument()
    })

    describe("and isBannerClickable is true", () => {
      const props = {
        ...isTapToApplyProps,
        bannerContent: {
          isBannerClickable: true,
        },
      }
      beforeEach(() => {
        renderPromoDrawerItem(props)
      })
      test("should apply code when image is clicked", async () => {
        await act(async () => { 

         fireEvent.click(getBanner()); 

         })
        expect(await findAppliedButton()).toBeInTheDocument()
      })
      test("should have role of button on banner", () => {
        expect(getBanner()).toHaveAttribute("role", "button")
      })
      test("should have an associated keyPress event on banner", async () => {
        fireEvent.keyPress(getBanner(), {key: "Enter", code: 13, charCode: 13})
        expect(await findAppliedButton()).toBeInTheDocument()
      })
    })

    describe("and isBannerClickable is false", () => {
      const props = {
        ...isTapToApplyProps,
        bannerContent: {
          isBannerClickable: false,
        },
      }

      beforeEach(() => {
        renderPromoDrawerItem(props)
      })

      test("should not call applyTap when image is clicked", async () => {
        await act(async () => { 

         fireEvent.click(getBanner()); 

         })
        expect(queryAppliedButton()).not.toBeInTheDocument()
      })

      test("should not have role of button on banner", () => {
        expect(props).not.toHaveProperty("role", "button")
      })

      test("should not have an associated keyPress event on banner", () => {
        fireEvent.keyPress(getBanner(), {key: "Enter", code: 13, charCode: 13})
        expect(queryAppliedButton()).not.toBeInTheDocument()
      })
    })

    describe("and is not tapped", () => {
      beforeEach(() => {
        renderPromoDrawerItem(isTapToApplyProps)
      })

      test("should display message in button", () => {
        expect(getNotAppliedButton()).toBeInTheDocument()
      })

      test("should not display tta mask component", () => {
        expect(screen.queryByText(/overlay text/i)).not.toBeInTheDocument()
      })
    })

    describe("and is tapped", () => {
      test("should pass isTappedMessage prop in button", async () => {
        (sessionStorage.getItem as jest.Mock).mockReturnValueOnce("true")
        renderPromoDrawerItem(defaultPromotionProps)
        expect(await findAppliedButton()).toBeInTheDocument()
      })
    })
  })

  describe("is auto apply promotion", () => {
    const props = {
      applicationDetails: {
        defaultMessage: "message",
        type: "auto" as const,
      },
    }

    test("should render application message", () => {
      renderPromoDrawerItem(props)
      expect(screen.getByText("message")).toHaveStyleRules({
        color: "#666",
        left: "1em",
        "font-weight": "700",
        "text-align": "left",
        "white-space": "normal",
        display: "inline-block",
        "max-width": "50%",
        "line-height": "1.1em",
      })
    })
  })
})
