// @ts-nocheck
"use client";
import {removePipes} from "@ecom-next/core/legacy/utility"
import {getCookie} from "@ecom-next/core/legacy/utility"

export const unauthenticatedUserCookie = "unknownShopperId"
export const authenticatedUserCookie = "cam"

;
const IS_SSR = typeof window === "undefined";
export const getShopperId = (isUseCookiePackage = false): string => {
  const unknownShopperId = !IS_SSR
    ? removePipes(getCookie(unauthenticatedUserCookie, isUseCookiePackage))
    : undefined
  const cam = !IS_SSR
    ? removePipes(getCookie(authenticatedUserCookie, isUseCookiePackage))
    : undefined
  const shopperId = cam ?? unknownShopperId
  return shopperId ?? ""
}
