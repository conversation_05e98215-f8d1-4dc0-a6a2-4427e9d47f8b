// @ts-nocheck
"use client";
import {
  EyesConfig,
  fireEvent,
  isEyesVariation,
  waitFor,
  within,
} from "../../../../setupTests/test-helpers"

const yourOffersButton = async (rootEl: HTMLElement): Promise<HTMLElement> =>
  waitFor(() => within(rootEl).getByRole("button", {name: /my offers/i}))

const openValueDrawer = async (rootEl: HTMLElement): Promise<HTMLElement> => {
  fireEvent.click(await yourOffersButton(rootEl))

  return waitFor(() => within(rootEl).getByTestId("drawer-content"))
}

export const eyes: EyesConfig = {
  async runBefore({rootEl}) {
    if (isEyesVariation("opened")) {
      return openValueDrawer(rootEl)
    }

    return yourOffersButton(rootEl)
  },
  variations: ["opened"],
}
