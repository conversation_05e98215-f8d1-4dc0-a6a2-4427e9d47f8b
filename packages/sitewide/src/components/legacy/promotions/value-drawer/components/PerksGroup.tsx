// @ts-nocheck
"use client";
import {CSSObject} from "@ecom-next/core/react-stitch"
import {GroupId, PerkItem} from "../types"
import PerkTile from "../PerkTile"

const sectionTitleStyle: CSSObject = {
  fontWeight: "bold",
  letterSpacing: "0.1em",
  paddingLeft: "1em",
  paddingTop: "1em",
  textTransform: "uppercase",
}

export interface PerksGroupProps {
  groupId: GroupId
  perks: PerkItem[]
  title: string
}

export function PerksGroup({
  groupId,
  perks,
  title,
}: PerksGroupProps): JSX.Element | null {
  const hasPromos = Boolean(perks?.length)

  return hasPromos ? (
    <>
      <h2 css={sectionTitleStyle}> {title} </h2>
      {perks.map((perk, index) => {
        const itemId = `tile-${index}`
        return (
          <PerkTile
            key={perk.description}
            groupId={groupId}
            itemId={itemId}
            {...perk}
          />
        )
      })}
    </>
  ) : null
}
