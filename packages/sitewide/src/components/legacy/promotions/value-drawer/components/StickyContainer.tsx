// @ts-nocheck
"use client";
import * as React from "react"
import {CSSObject} from "@ecom-next/core/react-stitch"
import {layering} from "@sitewide/components/legacy/sitewide-constants"

type StickyContainerProps = {
  children: React.ReactNode
}

function StickyContainer({children}: StickyContainerProps): JSX.Element {
  const stickyContainerStyle: CSSObject = {
    backgroundColor: "#FFFFFF",
    height: "80px",
    overflowY: "hidden",
    position: "fixed",
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-end",
    justifyContent: "center",
    marginRight: "1em",
    border: "#cccccc solid 1px",
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: layering.VALUE_DRAWER_BUTTON,
  }
  return (
    <div css={stickyContainerStyle} role="tabpanel">
      {children}
    </div>
  )
}

export default StickyContainer
