// @ts-nocheck
const baseRectangularLogo =
  "data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='110' height='16' viewBox='201.1 248 110 16' enable-background='new 201.1 248 110 16'%3e%3cpath fill='%23010101' d='M230.8 258.1l-1.9-4.3-2 4.3h3.9zm.7 1.8h-5.4l-1.4 3.1h-2l6.2-13.4 6 13.4h-2.1l-1.3-3.1zm11.1-7.8V263h-1.9v-10.9h-2.9v-1.8h7.7v1.8zm9.8 3.2h5.4v-5h1.9V263h-1.9v-5.9h-5.4v5.9h-1.9v-12.7h1.9zm15.6-5v10.9h3.7v1.8h-5.6v-12.7zm15.7 1.8h-5v3.1h4.9v1.8h-4.9v4.2h5v1.8h-6.9v-12.7h6.9zm9.3 0V263h-1.9v-10.9h-2.9v-1.8h7.7v1.8zm12 1.7l1.9 4.3h-3.8l1.9-4.3zm-6.2 9.2h2l1.4-3.1h5.4l1.3 3.1h2.1l-6-13.4-6.2 13.4z'%3e%3c/path%3e%3cdefs%3e%3cpath id='a' d='M201.1 248h110v16h-110z'%3e%3c/path%3e%3c/defs%3e%3cclipPath id='b'%3e%3cuse xlink:href='%23a' overflow='visible'%3e%3c/use%3e%3c/clipPath%3e%3cpath clip-path='url(%23b)' fill='%2377777A' d='M209 248c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8'%3e%3c/path%3e%3cpath fill='%23FFFFFE' d='M213.4 258.1l.5 2.6-4.8-4.7 1.9 4.3-1.2 2.4-.7-6.7-1.3 4.6-2.5.9 3.8-5.5-4 2.6-2.5-.8 6.5-1.8-4.7-.5-1.4-2.3 6.1 2.8-3.3-3.5.4-2.6 2.9 6.1-.3-4.8 1.9-1.7-1.6 6.5 2.8-3.9 2.6-.1-5.4 4 4.6-1.2 2.1 1.7-6.7-.5z'%3e%3c/path%3e%3c/svg%3e" // NOSONAR

const rectangularAthletaLogoImgPaths = {
  logoImgPath: baseRectangularLogo,
  darkLogoImgPath: baseRectangularLogo,
  lightLogoImgPath:
    "data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='110' height='16' viewBox='201.1 248 110 16' enable-background='new 201.1 248 110 16'%3e%3cpath fill='%23ffffff' d='M230.8 258.1l-1.9-4.3-2 4.3h3.9zm.7 1.8h-5.4l-1.4 3.1h-2l6.2-13.4 6 13.4h-2.1l-1.3-3.1zm11.1-7.8V263h-1.9v-10.9h-2.9v-1.8h7.7v1.8zm9.8 3.2h5.4v-5h1.9V263h-1.9v-5.9h-5.4v5.9h-1.9v-12.7h1.9zm15.6-5v10.9h3.7v1.8h-5.6v-12.7zm15.7 1.8h-5v3.1h4.9v1.8h-4.9v4.2h5v1.8h-6.9v-12.7h6.9zm9.3 0V263h-1.9v-10.9h-2.9v-1.8h7.7v1.8zm12 1.7l1.9 4.3h-3.8l1.9-4.3zm-6.2 9.2h2l1.4-3.1h5.4l1.3 3.1h2.1l-6-13.4-6.2 13.4z'%3e%3c/path%3e%3cdefs%3e%3cpath id='a' d='M201.1 248h110v16h-110z'%3e%3c/path%3e%3c/defs%3e%3cclipPath id='b'%3e%3cuse xlink:href='%23a' overflow='visible'%3e%3c/use%3e%3c/clipPath%3e%3cpath clip-path='url(%23b)' fill='%2377777A' d='M209 248c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8'%3e%3c/path%3e%3cpath fill='%23FFFFFE' d='M213.4 258.1l.5 2.6-4.8-4.7 1.9 4.3-1.2 2.4-.7-6.7-1.3 4.6-2.5.9 3.8-5.5-4 2.6-2.5-.8 6.5-1.8-4.7-.5-1.4-2.3 6.1 2.8-3.3-3.5.4-2.6 2.9 6.1-.3-4.8 1.9-1.7-1.6 6.5 2.8-3.9 2.6-.1-5.4 4 4.6-1.2 2.1 1.7-6.7-.5z'%3e%3c/path%3e%3c/svg%3e", // NOSONAR
}

const baseSquareLogo =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='rgba(18, 35, 68, 1)' class='logo_gap logo_gap_text-only' viewBox='0 0 110 83.8'%3E%3Cpath class='logo_gap--text' d='M97.8,1.5H83.1v2.4H87v74.4c-0.1,1.6-0.7,2.2-3.1,2.3h-0.8v2.1h16.6v-2.1H98c-2.8-0.1-3.8-1.2-3.9-3.7V48.1h4.8c7,0,11.1-3.4,11.1-7.9V10.1C110,5.6,106.5,1.5,97.8,1.5 M102.9,24.5v16c0,2.5-1.6,4.9-4.7,4.8h-4.1V3.9h3.7c2.8,0,5.1,2.3,5.1,5.1v0.1V24.5L102.9,24.5z M11.3,48.1h4.3v29.1c0.1,2.5-1.9,4.6-4.4,4.7h-0.1c-2.5,0-4.3-2.1-4.3-4.7V8.9c0-1.9,0.4-6.3,3.6-6.2c2.6,0,4.5,2.4,5.3,6.1c0.9,3.8,1.5,12.7,1.5,16.6c0,1.3,0.5,1.8,1.9,1.8h3.1V1.5h-2.5v1.2c0,1.3-0.8,1.2-1.5,0.8c-2.4-1.6-5.1-2.7-7.7-2.7C6.2,0.8,0,2.7,0,10.9v60.3c0.1,10.5,5.6,12.4,11.1,12.5c4.2,0,6.2-2.2,7.8-3c1.1-0.5,1.9,0.8,1.9,2.4H23v-35h3.6v-2.6H11.3V48.1z M47.2,45.5l3.9-32.6l4.4,32.7L47.2,45.5L47.2,45.5z M66.4,76.9L66.4,76.9L57,1.5l0,0l0,0h-7.4l-9.4,75.6c-0.5,2.4-0.6,3-4.2,3.2v0.5v1.8h11.6v-2.1c-1.3,0.1-2.6-0.3-3.6-1.1c-0.4-0.5-0.6-1-0.6-1.6L46.9,48h9l3.7,29.8c0,1.9-0.2,2.6-2.5,2.7H56v2.1h14.8v-2.1h-0.4C67.5,80.5,66.6,78.7,66.4,76.9'%3E%3C/path%3E%3C/svg%3E" // NOSONAR

const squareGapLogoImgPaths = {
  logoImgPath: baseSquareLogo,
  darkLogoImgPath: baseSquareLogo,
  lightLogoImgPath:
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' class='logo_gap logo_gap_text-only' viewBox='0 0 110 83.8'%3E%3Cpath class='logo_gap--text' d='M97.8,1.5H83.1v2.4H87v74.4c-0.1,1.6-0.7,2.2-3.1,2.3h-0.8v2.1h16.6v-2.1H98c-2.8-0.1-3.8-1.2-3.9-3.7V48.1h4.8c7,0,11.1-3.4,11.1-7.9V10.1C110,5.6,106.5,1.5,97.8,1.5 M102.9,24.5v16c0,2.5-1.6,4.9-4.7,4.8h-4.1V3.9h3.7c2.8,0,5.1,2.3,5.1,5.1v0.1V24.5L102.9,24.5z M11.3,48.1h4.3v29.1c0.1,2.5-1.9,4.6-4.4,4.7h-0.1c-2.5,0-4.3-2.1-4.3-4.7V8.9c0-1.9,0.4-6.3,3.6-6.2c2.6,0,4.5,2.4,5.3,6.1c0.9,3.8,1.5,12.7,1.5,16.6c0,1.3,0.5,1.8,1.9,1.8h3.1V1.5h-2.5v1.2c0,1.3-0.8,1.2-1.5,0.8c-2.4-1.6-5.1-2.7-7.7-2.7C6.2,0.8,0,2.7,0,10.9v60.3c0.1,10.5,5.6,12.4,11.1,12.5c4.2,0,6.2-2.2,7.8-3c1.1-0.5,1.9,0.8,1.9,2.4H23v-35h3.6v-2.6H11.3V48.1z M47.2,45.5l3.9-32.6l4.4,32.7L47.2,45.5L47.2,45.5z M66.4,76.9L66.4,76.9L57,1.5l0,0l0,0h-7.4l-9.4,75.6c-0.5,2.4-0.6,3-4.2,3.2v0.5v1.8h11.6v-2.1c-1.3,0.1-2.6-0.3-3.6-1.1c-0.4-0.5-0.6-1-0.6-1.6L46.9,48h9l3.7,29.8c0,1.9-0.2,2.6-2.5,2.7H56v2.1h14.8v-2.1h-0.4C67.5,80.5,66.6,78.7,66.4,76.9'%3E%3C/path%3E%3C/svg%3E", // NOSONAR
}

export {rectangularAthletaLogoImgPaths, squareGapLogoImgPaths}
