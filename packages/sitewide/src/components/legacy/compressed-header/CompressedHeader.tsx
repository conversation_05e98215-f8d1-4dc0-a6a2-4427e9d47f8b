// @ts-nocheck
'use client';
import { useContext, lazy } from 'react';
import { CSSObject } from '@emotion/react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { mapDataToProps } from '@mui/components/legacy/helper';

const DesktopCompressedHeader = lazy(() => import('./components/DesktopCompressedHeader').then(mod => ({ default: mod.DesktopCompressedHeader })));

const MobileCompressedHeader = lazy(() => import('./components/MobileCompressedHeader').then(mod => ({ default: mod.MobileCompressedHeader })));

export const CompressedHeader = ({
  children,
  serverBrandLogo,
  serverShoppingBagLink,
}: {
  children?: JSX.Element;
  serverBrandLogo: JSX.Element;
  serverShoppingBagLink: JSX.Element;
}): JSX.Element | null => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const isDesktop = greaterOrEqualTo(XLARGE);

  const containerStyle: CSSObject = {
    position: 'relative',
    ...(isDesktop && {
      display: 'flex',
      justifyContent: 'center',
    }),
  };

  const fullWidth: CSSObject = { width: '100%' };

  return (
    <div css={containerStyle}>
      <div css={fullWidth}>
        {isDesktop ? (
          <DesktopCompressedHeader serverBrandLogo={serverBrandLogo}>{children}</DesktopCompressedHeader>
        ) : (
          <MobileCompressedHeader serverShoppingBagLink={serverShoppingBagLink} serverBrandLogo={serverBrandLogo} />
        )}
      </div>
    </div>
  );
};

export default mapDataToProps(CompressedHeader);
