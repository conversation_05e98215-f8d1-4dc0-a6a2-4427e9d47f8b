// @ts-nocheck
"use client";
import {Brand, getBrandInformation, Market} from "@mfe/brand-info"
import datalayer from "@mfe/data-layer"

type BaseDatalayerData = {
  brand_code: string
  brand_name: string
  brand_number: string
  business_unit_abbr_name: string
  business_unit_description: string
  business_unit_id: string
  channel?: string
  page_name?: string
  page_type?: string
  recognition_status?: string
}

export const beaconPresent = {
  data: {
    test_coe: "beacon-present",
  },
}

const buildBaseDatalayerData = async (
  brand: Brand,
  market: Market
): Promise<BaseDatalayerData> => {
  const {
    displayName,
    marketAgnosticBrandCode,
    marketAwareBusinessAbbrName,
    marketAwareBrandCode,
  } = getBrandInformation(brand, market)

  const {
    channel,
    page_name,
    page_type,
    recognition_status,
  } = await datalayer.build()

  return {
    brand_code: brand.toUpperCase(),
    brand_name: displayName,
    brand_number: marketAgnosticBrandCode,
    business_unit_abbr_name: marketAwareBusinessAbbrName,
    business_unit_description: displayName,
    business_unit_id: marketAwareBrandCode,
    channel,
    page_name,
    page_type,
    recognition_status,
  }
}

export const sendBeaconClickedEvent = async (brand: Brand, market: Market) => {
  const baseDatalayerData = await buildBaseDatalayerData(brand, market)

  datalayer.link({...baseDatalayerData, test_coe: "beacon-click"})
}

export const sendBeaconSeenEvent = () => {
  datalayer.viewWithQueue?.([beaconPresent])
}
