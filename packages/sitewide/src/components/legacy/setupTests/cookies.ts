// @ts-nocheck
import Cookies from "js-cookie"


// had to mock out js-cookie and use this method to determine domain because
// js-cookie won't allow us to get the domain that a cookie is set with
// after it is set. additionally, for some reason document.cookies is not
// returning the domain associated as well

/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
export const expectCookieSetWithDomain = (
  name: string,
  value: unknown,
  domain: string,
  isSecure: boolean
): void => {
  const MockedCookies = Cookies

  expect(MockedCookies.set).toBeCalledWith(name, value, {
    secure: isSecure,
    domain,
  })
}

export const expectCookieRemoved = (
  name: string,
  domain: string,
  isSecure: boolean
): void => {
  const MockedCookies = Cookies

  expect(MockedCookies.remove).toBeCalledWith(name, {secure: isSecure, domain})
}
