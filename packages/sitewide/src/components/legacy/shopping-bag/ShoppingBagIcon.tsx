// @ts-nocheck
"use client";
import {useTheme} from "@ecom-next/core/react-stitch"
import styled from "@emotion/styled"
import {FONT_COLOR_UPDATE_RGB} from "@sitewide/components/legacy/sitewide-constants"
import {FeatureFlagsContext} from "@ecom-next/core/legacy/feature-flags"
import {useAppState} from "@ecom-next/sitewide/app-state-provider"  
import Beacon from '@ecom-next/sitewide/beacon';
import {useContext} from "react"
import { checkBrandCondition } from '../../../utils/brandUtil';
import useHeaderRedesign2024 from "../sister-brands-bar-redesign-2024/useHeaderRedesign2024"
import {
  useFeature
} from "../experiments"
import { useShoppingBagBeacon } from './hooks/useShoppingBagBeacon';

type DefaultShoppingBagIconProps = {
  fill: string
  stroke: string
  totalItemCount: number
}

type RedesignedShoppingBagIconProps = DefaultShoppingBagIconProps & {
  totalItemCount: number
}

export const StyledShoppingBagIconDesktop = styled.svg()

export const RedesignedShoppingBagIconDesktop = ({
  fill,
  stroke,
  totalItemCount,
}: RedesignedShoppingBagIconProps): JSX.Element => {
  const bagFillRule = totalItemCount < 1 ? "evenodd" : "inherit"  
  const { shouldRenderBeacon, animationDuration, animationIterationCount } =
  useShoppingBagBeacon(totalItemCount);

    const brandMarketSpecificBeaconStyles = {
      animationIterationCount: animationIterationCount,
      animationDuration: animationDuration
    }
  return (
    <>
    {shouldRenderBeacon && <Beacon device='desktop' style={brandMarketSpecificBeaconStyles} />}
    <StyledShoppingBagIconDesktop
      data-testid="redesigned-shopping-bag-icon-desktop"
      fill={fill}
      height="27"
      stroke={stroke}
      viewBox="0 0 20 27"
      width="20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Bag - OFF">
        <g id="Bag Icon">
          <g id="Bag">
            <path
              clipRule="evenodd"
              d="M15.5 18.521V33.5455H30.5V18.521H15.5ZM15 17.0664C14.4477 17.0664 14 17.5006 14 18.0361V34.0304C14 34.5659 14.4477 35.0001 15 35.0001H31C31.5523 35.0001 32 34.5659 32 34.0304V18.0361C32 17.5006 31.5523 17.0664 31 17.0664H15Z"
              data-testid="redesigned-shopping-bag-icon-desktop-bag-path"
              fillRule={bagFillRule}
              id="Rectangle 3 (Stroke)"
              strokeWidth={0}
              transform="translate(-13,-10)"
            />
            <path
              clipRule="evenodd"
              d="M17.4473 16.2107C17.4473 13.3071 19.9602 11 22.9997 11C26.0391 11 28.5521 13.3071 28.5521 16.2107V20.6941C28.5521 21.0958 28.2163 21.4214 27.8021 21.4214C27.3879 21.4214 27.0521 21.0958 27.0521 20.6941V16.2107C27.0521 14.162 25.2648 12.4545 22.9997 12.4545C20.7345 12.4545 18.9473 14.162 18.9473 16.2107V20.6941C18.9473 21.0958 18.6115 21.4214 18.1973 21.4214C17.7831 21.4214 17.4473 21.0958 17.4473 20.6941V16.2107Z"
              data-testid="redesigned-shopping-bag-icon-desktop-handle-path"
              fillRule="evenodd"
              id="Path (Stroke)"
              strokeWidth={0}
              transform="translate(-13,-10)"
            />
          </g>
        </g>
      </g>
    </StyledShoppingBagIconDesktop>
   </>
  )
}

export const DefaultShoppingBagIconDesktop = ({
  fill,
  stroke,
  totalItemCount
}: DefaultShoppingBagIconProps): JSX.Element => {
  const { shouldRenderBeacon, animationDuration, animationIterationCount } =
  useShoppingBagBeacon(totalItemCount);

    const brandMarketSpecificBeaconStyles = {
      animationIterationCount: animationIterationCount,
      animationDuration: animationDuration
    }
    return (
    <>
    {shouldRenderBeacon && <Beacon device='desktop' style={brandMarketSpecificBeaconStyles} />}
    <StyledShoppingBagIconDesktop
      aria-hidden="true"
      data-testid="default-shopping-bag-icon-desktop"
      fill={fill}
      height="27"
      stroke={stroke}
      viewBox="0 0 20 27"
      width="20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 6.4v18.1S0 27 2.5 27h15s2.5 0 2.5-2.5V6.4H0z"
        data-testid="default-shopping-bag-icon-desktop-bag-path"
        strokeWidth="0"
      />
      <path
        d="M5.5 7.1S4.8 1 10 1c5 0 4.5 6.1 4.5 6.1"
        data-testid="default-shopping-bag-icon-desktop-handle-path"
        fill="none"
        strokeMiterlimit="10"
        strokeWidth="2"
      />
    </StyledShoppingBagIconDesktop>
    </>
  )
}

export const ShoppingBagIconDesktop = ({
  fill,
  totalItemCount,
  stroke,
}: RedesignedShoppingBagIconProps): JSX.Element => {
  const {shouldUseHeaderRedesign2024} = useHeaderRedesign2024()

  return (
    <>
      {shouldUseHeaderRedesign2024 ? (
        <RedesignedShoppingBagIconDesktop
          fill={fill}
          stroke={stroke}
          totalItemCount={totalItemCount}
        />
      ) : (
        <DefaultShoppingBagIconDesktop fill={fill} stroke={stroke} totalItemCount={totalItemCount}/>
      )}
    </>
  )
}

export const StyledShoppingBagIconMobileRect = styled.rect()
export const StyledShoppingBagIconMobilePath = styled.path()

type BagIconMobileProps = {
  iconStyles: {
    fill: string
    stroke: string
  }
}

export const RedesignedShoppingBagIconMobile = ({
  iconStyles,
}: BagIconMobileProps): JSX.Element => {
  return (
    <svg
      data-testid="redesigned-shopping-bag-icon-mobile"
      fill="none"
      height="25"
      viewBox="0 0 18 25"
      width="18"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Bag Icon">
        <g id="Bag">
          <g id="Rectangle 3">
            <mask fill="white" id="path-1-inside-1_1362_455">
              <rect height="18.4941" rx="1" width="18" y="6.50589" />
            </mask>
            <StyledShoppingBagIconMobileRect
              height="18.4941"
              mask="url(#path-1-inside-1_1362_455)"
              rx="1"
              stroke={iconStyles.stroke}
              strokeWidth="3"
              width="18"
              y="6.50589"
            />
          </g>
          <StyledShoppingBagIconMobilePath
            d="M13.8016 9.24705V5.62352C13.8016 3.07002 11.6515 1 8.99918 1C6.34688 1 4.19678 3.07002 4.19678 5.62352V9.09116V9.24705"
            id="Path"
            stroke={iconStyles.stroke}
            strokeLinecap="round"
            strokeWidth="1.5"
          />
        </g>
      </g>
    </svg>
  )
}

export const DefaultShoppingBagIconMobile = ({
  iconStyles,
}: BagIconMobileProps): JSX.Element => {
  return (
    <svg
      aria-hidden="true"
      data-testid="shopping-bag-icon-mobile"
      height="23px"
      version="1.1"
      viewBox="0 0 17 23"
      width="17px"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g fill="none" fillRule="evenodd" stroke="none" strokeWidth="1">
        <g
          fillRule="nonzero"
          strokeWidth="2"
          transform="translate(-344.000000, -163.000000)"
        >
          <g transform="translate(0.000000, 151.000000)">
            <g transform="translate(344.000000, 7.000000)">
              <g id="Bag" transform="translate(0.000000, 6.507246)">
                <StyledShoppingBagIconMobileRect
                  fill={iconStyles.fill}
                  height="14"
                  rx="1"
                  stroke={iconStyles.stroke}
                  width="14.3043478"
                  x="1"
                  y="5.76735317"
                />
                <StyledShoppingBagIconMobilePath
                  d="M12.5,8.99275362 L12.5,4.99275362 C12.5,2.78361462 10.5524387,0.992753623 8.15,0.992753623 C5.74756134,0.992753623 3.8,2.78361462 3.8,4.99275362 L3.8,7.99275362 L3.8,8.99275362" // NOSONAR
                  stroke={iconStyles.stroke}
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export const ShoppingBagIconMobile = (): JSX.Element => {
  const {color} = useTheme()
  const isGapColorUpdateEnabled = true

  let stroke = color.b1

  if (isGapColorUpdateEnabled) {
    stroke = FONT_COLOR_UPDATE_RGB
  }

  const commonStyles = {
    fill: "transparent",
  }

  const iconStyles = {
    ...commonStyles,
    stroke,
  }

  const redesignIconStyles = {
    ...commonStyles,
    stroke: color.bk,
  }

  const {shouldUseHeaderRedesign2024} = useHeaderRedesign2024()

  return (
    <>
      {shouldUseHeaderRedesign2024 ? (
        <RedesignedShoppingBagIconMobile iconStyles={redesignIconStyles} />
      ) : (
        <DefaultShoppingBagIconMobile iconStyles={iconStyles} />
      )}
    </>
  )
}
