// @ts-nocheck
import {Brands} from "@ecom-next/core/legacy/utility"
import { render, RenderResult, screen, waitFor, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import MinibagDrawer from "../MinibagDrawer"
import {StickyProviderFragment} from "../../sticky-manager-new/StickyProviderFragment"
import {MinibagContextProvider} from "../MinibagDrawer/context"

const mockImportMinibagModule = jest.fn(() =>
  Promise.resolve(import("../__fixtures__/DummyComponent"))
)
window.importMinibagModule = mockImportMinibagModule

const blueBg = "background-color: blue;"

jest.mock("@sitewide/components/legacy/sitewide-constants", () => {
  const orig = jest.requireActual('@sitewide/components/legacy/sitewide-constants');
  
  return {
  _esModule: true,
  ...orig,
  useFeature: () => true,
  // needed because 2024 header color is the same as the standard background color
  BR_2024_HEADER_COLOR: "blue",
  }
})

const renderForBr2024 = (): RenderResult => {
  return render(
    <MinibagContextProvider
      initialProps={{
        isMinibagOpen: true,
      }}
    >
      <StickyProviderFragment>
        <MinibagDrawer />
      </StickyProviderFragment>
    </MinibagContextProvider>,
    {
      appState: {brandName: Brands.BananaRepublic},
    }
  )
}

describe("br 2024 colors", () => {
  /* placeholder test MUST BE FIRST as browser/JSDom will cache the result of a dynamic import */
  it("should render br 2024 colors for loading placeholder", async () => {
    renderForBr2024()

    const placeholder = await waitFor(() =>
      screen.getByTestId("loading-minibag-placeholder")
    )

    const selectors = [
      "placeholder-footer-wrapper",
      "minibag-contents-container",
      "product-card-container",
      "minibag-order-summary-container",
      "product-tiles-wrapper",
    ]

    selectors.forEach((selector) => {
      const element = placeholder.querySelector(`div[data-testid=${selector}]`)
      expect(element).toHaveStyle(blueBg)
    })
  })
  it("should render br 2024 colors for minibag drawer title", async () => {
    renderForBr2024()
    expect(
      await waitFor(() => screen.findByTestId("minibag-drawer-title"))
    ).toHaveStyle(blueBg)
  })

  it("should render br 2024 colors for minibag bottom button elements", async () => {
    renderForBr2024()

    const minibagFooter = await waitFor(() =>
      screen.findByTestId("minibag-footer")
    )

    const buttonSelectors = [
      {selector: "minibag-view-bag-button", style: blueBg},
      // disabled status takes precedence over brand styles, but am leaving here in case this changes in the future
      {
        selector: "minibag-checkout-button",
        style: "background-color: rgb(204, 204, 204);",
      },
    ]

    expect(minibagFooter).toHaveStyle(blueBg)
    buttonSelectors.forEach(({selector, style}) => {
      expect(
        minibagFooter.querySelector(`button[data-testid=${selector}]`)
      ).toHaveStyle(style)
    })
  })
})
