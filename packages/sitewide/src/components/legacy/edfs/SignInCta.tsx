// @ts-nocheck
"use client";
import {useContext, useEffect, useState} from "react"
import {CSSObject, Brands, useTheme} from "@ecom-next/core/react-stitch"
import {Size} from "@ecom-next/core/legacy/fixed-button"
import {BreakpointContext, LARGE} from "@ecom-next/core/breakpoint-provider"
import {useAppState} from "@ecom-next/sitewide/app-state-provider"
import {ComposableButton as FixedOrComposableButton} from "@mui/components/legacy/components/ComposableButton"
import {
  COLORS,
  MIN_FONTSIZE_MOBILE,
  PADDING_LEFT_10PX,
  PADDING_LEFT_11PX,
  PADDING_LEFT_16PX,
} from "./constants"
import {features} from "@sitewide/components/legacy/sitewide-constants"
import {useFeature} from "@sitewide/components/legacy/sitewide-constants"

const {black, white, alabaster, navyBlue} = COLORS

export const headerRedesign2024Styles = {
  [Brands.Gap]: {
    signInCtaButton: {
      color: white,
      backgroundColor: black,
      display: "inline",
      fontSize: MIN_FONTSIZE_MOBILE,
      fontWeight: 500,
      letterSpacing: "0.22px",
      minHeight: "0",
      padding: PADDING_LEFT_11PX,
      position: "relative",
      top: "-1px",
    },
  },
  [Brands.GapFactoryStore]: {
    signInCtaButton: {
      color: white,
      backgroundColor: black,
      display: "inline",
      fontSize: MIN_FONTSIZE_MOBILE,
      fontWeight: 500,
      letterSpacing: "0.22px",
      minHeight: "0",
      padding: PADDING_LEFT_11PX,
      position: "relative",
      top: "-1px",
    },
  },
  [Brands.OldNavy]: {
    signInCtaButton: {
      color: navyBlue,
      display: "inline",
      fontSize: MIN_FONTSIZE_MOBILE,
      fontWeight: 500,
      letterSpacing: "0.24px",
      lineHeight: 1,
      minHeight: "0",
      position: "relative",
      top: "-1px",
      padding: PADDING_LEFT_16PX,
    },
  },
  [Brands.BananaRepublic]: {
    signInCtaButton: {
      color: black,
      backgroundColor: alabaster,
      display: "inline",
      fontSize: MIN_FONTSIZE_MOBILE,
      fontWeight: "400",
      minHeight: "0",
      padding: PADDING_LEFT_10PX,
      position: "relative",
      top: "-1px",
    },
  },
  [Brands.BananaRepublicFactoryStore]: {
    signInCtaButton: {
      color: black,
      backgroundColor: alabaster,
      display: "inline",
      fontSize: MIN_FONTSIZE_MOBILE,
      fontWeight: "400",
      minHeight: "0",
      padding: PADDING_LEFT_10PX,
      position: "relative",
      top: "-1px",
    },
  },
  [Brands.Athleta]: {
    signInCtaButton: {
      color: black,
      backgroundColor: white,
      display: "inline",
      fontSize: "12px",
      fontWeight: 400,
      lineHeight: 1.1,
      minHeight: "0",
      letterSpacing: "0.16px",
      padding: PADDING_LEFT_10PX,
      position: "relative",
      top: "-1px",
    },
  },
}

export const headerRedesignDesktop2024Styles = {
  ...headerRedesign2024Styles,
  ...{
    [Brands.Gap]: {
      signInCtaButton: {
        fontWeight: 500,
        marginBottom: 0,
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
    [Brands.GapFactoryStore]: {
      signInCtaButton: {
        fontWeight: 500,
        marginBottom: 0,
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
    [Brands.OldNavy]: {
      signInCtaButton: {
        color: white,
        fontWeight: 400,
        letterSpacing: "0.22px",
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
    [Brands.BananaRepublic]: {
      signInCtaButton: {
        color: black,
        fontWeight: 350,
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
    [Brands.BananaRepublicFactoryStore]: {
      signInCtaButton: {
        color: black,
        fontWeight: 350,
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
    [Brands.Athleta]: {
      signInCtaButton: {
        fontWeight: 400,
        letterSpacing: "0.24px",
        lineHeight: 1.2,
        marginBottom: 0,
        minHeight: 0,
        padding: PADDING_LEFT_16PX,
      },
    },
  },
}

const baseStyles: CSSObject = {
  backgroundColor: "transparent",
  border: 0,
  padding: "0.5em",
  textDecoration: "underline",
  textTransform: "none",
}

export interface SignInCtaProps {
  text?: string
  createAccount?: boolean
  path?: string
  targetURL?: string
  style?: CSSObject
}

const getTargetUrlValue = (customTargetUrl: string | undefined): string => {
  return customTargetUrl ?? window?.location?.href
}

const escapeSpecialCharacters = (targetUrl: string): string => {
  return targetUrl.replace(/\?/g, "\\?").replace(/&/g, "\\&");
}

export const SignInCta = ({
  text,
  createAccount = false,
  path = "/my-account/sign-in",
  targetURL,
  style,
}: SignInCtaProps): JSX.Element => {
  const [href, setHref] = useState(path)
  const {minWidth} = useContext(BreakpointContext)
  const isDesktop = minWidth(LARGE)

  const {brandName} = useAppState()
  const {brandFont} = useTheme()

  const shouldUseHeaderRedesign2024ForEdfs = useFeature(
    features.SWF_2024_HEADER_REDESIGN_EDFS
  )

  const headerRedesign2024BrandStyles =
    headerRedesign2024Styles[brandName] || {}

  const headerRedesignDesktop2024BrandStyles =
    headerRedesignDesktop2024Styles[brandName] || {}

  const headerRedesign2024MobileStyles = (shouldUseHeaderRedesign2024ForEdfs && {
    ...brandFont,
    ...headerRedesign2024BrandStyles.signInCtaButton,
  }) as CSSObject

  const headerRedesign2024DesktopStyles = (shouldUseHeaderRedesign2024ForEdfs && {
    ...brandFont,
    ...headerRedesignDesktop2024BrandStyles.signInCtaButton,
  }) as CSSObject

  const defaultMobileStyles: CSSObject = {
    ...{
      ...baseStyles,
      color: "#999",
      cursor: "pointer",
      fontSize: "12px",
      textTransform: "none",
    },
    ...((shouldUseHeaderRedesign2024ForEdfs &&
      headerRedesign2024MobileStyles) as CSSObject),
  }

  const defaultDesktopStyles: CSSObject = {
    ...{
      ...baseStyles,
      color: "#FFFFFF",
      font: "400 11px",
      fontSize: "1em",
      marginBottom: "2px",
    },
    ...((shouldUseHeaderRedesign2024ForEdfs &&
      headerRedesign2024DesktopStyles) as CSSObject),
  }
  useEffect(() => {
    const targetUrlValue = getTargetUrlValue(targetURL)
    const actualTargetUrl = targetUrlValue
      ? `targetURL=${escapeSpecialCharacters(targetUrlValue)}`
      : ""
    const actualCreateAccount = createAccount ? "&createAccount=true" : ""

    setHref(`${path}?${actualTargetUrl}${actualCreateAccount}`)
  }, [path, targetURL, createAccount])

  const defaultStyles: CSSObject = isDesktop
    ? defaultDesktopStyles
    : defaultMobileStyles

  return (
    <FixedOrComposableButton
      as="a"
      css={{...defaultStyles, ...style}}
      href={href}
      size={Size.small}
    >
      {text}
    </FixedOrComposableButton>
  )
}
