// @ts-nocheck
import { RenderOptions, RenderResult, act } from "test-utils";

import {merge} from "lodash"
import {Brand} from "types"
import {render, screen, fireEvent} from "@sitewide/components/legacy/setupTests/test-helpers"
import patchLoyaltyCreditCard from "../../acquisition/loyaltyCreditCardEnrollmentService"
import EdfsSmall, {headerRedesign2024Styles} from "../EdfsSmall"
import useHeaderRedesign2024 from "../../sister-brands-bar-redesign-2024/useHeaderRedesign2024"
import {features} from "@sitewide/components/legacy/sitewide-constants"

jest.mock("../../sister-brands-bar-redesign-2024/useHeaderRedesign2024")

const generateUseHeaderRedesign2024 = (
  shouldUseHeaderRedesign2024 = false,
  variablesValues = {}
) => {
  return {
    shouldUseHeaderRedesign2024,
    headerRedesign2024FeatureVariables: {
      useNewAccountHeaderIcon: false,
      useNewMembershipStatus: false,
      useNewRewardsLanguage: false,
      useNewEdfsSignIn: false,
      ...variablesValues,
    },
  }
}

const modalProps = {
  modalCloseButtonAriaLabel: "Close",
  modalTitle: "Free shipping!",
  modalUrl: "gap.com",
}

const textContentProps = {
  text: "on all orders",
  textStrong: "Free Shipping",
  detailsLink: "details",
}
const getApplyNowButton = (): HTMLElement =>
  screen.getByRole("button", {name: /apply now/i})

const getGreetingMessage = (): HTMLElement =>
  screen.queryByTestId("user-greeting-text") as HTMLElement

const DEFAULT_FIRST_NAME = "Macgayver"

const defaultEdfsData = {
  ...textContentProps,
  signInCta: {
    text: "Click to Sign In!",
  },
  acquisition: {
    applyButton: {
      alt: "apply now",
      desktopStyle: {},
      text: "Apply Now",
      style: {},
    },
    sitecode: "1234",
    dynamicCustomerName: {
      greeting: {
        desktopStyle: {},
        style: {},
        text: "Hi, ",
      },
    },
  },
}
jest.mock("@ecom-next/core/legacy/hooks", () => {
  return {
    __esModule: true,
    useLoyaltyCreditCardEnrollmentService: jest.fn(() => ({
      viewPrescreenOffer: jest.fn(),
    })),
  }
})

jest.mock("../../acquisition/loyaltyCreditCardEnrollmentService", () => ({
  __esModule: true,
  default: jest.fn(() => null),
}))
const mockedPatchCreditCard = patchLoyaltyCreditCard

const getText = (): HTMLElement => screen.getByText(textContentProps.text)
const getStrongText = (): HTMLElement =>
  screen.getByText(textContentProps.textStrong)
const getDetailsLink = (): HTMLElement => screen.getByRole("button")
const getModal = (): HTMLElement => screen.getByRole("dialog")
const getSignInCta = (): HTMLElement =>
  screen.getByRole("link", {name: /click to sign in!/i})
const getEdfsMessage = (): HTMLElement => screen.getByTestId("edfs-message")

const renderEdfsSmall = (
  edfsSmallData = {},
  options?: RenderOptions
): RenderResult => render(<EdfsSmall data={edfsSmallData} />, options)

describe("<EdfsSmall />", () => {
  const useHeaderRedesign2024Mock = useHeaderRedesign2024
  beforeEach(() => {
    useHeaderRedesign2024Mock.mockReturnValue(
      generateUseHeaderRedesign2024(false)
    )
  })
  afterEach(() => {
    useHeaderRedesign2024Mock.mockClear()
  })

  it("renders text content correctly", () => {
    renderEdfsSmall({...textContentProps, ...modalProps})

    expect(getText()).toBeInTheDocument()
    expect(getStrongText()).toBeInTheDocument()
    expect(getDetailsLink()).toBeInTheDocument()
  })

  it("renders edfs details text content correctly when edfspassed down as an array", () => {
    const textArray = [{text: "FREE-"}, {text: "SHIPPING-"}, {text: "EVERYDAY"}]
    renderEdfsSmall({text: textArray})

    textArray.forEach(({text}) => {
      expect(screen.getByText(text)).toBeInTheDocument()
    })
  })

  it("renders edfs details text content inline custom styles correctly", () => {
    const textArray = [
      {text: "FREE-"},
      {text: "SHIPPING-"},
      {text: "EVERYDAY", inlineStyle: {fontWeight: "bold"}},
    ]
    renderEdfsSmall({text: textArray})

    const boldText = textArray.pop()?.text as string
    expect(screen.getByText(boldText)).toHaveStyleRule("font-weight", "bold")

    textArray.forEach(({text}) => {
      expect(screen.getByText(text)).not.toHaveStyleRule("font-weight", "bold")
    })
  })

  it("does not transform the text by default", () => {
    renderEdfsSmall({...textContentProps, ...modalProps})
    const uppercaseText = "ON ALL ORDERS"
    const defaultText = textContentProps.text

    expect(screen.queryByText(uppercaseText)).not.toBeInTheDocument()
    expect(screen.queryByText(defaultText)).not.toHaveAttribute(
      "text-transform"
    )
  })

  it("renders backwards compatible text content correctly", () => {
    renderEdfsSmall({defaultData: textContentProps, ...modalProps})

    expect(getText()).toBeInTheDocument()
    expect(getStrongText()).toBeInTheDocument()
    expect(getDetailsLink()).toBeInTheDocument()
  })

  it("renders details modal correctly", async () => {
    renderEdfsSmall({...textContentProps, ...modalProps})
    await act(async () => { 

     fireEvent.click(getDetailsLink()); 

     })

    expect(getModal()).toBeInTheDocument()
  })

  it("allows style overrides", () => {
    renderEdfsSmall({
      ...textContentProps,
      styles: {
        headline: {fontSize: "13px"},
        headlineStrongText: {color: "red"},
        detailsButton: {backgroundColor: "blue"},
      },
    })

    expect(getText()).toHaveStyleRule("font-size", "13px")
    expect(getStrongText()).toHaveStyleRule("color", "red")
    expect(getDetailsLink()).toHaveStyleRule("background-color", "blue")
  })

  it("should render signInCta correctly when signInCta props are passed", () => {
    renderEdfsSmall({
      ...textContentProps,
      signInCta: {
        text: "Click to Sign In!",
      },
    })

    expect(getSignInCta()).toBeInTheDocument()
  })

  it("should not render signInCta when signInCta props are passed but user is authenticated", () => {
    renderEdfsSmall(
      {
        ...textContentProps,
        signInCta: {
          text: "Click to Sign In!",
        },
      },
      {
        personalizationData: {
          isLoggedInUser: true,
        },
      }
    )

    expect(
      screen.queryByRole("link", {name: /click to sign in!/i})
    ).not.toBeInTheDocument()
  })
  describe("Acquisition", () => {
    it("should trigger read call to loyalty ONCE per session", () => {
      renderEdfsSmall(defaultEdfsData, {
        personalizationData: {
          isLoggedInUser: true,
        },
      })

      expect(mockedPatchCreditCard).toHaveBeenCalledTimes(1)
      mockedPatchCreditCard.mockClear()
      window.location.reload()
      expect(mockedPatchCreditCard).toHaveBeenCalledTimes(0)
    })
    it("should render the apply button when acquisition is present", () => {
      renderEdfsSmall(defaultEdfsData, {
        personalizationData: {
          isLoggedInUser: true,
        },
      })
      const applyButton = getApplyNowButton()
      expect(applyButton).toBeInTheDocument()
    })
    it("should render the greeting message when acquisition is present and the user recognized", () => {
      const enabledGreeting = {
        acquisition: {
          dynamicCustomerName: {
            showGreeting: true,
          },
        },
      }
      const props = merge(defaultEdfsData, enabledGreeting)

      renderEdfsSmall(props, {
        personalizationData: {
          isLoggedInUser: true,
          firstName: DEFAULT_FIRST_NAME,
        },
      })
      const greetingMessage = getGreetingMessage()
      expect(greetingMessage).toBeInTheDocument()
    })
    it("should not render the greeting message when acquisition is present but showGreeting is false", () => {
      const disabledGreeting = {
        acquisition: {
          dynamicCustomerName: {
            showGreeting: false,
          },
        },
      }
      const props = merge(defaultEdfsData, disabledGreeting)
      renderEdfsSmall(props, {
        personalizationData: {
          isLoggedInUser: true,
          firstName: DEFAULT_FIRST_NAME,
        },
      })
      const greetingMessage = getGreetingMessage()
      expect(greetingMessage).not.toBeInTheDocument()
    })
  })
  describe("Header Redesign", () => {
    describe.each`
      brandName
      ${Brand.Gap}
      ${Brand.GapFactoryStore}
      ${Brand.OldNavy}
      ${Brand.BananaRepublic}
      ${Brand.BananaRepublicFactoryStore}
      ${Brand.Athleta}
    `("when brand is $brandName", ({brandName}: {brandName: Brand}) => {
      const brandStyles = headerRedesign2024Styles[brandName]
      describe("and header redesign is active", () => {
        beforeEach(() => {
          useHeaderRedesign2024Mock.mockReturnValue(
            generateUseHeaderRedesign2024(true)
          )
        })
        it("should not have border by default on edfs message", () => {
          renderEdfsSmall(
            {...textContentProps},
            {
              appState: {brandName},
              enabledFeatures: {[features.SWF_2024_HEADER_REDESIGN_EDFS]: true},
            }
          )
          const edfsMessage = getEdfsMessage()
          expect(edfsMessage).toHaveStyle("border: 0")
        })
        it("should have edfs headline redesign style values", () => {
          renderEdfsSmall(
            {...textContentProps},
            {
              appState: {brandName},
              enabledFeatures: {[features.SWF_2024_HEADER_REDESIGN_EDFS]: true},
            }
          )
          const edfsHeadline = screen.getByTestId("edfs-headline")
          expect(edfsHeadline).toHaveStyle(brandStyles.headline)
        })
        it("should have edfs details button redesign style values", () => {
          renderEdfsSmall(
            {...textContentProps},
            {
              appState: {brandName},
              enabledFeatures: {[features.SWF_2024_HEADER_REDESIGN_EDFS]: true},
            }
          )
          const edfsDetailsButton = screen.getByRole("button")
          expect(edfsDetailsButton).toHaveStyle(brandStyles.detailsButton)
        })
        describe("and feature variable fontSizeWithSignIn is active", () => {
          beforeEach(() => {
            useHeaderRedesign2024Mock.mockReturnValue(
              generateUseHeaderRedesign2024(true, {useNewEdfsSignIn: true})
            )
            renderEdfsSmall(
              {...textContentProps},
              {
                appState: {brandName},
                enabledFeatures: {
                  [features.SWF_2024_HEADER_REDESIGN_EDFS]: true,
                },
              }
            )
          })
          it(`should edsf headline fontsize value be ${brandStyles.fontSizeWithSignIn}`, () => {
            const edfsHeadline = screen.getByTestId("edfs-headline")
            expect(edfsHeadline).toHaveStyle(
              `font-size: ${brandStyles.fontSizeWithSignIn}`
            )
          })
          it(`should edsf details button fontsize value be ${brandStyles.fontSizeWithSignIn}`, () => {
            const edfsDetailsButton = screen.getByRole("button")
            expect(edfsDetailsButton).toHaveStyle(
              `font-size: ${brandStyles.fontSizeWithSignIn}`
            )
          })
        })
        describe("and feature variable fontSizeWithSignIn is disabled", () => {
          beforeEach(() => {
            useHeaderRedesign2024Mock.mockReturnValue(
              generateUseHeaderRedesign2024(true, {useNewEdfsSignIn: false})
            )
            renderEdfsSmall(
              {...textContentProps},
              {
                appState: {brandName},
                enabledFeatures: {
                  [features.SWF_2024_HEADER_REDESIGN_EDFS]: true,
                },
              }
            )
          })
          it(`should edsf headline fontsize value be ${brandStyles.defaultFontSize}`, () => {
            const edfsHeadline = screen.getByTestId("edfs-headline")
            expect(edfsHeadline).toHaveStyle(
              `font-size: ${brandStyles.defaultFontSize}`
            )
          })
          it(`should edsf details button fontsize value be ${brandStyles.defaultFontSize}`, () => {
            const edfsDetailsButton = screen.getByRole("button")
            expect(edfsDetailsButton).toHaveStyle(
              `font-size: ${brandStyles.defaultFontSize}`
            )
          })
        })
      })
      describe("and header redesign is not active", () => {
        beforeEach(() => {
          useHeaderRedesign2024Mock.mockReturnValue(
            generateUseHeaderRedesign2024(false, {useNewEdfsSignIn: false})
          )
          renderEdfsSmall(
            {...textContentProps},
            {
              appState: {brandName},
              enabledFeatures: {
                [features.SWF_2024_HEADER_REDESIGN_EDFS]: false,
              },
            }
          )
        })
        it("should have border by default on edfs message", () => {
          const edfsMessage = getEdfsMessage()
          expect(edfsMessage).not.toHaveStyle("border: 0")
        })
        it("should not have edfs headline redesign style values", () => {
          const edfsHeadline = screen.getByTestId("edfs-headline")
          expect(edfsHeadline).not.toHaveStyle(brandStyles.headline)
        })
        it("should not have edfs details button redesign style values", () => {
          const edfsDetailsButton = screen.getByRole("button")
          expect(edfsDetailsButton).not.toHaveStyle(brandStyles.detailsButton)
        })
      })
    })
  })
})
