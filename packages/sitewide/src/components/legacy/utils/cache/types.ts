// @ts-nocheck
"use client";
import NodeCache from "node-cache"
import {Response as FetchResponse, Headers} from "node-fetch"
import {Response} from "../sitewideFetch/types"

export type CachedResponse = {
  json: Record<string, unknown> | undefined
  text: string | undefined
  ok: boolean
  status: number
  url: string
  contentType: string
  headers: Headers
}

export type ExpiredResponse = Response & {
  expiredCache: true
}

export type NodeCacheInstance = NodeCache & {
  uuid?: string
}

export type CacheInstance = {
  cache: NodeCache | NodeCacheInstance
  getCachedResponse: (key: string) => Response | undefined
  getExpiredResponse: (key: string) => ExpiredResponse | undefined
  setCachedResponse: (url: string, key: string, response: FetchResponse) => void
  logStatsReport: () => void
}
