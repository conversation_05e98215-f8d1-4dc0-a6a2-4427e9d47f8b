// @ts-nocheck
'use client';
const noReferrer = 'noopener noreferrer';
const sustainability = 'https://www.gapinc.com/en-us/values/sustainability';
const privacy = 'https://www.gapinc.com/en-us/consumer-privacy-policy';

export const oneTrustCopyRightRows = {
  rows: [
    [
      {
        text: '© 2020 The Gap, Inc.',
      },
      {
        text: 'Privacy Policy',
        to: privacy,
      },
      {
        text: 'Do Not Sell or Share My Personal Info',
        doNotSell: true,
      },
      {
        text: 'Your California Privacy Rights',
        to: 'https://corporate.gapinc.com/en-us/consumer-privacy-policy?#california',
      },
      {
        text: 'California Transparency in Supply Chains Act',
        to: 'https://www.gapinc.com/en-us/policy/uk-modern-slavery-act',
      },
      {
        text: 'Terms of Use',
        to: '/customerService/info.do?cid=1037507',
      },
      {
        text: 'Careers',
        to: 'https://www.gapinc.com/en-us/careers/gap-careers',
        target: '_blank',
        rel: noReferrer,
      },
      {
        text: 'Sustainability',
        to: sustainability,
        target: '_blank',
        rel: noReferrer,
      },
      {
        text: 'About Gap Inc.',
        to: 'https://www.gapinc.com/en-us/about',
        target: '_blank',
        rel: noReferrer,
      },
    ],
    [
      {
        text: 'Americans with Disabilities Act',
        to: '/customerService/info.do?cid=1041729',
      },
      {
        text: 'Gap Inc. Policies',
        to: sustainability,
        target: '_blank',
        rel: noReferrer,
      },
    ],
  ],
};
