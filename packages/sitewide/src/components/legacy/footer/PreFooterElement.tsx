// @ts-nocheck
"use client";
import {BreakpointContext, LARGE} from "@ecom-next/core/breakpoint-provider"
import {CSSObject, styled} from "@ecom-next/core/react-stitch"
import {useContext} from "react"
import * as React from "react"

interface PreFooterElementProps {
  /**
   * The pre footer column contents.
   */
  children: React.ReactNode
  className?: string
  css?: CSSObject
}

interface StyledPreFooterElementProps {
  gridAttribute: "gridRow" | "gridColumn"
  styles?: CSSObject
}

const StyledPreFooterElement = styled.div<StyledPreFooterElementProps>(
  ({gridAttribute, styles}) => ({
    [gridAttribute]: 1,
    ...styles,
  })
)

export const PreFooterElement = ({
  className,
  children,
  css,
}: PreFooterElementProps): JSX.Element => {
  const {minWidth} = useContext(BreakpointContext)
  const gridAttribute = minWidth(LARGE) ? "gridRow" : "gridColumn"

  return (
    <StyledPreFooterElement
      className={className}
      gridAttribute={gridAttribute}
      styles={css}
    >
      {children}
    </StyledPreFooterElement>
  )
}
