// @ts-nocheck
import { render, screen, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import {DesktopCustomerSupport} from "../../CustomerSupport/DesktopCustomerSupport"
import {defaultCustomerSupportData} from "../../gap/us/dataDefaults"
import {GapFakeBrandSiteData} from "@sitewide/components/legacy/fixtures/BrandSiteData"
import {CustomerSupportColumn} from "../../CustomerSupport/types"

const getHeader = (name: RegExp): HTMLElement =>
  screen.getByRole("heading", {name})
describe("<DesktopCustomerSupport/>", () => {
  describe("renders desktop customer support element", () => {
    const {
      desktop: {columns},
    } = defaultCustomerSupportData()
    beforeEach(() => {
      render(
        <DesktopCustomerSupport columns={columns} shouldUseDarkTheme={false} />,
        {
          appState: {
            brandSiteData: GapFakeBrandSiteData,
          },
        }
      )
    })

    it("renders headers", () => {
      expect(getHeader(/customer support/i)).toBeInTheDocument()
      expect(getHeader(/about us/i)).toBeInTheDocument()
      expect(getHeader(/ways to shop/i)).toBeInTheDocument()
    })
  })

  describe("renders component with changes from br 2024 color change", () => {
    const renderForBr2024ColorTests = ({
      shouldUseBR2024BackgroundColor,
    }: {
      shouldUseBR2024BackgroundColor: boolean
    }) =>
      render(
        <DesktopCustomerSupport
          columns={[] as CustomerSupportColumn[]}
          shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
          shouldUseDarkTheme={false}
        />
      )

    // jest wants to convert these hex colors to rgb and i don't feel like arguing
    it.each([
      {
        shouldUseBR2024BackgroundColor: true,
        backgroundColorConvertedToRgb: "rgb(246, 244, 236);",
      },
      {
        shouldUseBR2024BackgroundColor: false,
        backgroundColorConvertedToRgb: "rgb(246, 244, 235);",
      },
    ])(
      "with background color set to $backgroundColorConvertedToRgb when shouldUseBR2024BackgroundColor is set to $shouldUseBR2024BackgroundColor",
      ({shouldUseBR2024BackgroundColor, backgroundColorConvertedToRgb}) => {
        renderForBr2024ColorTests({shouldUseBR2024BackgroundColor})
        expect(screen.getByTestId("footer-textlink-container")).toHaveStyle({
          backgroundColor: backgroundColorConvertedToRgb,
        })
      }
    )
  })
})
