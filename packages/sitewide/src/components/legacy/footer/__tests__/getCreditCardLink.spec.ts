// @ts-nocheck
import {Brand} from "@sitewide/components/legacy/types"
import {getCreditCardLink} from "../getCreditCardLinks"
import {DeviceType} from "../types"
import {expectedResults} from "../__fixtures__/getCreditCardLink"

describe("getCreditCardLink", () => {
  const devices: Array<DeviceType> = ["desktop", "mobile"]
  const brands: Array<Brand> = Object.values(Brand)

  describe.each(brands)("when get link for brand %s", (brand) => {
    describe.each(devices)("for %s", (device) => {
      const barclaysLinks = getCreditCardLink(brand, device)

      it.each(expectedResults[brand][device])(
        "should return correct url for %s",
        (linkName, linkExpectedValue) => {
          const link = barclaysLinks.find((link) => link.text === linkName)

          expect(link).not.toBeUndefined()
          expect(link?.to).toEqual(linkExpectedValue)
        }
      )
    })
  })
})
