{"instanceName": "gap-footer", "name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 style=\"font-size: min(max(16px, calc(1rem + ((1vw - 3.2px) * 3.5794))), 32px); min-height: 0vw;\">Sign Up for Email &amp; Get 25% Off<span>*</span></h3>", "style": {}}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "signButton label-a", "variant": "border", "size": "medium", "fullWidth": true, "crossBrand": false, "color": "white"}, "desktop": {"className": "signButton label-a", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "white"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p style=\"font-size: 10px;\"><span>*</span>Valid for first-time registrants only &amp; applies to reg. price items only. <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" style=\"text-decoration: underline;\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto", "maxWidth": "1260px", "position": "relative"}, "components": [{"instanceName": "BOPIS_footer", "instanceDesc": "032921 - Created", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0", "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,20262978,LP_BOPIS_footer_CTA", "style": {"color": "#2B2B2B", "display": "block", "fontSize": "min(max(14px, calc(0.875rem + ((1vw - 3.2px) * 1.3423))), 20px)", "lineHeight": "1", "min-height": "0vw", "padding": "16px", "textTransform": "uppercase"}}, "components": ["Buy Online, Pick Up In Store or Curbside."]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"borderBottom": "1px solid #122344"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,20262978,LP_BOPIS_footer_CTA", "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "fontSize": "18px", "lineHeight": "1", "min-height": "0vw", "padding": "16px", "textTransform": "uppercase"}}, "components": [{"name": "img", "type": "builtin", "data": {"props": {"alt": "Gap Shopping Bag Icon", "src": "/Asset_Archive/GPWeb/content/0020/262/978/assets/Shopping_bag_2.svg", "style": {"marginRight": "8px"}}}}, {"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B", "marginRight": "1rem"}}, "components": ["Buy Online, Pick Up In Store or Curbside."]}}, {"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B", "marginRight": "8px"}}, "components": ["Learn More"]}}, {"name": "img", "type": "builtin", "data": {"props": {"alt": "Blue arrow icon", "src": "/Asset_Archive/GPWeb/content/0020/262/978/assets/arrow--blue2.svg", "style": {"height": "16px"}}}}]}}]}}}}, {"instanceName": "edfs_footer", "instanceDesc": "032921 - Rebuilt as Live Text", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0", "margin": "0 auto", "maxWidth": "640px", "position": "relative", "width": "100%"}, "desktop": {"margin": "0 auto", "maxWidth": "1920px", "position": "relative", "width": "100%"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"display": "block", "fontSize": "min(max(14px, calc(0.875rem + ((1vw - 3.2px) * 1.3423))), 20px)", "lineHeight": "1", "min-height": "0vw", "padding": "16px"}}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B", "marginBottom": "4px", "marginRight": "1rem", "textTransform": "uppercase"}}, "components": ["4 Brands 1 Easy Checkout."]}}, {"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B"}}, "components": ["Free shipping on orders of $50+. Free Returns."]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"bottom": "0", "left": "0", "position": "absolute", "right": "0", "top": "0"}, "ctas": [{"composableButtonData": {"children": "Details", "font": "primary", "style": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#FFFFFF", "fontSize": "10px", "fontWeight": "400", "padding": "3px 4px", "textDecoration": "underline", "textTransform": "uppercase"}}, "modalData": {"closeButtonAriaLabel": "Close Pop-up", "modalSize": "max", "iframeData": {"title": "Everyday Free Shipping", "src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-Narvar.html?v=0", "tid": "FooterEDFS_Details", "height": "500px"}}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"display": "flex", "fontSize": "18px", "lineHeight": "1", "min-height": "0vw", "padding": "16px"}}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B", "flexGrow": "1", "marginRight": "1rem", "textTransform": "uppercase"}}, "components": ["4 Brands 1 Easy Checkout."]}}, {"name": "div", "type": "builtin", "data": {"props": {"style": {"color": "#2B2B2B", "textTransform": "uppercase"}}, "components": ["Free shipping on orders of $50+. Free Returns."]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"bottom": "0", "left": "0", "position": "absolute", "right": "0", "top": "0"}, "ctas": [{"composableButtonData": {"children": "Details", "font": "primary", "style": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#FFFFFF", "fontSize": "10px", "fontWeight": "400", "padding": "3px 4px", "textDecoration": "underline", "textTransform": "uppercase"}}, "modalData": {"closeButtonAriaLabel": "Close Pop-up", "modalSize": "max", "iframeData": {"title": "Everyday Free Shipping", "src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-Narvar.html?v=0", "tid": "FooterEDFS_Details", "height": "500px"}}}]}}}]}}}}, {"instanceDesc": "footerlinks", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"borderBottom": "1px solid #122344", "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"borderTop": "1px solid #122344", "margin": "0 auto", "maxWidth": "1920px", "padding": "20px 0", "width": "100%"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"a, button": {"borderColor": "#122344", "borderWidth": "1px 0 0", "fontSize": "min(max(14px, calc(0.875rem + ((1vw - 3.2px) * 1.3423))), 20px)", "fontWeight": "400", "padding": "14px 16px", "textAlign": "left", "&:focus": {"outline": "none"}, "&:hover": {"backgroundColor": "#122344", "color": "#FFFFFF"}}, "button span": {"font-size": "1.3em"}, "ul": {"boxShadow": "none"}, "li": {"borderWidth": "0", "padding": "0", "a": {"letterSpacing": "0", "paddingBottom": "4px", "paddingTop": "4px", "textTransform": "none"}, "&:last-child": {"marginBottom": "20px"}}}, "desktopStyle": {"display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "maxWidth": "100%", "padding": "0 8px", "div": {"padding": "0 8px", "width": "25%"}, "a, button": {"backgroundColor": "transparent", "fontSize": "16px"}, "button": {"borderWidth": "0", "margin": "0 0 12px", "padding": "0", "pointerEvents": "none", "span": {"display": "none"}, "&:hover": {"backgroundColor": "transparent", "color": "#122344"}}, "ul": {"backgroundColor": "transparent", "maxHeight": "400px", "padding": "0", "zIndex": "699", "visibility": "visible", "li": {"margin": "0 0 4px", "a": {"fontSize": "14px", "padding": "2px 0", "&:hover": {"backgroundColor": "transparent", "color": "#122344", "textDecoration": "underline"}}}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gapincsustainability.com/", "target": "_blank"}, {"text": "Gap for Good", "href": "/browse/info.do?cid=1086537&mlink=55278,20262978,CS_Footer_G4G"}]}}, {"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Store Locator", "href": "/stores?mlink=55278,20262978,cs_footer_storelocator&clink=20262978"}, {"text": "Customer Service", "href": "/customerService/info.do?cid=2136"}, {"text": "Shipping", "href": "/customerService/info.do?cid=81265&cs=shipping_and_handling&mlink=55278,20262978,CS_Footer_Shipping&clink=20262978"}, {"text": "Returns", "href": "/customerService/info.do?cid=81264&cs=items_bought_online&mlink=55278,20353697,CS_Footer_Returns&clink=20353697"}, {"text": "Track Your Order", "href": "/profile/order_history.do?isNav=true&mlink=55278,20262978,CS_Footer_TrackYourOrder&clink=20262978"}, {"text": "1.800.GAPSTYLE (**************)", "href": "tel:+18004277895"}]}}, {"buttonDropdownData": {"heading": {"text": "GapCard"}, "submenu": [{"text": "Apply Now", "href": "/credit-card-accept/digital-applications/gap?returnURL=https://www.gap.com/origin-when-cross-origin&externalCustomerId=27BD3210444A11E9A18CF31F9039DA0D"}, {"text": "Activate Your Card", "href": "https://gap.syf.com/activate/load?clientId=gap&langId=en", "target": "_blank"}, {"text": "Review Card Benefits", "href": "/profile/info.do?cid=1033021&mlink=55278,20262978,CS_Footer_GapCard&clink=20262978"}, {"text": "Pay Bill", "href": "https://gap.syf.com/login/", "target": "_blank"}]}}, {"buttonDropdownData": {"heading": {"text": "Ways to Shop"}, "submenu": [{"text": "Get the App", "href": "https://apps.apple.com/us/app/gap/id326347260"}, {"text": "Gap Good Rewards", "href": "/customerService/info.do?cid=1099008"}, {"text": "Apple Pay", "href": "/customerService/info.do?cid=81265&cs=payment_options"}, {"text": "Afterpay", "href": "/customerService/info.do?cid=81265&cs=payment_options"}, {"text": "PayPal", "href": "/customerService/info.do?cid=81265&cs=payment_options"}, {"text": "Giftcards", "href": "/customerService/info.do?cid=2116&mlink=55278,20262978,Footer_1_GC&clink=20262978"}]}}]}}}, {"instanceName": "footer_social-media-links", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "padding": "12px 0", "width": "100%"}, "desktop": {"display": "none", "margin": "0 auto", "maxWidth": "1920px"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "baseline", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Instagram", "srcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_ig--blue.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_ig--blue.svg"}, "linkData": {"to": "https://www.instagram.com/gap/", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Twitter", "srcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_twitter--blue.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_twitter--blue.svg"}, "linkData": {"to": "https://twitter.com/gap?lang=en", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Facebook", "srcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_fb--blue.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_fb--blue.svg"}, "linkData": {"to": "https://www.facebook.com/gap/", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "YouTube", "srcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_youtube--blue.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_youtube--blue.svg"}, "linkData": {"to": "https://www.youtube.com/user/Gap/featured", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "TikTok", "srcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_tiktok--blue.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0020/262/978/assets/footer_social-icon_tiktok--blue.svg"}, "linkData": {"to": "https://vm.tiktok.com/ZMeDk5Wvw", "target": "_blank"}}}}]}}}}, {"instanceName": "footer_bottom-section", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"backgroundColor": "#122344", "padding": "16px 0 0", "textAlign": "center"}, "desktop": {"display": "none"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://apps.apple.com/us/app/gap/id326347260", "style": {"color": "#FFFFFF", "display": "inline-block", "fontSize": "12px", "padding": "16px", "textAlign": "center", "&:hover": {"textDecoration": "underline"}}}, "components": ["Download Our App"]}}]}}}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": false, "data": {"style": {"display": "none"}}}}}, "copyRights": {"rows": [[{"text": "© 2021 The Gap, Inc.", "style": {"fontSize": "11px"}}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=55278,20353697,CS_Footer_PrivacyPolicy&clink=20353697", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Do Not Sell My Info", "doNotSell": true, "style": {"fontSize": "11px"}}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Your California Privacy Rights", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=55278,20353697,<PERSON>_Footer_Returns_CA_Policy&clink=20353697#Your-California-Privacy-Rights", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=6754", "style": {"fontSize": "11px"}}, {"text": "Careers", "to": "https://jobs.gapinc.com/gap-home", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Sustainability", "to": "https://www.gapincsustainability.com/", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/content/gapinc/html/aboutus.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}], [{"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1005563", "style": {"fontSize": "11px"}}, {"text": "Gap Inc. Policies", "to": "http://www.gapincsustainability.com/policies", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}]]}}}