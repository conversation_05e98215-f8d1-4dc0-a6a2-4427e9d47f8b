// @ts-nocheck
"use client";
import {oneTrustCopyRightRows as oneTrustCopyRightRowsEn} from "./en_CA/defaultCopyRight"
import defaultSmsFormEn from "./en_CA/defaultSmsForm"
import defaultEmailRegistrationDataEn from "./en_CA/defaultEmailRegistrationData"
import defaultCustomerSupportDataEn from "./en_CA/defaultCustomerSupportData"
import defaultFooterCarouselDataEn from "./en_CA/defaultFooterCarouselData"

import {oneTrustCopyRightRows as oneTrustCopyRightRowsFr} from "./fr_CA/defaultCopyRight"
import defaultSmsFormFr from "./fr_CA/defaultSmsForm"
import defaultEmailRegistrationDataFr from "./fr_CA/defaultEmailRegistrationData"
import defaultCustomerSupportDataFr from "./fr_CA/defaultCustomerSupportData"
import defaultFooterCarouselDataFr from "./fr_CA/defaultFooterCarouselData"

export type TrimmedLocale = "en" | "fr"
interface BRFSDefaults {
  oneTrustCopyRightRows: typeof oneTrustCopyRightRowsEn
  defaultSmsForm: typeof defaultSmsFormEn
  defaultEmailRegistrationData: typeof defaultEmailRegistrationDataEn
  defaultCustomerSupportData: typeof defaultCustomerSupportDataEn
  defaultFooterCarouselData: typeof defaultFooterCarouselDataEn
}
type DefaultsByLocale = {
  [locale in TrimmedLocale]: BRFSDefaults
}

const brfsDataDefaults: DefaultsByLocale = {
  en: {
    oneTrustCopyRightRows: oneTrustCopyRightRowsEn,
    defaultSmsForm: defaultSmsFormEn,
    defaultEmailRegistrationData: defaultEmailRegistrationDataEn,
    defaultCustomerSupportData: defaultCustomerSupportDataEn,
    defaultFooterCarouselData: defaultFooterCarouselDataEn,
  },
  fr: {
    oneTrustCopyRightRows: oneTrustCopyRightRowsFr,
    defaultSmsForm: defaultSmsFormFr, // unknown if there will be french data
    defaultEmailRegistrationData: defaultEmailRegistrationDataFr, // unknown if there will be french data
    defaultCustomerSupportData: defaultCustomerSupportDataFr,
    defaultFooterCarouselData: defaultFooterCarouselDataFr, // unknown if there will be french data
  },
}

export default brfsDataDefaults
