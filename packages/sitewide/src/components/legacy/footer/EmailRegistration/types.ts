// @ts-nocheck
"use client";
import {FixedButtonProps} from "@ecom-next/core/legacy/fixed-button"
import {CSSObject, Interpolation, Theme} from "@ecom-next/core/legacy/react-stitch/types"
import {CustomTextData} from "../../customer-aquisition/EmailRegistrationForm/CustomText"
import {ViewportElement} from "../../customer-aquisition/EmailRegistrationForm/types"

export interface EmailRegistrationStyles {
  wrapper?: Interpolation<Theme>
  titleContainer?: Interpolation<Theme>
  title?: Interpolation<Theme>
  subtitle?: Interpolation<Theme>
  formContainer?: Interpolation<Theme>
  form?: Interpolation<Theme>
  inputContainer?: Interpolation<Theme>
  input?: Interpolation<Theme>
  inputsContainer?: Interpolation<Theme>
  disclaimer?: Interpolation<Theme>
  details?: Interpolation<Theme>
  submitButtonContainer?: Interpolation<Theme>
  submitButton?: Interpolation<Theme>
}

export interface SubmitButtonProps extends Omit<FixedButtonProps, "children"> {
  text?: string
}

export interface EmailRegistrationProps {
  detailsLink?: React.ReactNode
  detailsModal?: React.ReactNode
  disclaimerText?: CustomTextData
  emailPlaceholderText?: string
  errorNotificationAriaLabel: string
  inverse?: boolean
  notificationStyles?: CSSObject
  notificationsLocation?: string | ViewportElement<CustomTextData>
  sourceGenericCode?: string
  sourceSpecificCode?: string
  styles?: EmailRegistrationStyles
  submitButtonOptions?: ViewportElement<SubmitButtonProps>
  submitButtonText: string
  subtitle?: CustomTextData
  targetURL: string
  title?: CustomTextData
  validationMessage?: string
  shouldUseDarkTheme?: boolean
  shouldUseBR2024BackgroundColor?: boolean
}
