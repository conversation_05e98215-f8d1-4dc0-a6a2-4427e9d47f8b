// @ts-nocheck
"use client";
import {css, Theme} from "@ecom-next/core/react-stitch"
import {EmailRegistrationStyles} from "../EmailRegistration"

export function emailRegistrationStyles({
  color,
  brandFont,
}: Theme): EmailRegistrationStyles {
  const textStyles = css`
    ${brandFont};
    font-size: 13px;
    letter-spacing: 0.5px;
  `

  return {
    title: css`
      color: ${color.bk};
      ${brandFont};
      font-size: 19px;
      font-weight: 700;
      margin-bottom: 11px;
      padding-top: 0.5em;
      text-transform: uppercase;
    `,
    input: css`
      margin-top: 23px;
      text-align: left !important;
      width: 100%;
    `,
    subtitle: textStyles,
    disclaimer: textStyles,
    submitButtonContainer: css`
      margin: 24px auto;
      width: 123px;
    `,
  }
}
