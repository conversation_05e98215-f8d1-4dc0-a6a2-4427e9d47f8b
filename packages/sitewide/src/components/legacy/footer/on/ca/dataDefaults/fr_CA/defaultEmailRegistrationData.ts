// @ts-nocheck
"use client";
import {Variant, Size, Color} from "@ecom-next/core/legacy/fixed-button"

const config = {
  disclaimerText: {
    data: {
      html:
        'Oui! J\'aimerais recevoir des nouvelles sur la mode et des offres exclusives de Gap Inc., les sociétés associées et marques incluant Gap (Canada) Inc. et Old Navy (Canada) Inc. et Banana Republic. Vous pouvez retirer votre accord en tout temps. <br><br>POUR VOIR PLUS DE DÉTAILS, VEUILLEZ CONSULTER NOTRE <a href="https://corporate.gapinc.com/fr-ca/consumer-privacy-policy" target="_blank"><u>POLITIQUE DE CONFIDENTIALITÉ</u></a> OR <a href="/customerService/info.do?cid=3332" target="_blank" onclick="return mkt_contentClickTracking( this, \'Footer_Email_Contact_Us\' );"><u>NOUS CONTACTER</u></a>.',
    },
  },
  title: {
    data: {
      html: "Inscris-toi aux courriels",
    },
  },
  subtitle: {
    data: {
      html:
        "Compte parmi les premiers à connaître les nouveautés, les soldes irrésistibles et bien d'autres choses exceptionnelles.",
    },
  },
  submitButtonOptions: {
    desktop: {
      className: "signButton",
      variant: Variant.solid,
      size: Size.medium,
      fullWidth: false,
      crossBrand: false,
      color: Color.black,
    },
    mobile: {
      className: "signButton",
      variant: Variant.solid,
      size: Size.small,
      fullWidth: true,
      crossBrand: false,
      color: Color.black,
    },
  },
  submitButtonText: "S'inscrire",
  emailPlaceholderText: "Saisissez votre courriel",
  errorNotificationAriaLabel: "Erreur",
  validationMessage: "S'il vous plaît, mettez une adresse email valide",
}

export default config
