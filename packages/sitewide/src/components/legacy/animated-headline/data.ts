// @ts-nocheck
"use client";
const countdownHeadlineDataStyle = {
  desktop: {
    color: "",
    display: "inline-block",
  },
  mobile: {
    color: "",
  },
  desktopAndMobile: {
    display: "inline-block",
  },
}

export const json = {
  name: "AnimatedHeadline",
  type: "sitewide",
  data: {
    style: {
      backgroundColor: "white",
    },
    options: {
      isDesktopVisible: true,
      isMobileVisible: true,
    },
    components: [
      {
        type: "builtin",
        name: "div",
        data: {
          props: {
            style: {
              fontFamily: "Helvetica, sans-serif",
              fontSize: "11px",
              width: "100%",
              height: "100%",
            },
          },
          components: [
            {
              type: "builtin",
              name: "div",
              data: {
                excludePageTypes: ["product"],
                props: {
                  style: {
                    fontWeight: "bold",
                    color: "#000000",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  },
                },
                components: [
                  "FREE SHIPPING ON $50+, FREE RETURN ON ALL ORDERS",
                  {
                    type: "builtin",
                    name: "a",
                    data: {
                      props: {
                        href: "https://www.gap.com",
                        target: "_blank",
                        style: {
                          color: "#000000",
                          textDecoration: "underline",
                          paddingLeft: "5px",
                          fontWeight: "normal",
                        },
                      },
                      components: ["details"],
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      {
        type: "builtin",
        name: "div",
        data: {
          props: {
            style: {
              fontFamily: "Helvetica, sans-serif",
              fontSize: "11px",
              width: "100%",
              height: "100%",
            },
          },
          components: [
            {
              type: "builtin",
              name: "div",
              data: {
                props: {
                  style: {
                    fontWeight: "bold",
                    color: "#000000",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  },
                },
                components: [
                  "BUY ONLINE, PICKUP IN-STORE",
                  {
                    type: "builtin",
                    name: "a",
                    data: {
                      props: {
                        href: "https://www.gap.com",
                        target: "_blank",
                        style: {
                          color: "#000000",
                          textDecoration: "underline",
                          paddingLeft: "5px",
                          fontWeight: "normal",
                        },
                      },
                      components: ["learn more"],
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      {
        type: "sitewide",
        name: "Countdown",
        data: {
          style: {
            fontWeight: "300",
            fontSize: "11px",
          },
          ongoingHeadline: {
            type: "sitewide",
            name: "TextHeadline",
            data: {
              text: "",
              style: countdownHeadlineDataStyle,
            },
          },
          endedHeadline: {
            type: "sitewide",
            name: "TextHeadline",
            data: {
              text: "",
              style: countdownHeadlineDataStyle,
            },
          },
          endDate: "2020-10-18T16:46:59.628-04:00",
          timeDisplayText: {
            day: {
              showAttribute: true,
              showAtZero: true,
              text: "Days",
              textSingular: "Day",
            },
            hour: {
              showAttribute: true,
              showAtZero: true,
              text: "Hrs",
              textSingular: "Hr",
            },
            minute: {
              showAttribute: true,
              showAtZero: true,
              text: "Mins",
              textSingular: "Min",
            },
            second: {
              showAttribute: true,
              showAtZero: true,
              text: "Secs",
              textSingular: "Sec",
            },
          },
        },
      },
    ],
  },
}
