// @ts-nocheck
import {SMALL} from "@ecom-next/core/breakpoint-provider"
import {DynamicMarketing as Marketing} from "@mui/components/json-marketing.client";
import {
  render,
  RenderResult,
  act,
  screen,
  fireEvent,
  RenderOptions,
} from "@sitewide/components/legacy/setupTests/test-helpers"
import AnimatedHeadline, {
  DEFAULT_DURATION,
  DEFAULT_TRANSITION_SPEED,
} from "../AnimatedHeadline"
import {AnimatedHeadlineProps, AnimatedHeadlineComponent} from "../types"

type TextHeadlineMockProps = {
  data?: {
    text?: string
  }
}

const TextHeadlineMock = ({data}: TextHeadlineMockProps): JSX.Element => (
  <button>{data?.text}</button>
)

const defaultFirstMessage = "50% OFF STOREWIDE"

const defaultData: AnimatedHeadlineProps = {
  components: [
    {
      name: "TextHeadline",
      type: "sitewide",
      instanceName: "test01",
      data: {
        text: defaultFirstMessage,
      },
    },
    {
      name: "TextHeadline",
      type: "sitewide",
      instanceName: "test02",
      data: {
        text: "EXTRA 30% OFF YOUR ORDER",
      },
    },
    {
      name: "TextHeadline",
      type: "sitewide",
      experimentRunning: true,
      instanceName: "test03",
      data: {
        text: "placeholderForTest03",
      },
    },
    {
      name: "TextHeadline",
      type: "sitewide",
      experimentRunning: true,
      instanceName: "test04",
      data: {
        text: "placeholderForTest04",
      },
    },
    {
      name: "TextHeadline",
      type: "sitewide",
      redpointExperimentRunning: true,
      instanceName: "test05",
      data: {
        text: "placeholderForTest05",
      },
    },
  ],
}

const test03PersonalizedHeadline = {
  name: "TextHeadline",
  type: "sitewide",
  instanceName: "test03",
  data: {
    text: "Personalized headline",
  },
}

const test04PersonalizedHeadline = {
  name: "TextHeadline",
  type: "sitewide",
  instanceName: "test04",
  data: {
    text: "Personalized headline number 2",
  },
}

const test05PersonalizedHeadlineRedpoint = {
  name: "TextHeadline",
  type: "sitewide",
  instanceName: "test05",
  data: {
    text: "Personalized headline Redpoint",
  },
}

const setPersonalizedHeadlineDataRedpoint = (
  headlineId: string,
  headlineData: AnimatedHeadlineComponent
): void => {
  if (!window.personalizationData?.redpointPersonalizedMarketingData) {
    window.personalizationData = {
      redpointPersonalizedMarketingData: {
        components: {},
      },
    }
  }
  if (window.personalizationData.redpointPersonalizedMarketingData.components) {
    window.personalizationData.redpointPersonalizedMarketingData.components[
      headlineId
    ] = headlineData
  }
}

const setPersonalizedHeadlineData = (
  headlineId: string,
  headlineData: AnimatedHeadlineComponent
): void => {
  if (!window.personalizedMarketingData) {
    window.personalizedMarketingData = {components: {}}
  }
  if (window.personalizedMarketingData.components) {
    window.personalizedMarketingData.components[headlineId] = headlineData
  }
}

const renderHeadline = (data: AnimatedHeadlineProps = defaultData, options: RenderOptions = {}): RenderResult => {
  {(Marketing as jest.Mock).mockImplementation(TextHeadlineMock as () => JSX.Element);}

  return render(
    <AnimatedHeadline data={{...data, vertical: false}} />,
    {appState: {pageType: "home"}, ...options}
  );
}

const queryHeadline = (name: string | RegExp): null | HTMLElement =>
  screen.queryByRole("button", {name})
const getHeadline = (name: string): HTMLElement =>
  screen.getByRole("button", {name})

function waitForNextHeadline(): void {
  act(() => {
    jest.advanceTimersByTime(DEFAULT_DURATION + DEFAULT_TRANSITION_SPEED)
  })
}

function fireLongPressEvent(headline: HTMLElement): void {
  fireEvent.touchStart(headline)
  act(() => {
    jest.advanceTimersByTime(1000)
  })
  fireEvent.touchEnd(headline)
}

jest.useFakeTimers()

global.IS_SSR = false

describe("<AnimatedHeadline/>", () => {
  it("renders first slide correctly", () => {
    renderHeadline()
    expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
  })

  it("renders second slide after the set duration", () => {
    renderHeadline()
    expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()

    waitForNextHeadline()

    expect(queryHeadline("EXTRA 30% OFF YOUR ORDER")).toBeInTheDocument()
    expect(queryHeadline(defaultFirstMessage)).not.toBeInTheDocument()
  })

  it("renders second component if first is excluded by page type", () => {
    const data = {...defaultData}

    data.components[0].data.excludePageTypes = ["home"]

    renderHeadline(data)
    expect(queryHeadline("EXTRA 30% OFF YOUR ORDER")).toBeInTheDocument()
  })

  it("renders second component if first is included by other page type", () => {
    const data = {...defaultData}
    data.components[0].data.includePageTypes = ["division"]

    renderHeadline(data)
    expect(queryHeadline("EXTRA 30% OFF YOUR ORDER")).toBeInTheDocument()
  })

  it("renders first component if included AND excluded are set with the same page type", () => {
    const data = {...defaultData}

    data.components[0].data.excludePageTypes = ["home"]
    data.components[0].data.includePageTypes = ["home"]

    renderHeadline(data)
    expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
  })

  it("does not render personalized headline placeholders", () => {
    renderHeadline()
    waitForNextHeadline()

    waitForNextHeadline()
    expect(queryHeadline("placeholderForTest03")).not.toBeInTheDocument()

    waitForNextHeadline()
    expect(queryHeadline("placeholderForTest04")).not.toBeInTheDocument()
  })

  describe("with personalized headlines experiments", () => {
    beforeEach(() => {
      global.IS_SSR = false
      delete window.personalizedMarketingData
      delete window.personalizationData
    })

    it("renders redpoint personalized headlines", () => {
      renderHeadline()

      waitForNextHeadline()
      setPersonalizedHeadlineDataRedpoint(
        "test05",
        test05PersonalizedHeadlineRedpoint
      )

      waitForNextHeadline();waitForNextHeadline();waitForNextHeadline()
      
      expect(
        queryHeadline(/personalized headline redpoint/i)
      ).toBeInTheDocument()
    })

    it("renders personalized headlines", () => {
      renderHeadline()

      waitForNextHeadline()
      setPersonalizedHeadlineData("test03", test03PersonalizedHeadline)

      waitForNextHeadline()
waitForNextHeadline()
waitForNextHeadline()
      expect(queryHeadline("Personalized headline")).toBeInTheDocument()
    })

    it("does not render personalized headlines on the server side", () => {
      global.IS_SSR = true
      renderHeadline()

      waitForNextHeadline()
      setPersonalizedHeadlineData("test03", test03PersonalizedHeadline)

      waitForNextHeadline()

      expect(queryHeadline("Personalized headline")).not.toBeInTheDocument()
      expect(queryHeadline("placeholderForTest03")).not.toBeInTheDocument()
      expect(queryHeadline("placeholderForTest04")).not.toBeInTheDocument()
    })

    it("renders personalized headlines that are set later than others in page load", () => {
      renderHeadline()
      setPersonalizedHeadlineData("test03", test03PersonalizedHeadline)

      waitForNextHeadline()
      setPersonalizedHeadlineData("test04", test04PersonalizedHeadline)

      waitForNextHeadline()
waitForNextHeadline()
waitForNextHeadline()
waitForNextHeadline()
waitForNextHeadline()
      waitForNextHeadline()
      expect(
        queryHeadline("Personalized headline number 2")
      ).toBeInTheDocument()
    })

    it("should stop to checking for personalized content after 7 seconds", () => {
      renderHeadline()
      jest.advanceTimersByTime(7000)
      setPersonalizedHeadlineData("test03", test03PersonalizedHeadline)

      expect(queryHeadline("Personalized headline")).not.toBeInTheDocument()
    })
  })

  describe("autoplay play/pause functionality", () => {
    it("pauses the carousel when the user presses down for 1 second", () => {
      renderHeadline()

      const headline = getHeadline(defaultFirstMessage)
      fireLongPressEvent(headline)
      waitForNextHeadline()

      expect(headline).toBeInTheDocument()
    })

    it("plays the carousel when the user presses down for 1 second when paused", () => {
      renderHeadline()

      const headline = getHeadline(defaultFirstMessage)
      fireLongPressEvent(headline)
      waitForNextHeadline()

      fireLongPressEvent(headline)
      waitForNextHeadline()

      expect(queryHeadline("EXTRA 30% OFF YOUR ORDER")).toBeInTheDocument()
    })
  })

  describe("hide/show functionality", () => {
    describe("on desktop", () => {
      it("renders on desktop by default", () => {
        renderHeadline(defaultData)

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("renders on desktop when options is empty", () => {
        const props = {
          ...defaultData,
          options: {},
        }

        renderHeadline(props)

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("renders on desktop when isDesktopVisible is true", () => {
        const props = {
          ...defaultData,
          options: {
            isDesktopVisible: true,
          },
        }

        renderHeadline(props)

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("does not render on desktop when isDesktopVisible is false", () => {
        const props = {
          ...defaultData,
          options: {
            isDesktopVisible: false,
          },
        }

        renderHeadline(props)

        expect(queryHeadline(defaultFirstMessage)).not.toBeInTheDocument()
      })
    })

    describe("on mobile", () => {
      it("renders on mobile by default", () => {
        renderHeadline(defaultData, {breakpoint: SMALL})

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("renders on mobile when options is empty", () => {
        const props = {
          ...defaultData,
          options: {},
        }

        renderHeadline(props, {breakpoint: SMALL})

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("renders on mobile when isMobileVisible is true", () => {
        const props = {
          ...defaultData,
          options: {
            isMobileVisible: true,
          },
        }

        renderHeadline(props, {breakpoint: SMALL})

        expect(queryHeadline(defaultFirstMessage)).toBeInTheDocument()
      })

      it("does not render on mobile when isMobileVisible is false", () => {
        const props = {
          ...defaultData,
          options: {
            isMobileVisible: false,
          },
        }

        renderHeadline(props, {breakpoint: SMALL})

        expect(queryHeadline(defaultFirstMessage)).not.toBeInTheDocument()
      })
    })
  })
})
