# AnimatedHeadline

This component is to be used in the mobile header. It can contain multiple content from brands. It is able to involve content such as texts and Countdown.

## Definition

AnimatedHeadline is responsible for cycling through multiple headlines in the same space.

## Highly Recommended Usage

Fontsize should be 11px or 12px
Use maximum of 4 components per page

## Layout

AnimatedHeadline contains multiple components, duration and style.
The height of the component is `30px` and the components used within it should respect that height.

```json
"animatedheadline": {
    "name": "AnimatedHeadline",
    "type": "sitewide",
    "data": {
        "style": {},
        "components": [],
        "duration": 3000,
        "transitionSpeed": 500,
        "useContainerFullHeight": false
    }
},
```

## Duration

It is the time in milliseconds that each headline will be visible before transitioning to the next.

## Styling the AnimatedHeadline

If you need to have a different background color for a specific component, you probably would need to set `useContainerFullHeight` to `true` and add this style to your component to have it aligned:

```json
{
  "backgroundColor": "your-color",
  "height": "100%",
  "alignItems": "center",
  "display": "flex",
  "justifyContent": "center"
}
```

### The Style Object

The `AnimatedHeadline` can be styled with a `style` object with the following pattern:

```json
{
  "backgroundColor": "white", // optional
  "height": "60px" // optional, defaults to 30px
}
```

Each component can be styled according to its API.

We also allow configurations for specifically desktop or mobile. These optional styles will overwrite
any shared styles within the `style` block for that breakpoint:

```json
{
  "backgroundColor": "white",
  "height": "60px",
  "desktop": {
    "height": "30px" // overwrites the shared height for desktop only
  },
  "mobile": {
    "backgroundColor": "orange" // overwrites the shared backgroundColor for mobile only
  }
}
```

## The hide feature based on breakpoint (desktop or mobile)

You can hide the whole component on desktop and still show it on a mobile device (or the opposite) using the options below inside `data` prop:

```json
"data": {
    "options": {
        "isDesktopVisible": false,
        "isMobileVisible": true
    },
}
```

If no value is set, the default value is `true` for both `isDesktopVisible` and `isMobileVisible`.

## Including/Excluding components by page type

Inside each component you are able to include or exlude it from a page based on its type.

Set the `includePageTypes` or `excludePageTypes` inside the `data` key from each component (see the full example below).

## Example

```json
"animatedheadline": {
    "name": "AnimatedHeadline",
    "type": "sitewide",
    "data": {
        "style": {
            "backgroundColor": "white"
        },
        "options": {
            "isMobileVisible": true,
            "isDesktopVisible": false
        },
        "components": [
            {
                "name": "TextHeadline",
                "type": "sitewide",
                "data": {
                    "excludePageTypes": ["category"],
                    "text": "Text",
                    "style": {
                        "mobile": {
                            "whiteSpace": "normal",
                            "textAlign": "center",
                            "fontSize": "1em",
                            "textTransform": "uppercase"
                        }
                    }
                }
            },
            {
                "name": "MktEdfsSmall",
                "type": "sitewide",
                "data": {
                    "includePageTypes": ["home"],
                    "lazy": false,
                    "experimentRunning": false,
                    "defaultData": {
                        "textStrong": "Free Shipping small",
                        "text": "on $50+, Free Returns on All Orders",
                        "detailsLink": "Details"
                    },
                    "modalTitle": "EVERYDAY FREE SHIPPING",
                    "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-ON-FS50.html",
                    "modalCloseButtonAriaLabel": "Close Popup"
                }
            }
        ],
        "duration": 3000
    }
},
```

## Including/Excluding placements

By default, when Animated Headline is provided, only `mobileemergencybanner`, `countdown` and `animatedheadline` itself are included in the header placements. Others like `headline`, `edfssmall`, `secondary-headline` will be hidden by default. However, you can either include or exclude placements to display in the header by page type using the key `placementConfiguration` below:

### Example

```json
"animatedheadline": {
    "name": "AnimatedHeadline",
    "type": "sitewide",
    "data": {},
    "placementConfiguration": {
        "home": {
            "include": ["secondary-headline"],
            "exclude": ["mobileemergencybanner"]
        },
        "category": {
            "include": ["secondary-headline"]        }
    }
```

## Adding FlexHeadline component inside AnimatedHeadline

It is possible to include a FlexHeadline component as part of AnimatedHeadline components list.

It's recommended to follow AnimatedHeadline [limitations of fontSize and number of components](#highly-recommended-usage).

You can configure desktop and mobile styles within flex items using [FlexHeadline Style Object](/info/sitewide-flexheadline--default#the-style-object).

### Example

```json
"animatedheadline": {
    "name": "AnimatedHeadline",
    "type": "sitewide",
    "data": {
        "duration": 3000,
        "transitionSpeed": 1000,
        "useContainerFullHeight": true,
        "style": {
          "backgroundColor": "#FFFFFF",
          "height": "100%"
        },
        "components": [
          {
            "sitewide-secondary-headline-ciid": "00000000",
            "type": "sitewide",
            "name": "FlexHeadline",
            "experimentRunning": false,
            "instanceName": "react-flexHeadline",
            "data": {
              "shouldWaitForOptimizely": false,
              "lazy": false,
              "placeholderSettings": {
                "useGreyLoadingEffect": false,
                "desktop": {
                  "backgroundColor": "transparent",
                  "width": "0px",
                  "height": "0px",
                  "margin": "0px auto"
                },
                "mobile": {
                  "width": "0px",
                  "height": "0px"
                }
              },
              "defaultHeight": {
                "large": "0px",
                "small": "0px"
              },
              "styles": {
                "root": {
                  "base": {
                    "textTransform": "uppercase",
                    "fontWeight": "bold",
                    "color": "#003764",
                    "backgroundColor": "#E8E8E8",
                    "width": "100%",
                    "alignItems": "center",
                    "justifyContent": "center",
                    "fontSize": "2.3vw"
                  },
                  "desktop": {
                    "backgroundColor": "#E8E8E8"
                  },
                  "mobile": {
                    "backgroundColor": "#E8E8E8",
                    "padding": "1em 0px"
                  }
                },
                "flexItem": {
                  "base": {
                    "display": "flex",
                    "alignItems": "center",
                    "fontSize": "1.5rem",
                  },
                },
                "flexItemElement": {
                  "base": {
                    "color": "#003764",
                    "fontFamily": "FranklinGothic, Open Sans, Gap Sans, Helvetica, Arial, Roboto, sans-serif"
                  },
                  "mobile": {
                    "color": "#333",
                    "fontSize": ".9rem",
                    "fontWeight": 400,
                    "letterSpacing": ".1px",
                    "lineHeight": "1.2em"
                  },
                  "desktop": {
                    "fontSize": "1.5rem"
                  }
                }
              },
              "flexItems": [
                {
                  "elements": [
                    {
                      "text": "FREE SHIPPING",
                      "style": {
                        "base":{
                          "fontWeight": "bold"
                        },
                        "mobile": {
                          "fontSize": "9px"
                        }
                      }
                    },
                    {
                      "text": "on $50+, Free Returns on All Orders",
                      "style": {
                        "base":{
                          "textTransform": "uppercase",
                          "color": "#003764",
                          "textDecoration": "none",
                          "paddingLeft": "3px",
                          "fontWeight": "normal",
                          "fontSize": "1.5rem"
                        },
                        "mobile": {
                          "fontSize": "9px"
                        }
                      }
                    },
                    {
                      "text": "details",
                      "modalProps": {
                        "src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-ON-US-101520.html",
                        "height": "500px",
                        "width": "100%",
                        "closeButtonAriaLabel": "Close Modal",
                        "title": "EVERYDAY FREE SHIPPING"
                      },
                      "style": {
                        "base": {
                          "color": "#003764",
                          "backgroundColor": "transparent",
                          "border": "0px",
                          "textTransform": "lowercase",
                          "textDecoration": "underline",
                          "fontWeight": "normal",
                          "fontSize": "1.3rem",
                          "marginLeft": ".5rem"
                        },
                        "mobile": {
                          "fontSize": "8px"
                        }
                      }
                    }
                  ]
                }
              ]
            }
          },
          {
                "name": "TextHeadline",
                "type": "sitewide",
                "data": {
                    "excludePageTypes": ["category"],
                    "text": "Text",
                    "style": {
                        "mobile": {
                            "whiteSpace": "normal",
                            "textAlign": "center",
                            "fontSize": "1em",
                            "textTransform": "uppercase"
                        }
                    }
                }
            },
        ]
      }
```

### Example with two lines

```json
"animatedheadline": {
    "name": "AnimatedHeadline",
    "type": "sitewide",
    "data": {
        "duration": 3000,
        "transitionSpeed": 1000,
        "useContainerFullHeight": true,
        "style": {
          "backgroundColor": "#FFFFFF",
          "height": "100%"
        },
        "components": [
          {
            "sitewide-secondary-headline-ciid": "00000000",
            "type": "sitewide",
            "name": "FlexHeadline",
            "experimentRunning": false,
            "instanceName": "react-flexHeadline",
            "data": {
              "shouldWaitForOptimizely": false,
              "lazy": false,
              "placeholderSettings": {
                "useGreyLoadingEffect": false,
                "desktop": {
                  "backgroundColor": "transparent",
                  "width": "0px",
                  "height": "0px",
                  "margin": "0px auto"
                },
                "mobile": {
                  "width": "0px",
                  "height": "0px"
                }
              },
              "defaultHeight": {
                "large": "0px",
                "small": "0px"
              },
              "styles": {
                "root": {
                  "base": {
                    "textTransform": "uppercase",
                    "fontWeight": "bold",
                    "color": "#003764",
                    "backgroundColor": "#E8E8E8",
                    "width": "100%",
                    "alignItems": "center",
                    "justifyContent": "center",
                    "fontSize": "2.3vw",
                    "flexDirection": "column",
                  },
                  "desktop": {
                    "backgroundColor": "#E8E8E8"
                  },
                  "mobile": {
                    "backgroundColor": "#E8E8E8",
                    "padding": "1em 0px"
                  }
                },
                "flexItem": {
                  "base": {
                    "display": "flex",
                    "alignItems": "center",
                    "fontSize": "1.5rem",
                  },
                },
                "flexItemElement": {
                  "base": {
                    "color": "#003764",
                    "fontFamily": "FranklinGothic, Open Sans, Gap Sans, Helvetica, Arial, Roboto, sans-serif"
                  },
                  "mobile": {
                    "color": "#333",
                    "fontSize": ".9rem",
                    "fontWeight": 400,
                    "letterSpacing": ".1px",
                    "lineHeight": "1.2em"
                  },
                  "desktop": {
                    "fontSize": "10px"
                  }
                }
              },
              "flexItems": [
                {
                  "elements": [
                    {
                      "text": "FASTER RETURNS & EXCHANGES AT OUR CONVENIENCE SPOT",
                      "style": {
                        "mobile": {
                          "fontSize": "9px"
                        }
                      }
                    }
                  ]
                },
                {
                  "elements": [
                    {
                      "text": "FREE SHIPPING",
                      "style": {
                        "base": {
                          "fontWeight": "bold"
                        },
                        "mobile": {
                          "fontSize": "9px"
                        }
                      }
                    },
                    {
                      "text": "on $50+, Free Returns on All Orders",
                      "style": {
                        "base": {
                          "textTransform": "uppercase",
                          "color": "#003764",
                          "textDecoration": "none",
                          "paddingLeft": "3px",
                          "fontWeight": "normal"
                        },
                        "mobile": {
                          "fontSize": "9px"
                        }
                      }
                    },
                    {
                      "text": "details",
                      "modalProps": {
                        "src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-ON-US-101520.html",
                        "height": "500px",
                        "width": "100%",
                        "closeButtonAriaLabel": "Close Modal",
                        "title": "EVERYDAY FREE SHIPPING"
                      },
                      "style": {
                        "base": {
                          "color": "#003764",
                          "backgroundColor": "transparent",
                          "border": "0px",
                          "textTransform": "lowercase",
                          "textDecoration": "underline",
                          "fontWeight": "normal",
                          "fontSize": "10px",
                          "marginLeft": ".5rem"
                        },
                        "mobile": {
                          "fontSize": "8px"
                        }
                      }
                    }
                  ]
                }
              ]
            }
          },
        ]
      }
```
