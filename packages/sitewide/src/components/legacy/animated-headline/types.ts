// @ts-nocheck
"use client";
import {CSSObject} from "@ecom-next/core/react-stitch"
import {PageType} from "@sitewide/components/legacy/types"

export type StyleProps = {
  backgroundColor?: string,
  height?: string
}

export type FilterKeys = "includePageTypes" | "excludePageTypes"

export type FilterOptions = {
  [FilterKey in FilterKeys]?: PageType[]
}

export type AnimatedHeadlineComponent = {
  data: FilterOptions & {
    meta?: FilterOptions
    options?: FilterOptions
    style?: CSSObject,
    text?: string
  },
  experimentRunning?: boolean,
  instanceName?: string
  name: string,
  redpointExperimentRunning?: boolean
  type: string
}

type AnimatedHeadlineDataOptions = {
  isDesktopVisible?: boolean
  isMobileVisible?: boolean
}

export type AnimatedHeadlineProps = {
  components: AnimatedHeadlineComponent[]
  duration?: number
  options?: AnimatedHeadlineDataOptions
  style?: StyleProps & {
    desktop?: StyleProps
    mobile?: StyleProps
  }
  transitionSpeed?: number
  useContainerFullHeight?: boolean
  /**
   * For tests, this needs to be false to avoid a runtime error
   * Issue: https://github.com/akiran/react-slick/issues/1310
   */
  vertical?: boolean
}
