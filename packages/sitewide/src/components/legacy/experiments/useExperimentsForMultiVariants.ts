// @ts-nocheck
"use client";
import {useEffect, useState} from "react"
import {useAppState} from "@ecom-next/sitewide/app-state-provider"
import {ExperimentOptions} from "./useExperiment"

type ExperimentMultipleVariants = {
  experimentStatus: string
}

export function useExperimentsForMultiVariants(
  experimentName: string,
  options: ExperimentOptions = {}
): ExperimentMultipleVariants {
  const {isBranded} = options
  const {abSeg, brandName} = useAppState()
  const experimentKey = isBranded
    ? `${brandName}${experimentName}`
    : experimentName
  const initialExperimentValue = abSeg[experimentKey]

  const [experimentStatus, setExperimentStatus] = useState(
    initialExperimentValue
  )

  useEffect(() => {
    setExperimentStatus(initialExperimentValue)
  }, [initialExperimentValue])

  return {experimentStatus}
}
