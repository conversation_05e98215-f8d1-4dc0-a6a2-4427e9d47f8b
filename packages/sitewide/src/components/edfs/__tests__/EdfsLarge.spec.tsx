/* eslint-disable react/display-name */
import { ReactNode } from 'react';
import { render } from '@testing-library/react';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import EdfsLarge from '../EdfsLarge';

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

// jest.mock('../UserGreeting', () => () => <div data-testid='mock-user-greeting'>UserGreeting</div>);
jest.mock('../SignInCta', () => () => <div data-testid='mock-sign-in-cta'>SignInCta</div>);
jest.mock('../DetailsButton', () => ({ children }: { children: ReactNode }) => <button data-testid='mock-details-button'>{children}</button>);
jest.mock('../LoyaltyCall', () => () => <div data-testid='mock-loyalty-call'>LoyaltyCall</div>);
// jest.mock('../AcquisitionApplyButton', () => () => <div data-testid='mock-apply-button'>AcquisitionApplyButton</div>);

describe('EdfsLarge', () => {
  const mockAuthorableStyles = {};

  beforeEach(() => {
    jest.clearAllMocks();
    (usePageContext as jest.Mock).mockReturnValue({ locale: 'en_US' });
  });

  it('should render EdfsLarge with provided props and components', async () => {
    const mockEdfsLargeProps = {
      styles: { container: { background: 'red' }, headline: {}, detailsButton: {} },
      defaultData: { text: 'Mock Text', detailsLink: 'Mock Link' },
      signInCta: { text: 'Sign in' },
      modalCloseButtonAriaLabel: 'Mock Modal Close Button',
      modalTitle: 'Mock Modal Title',
      modalUrl: '/mock/modal/url.page',
    };

    const { getByTestId } = render(<EdfsLarge {...mockEdfsLargeProps} />);

    expect(getByTestId('edfs-large-container-test-id')).toBeInTheDocument();
    expect(getByTestId('mock-loyalty-call')).toBeInTheDocument();
    expect(getByTestId('mock-sign-in-cta')).toBeInTheDocument();
    expect(getByTestId('mock-details-button')).toHaveTextContent('Mock Link');
    // TODO: Temporarily disabling the acquisition-related code. This might change later if the brands confirm it's needed. [PWFO-1446]
    // expect(await screen.findByTestId('mock-user-greeting')).toBeInTheDocument();
    // expect(await screen.findByTestId('mock-apply-button')).toBeInTheDocument();
  });

  it('renders when styles are not defined', () => {
    const mockEdfsLargeProps = {
      defaultData: { text: 'Mock Text', detailsLink: 'Mock Link' },
      signInCta: { text: 'Sign in' },
      modalCloseButtonAriaLabel: 'Mock Modal Close Button',
      modalTitle: 'Mock Modal Title',
      modalUrl: '/mock/modal/url.page',
    };

    const { getByTestId } = render(<EdfsLarge {...mockEdfsLargeProps} />);

    expect(getByTestId('edfs-large-container-test-id')).toBeInTheDocument();
  });

  it('renders text and details link from base props when defaultData is not provided', () => {
    const mockEdfsLargeProps = {
      styles: { container: {}, headline: {}, detailsButton: {} },
      text: 'Base Text',
      detailsLink: 'Base Link',
      signInCta: { text: 'Sign in' },
      modalCloseButtonAriaLabel: 'Mock Modal Close Button',
      modalTitle: 'Mock Modal Title',
      modalUrl: '/mock/modal/url.page',
    };

    const { getByText } = render(<EdfsLarge {...mockEdfsLargeProps} />);

    expect(getByText(mockEdfsLargeProps.text)).toBeInTheDocument();
    expect(getByText(mockEdfsLargeProps.detailsLink)).toBeInTheDocument();
  });

  it('renders hardcoded text options when defaultData and and text/detials props are not provided', () => {
    const mockEdfsLargeProps = {
      styles: { container: {}, headline: {}, detailsButton: {} },
      signInCta: { text: 'Sign in' },
      modalCloseButtonAriaLabel: 'Mock Modal Close Button',
      modalTitle: 'Mock Modal Title',
      modalUrl: '/mock/modal/url.page',
    };

    const { getByText } = render(<EdfsLarge {...mockEdfsLargeProps} />);

    expect(getByText('FREE SHIPPING ON $50+ FOR REWARDS MEMBERS')).toBeInTheDocument();
    expect(getByText('DETAILS')).toBeInTheDocument();
  });

  it('should apply correct container styles from extracted props', async () => {
    const mockEdfsLargeProps = {
      styles: { container: { background: 'blue' }, headline: {}, detailsButton: {} },
      defaultData: { text: 'Mock Text', detailsLink: 'Mock Link' },
      signInCta: { text: 'Sign in' },
      modalCloseButtonAriaLabel: 'Mock Modal Close Button',
      modalTitle: 'Mock Modal Title',
      modalUrl: '/mock/modal/url.page',
    };

    const { getByTestId } = render(<EdfsLarge {...mockEdfsLargeProps} />);

    expect(getByTestId('edfs-large-container-test-id')).toHaveStyle('background-color: blue');
  });
});
