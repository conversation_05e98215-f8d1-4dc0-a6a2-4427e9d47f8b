import { render } from '@testing-library/react';
import StickyConfig from '../StickyConfg';
import StickyUi from '../StickyUi';
import StickyWrapper from '../StickyWrapper';

jest.mock('../StickyConfg');
const mockStickyConfig = StickyConfig as jest.Mock;

jest.mock('../StickyUi');
const mockStickyUi = StickyUi as jest.Mock;
mockStickyUi.mockImplementation(({ children }) => <div data-testid='sticky-ui'>{children}</div>);

describe('StickyWrapper', () => {
  it('should render content wrapped in StickyUi when StickyConfig sets isSticky to true', () => {
    mockStickyConfig.mockImplementation(({ children }) => children({ isSticky: true }));
    const { getByTestId } = render(
      <StickyWrapper>
        <div data-testid='content'>Test</div>
      </StickyWrapper>
    );
    const stickyUi = getByTestId('sticky-ui');
    const content = getByTestId('content');
    expect(stickyUi).toBeInTheDocument();
    expect(stickyUi).toContainElement(content);
  });

  it('should not render StickyUi when StickyConfig sets isSticky to false', () => {
    mockStickyConfig.mockImplementation(({ children }) => children({ isSticky: false }));
    const { getByTestId, queryByTestId } = render(
      <StickyWrapper>
        <div data-testid='content'>Test</div>
      </StickyWrapper>
    );
    expect(queryByTestId('sticky-ui')).not.toBeInTheDocument();
    expect(getByTestId('content')).toBeInTheDocument();
  });
});
