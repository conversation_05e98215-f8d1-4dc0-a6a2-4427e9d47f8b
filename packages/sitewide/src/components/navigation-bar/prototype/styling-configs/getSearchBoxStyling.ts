import { Page } from '@ecom-next/utils/server';
import { fetchBrandBarConfigs } from './fetchBrandBarConfigs';

const searchBoxOverTransparencyStyles = `[&_form]:border-b-wh [&_*]:transition [&_*]:duration-200 [&_*]:ease-in-out [&_input]:placeholder:duration-200 [&_path]:duration-[50ms]
[&_input]:text-wh [&_input]:text-lg [&_input]:placeholder-wh [&_path]:fill-wh
group-hover:[&_form]:border-b-b1 group-hover:[&_input]:text-b1 group-hover:[&_input]:placeholder-b1 group-hover:[&_path]:fill-b1`;

const searchBoxOverSolidBgStyles = '[&_form]:border-b-b1 [&_input]:text-b1 [&_input]:placeholder-b1 [&_path]:fill-b1 [&_input]:text-lg';

export default async function getSearchBoxStyling(cid: string, pageType: Page) {
  const { isFullBleedEnabled } = await fetchBrandBarConfigs(cid, pageType);
  return isFullBleedEnabled ? searchBoxOverTransparencyStyles : searchBoxOverSolidBgStyles;
}
