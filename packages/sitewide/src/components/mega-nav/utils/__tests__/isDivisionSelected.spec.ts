import isDivisionSelected from '../isDivisionSelected';
import { navData } from '../../__fixtures__/navData';

describe('isDivisionSelected', () => {
  const newDivision = navData.activeDivisions[0];

  it('should return true when selectedCid equals division ID', () => {
    const selectedCid = newDivision.id;
    expect(isDivisionSelected(selectedCid, newDivision)).toEqual(true);
  });

  it('should return true when selectedCid is child category of division', () => {
    const gapCultGaiaCategory = newDivision.children?.[1][0].children?.[0];
    const selectedCid = gapCultGaiaCategory?.id ?? null;
    expect(isDivisionSelected(selectedCid, newDivision)).toEqual(true);
  });

  it('should return false when selectedCid is not child category of division', () => {
    const selectedCid = '12345';
    expect(isDivisionSelected(selectedCid, newDivision)).toEqual(false);
  });

  it('should return false when selectedCid is not defined', () => {
    const selectedCid = null;
    expect(isDivisionSelected(selectedCid, newDivision)).toEqual(false);
  });

  it('should return false when division is not provided', () => {
    const selectedCid = null;
    expect(isDivisionSelected(selectedCid)).toEqual(false);
  });
});
