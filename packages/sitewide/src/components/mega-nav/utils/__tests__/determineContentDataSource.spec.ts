import type { ContentData } from '@sitewide/api/capi-for-navs';
import { getPageContext } from '@ecom-next/utils/server';
import { determineContentDataSource } from '../determineContentDataSource';

jest.mock('@ecom-next/utils/server', () => ({
  getPageContext: jest.fn(),
}));

const localeBrandCombinations = [
  { locale: 'en_US', brand: 'gap', experimentName: 'gap12' },
  { locale: 'en_US', brand: 'gapfs', experimentName: 'gapfs12' },
  { locale: 'en_US', brand: 'br', experimentName: 'br12' },
  { locale: 'en_US', brand: 'brfs', experimentName: 'brfs12' },
  { locale: 'en_US', brand: 'on', experimentName: 'on12' },
  { locale: 'en_US', brand: 'at', experimentName: 'at12' },
  { locale: 'en_CA', brand: 'gap', experimentName: 'gap13' },
  { locale: 'en_CA', brand: 'br', experimentName: 'br13' },
  { locale: 'en_CA', brand: 'on', experimentName: 'on13' },
  { locale: 'en_CA', brand: 'at', experimentName: 'at13' },
  { locale: 'fr_CA', brand: 'gap', experimentName: 'gap14' },
  { locale: 'fr_CA', brand: 'br', experimentName: 'br14' },
  { locale: 'fr_CA', brand: 'on', experimentName: 'on14' },
  { locale: 'fr_CA', brand: 'at', experimentName: 'at14' },
];

const defaultData = 'defaultData';
const abTestData = 'abTestData';

const mockContentData = {
  sitewide: {
    topnav: {
      data: defaultData,
      abtest: abTestData,
    },
  },
} as unknown as ContentData;

describe('determineContentDataSource: brand agnostic tests', () => {
  beforeAll(() => {
    (getPageContext as jest.Mock).mockReturnValue({ brand: 'fake', locale: 'fake' });
  });

  afterAll(() => {
    jest.clearAllMocks();
  });
  it('should return topnav.data when abSeg is null', () => {
    const result = determineContentDataSource(mockContentData, null);
    expect(result).toBe(defaultData);
  });

  it('should return topnav.data when abSeg is undefined', () => {
    const result = determineContentDataSource(mockContentData, undefined);
    expect(result).toBe(defaultData);
  });

  it('should return topnav.data when experiment does not exist', () => {
    const result = determineContentDataSource(mockContentData, {});
    expect(result).toBe(defaultData);
  });
});

describe.each(localeBrandCombinations)('determineContentDataSource: locale: $locale, brand: $brand', ({ locale, brand, experimentName }) => {
  beforeAll(() => {
    (getPageContext as jest.Mock).mockReturnValue({ brand, locale });
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('should return topnav.data when abSeg does not match ABTEST_VARIANT', () => {
    const result = determineContentDataSource(mockContentData, { [experimentName]: 'x' });
    expect(result).toBe(defaultData);
  });

  it('should return topnav.data when topnav.abtest is falsy', () => {
    const mockContentDataWithoutAbTest = {
      sitewide: {
        topnav: {
          data: defaultData,
          abtest: null,
        },
      },
    } as unknown as ContentData;

    const result = determineContentDataSource(mockContentDataWithoutAbTest, { [experimentName]: 'a' });
    expect(result).toBe(defaultData);
  });

  it('should return topnav.abtest when abSeg matches ABTEST_VARIANT and experimentName exists and topnav data exists', () => {
    const result = determineContentDataSource(mockContentData, { [experimentName]: 'a' });
    expect(result).toBe(abTestData);
  });
});
