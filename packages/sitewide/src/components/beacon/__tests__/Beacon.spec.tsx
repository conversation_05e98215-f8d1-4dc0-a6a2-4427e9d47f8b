import React from 'react';
import { render } from '@testing-library/react';
import BeaconComponent from '../Beacon';

describe('<BeaconComponent />', () => {
  const brandMarketSpecificBeaconStyles = {
    animationIterationCount: 'Infinite',
    animationDuration: '1.6S',
  };
  it('returns initialized as true', () => {
    const { container } = render(<BeaconComponent device='desktop' style={brandMarketSpecificBeaconStyles} />);
    const circles = container.getElementsByClassName('circle');
    expect(circles).toHaveLength(2);
  });

  it('renders both circles when device is "desktop"', () => {
    const { container } = render(<BeaconComponent device='desktop' style={brandMarketSpecificBeaconStyles} />);
    const circles = container.getElementsByClassName('circle');
    expect(circles).toHaveLength(2);
  });

  it('renders both circles when device is "mobile"', () => {
    const brandMarketStyle = {
      animationIterationCount: '2',
      animationDuration: '1.6s',
    };
    const { container } = render(<BeaconComponent device='mobile' style={brandMarketStyle} />);
    const circles = container.getElementsByClassName('pulse-base circle');
    expect(circles).not.toBeNull();
  });

  it('applies "pulse-base" class when brand is not "gap"', () => {
    const brandMarketStyle = {
      animationIterationCount: 'Infinite',
      animationDuration: '1.6s',
    };
    const { container } = render(<BeaconComponent device='desktop' style={brandMarketStyle} />);
    const pulseBase = container.getElementsByClassName('circle');
    expect(pulseBase).not.toBeNull();
  });
});
