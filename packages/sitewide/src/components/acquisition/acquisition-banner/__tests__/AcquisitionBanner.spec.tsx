// @ts-nocheck
import { screen, render, RenderResult, RenderOptions } from '@sitewide/components/legacy/setupTests/test-helpers';
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';

import { DynamicMarketing as Marketing } from '@mui/components/json-marketing.client';
import { dataRequiredProps as defaultProps } from '../__fixtures__/dataProps';
import AcquisitionBanner from '../AcquisitionBanner';
import { AcquisitionBannerProps } from '../types';

jest.mock('@ecom-next/core/legacy/hooks', () => {
  return {
    __esModule: true,
    useLoyaltyCreditCardEnrollmentService: jest.fn(() => ({
      viewPrescreenOffer: jest.fn(),
    })),
  };
});

const mockedClientSideSessionStorage = sessionStorage.setItem;

const MockMarketingComponent = ({ name }: Record<string, unknown>): JSX.Element => <button>{name as string}</button>;

const FIRST_NAME_TEXT = 'Mary';
const GREETING_TEXT = 'Hi,';

const defaultPersonalizationData = {
  preScreenId: '123',
  profileId: '123',
};

const userData: Partial<PersonalizationContextData> = {
  ...defaultPersonalizationData,
  firstName: FIRST_NAME_TEXT,
};

const getApplyNowButton = (): HTMLElement => screen.getByRole('button', { name: /apply now/i });

const renderComponent = (props: AcquisitionBannerProps = defaultProps, options: RenderOptions = {}): RenderResult => {
  {
    (Marketing as jest.Mock).mockImplementation(MockMarketingComponent as () => JSX.Element);
  }

  return render(<AcquisitionBanner {...props} data={{}} />, {
    // @ts-ignore
    personalizationData: defaultPersonalizationData,
    appState: {
      hosts: {
        creditCardDomain: 'https://www.gap.com',
        secure: 'https://secure-www.gap.com',
      },
      errorLogger: jest.fn(),
    },
    ...options,
  });
};

describe('<AcquisitionBanner/>', () => {
  afterEach(() => {
    jest.clearAllMocks();
    sessionStorage.clear();
  });

  it('should apply styles to the container', () => {
    const props = {
      ...defaultProps,
      desktopStyle: {
        padding: '1px',
      },
    };

    renderComponent(props, { breakpoint: LARGE });
    expect(screen.getByTestId('sw_acquisition-banner__container')).toHaveStyle("padding: '1px'");
  });

  describe.each([SMALL, LARGE] as Size[])('When breakpoint is %s', breakpoint => {
    describe('LoyaltyLogo', () => {
      const getLoyaltyLogoDynamicMarketing = (): HTMLElement => screen.getByRole('button', { name: /image/i });
      it('should render a component inside the loyaltyLogo placement', () => {
        renderComponent(defaultProps, { breakpoint });
        expect(getLoyaltyLogoDynamicMarketing()).toBeInTheDocument();
      });

      it('should apply passed in styles to the loyaltyLogo container', () => {
        const props = {
          ...defaultProps,
          loyaltyLogo: {
            ...defaultProps.loyaltyLogo,
            style: {
              backgroundColor: 'pink',
            },
            desktopStyle: {
              backgroundColor: 'pink',
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        const loyaltyLogoContainer = getLoyaltyLogoDynamicMarketing().closest('div');

        expect(loyaltyLogoContainer).toHaveStyle('background-color: pink');
      });
    });

    describe('UserGreeting', () => {
      const getGreetingText = (): HTMLElement => screen.getByText(GREETING_TEXT);
      const getFirstNameText = (): HTMLElement => screen.getByText(FIRST_NAME_TEXT);
      const queryGreetingText = (): HTMLElement | null => screen.queryByText(GREETING_TEXT);
      const queryFirstNameText = (): HTMLElement | null => screen.queryByText(FIRST_NAME_TEXT);
      it('should render a greeting and the first name of user if showGreeting is not provided', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            greeting: {
              text: GREETING_TEXT,
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        expect(getGreetingText()).toBeInTheDocument();
        expect(getFirstNameText()).toBeInTheDocument();
        expect(screen.getByTestId('sw_acquisition-banner__container')).toHaveClass(
          'sw_acquisition-banner__container sw_acquisition-banner__container--greeting-visible'
        );
      });

      it('should provide specific style for the greeting text', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            greeting: {
              text: GREETING_TEXT,
              style: {
                fontSize: '9px',
              },
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        expect(getGreetingText()).toHaveStyle('font-size: 9px');
      });

      it('should provide specific style for the first name text', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            greeting: {
              text: GREETING_TEXT,
            },
            firstName: {
              style: {
                color: 'blue',
              },
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        expect(getFirstNameText()).toHaveStyle('color: blue');
      });

      it('should apply passed in styles to the dynamicCustomerName / UserGreeting container', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            greeting: {
              text: 'Hola, ',
            },
            style: { color: 'purple' },
            desktopStyle: { color: 'purple' },
          },
        };

        renderComponent(props, { personalizationData: userData });

        const userGreetingContainer = screen.getByTestId('sw_acquisition-banner__user-greeting-container');
        expect(userGreetingContainer).toHaveStyle(`color: purple`);
      });

      it('should not render a greeting message if showGreeting is false', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            showGreeting: false,
            greeting: {
              text: GREETING_TEXT,
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        expect(queryGreetingText()).not.toBeInTheDocument();
        expect(queryFirstNameText()).not.toBeInTheDocument();
        expect(screen.getByTestId('sw_acquisition-banner__container')).toHaveClass('sw_acquisition-banner__container');
        expect(screen.getByTestId('sw_acquisition-banner__container')).not.toHaveClass('sw_acquisition-banner__container--greeting-visible');
      });

      it('should not render a greeting message if we dont have firstName but showGreeting is true', () => {
        const props = {
          ...defaultProps,
          dynamicCustomerName: {
            showGreeting: true,
            greeting: {
              text: GREETING_TEXT,
            },
          },
        };
        renderComponent(props, { breakpoint });
        expect(queryGreetingText()).not.toBeInTheDocument();
        expect(queryFirstNameText()).not.toBeInTheDocument();
      });
    });

    describe('Acquisition Offer', () => {
      const getOfferDynamicMarketing = (): HTMLElement => screen.getByRole('button', { name: /headline/i });
      it('should render a component inside the offer placement', () => {
        renderComponent(defaultProps, { breakpoint });
        expect(getOfferDynamicMarketing()).toBeInTheDocument();
      });

      it('should apply passed in styles to the offer container', () => {
        const props = {
          ...defaultProps,
          offer: {
            ...defaultProps.offer,
            style: {
              border: '2px dashed purple',
            },
            desktopStyle: {
              border: '2px dashed purple',
            },
          },
        };
        renderComponent(props, { personalizationData: userData, breakpoint });
        const offerContainer = getOfferDynamicMarketing().closest('div');

        expect(offerContainer).toHaveStyle(`border: 2px dashed purple`);
      });
    });

    describe('Apply Now', () => {
      it('should render a text with custom style inside the apply placement', () => {
        const props = {
          ...defaultProps,
          applyButton: {
            button: {
              text: 'Apply Now',
              style: {
                fontSize: '12px',
              },
            },
          },
        };
        renderComponent(props, { breakpoint });
        const applyNowButton = getApplyNowButton();
        expect(applyNowButton).toBeInTheDocument();
        expect(applyNowButton).toHaveStyle('font-size: 12px');
      });

      it('should apply passed in styles to the Apply button container', () => {
        const props = {
          ...defaultProps,
          applyButton: {
            button: {
              text: 'Apply Now',
            },
            style: {
              backgroundColor: 'black',
            },
          },
        };
        renderComponent(props, { breakpoint });
        const applyNowButtonContainer = getApplyNowButton().closest('div');
        expect(applyNowButtonContainer).toHaveStyle('background-color: black');
      });
    });

    describe('Loyalty Credit Card Enrollment Api', () => {
      afterEach(() => {
        jest.clearAllMocks();
        sessionStorage.clear();
      });

      it('should call creditCardEnrollment with status view', () => {
        renderComponent();
        expect(mockedClientSideSessionStorage).toHaveBeenCalledWith('AcquisitionViewed', JSON.stringify(true));
      });

      it('should not call creditCardEnrollment with status view if AcquisitionViewed already true', () => {
        sessionStorage.setItem('AcquisitionViewed', JSON.stringify(true));
        jest.clearAllMocks();

        renderComponent();
        expect(mockedClientSideSessionStorage).not.toHaveBeenCalled();
      });
    });
  });
});
