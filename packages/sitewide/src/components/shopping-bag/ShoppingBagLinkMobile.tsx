'use client';
import { ShoppingBagMobileIcon } from './ShoppingBagIcon';

export interface ShoppingBagLinkMobileProps {
  totalItemCount: number;
}

const ShoppingBagLinkMobile = ({ totalItemCount }: ShoppingBagLinkMobileProps): JSX.Element => {
  return (
    <div className='sw_mobile_shopping-bag__icon-container'>
      <div className='sw_mobile_shopping-bag__icon-container--brand'>
        <ShoppingBagMobileIcon totalItemCount={totalItemCount} />

        {totalItemCount > 0 && (
          <span className='sw_mobile_shopping-bag__bag-count' data-testid='bag-count-mobile'>
            {totalItemCount > 99 ? '99+' : totalItemCount}
          </span>
        )}
      </div>
    </div>
  );
};
export default ShoppingBagLinkMobile;
