import React from 'react';
import { render, screen } from '@testing-library/react';
import { ShoppingBagLink } from '../ShoppingBagLink';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import '@testing-library/jest-dom';
import { PageAppState } from '@sitewide/providers/PageContextProvider';
import { useShoppingBagBeacon } from '../../legacy/shopping-bag/hooks/useShoppingBagBeacon';

jest.mock('../../../hooks/usePageContext', () => ({
  usePageContext: () => {
    return {
      brand: 'at',
      brandSiteData: {
        gidBrandSites: { gp: { secureUrl: 'https://example.com' } },
        currentBrandCode: 'gp',
      },
    };
  },
}));

jest.mock('../../../hooks/usePersonalizationContext', () => ({
  PersonalizationContext: () => {
    return {
      totalItemCount: '1',
    };
  },
}));

jest.mock('../ShoppingBagAnchor', () => ({
  ShoppingBagAnchor: ({ children }: { children: React.ReactNode }) => <div data-testid='shopping-anchor'> {children}</div>,
}));

jest.mock('../../legacy/shopping-bag/hooks/useShoppingBagBeacon', () => ({
  useShoppingBagBeacon: jest.fn(() => ({
    shouldRenderBeacon: false,
  })),
}));

const renderComponentWithContext = (ui: React.ReactElement, itemCount: number) => {
  const fixedContextValue = {
    shoppingBag: {
      totalItemCount: itemCount,
    },
  };

  return render(<PersonalizationContext.Provider value={fixedContextValue}>{ui}</PersonalizationContext.Provider>);
};

describe('ShoppingBagLink', () => {
  afterEach(() => {
    jest.clearAllMocks(); //
  });

  describe('mobile view', () => {
    it('should renders correctly', () => {
      renderComponentWithContext(<ShoppingBagLink pageContext={{} as PageAppState} totalItemCount={0} isDesktop={false} />, 0);
      expect(screen.getByTestId('shopping-bag-icon-mobile')).toBeInTheDocument();
      expect(screen.queryByTestId('shopping-bag-icon-desktop')).not.toBeInTheDocument();
    });

    it('should renders the shopping bag beacon when have items in bag', () => {
      (useShoppingBagBeacon as jest.Mock).mockImplementationOnce(() => ({
        shouldRenderBeacon: true,
      }));
      renderComponentWithContext(<ShoppingBagLink pageContext={{} as PageAppState} totalItemCount={2} isDesktop={false} />, 2);
      expect(screen.getByTestId('shopping-bag-beacon')).toBeInTheDocument();
    });
  });

  describe('desktop view', () => {
    it('should renders correctly', () => {
      renderComponentWithContext(<ShoppingBagLink pageContext={{} as PageAppState} totalItemCount={0} isDesktop={true} />, 0);
      expect(screen.getByTestId('shopping-bag-icon-desktop')).toBeInTheDocument();
      expect(screen.queryByTestId('shopping-bag-icon-mobile')).not.toBeInTheDocument();
    });

    it('should renders the shopping bag beacon when have items in bag', () => {
      (useShoppingBagBeacon as jest.Mock).mockImplementationOnce(() => ({
        shouldRenderBeacon: true,
      }));
      renderComponentWithContext(<ShoppingBagLink pageContext={{} as PageAppState} totalItemCount={2} isDesktop={true} />, 2);
      expect(screen.getByTestId('shopping-bag-beacon')).toBeInTheDocument();
    });
  });
  it('applies styled bag count mobile class when shopping bag has items for mobile view', () => {
    renderComponentWithContext(<ShoppingBagLink pageContext={{} as PageAppState} totalItemCount={2} isDesktop={false} />, 2);
    const bagContainer = screen.getByTestId('bag-count-mobile');
    expect(bagContainer).toHaveClass('sw_mobile_shopping-bag__bag-count');
  });
});
