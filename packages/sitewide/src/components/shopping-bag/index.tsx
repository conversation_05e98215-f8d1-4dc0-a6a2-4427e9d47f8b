'use client';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useContext } from 'react';
import { PersonalizationContext } from '@sitewide/providers/personalization';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { ShoppingBagLink as ShoppingBag } from './ShoppingBagLink';

export const ShoppingBagLink = (): JSX.Element => {
  const pageContext = usePageContext();
  const { shoppingBag } = useContext(PersonalizationContext);
  const totalCount = shoppingBag ? shoppingBag.totalItemCount : 0;

  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(XLARGE);

  return <ShoppingBag isDesktop={isDesktop} totalItemCount={totalCount} pageContext={pageContext} />;
};
