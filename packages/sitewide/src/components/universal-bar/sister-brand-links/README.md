# Sister Brand Links

## Overview

The Sister Brand Links are the links at the top of the page that you can click to navigate to a sister brand. For example, on Gap US you would see the following links:

`| Gap | Gap Factory | Old Navy | Banana Republic | Athleta |`

## Key Features

**Navigate to Sister Brands**: This feature allows users to easily go from one brand to another.

## Technical Details

This is a server component written in TypeScript and React.

The component includes one utility function:

- `getStaticSisterBrands`: This function interacts with static data to return the list of sister brand links for the current brand.

All styles for this component are defined in <u>_styles.css_ / [IDE](styles.css) / [GitHub](https://github.gapinc.com/ecomfrontend/ecom-next/tree/main/packages/sitewide/src/components/universal-bar/sister-brand-links/styles.css)</u>.

### Handling Request Type and Domain

Current implementation retrieves brand URL templates defined in <u>_static-data_ / [IDE](static-data/brand-site-data.ts) / [GitHub](https://github.gapinc.com/ecomfrontend/ecom-next/tree/main/packages/sitewide/src/components/universal-bar/sister-brand-links/static-data)</u> for a given environment and adapts them based on host `requestType` and `domain`. For example, Preview template is: `https://www.${requestType}.prod.gaptecholapps.com?ssiteID=GAP`

If `requestType` is `app`, the final link will be: `https://www.app.prod.gaptecholapps.com?ssiteID=GAP`

Otherwise, we will have: `https://www.wip.prod.gaptecholapps.com?ssiteID=GAP`

## Useful Links

- [SPIKE: Investigate feasibility of using new format for sister brand data from JSON or Catalog API](https://gapinc.atlassian.net/browse/PWFO-1228)
