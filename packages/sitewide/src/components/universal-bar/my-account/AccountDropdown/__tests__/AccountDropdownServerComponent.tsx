// @ts-nocheck
import { render, RenderOptions, screen, RenderResult } from 'test-utils';
import AccountDropdownServerComponent from '../AccountDropdownServerComponent';

jest.mock('../AccountDropdownClientComponent');

const getAccountDropdownServerComponent = (): HTMLElement => {
  return screen.getByTestId('account-dropdown-server-component');
};

const renderServerAccountDropdown = (isOpen: boolean, options: RenderOptions = {}): RenderResult => {
  return render(<AccountDropdownServerComponent />, {
    ...options,
  });
};

describe('AccountDropdownServerComponent', () => {
  it('should render with expected classes when closed', () => {
    mockGetPageContext.mockReturnValue({
      brand: 'gap',
      market: 'us',
    });

    renderServerAccountDropdown(false, {});

    expect(getAccountDropdownServerComponent()).toHaveClass('sw_my-account-dropdown');
    expect(getAccountDropdownServerComponent()).not.toHaveClass('sw_my-account-dropdown--open');
  });

  it('should render with expected classes when open', () => {
    renderServerAccountDropdown(false, {});

    expect(getAccountDropdownServerComponent()).toHaveClass('sw_my-account-dropdown');
    expect(getAccountDropdownServerComponent()).toHaveClass('sw_my-account-dropdown--open');
  });
});
