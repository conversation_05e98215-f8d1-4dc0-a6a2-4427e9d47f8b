// @ts-nocheck
import { render, RenderOptions, screen, RenderResult } from 'test-utils';
import { features } from '@sitewide/constants';
import { LOCALIZATION_DATA } from '../__fixture__/localization-data';
import { CashBasedRewardsInfo } from '../RewardsInfo/CashBasedRewardsInfo';
import { mockBrandSites, brandCodeMap } from '../__fixture__/mockBrandSites';

const getRewardsInfoContainer = (): HTMLElement => {
  return screen.queryByTestId('rewards-info-container');
};

const getRewardsInfoLinkMessage = (): HTMLElement => {
  return screen.queryByTestId('rewards-info-link-message');
};

const getRewardsInfoLinkRewardsPrintout = (): HTMLElement => {
  return screen.queryByTestId('rewards-info-link-rewards-printout');
};

const getRewardsInfoLinkCashOfferPrintout = (): HTMLElement => {
  return screen.queryByTestId('rewards-info-link-cash-printout');
};

const lowCashOffer = {
  brandCode: '1',
  amount: 10,
  cashType: 'Gap Cash',
};

const highCashOffer = {
  brandCode: '1',
  amount: 20,
  cashType: 'Gap Cash',
};

const renderCashBasedRewardsInfo = (options: RenderOptions = {}): RenderResult => {
  const optionsWithoutDefaults = { ...options };
  delete optionsWithoutDefaults.localization;
  delete optionsWithoutDefaults.brandSiteData;

  return render(<CashBasedRewardsInfo />, {
    localization: {
      ...LOCALIZATION_DATA['gap']['en_US'],
      ...options?.localization,
    },
    brandSiteData: {
      gidBrandSites: mockBrandSites,
      currentBrandCode: brandCodeMap['gap'],
      ...options?.brandSiteData,
    },
    ...optionsWithoutDefaults,
  });
};

describe('CashBasedRewardsInfo', () => {
  it('should not render when should hide rewards feature flag is active', () => {
    renderCashBasedRewardsInfo({
      enabledFeatures: { [features.SWF_HIDE_REWARDS_INFO]: true },
    });
    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if virtualValueInterrupterStatus is not present', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {},
    });
    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if bounceback is not active', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: false,
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if hasOffers is not present', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {},
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if hasOffers does not equal "true"', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'false',
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if cashOffer array is not present', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if cashOffer is not an array', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: null,
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should not display if cashOffer is an empty array', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [],
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should display cash if there is a valid cash offer for the active brand', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkCashOfferPrintout()).toHaveTextContent(`${lowCashOffer.cashType}: $${lowCashOffer.amount}`);
  });

  it('should NOT display cash if there is a valid cash offer for a different brand than the active brand', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [{ ...lowCashOffer, brandCode: '2' }],
        },
      },
    });

    expect(getRewardsInfoContainer()).not.toBeInTheDocument();
  });

  it('should display the highest cash offer for a brand available', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer, highCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkCashOfferPrintout()).toHaveTextContent(`${highCashOffer.cashType}: $${highCashOffer.amount}`);
  });

  it('should display not display rewards if there is no totalRewardsValue', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkRewardsPrintout()).not.toBeInTheDocument();
  });

  it('should display not display rewards if totalRewardsValue is less than one', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          totalRewardValue: 0,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkRewardsPrintout()).not.toBeInTheDocument();
  });

  it('should display rewards text when rewards amount is greater than zero', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          totalRewardValue: 100,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkRewardsPrintout()).toHaveTextContent('Rewards: $100');
  });

  it('should display a message if bouncebackActive is true and bouncebackText is present', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          bouncebackText: 'an offer',
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkMessage()).toHaveTextContent('You have an offer!');
  });

  it('should display a rewards specific message if bouncebackActive is true and bouncebackText is present and rewardsActive is true', () => {
    renderCashBasedRewardsInfo({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          bouncebackText: 'an offer',
          rewardsActive: true,
        },
        customerAttributes: {
          hasOffers: 'true',
          cashOffer: [lowCashOffer],
        },
      },
    });

    expect(getRewardsInfoLinkMessage()).toHaveTextContent('You have Rewards & an offer!');
  });
});
