import { BrandSiteData, Locales } from '@sitewide/components/legacy/types';
import {
  ordersAndReturnsItem,
  AbbreviatedLocale,
  loyaltyItem,
  loyaltyItemCaAthleta,
} from '@sitewide/components/legacy/navigation/static-data/navigation/account-dropdown/baseItems';
import { getMyAccountUrl, UrlType } from '../../getMyAccountUrl';
import { AccountDropdownItem } from '../AccountDropdownItem/AccountDropdownItem';
import { AccountDropdownItem as AccountDropdownItemType } from '@sitewide/components/types';

export interface USandCADropdownProps {
  accountDropdownItems: AccountDropdownItemType[];
  isLoggedInUser: boolean;
}

interface FoundLoyaltyItem {
  localeAbbr: AbbreviatedLocale;
  loyaltyIndex: number;
  loyaltyTitleWasFound: boolean;
}

export const findLoyaltyItem = (dropdownItems: AccountDropdownItemType[], locale: Locales): FoundLoyaltyItem => {
  const localeAbbr = locale.slice(0, 2) as AbbreviatedLocale;
  const loyaltyIndex = dropdownItems.findIndex(item => {
    return item.text === loyaltyItem(localeAbbr).text;
  });
  const loyaltyTitleWasFound = loyaltyIndex !== -1;

  return { loyaltyIndex, loyaltyTitleWasFound, localeAbbr };
};

export const buildLoyaltyLink = (brandSiteData: BrandSiteData | undefined, isLoggedInUser: boolean, secureUrl: string): string => {
  const loyaltyTarget = '/my-account/value-center';

  if (isLoggedInUser) {
    return `${secureUrl}${loyaltyTarget}`;
  }

  const myAccountUrl = getMyAccountUrl({
    gidBrandSites: brandSiteData?.gidBrandSites,
    currentBrandCode: brandSiteData?.currentBrandCode,
    myAccountUrlType: UrlType.signIn,
  });

  const targetUrl = `${secureUrl}${loyaltyTarget}`;

  return `${myAccountUrl}?targetURL=${targetUrl}`;
};

export const updateLoyaltyLink = (
  dropdownItems: AccountDropdownItemType[],
  newLoyaltyLink: string,
  isCanadaMarket: boolean,
  isAthletaBrand: boolean,
  locale: Locales
): AccountDropdownItemType[] => {
  const dropdownItemsCopy = JSON.parse(JSON.stringify(dropdownItems));
  const { loyaltyIndex, loyaltyTitleWasFound, localeAbbr } = findLoyaltyItem(dropdownItemsCopy, locale);

  const loyaltyText = isAthletaBrand && isCanadaMarket ? loyaltyItemCaAthleta(localeAbbr).text : loyaltyItem(localeAbbr).text;

  const newLoyaltyItem = {
    link: newLoyaltyLink,
    text: loyaltyText,
  };

  if (loyaltyTitleWasFound) {
    dropdownItemsCopy.splice(loyaltyIndex, 1, newLoyaltyItem);
  } else if (isCanadaMarket) {
    dropdownItemsCopy.splice(1, 0, newLoyaltyItem);
  }

  return dropdownItemsCopy;
};

export const updateOrdersAndReturnsLinkToSecure = (
  locale: Locales,
  secureUrl: string,
  dropdownItems: AccountDropdownItemType[],
  isCanadaMarket: boolean
): AccountDropdownItemType[] => {
  const dropdownItemsCopy: AccountDropdownItemType[] = JSON.parse(JSON.stringify(dropdownItems));
  const localeAbbr = locale.slice(0, 2) as AbbreviatedLocale;
  const ordersAndReturnsLocalizedText = ordersAndReturnsItem(localeAbbr).text;

  const isEnglishCanada = isCanadaMarket && localeAbbr !== 'fr';
  const updatedOrdersAndReturnsLink = {
    link: `${secureUrl}/my-account/order-history`,
    text: isEnglishCanada ? 'Orders and Returns' : ordersAndReturnsLocalizedText,
  };

  const ordersAndReturnIndex = dropdownItemsCopy.findIndex(item => item.text === ordersAndReturnsLocalizedText);

  const foundOrderAndReturnItem = ordersAndReturnIndex !== -1;
  if (foundOrderAndReturnItem) {
    dropdownItemsCopy.splice(ordersAndReturnIndex, 1, updatedOrdersAndReturnsLink);
  }

  return dropdownItemsCopy;
};

type AccountDropdownItemsProps = {
  dropdownItems: AccountDropdownItemType[];
};

export const AccountDropdownItems = ({ dropdownItems }: AccountDropdownItemsProps): JSX.Element => {
  return (
    <>
      {dropdownItems.map(({ link, text }) => {
        return <AccountDropdownItem key={text} link={link} text={text} />;
      })}
    </>
  );
};
