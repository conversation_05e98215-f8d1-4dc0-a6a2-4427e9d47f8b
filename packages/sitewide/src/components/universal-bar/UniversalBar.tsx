import { Props as NavigationProps } from './navigation/Navigation';
import { DesktopUniversalBar } from './DesktopUniversalBar';
import { MobileUniversalBar } from './MobileUniversalBar';

type UniversalBarProps = {
  isDesktop?: boolean;
} & Pick<NavigationProps, 'disableAutoRender'>;

export default function UniversalBar(props: UniversalBarProps): JSX.Element {
  const { isDesktop = true } = props;
  return <div className='bg-bk br:bg-inverse-g1 br:text-bk br:bg-b2 text-wh w-full'>{isDesktop ? <DesktopUniversalBar /> : <MobileUniversalBar />}</div>;
}
