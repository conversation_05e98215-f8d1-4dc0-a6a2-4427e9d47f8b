'use client';
import dynamic from 'next/dynamic';
import { Popup, Preheader } from '@ecom-next/marketing-ui/sitewide';
import { PropsWithChildren, Suspense, use } from 'react';
import { SearchConfigProvider } from '@ecom-next/plp-ui/legacy/search-config-provider';
import Header from '@sitewide/components/legacy/header';
import { HeaderProvider } from '@sitewide/components/legacy/compressed-header/HeaderContext';
import { AcquisitionSnackbar } from '@sitewide/components/legacy/acquisition/snackbar/AcquisitionSnackbar';
import { MarketingProvider } from '@ecom-next/marketing-ui/marketing-provider';
import { AttributionImg, type SpecialtyBrands } from '@sitewide/components/third-parties/IosAppsflyerAttribution';
import SkipLinks from '@sitewide/components/legacy/navigation/skip-links';
import { FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { SitewideContextProvider } from '@ecom-next/core/legacy/sitewide';
import { StickyProviderFragment } from '@sitewide/components/legacy/sticky-manager-new/StickyProviderFragment';
import { NavigationProviderWrapper as NavigationProvider } from '@sitewide/components/navigation/navigation-provider';
import { StitchStyleProvider, StitchInverseStyleProvider } from '@ecom-next/core/react-stitch';
import CmsMarketingComponents from '@mui/components/legacy-mui-entry';
import JsonDynamicMarketing from '@mui/components/json-marketing';
import { PageContextProvider } from '@ecom-next/sitewide/app-state-provider';
import { PersonalizationContextProvider } from '@sitewide/providers/personalization';
import BrandInfoProvider from '@sitewide/providers/BrandInfoProvider';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import LocalizationProvider from '@sitewide/providers/localization';
import { CID } from '@mui/fetchMarketing';
import { PageDataPromises } from '@sitewide/state-builder';
import { ContentType } from '@sitewide/components/legacy/types';
import { PageContext } from '@ecom-next/utils/server';
import { features } from '@sitewide/constants';
import { SmartBanner } from '@sitewide/components/smart-banner';
import AppsFlyerPlayerholder from '@sitewide/components/third-parties/AppsFlyer/AppsFlyerPlaceholder';
import { AppConfigHosts, PageParams, PageWrapperProps } from './PageWrapper';

const DynamicShoppingBagReminderPopup = dynamic(() => import('@ecom-next/sitewide/shopping-bag-reminder-popup').then(mod => mod.default), { ssr: false });

interface Props {
  appsFlyer: JSX.Element | null;
  cid: CID;
  contentData: PageDataPromises['marketingDataPromise'];
  contentType: ContentType;
  featureFlagPromise: PageDataPromises['enabledFeaturesPromise'];
  hosts: AppConfigHosts;
  invert: boolean;
  isAcquisitionSnackbarEnabled: boolean;
  isSearchApiEnabled: boolean;
  navDataPromise: PageDataPromises['navDataPromise'];
  pageContext: PageContext;
  pageType: PageWrapperProps['pageType'];
  promoContent: PageWrapperProps['promoContent'];
  searchParams: PageParams['searchParams'];
  serverBrandLogo: JSX.Element | null;
  serverMegaNav: JSX.Element | null;
  serverMyAccount: JSX.Element | null;
  serverNavigationBar: JSX.Element | null;
  serverShoppingBagLink: JSX.Element;
  serverSisterBrandLinks: JSX.Element | null;
  shouldUseIosAppsFlyerAttribution: boolean;
  staticDataPromise: PageDataPromises['staticDataPromise'];
  targetEnv: typeof process.env.TARGET_ENV;
  translations: Record<string, string>;
}

export function CommonAppProviders(props: PropsWithChildren<Props>) {
  const {
    invert,
    promoContent,
    contentData: marketingDataPromise,
    pageContext,
    featureFlagPromise,
    staticDataPromise,
    translations,
    contentType,
    cid,
    targetEnv,
    pageType = 'sitewide',
    hosts,
    navDataPromise,
    children,
  } = props;

  const staticData = use(staticDataPromise);
  const { market, locale, brand, breakpoint, isDesktop } = pageContext;
  const { engineEndpointConfiguration, abSeg, apis, appConfig } = staticData;
  const searchConfigs = JSON.parse(JSON.stringify(engineEndpointConfiguration));
  const autoSuggestUrl = searchConfigs.ProductSearch.autosuggest.url[market];
  searchConfigs.ProductSearch.autosuggest.url = autoSuggestUrl;

  const { enabledFeatures, featureVariables } = use(featureFlagPromise);
  const contentData = use(marketingDataPromise);
  const navData = use(navDataPromise);

  const { desktopNav, otherNav, mobileNav, currentSubcategories } = navData;
  const { brandSiteData, topSearchTerms = [] } = desktopNav;

  const navProviderData = {
    currentSubcategories,
    hamburgerNavData: mobileNav.hamburgerNavData as any,
    hamburgerNavFooterData: mobileNav.hamburgerNavFooterData as any,
    topNavWCDData: (contentData?.sitewide?.topnav?.data as any) ?? {},
    webHierarchy: isDesktop ? (desktopNav.children as any) : [],
  };

  const pageContextProps = {
    ...pageContext,
    brandSiteData,
    contentType,
    brandName: brand,
    abSeg,
    cid,
    cidForPageType: cid,
    targetEnv,
    apis,
    topSearchTerms,
    engineEndpointConfiguration,
    pageType,
    hosts,
    appConfig,
    ...otherNav,
  };

  return (
    <BrandInfoProvider brand={brand} market={market}>
      <LocalizationProvider locale={locale} market={market} translations={translations}>
        <BreakpointProvider initialSizeClass={breakpoint}>
          <FeatureFlagsProvider enabledFeatures={enabledFeatures} featureVariables={featureVariables as any}>
            <PageContextProvider value={pageContextProps}>
              <PersonalizationContextProvider>
                <StitchStyleProvider enabledFeatures={enabledFeatures} brand={brand}>
                  <StitchInverseStyleProvider invert={!!invert}>
                    <MarketingProvider
                      value={promoContent || contentData}
                      jsonMarketingComponent={JsonDynamicMarketing}
                      cmsMarketingComponent={CmsMarketingComponents}
                    >
                      <NavigationProvider {...navProviderData}>{children}</NavigationProvider>
                    </MarketingProvider>
                  </StitchInverseStyleProvider>
                </StitchStyleProvider>
              </PersonalizationContextProvider>
            </PageContextProvider>
          </FeatureFlagsProvider>
        </BreakpointProvider>
      </LocalizationProvider>
    </BrandInfoProvider>
  );
}

export default function ClientHeaderComponent(props: Props) {
  const {
    shouldUseIosAppsFlyerAttribution,
    isAcquisitionSnackbarEnabled,
    searchParams,
    isSearchApiEnabled,
    appsFlyer,
    pageContext,
    featureFlagPromise,
    staticDataPromise,
    pageType,
    serverMyAccount,
    serverSisterBrandLinks,
    serverMegaNav,
    serverShoppingBagLink,
    serverBrandLogo,
    serverNavigationBar,
  } = props;

  const staticData = use(staticDataPromise);
  // the value of breakpoint below is set by the 'client-size-class' header: 'Mobile' or 'Desktop'
  const { market, brand, isDesktop } = pageContext;
  const { engineEndpointConfiguration } = staticData;
  const searchConfigs = JSON.parse(JSON.stringify(engineEndpointConfiguration));
  const autoSuggestUrl = searchConfigs.ProductSearch.autosuggest.url[market];
  searchConfigs.ProductSearch.autosuggest.url = autoSuggestUrl;

  const { enabledFeatures } = use(featureFlagPromise);

  const isShoppingBag = pageType?.toLowerCase() === 'shoppingbag';
  const isUSSpecialtyBrand = market === 'us' && brand !== 'brfs' && brand !== 'gapfs';
  const shouldLoadAttributionImg = !isShoppingBag && isUSSpecialtyBrand;

  const isShoppingBagReminderEnabled = enabledFeatures[features.SWF_SHOPPING_BAG_REMINDER];
  const isOldNavyUs = brand === 'on' && market === 'us';

  const header = (
    <SearchConfigProvider enabledFeatures={enabledFeatures} engineEndpointConfiguration={searchConfigs} isSearchPsApiEnabled={isSearchApiEnabled}>
      <HeaderProvider>
        <SitewideContextProvider>
          <StickyProviderFragment>
            <Header
              serverSisterBrandLinks={serverSisterBrandLinks}
              serverMyAccount={serverMyAccount}
              serverMegaNav={serverMegaNav}
              serverShoppingBagLink={serverShoppingBagLink}
              serverBrandLogo={serverBrandLogo}
              serverNavigationBar={serverNavigationBar}
            />
          </StickyProviderFragment>
        </SitewideContextProvider>
      </HeaderProvider>
    </SearchConfigProvider>
  );

  const newHead = (
    <div id='sitewide-app' className={`z-1 relative ${brand}`} data-testid={`sitewide-app-${isDesktop ? 'desktop' : 'mobile'}`}>
      <Suspense>
        <SmartBanner serverPlaceholder={<AppsFlyerPlayerholder background='white' height='92px' />} />
        <SkipLinks />
        <Popup />
        <Preheader />
      </Suspense>
      {appsFlyer}
      {shouldLoadAttributionImg && <AttributionImg brand={brand as SpecialtyBrands} searchParams={searchParams} />}
      {isOldNavyUs && isShoppingBagReminderEnabled && <DynamicShoppingBagReminderPopup />}
      {header}
      {isAcquisitionSnackbarEnabled && <AcquisitionSnackbar />}
    </div>
  );

  return <CommonAppProviders {...props}>{newHead}</CommonAppProviders>;
}
