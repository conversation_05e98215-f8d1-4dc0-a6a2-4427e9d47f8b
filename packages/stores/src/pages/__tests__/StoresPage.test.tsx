import 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { getStoresPageData } from '../../utils/get-stores-page-data';
import StoresPage from '../StoresPage';

jest.mock('@ecom-next/utils/server', () => ({
  getPageContext: jest.fn(),
}));

jest.mock('../../utils/get-stores-page-data', () => ({
  getStoresPageData: jest.fn(),
}));

jest.mock('../../utils/get-query-params', () => ({
  getQueryParams: jest.fn(() => ({})),
}));

jest.mock('../../datalayer', () => {
  const MockDatalayer = () => <div data-testid='datalayer' />;
  MockDatalayer.displayName = 'MockDatalayer';
  return MockDatalayer;
});

jest.mock('@ecom-next/utils/server', () => {
  const original = jest.requireActual('@ecom-next/utils/server');

  return {
    __esModule: true,
    ...original,
    getPageContext: jest.fn(),
  };
});

describe('StoresPage', () => {
  const mockProps = {
    params: {
      slug: ['us', 'ca', 'san-francisco'],
    },
    searchParams: {},
  };

  beforeEach(() => {
    (getStoresPageData as jest.Mock).mockResolvedValue({ response: '<div class="main-wrapper">Content</div>', source: 'storage' });
    (getStoresPageData as jest.Mock).mockImplementation(() => Promise.resolve({ response: '<div class="main-wrapper">Content</div>', source: 'storage' }));
  });

  it('renders the StoresPage with correct data', async () => {
    await StoresPage(mockProps).then(Component => {
      render(Component);
    });

    const storesPage = screen.getByTestId('stores-page');
    expect(storesPage).toBeInTheDocument();
    expect(storesPage).toHaveAttribute('id', 'store-page');
    expect(storesPage).toHaveAttribute('data-source', 'storage');
    expect(storesPage.innerHTML).toContain('class="main-wrapper font-brand-base"');
  });

  it('renders the StoresPageDatalayer component', async () => {
    await StoresPage(mockProps).then(Component => {
      render(Component);
    });

    const datalayer = screen.getByTestId('datalayer');
    expect(datalayer).toBeInTheDocument();
  });
});
