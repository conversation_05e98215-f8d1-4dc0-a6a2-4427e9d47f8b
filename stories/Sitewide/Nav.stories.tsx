import { Meta } from '@storybook/react';

import UniversalBar from '@ecom-next/sitewide/universal-bar';
import StoryWrapper, { Props } from '../StoryWrapper';

const NavigationWrapper = (props: Props) => <StoryWrapper {...props} StoryComponent={() => <UniversalBar disableAutoRender />} />;

const meta = {
  title: 'Sitewide/Navigation',
  component: NavigationWrapper,
  argTypes: {
    brand: { control: 'radio', options: ['at', 'br'] },
  },
} satisfies Meta<typeof NavigationWrapper>;

export default meta;

export const Navigation = {
  args: {
    data: {},
    brand: 'at',
    isDesktop: true,
  },
};
