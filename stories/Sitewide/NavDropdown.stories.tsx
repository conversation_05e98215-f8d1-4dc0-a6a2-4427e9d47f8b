import { Meta } from '@storybook/react';

import { Dropdown } from '@ecom-next/sitewide/dropdown';
import StoryWrapper, { Props } from '../StoryWrapper';

const NavDropdownWrapper = (props: Props) => <StoryWrapper {...props} StoryComponent={() => <Dropdown disableAutoRender />} />;

const meta = {
  title: 'Sitewide/Navigation/NavDropdown',
  component: NavDropdownWrapper,
  argTypes: {
    brand: { control: 'radio', options: ['at', 'br'] },
  },
} satisfies Meta<typeof NavDropdownWrapper>;

export default meta;

export const NavDropdown = {
  args: {
    data: {},
    brand: 'at',
    isDesktop: true,
  },
};
