import { ChevronIcon } from '@ecom-next/core/fabric/icons';

const ModalNavigationContent = ({ modalPageNumber, navigateToNext }: { modalPageNumber: number; navigateToNext: () => void }) => {
  const textColor = modalPageNumber % 2 === 0 ? '#c5842a' : '#3a7bd5';

  return (
    <div className='flex flex-wrap'>
      <div className='font-brand-base font-font-weight-base-default tracking-font-letter-spacing-base w-fit md:w-[40rem]' style={{ color: textColor }}>
        <h3 className='mb-[1.25rem] text-[1rem]'>Modal Content {modalPageNumber}</h3>
        <p className='my-[1rem] text-[0.867rem]'>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing
          elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur
          adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.
        </p>
        <p className='my-[1rem] text-[0.867rem]'>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing
          elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur
          adipiscing elit, sed do eiusmod tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.
        </p>
        <div className='mt-8 flex w-full justify-end gap-4'>
          <a
            role='button'
            className='cursor-pointer text-sm font-bold'
            onClick={navigateToNext}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                navigateToNext();
              }
            }}
            tabIndex={0}
          >
            <span className='pr-1'>Next Modal Content</span>{' '}
            <ChevronIcon width={12} height={12} viewBox='0 0 12 12' shapeColors={{ pathStrokeColor: textColor }} strokeWidth={2} />
          </a>
        </div>
      </div>
    </div>
  );
};

export { ModalNavigationContent };
