import React from 'react';

type DrawerContentProps = {
  containerClass: string;
  contentClass: string;
};

const DrawerContentForStickyFooter = ({ containerClass, contentClass }: DrawerContentProps) => {
  return (
    <div className={containerClass}>
      <div className={contentClass}>
        Lorem ipsum odor amet, consectetuer adipiscing elit. Curae pellentesque platea fames parturient amet dignissim parturient. Montes tortor luctus ligula
        rhoncus consectetur ipsum fermentum ridiculus. Ut quisque gravida pretium bibendum himenaeos diam at ullamcorper duis. Dignissim ut sodales aenean
        fermentum varius lobortis mollis. Ex nec fames ipsum vivamus netus etiam eleifend venenatis libero.
      </div>
      <div className={contentClass}>
        Lorem ipsum odor amet, consectetuer adipiscing elit. Curae pellentesque platea fames parturient amet dignissim parturient. Montes tortor luctus ligula
        rhoncus consectetur ipsum fermentum ridiculus. Ut quisque gravida pretium bibendum himenaeos diam at ullamcorper duis. Dignissim ut sodales aenean
        fermentum varius lobortis mollis. Ex nec fames ipsum vivamus netus etiam eleifend venenatis libero.
      </div>
      <div className={contentClass}>
        Lorem ipsum odor amet, consectetuer adipiscing elit. Curae pellentesque platea fames parturient amet dignissim parturient. Montes tortor luctus ligula
        rhoncus consectetur ipsum fermentum ridiculus. Ut quisque gravida pretium bibendum himenaeos diam at ullamcorper duis. Dignissim ut sodales aenean
        fermentum varius lobortis mollis. Ex nec fames ipsum vivamus netus etiam eleifend venenatis libero.
      </div>
      <div className={contentClass}>
        Ut scelerisque accumsan ullamcorper diam erat? Mattis penatibus ante donec auctor condimentum imperdiet suspendisse. Tortor arcu fames dui et nec.
        Suscipit lectus efficitur aptent dictumst himenaeos iaculis mollis. Varius fames eu posuere cursus hendrerit. Maximus vestibulum mus erat morbi
        consequat accumsan netus phasellus. Tortor nam nascetur a facilisi in. Quam mollis vitae erat, nec accumsan magnis. Lorem rhoncus sollicitudin amet;
        feugiat semper risus tortor.
      </div>
      <div className={contentClass}>
        Suscipit turpis quisque elit; fames montes erat elementum vivamus iaculis. Purus lacus dapibus viverra vehicula dui iaculis. Cursus varius natoque
        malesuada bibendum, morbi torquent magna. Cubilia commodo integer ad pharetra facilisi metus congue. Ultrices pretium magnis amet ipsum quam nec. Eget
        cubilia condimentum gravida gravida rhoncus penatibus per justo. Per dis est malesuada ex purus volutpat. Velit laoreet habitant sollicitudin congue
        aliquam sit quam.
      </div>
    </div>
  );
};

export { DrawerContentForStickyFooter };
