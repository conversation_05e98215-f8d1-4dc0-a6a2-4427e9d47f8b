.fds_selector-dropdown__sb-container {
  .fds_selector-dropdown-container--hover {
    color: theme('colors.style-selector-size-font-hover');
    background-color: theme('colors.style-selector-size-available-background-hover');
    outline: theme('borderWidth.style-selector-size-border-border-width') solid theme('colors.style-selector-size-border-hover');

    .fds_selector-dropdown-container__control-wrapper {
      .fds_selector-dropdown-container__button {
        label {
          color: theme('colors.style-selector-size-font-hover');
        }

        .fds_selector-dropdown-container__dropdown-arrow {
          .fds_selector-dropdown-container__svg {
            fill: theme('colors.style-selector-size-font-hover');
          }
        }
      }
    }
  }

  .fds_selector-dropdown-container--has-value {
    &.fds_selector-dropdown-container--hover {
      .fds_selector-dropdown-container__control-wrapper {
        .fds_selector-dropdown-container__button {
          color: theme('colors.style-selector-size-font-hover');
        }
      }
    }
  }
}
