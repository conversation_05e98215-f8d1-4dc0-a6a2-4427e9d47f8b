import React, { useState } from 'react';
import { ChevronUp } from '@ecom-next/core/migration/icons';
import { Drawer as DrawerComponent, DrawerProps, DrawerContext } from '@ecom-next/core/fabric/drawer';
import { Button } from '@ecom-next/core/fabric/button';
import { Modal } from '@ecom-next/core/components/fabric/modal';
import { Radio, RadioProp } from '@ecom-next/core/components/fabric/radio';
import { FilterDrawerBopis } from './FilterDrawerBopis';
import { FilterProvider } from './FilterContext';
import { FilterDrawerAccordion } from './FilterDrawerAccordion';

type FilterDrawerProps = Omit<DrawerProps, 'children'> & { brand: string; buttonText?: string };

const FilterDrawer: React.FC<FilterDrawerProps> = (props): React.ReactNode => {
  const orientationValues = {
    bottom: {
      iconDirection: 'flex-row',
      toggleIconAngle: 'rotate-0',
      toggleIconRotationAngle: 'rotate-180',
    },
    top: {
      iconDirection: 'flex-row',
      toggleIconAngle: 'rotate-180',
      toggleIconRotationAngle: 'rotate-0',
    },
    left: {
      iconDirection: 'flex-row',
      toggleIconAngle: 'rotate-90',
      toggleIconRotationAngle: '-rotate-90',
    },
    right: {
      iconDirection: 'flex-row-reverse',
      toggleIconAngle: '-rotate-90',
      toggleIconRotationAngle: 'rotate-90',
    },
  };
  const {
    additionalContent,
    orientation = 'bottom',
    withButton = false,
    buttonText = 'Close Drawer',
  }: FilterDrawerProps & { additionalContent?: React.ReactNode; buttonText?: string; withButton?: boolean } = props;
  const { iconDirection, toggleIconAngle, toggleIconRotationAngle } = orientationValues[orientation];
  const [toggleState, setToggleState] = useState<DrawerProps['toggleState']>(props.toggleState);
  const [shouldClearFilters, setClearFilters] = useState<boolean>(false);
  const [shouldResetSwitch, setResetSwitch] = useState<boolean>(false);

  const toggleDrawer = () => {
    setToggleState(prevState => (!prevState || prevState === 'close' ? 'open' : 'close'));
  };
  const toggleIconClass = `text-white transition duration-500 transform ${toggleState === 'open' ? toggleIconRotationAngle : toggleIconAngle}`;

  let buttonClass = 'flex w-full h-fit items-center justify-center';

  if (props.isCrossBrand) {
    buttonClass = 'flex w-full h-fit items-center justify-center border-t border-t-cb-color-border-subtle bottom-0';
  }

  const clearFilters = () => {
    setClearFilters(true);
    setSelectedStore(undefined);
    setSelectStoreButtonDisabled(true);
  };

  const buttonContent = withButton && (
    <DrawerContext.Consumer>
      {({ closeDrawer }) => (
        <div className='p-spacing-m gap-spacing-m flex w-full'>
          <div className={buttonClass}>
            <Button onClick={clearFilters} isCrossbrand={props.isCrossBrand} kind='secondary'>
              Clear Filters
            </Button>
          </div>
          <div className={buttonClass}>
            <Button kind='critical' onClick={closeDrawer} isCrossbrand={props.isCrossBrand}>
              {buttonText}
            </Button>
          </div>
        </div>
      )}
    </DrawerContext.Consumer>
  );
  const drawerText = toggleState === 'open' ? `Click outside the Drawer to close` : `Open Drawer from the ${orientation}`;
  const updatedProps = { ...props, toggleState, callbackFn: toggleDrawer, buttonContent };
  const additionalContentStyle = `h-[75%] mr-4 ${toggleState === 'open' && 'overflow-hidden fixed'}`;

  const handleRadioSelection = (selectedValue: string | undefined) => {
    const newRadioOptions = radioOptions.map(option => {
      if (option?.value === selectedValue) {
        setSelectStoreButtonDisabled(false);
        return { ...option, checked: true };
      }
      return { ...option, checked: false };
    });
    setRadioOptions(newRadioOptions);
  };

  const [isModalOpen, setModalOpen] = useState<boolean>(false);

  const openModal = () => {
    handleRadioSelection(selectedStore);
    setModalOpen(true);
    setClearFilters(false);
    setResetSwitch(false);
  };

  const closeModal = (withStoreSelection: boolean) => () => {
    const selectedOption = radioOptions.find(option => option.checked)?.value as string;
    if (withStoreSelection) {
      setSelectedStore(selectedOption);
      setResetSwitch(false);
    } else if (!selectedStore) {
      setSelectStoreButtonDisabled(true);
      setResetSwitch(true);
    }
    setModalOpen(false);
    setClearFilters(false);
  };

  const modalRadioOptions: RadioProp[] = [
    {
      id: 'one',
      label: 'Promenade, St. Bruno',
      name: 'modal-psb',
      value: 'Promenade, St. Bruno',
      checked: true,
      disabled: false,
      onChange: () => {},
    },
    {
      id: 'two',
      label: 'Broadway Plaza, Walnut Creek',
      name: 'modal-bpwc',
      value: 'Broadway Plaza, Walnut Creek',
      checked: false,
      disabled: false,
      onChange: () => {},
    },
  ];

  const [radioOptions, setRadioOptions] = React.useState(modalRadioOptions);
  const [selectedStore, setSelectedStore] = React.useState<string | undefined>(undefined);
  const [isSelectStoreButtonDisabled, setSelectStoreButtonDisabled] = React.useState<boolean>(true);
  const handleModalChange = (e: { target: { value: string } }) => handleRadioSelection(e.target.value);
  const clearResetFlag = () => setClearFilters(false);

  return (
    <FilterProvider brand={props.brand}>
      <div className={`fds_filter-drawer-modal__container m-auto h-full w-full ${orientation}-0 p-0`}>
        <Modal
          withHeader
          headerContent='Change Store'
          withCloseIcon
          isOpen={isModalOpen}
          disableCloseOnClickOutside
          headerAlignment='left'
          callbackFn={closeModal(false)}
          portalRoot='#storybook-root'
        >
          <div className='flex w-full flex-col justify-center gap-8'>
            <div className='gap-utk-spacing-xl mt-4 flex w-full flex-col pt-2'>
              {radioOptions.map((option: RadioProp, index: number) => (
                <Radio {...option} onChange={handleModalChange} key={index} />
              ))}
            </div>
            <div className='flex'>
              <Button kind='critical' onClick={closeModal(true)} isCrossbrand={props.isCrossBrand} isDisabled={isSelectStoreButtonDisabled}>
                Select The Store
              </Button>
            </div>
          </div>
        </Modal>
        <div className={`mb-6 flex w-full items-center justify-center`}>
          <button
            aria-expanded='false'
            aria-haspopup='listbox'
            aria-label={drawerText}
            className={`pointer-events-auto relative mx-auto block max-h-[50px] w-[300px] overflow-hidden rounded-[5px] border-0 bg-black px-1.5 pb-2.5 pt-1.5 leading-normal text-white md:max-w-full`}
            data-testid='drawer-button'
            id='drawer-button'
            onClick={toggleDrawer}
          >
            <div className={`flex justify-center leading-normal ${iconDirection}`} data-testid='layout-component'>
              <div className='leading-10'>{drawerText}</div>
              <div className='ml-1 h-10 w-10'>
                <ChevronUp svgClassName={toggleIconClass} />
              </div>
            </div>
          </button>
        </div>
        <div className={additionalContentStyle}>{additionalContent}</div>
        <DrawerComponent {...updatedProps} disableCloseOnClickOutside={isModalOpen} className='fds_filter-drawer-demo'>
          <div className='fds_filter-drawer__container'>
            <FilterDrawerBopis
              callBack={openModal}
              selectedStore={selectedStore}
              shouldClearFilters={shouldClearFilters}
              shouldResetSwitch={shouldResetSwitch}
            />
            <FilterDrawerAccordion clearResetFlag={clearResetFlag} shouldClearFilters={shouldClearFilters} />
          </div>
        </DrawerComponent>
      </div>
    </FilterProvider>
  );
};

export { FilterDrawer };
