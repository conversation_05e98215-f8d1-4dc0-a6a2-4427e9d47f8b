import React from 'react';
import classNames from 'classnames';

interface PriceProps {
  className?: string;
  discountPercentage?: string;
  originalPrice?: string;
  salePrice?: string;
  subText?: string;
}
const Price: React.FC<PriceProps> = ({ className, originalPrice, discountPercentage, salePrice, subText }) => {
  return (
    <div className={classNames('fds_buy-box-price-container flex flex-col', className)}>
      <div className='fds_buy-box-price-container__holder flex'>
        <div className='fds_buy-box-price-container__price line-through'>{originalPrice}</div>
        <div className='fds_buy-box-price-container__price-discount'>{discountPercentage}</div>
      </div>
      <div className='fds_buy-box-price-container__sale-price'>{salePrice}</div>
      {subText && <div className='fds_buy-box-price-container__sub-text'>{subText}</div>}
    </div>
  );
};

export default Price;
