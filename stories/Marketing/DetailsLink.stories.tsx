import { Meta } from '@storybook/react';
import React from 'react';
import { DetailsLink as Details, DetailsLinkProps } from '@ecom-next/marketing-ui/sitewide';
import StoryWrapper, { Props } from '../StoryWrapper';

const DetailsLinkWrapper = (props: Props & DetailsLinkProps) => (
  <StoryWrapper {...props} StoryComponent={props => <Details {...(props as unknown as DetailsLinkProps)} />} />
);

const meta = {
  title: 'Marketing/DetailsLink',
  component: DetailsLinkWrapper,
  tags: ['autodocs'],
  argTypes: {
    brand: { control: 'radio', options: ['at', 'br'] },
  },
} satisfies Meta<typeof DetailsLinkWrapper>;

export default meta;

export const DetailsLink = {
  args: {
    brand: 'at',
    label: 'DETAILS',
    fontColor: 'black',
  },
};
