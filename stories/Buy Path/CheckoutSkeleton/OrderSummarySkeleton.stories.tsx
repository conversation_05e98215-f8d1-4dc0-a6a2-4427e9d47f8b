import { type Meta, type StoryObj } from '@storybook/react';
import { OrderSummarySkeleton as OrderSummaryComponent } from '@ecom-next/checkout/modules/Skeleton/OrderSummarySkeleton';
import StoryWrapper, { Props } from '../../StoryWrapper';

const OrderSummarySkeletonWrapper = (props: Props) => <StoryWrapper {...props} StoryComponent={() => <OrderSummaryComponent />} />;

const meta: Meta = {
  title: 'Buy Path/CheckoutSkeleton',
  component: OrderSummarySkeletonWrapper,
  argTypes: {
    brand: {
      control: 'select',
      options: ['at', 'br', 'on', 'gap', 'gapfs', 'brfs'],
    },
  },
};

export default meta;

export const OrderSummarySkeleton: StoryObj = {
  args: {
    brand: 'at',
  },
};
