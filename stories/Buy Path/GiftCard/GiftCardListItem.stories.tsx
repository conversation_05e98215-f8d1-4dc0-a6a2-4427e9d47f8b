import type { Meta, StoryObj } from '@storybook/react';
import { GiftCardList as GiftCardListComponent } from '@ecom-next/checkout/components/panels/PaymentPanel/GiftCards/GiftCardList/GiftCardList';
import { CheckoutProviderWrapper } from '@ecom-next/checkout/CheckoutProviderWrapper';
import { CardSprites } from '@ecom-next/checkout/components/panels/PaymentPanel/CardSprites';
import StoryWrapper, { Props } from '../../StoryWrapper';
import { giftCardsData as data } from './data/giftCards';

const GiftCardListItemWrapper = (props: Props & { isMobile: boolean }) => {
  return (
    <StoryWrapper
      {...props}
      StoryComponent={() => (
        <div className={props.isMobile ? 'w-[327px]' : 'w-[618px]'}>
          <CardSprites />
          <CheckoutProviderWrapper>
            <GiftCardListComponent giftCards={data} />
          </CheckoutProviderWrapper>
        </div>
      )}
    />
  );
};

const meta: Meta = {
  title: 'Buy Path/GiftCard',
  component: GiftCardListItemWrapper,
  parameters: {
    controls: {
      expanded: true,
    },
  },
  argTypes: {
    brand: {
      control: 'select',
      options: ['at', 'br', 'on', 'gap', 'gapfs', 'brfs'],
    },
    giftCards: {
      control: { type: 'object', value: data },
      description: 'Applied gift cards array',
      table: {
        defaultValue: { summary: 'array' },
      },
    },
  },
};

export default meta;
type Story = StoryObj;

export const GiftCardListDesktop: Story = {
  args: {
    brand: 'at',
    giftCards: data,
    isMobile: false,
  },
};
export const GiftCardListMobile: Story = {
  args: {
    brand: 'at',
    giftCards: data,
    isMobile: true,
  },
};
