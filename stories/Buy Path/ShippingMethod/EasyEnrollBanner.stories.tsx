import { Meta } from '@storybook/react';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { EasyEnrollBanner, EasyEnrollEligibleProps } from '@ecom-next/checkout/components/panels/ShippingMethod/EasyEnrollBanner';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import StoryWrapper, { type Props } from '../../StoryWrapper';

type LocalizationProvidesProps = {
  locale: 'en_US' | 'fr_CA' | 'en_CA';
  market: 'us' | 'ca';
};

const EasyEnrollBannerWrapper = (props: Props & EasyEnrollEligibleProps & LocalizationProvidesProps) => {
  const translations = getLocaleSpecificTranslations(props.locale, ['checkout']);

  return (
    <StoryWrapper
      {...props}
      StoryComponent={() => {
        return (
          <div>
            <LocalizationProvider locale={props.locale} market={props.market} translations={translations}>
              <EasyEnrollBanner isEasyEnrollEligible={props.isEasyEnrollEligible} />
            </LocalizationProvider>
          </div>
        );
      }}
    ></StoryWrapper>
  );
};

const meta: Meta = {
  title: 'Buy Path/ShippingMethod/EasyEnrollBanner',
  component: EasyEnrollBannerWrapper,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    locale: {
      control: 'select',
      options: ['en_US', 'en_CA', 'fr_CA'],
    },
  },
};

export default meta;

export const Default = {
  args: {
    isEasyEnrollEligible: true,
    brand: 'at',
    locale: 'en_US',
  },
};
