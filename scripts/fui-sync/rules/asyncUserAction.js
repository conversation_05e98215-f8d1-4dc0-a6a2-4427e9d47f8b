function transformer(nodePath, j) {
  const { statement } = j.template;
  let actAdded = false;

  function findParent(nPath, type) {
    let parentPath = nPath;
    while (parentPath && parentPath.name !== type && parentPath.value.type !== type) {
      parentPath = parentPath.parentPath;
    }
    // console.log(parentPath, type);
    return parentPath;
  }

  const wrapActReplacer = nPath => {
    const parentPath = findParent(nPath, 'ArrowFunctionExpression');
    const parentParent = parentPath.parentPath.parentPath;
    const parentParentNode = parentParent.value;
    if (
      parentParentNode.type === 'CallExpression' &&
      parentParentNode.callee.type === 'MemberExpression' &&
      parentParentNode.callee.property.name === 'forEach'
    ) {
      return nPath.value;
    } else {
      parentPath.value.async = true;
      return statement`await act(async () => { \n\n ${nPath.value} \n\n })`;
    }
  };
  const replacer = p => {
    const root = j(p.value);

    root
      .find(j.ExpressionStatement, {
        expression: {
          callee: {
            property: { name: 'click' },
          },
        },
      })
      .replaceWith(wrapActReplacer);

    root
      .find(j.ExpressionStatement, {
        expression: {
          callee: {
            name: 'removeFacetOption',
          },
        },
      })
      .replaceWith(wrapActReplacer);

    root
      .find(j.ExpressionStatement, {
        expression: {
          callee: {
            name: 'applyFacetOption',
          },
        },
      })
      .replaceWith(wrapActReplacer);

    root
      .find(j.ExpressionStatement, {
        expression: {
          callee: {
            callee: { name: 'facetChangeHandler' },
          },
        },
      })
      .replaceWith(wrapActReplacer);

    // console.log(p.value);
    return p.value;
  };

  nodePath
    .find(j.ExpressionStatement, {
      expression: {
        callee: { name: 'it' },
      },
    })
    .replaceWith(replacer);

  nodePath
    .find(j.ExpressionStatement, {
      expression: {
        callee: { name: 'test' },
      },
    })
    .replaceWith(replacer);

  nodePath.find(j.ImportDeclaration).replaceWith(p => {
    const imports = p.value.specifiers.map(node => node.local.name);
    const hasActImport = imports.includes('act');

    // this import has `act` and it s imported already
    if (hasActImport && actAdded) {
      const noActImports = p.value.specifiers.filter(node => node.local.name !== 'act');
      // remove `act` import
      if (noActImports.length) {
        // if imports left after `act` removal
        p.value.specifiers = noActImports;
      }
      // remove this import statement
      return [];
    }

    if (hasActImport) {
      actAdded = true;
      return p.value;
    }
    if (actAdded) return p.value;

    if (p.value.source.value !== 'test-utils' && p.value.source.value !== '@sitewide/components/legacy/setupTests/test-helpers') return p.value;

    const actImport = j.importSpecifier(j.identifier('act'));
    p.value.specifiers.push(actImport);
    actAdded = true;

    return p.value;
  });

  return nodePath;
}

module.exports = transformer;
