name: Run Playwright Monitor Tests
run-name: >-
  ${{ github.event_name != 'schedule' &&
    ( github.event_name != 'workflow_run' && inputs.environment || 'prod')
     || (github.event.schedule == '0 7 * * *' && 'preview' || 'prod')
  }}
  ${{ inputs.override-jobs && '- override ' }}- (User-Journey-Monitor-Tests)

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: false
        default: 'stage'
        type: choice
        options:
          - stage
          - preview
          - prod
      override-jobs:
        description: 'Filter by job variables (pipe separated)'
        required: false
        type: string
  schedule:
    - cron: '0 7 * * *'
    - cron: '0 8 * * *'
  workflow_run:
    workflows:
      - Deploy Multi App Prod On Latest Release Creation
    types:
      - completed

jobs:
  vars:
    runs-on: self-hosted
    if: ${{ (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') || github.event_name != 'workflow_run' }}
    outputs:
      environment: ${{ steps.set-environment.outputs.environment }}
      release-sha: ${{ steps.set-environment.outputs.release-sha }}
      prerelease-sha: ${{ steps.set-environment.outputs.prerelease-sha }}
    steps:
      - name: Determine environment
        id: set-environment
        uses: actions/github-script@v7
        with:
          script: |
            let env = '';
            if (${{ github.event_name == 'schedule' }}) {
              if(${{ github.event.schedule == '0 7 * * *' }}) {
                env = 'preview';
              } else {
                env = 'prod';
              }
            } else if (${{ github.event_name == 'workflow_run' }}) {
              env = 'prod';
            } else {
              env = '${{ inputs.environment }}'
            }
            core.setOutput('environment', env);

            const { owner, repo } = context.repo;
            const { data: releases } = await github.rest.repos.listReleases({
              owner,
              repo,
            });
            if (!releases.length) {
              core.setFailed("No releases found");
            }
            const preReleases = releases.filter(r => r.prerelease);
            core.setOutput("prerelease-sha", preReleases[0].name);
            const officialReleases = releases.filter(r => !r.prerelease);
            core.setOutput("release-sha", officialReleases[0].name);

  run-site-vr-tests:
    needs: vars
    if: ${{ (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') || github.event_name != 'workflow_run' }}
    uses: ./.github/workflows/ujt-site-vrt.yaml
    secrets: inherit
    with:
      environment: ${{ needs.vars.outputs.environment }}
  run-ujt-tests:
    if: ${{ (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') || github.event_name != 'workflow_run' }}
    needs: vars
    uses: ./.github/workflows/ujt-run-all-aks-test.yaml
    with:
      environment: ${{ needs.vars.outputs.environment }}
      override-jobs: '*'
      bypass-observepoint-script: true
      run-full-suite: true
    secrets: inherit
  report-slack-status:
    needs:
      - run-ujt-tests
      - run-site-vr-tests
      - vars
    if: ${{ (always() && needs.vars.outputs.environment != 'stage' && github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') || github.event_name == 'schedule' }}
    runs-on: [self-hosted, arc-dind-rootless-enterprise]
    steps:
      - name: Publish update to Slack
        uses: slackapi/slack-github-action@v1.24.0
        with:
          channel-id: ${{ needs.vars.outputs.environment == 'prod' && 'C08LXQ7RTK3' || 'C08L73RSTHV' }}
          payload: |
            {
              "attachments": [
                {
                  "color": "${{ (needs.run-ujt-tests.result == 'success' && needs.run-site-vr-tests.result == 'success') && '#00FF00' || ((needs.run-ujt-tests.result == 'cancelled' || needs.run-site-vr-tests.result == 'cancelled') && '#FFFF00' || '#FF0000') }}",
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "UJT Monitor for ${{ needs.vars.outputs.environment == 'prod' && 'Prod' || 'Preview' }} Status - ${{ github.event.repository.name }} "
                      }
                    },
                    {
                      "type": "divider"
                    },
                    {
                      "type": "section",
                      "fields": [
                        {
                          "type": "mrkdwn",
                          "text": "*Activity:*\nDeploying ${{ github.event.repository.name }} with commit: <${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.vars.outputs.environment == 'prod' && needs.vars.outputs.release-sha || needs.vars.outputs.prerelease-sha }}>"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*UJT Status:*\n${{ needs.run-ujt-tests.result }} ${{ needs.run-ujt-tests.result == 'success' && ':white_check_mark:' || (needs.run-ujt-tests.result == 'cancelled' && ':warning:' || ':x:') }} (view latest <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}| run>)"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*User:*\n${{ github.actor }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*VRT Status:*\n${{ needs.run-site-vr-tests.result }} ${{ needs.run-site-vr-tests.result == 'success' && ':white_check_mark:' || ( needs.run-site-vr-tests.result == 'cancelled' && ':warning:' || ':x:') }} (view latest <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}| run>)"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Version:*\n${{ needs.vars.outputs.environment == 'prod' && needs.vars.outputs.release-sha || needs.vars.outputs.prerelease-sha }}"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
